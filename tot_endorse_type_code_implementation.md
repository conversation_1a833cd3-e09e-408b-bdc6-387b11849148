# TOT业务endorse_type_code特殊处理实现说明

## 实现概述

根据业务需求，成功在AtrBussLrcTotService.java中实现了与AtrBussLrcDdService.java和AtrBussLrcFoService.java相同的endorse_type_code特殊处理逻辑，适用于TOT(比例合约分出)业务类型。

## TOT业务特殊性

### 核心特点
1. **实收即应收**：TOT业务不存在应收保费和手续费的概念，所有现金流都在当期确认
2. **无未来应收**：不存在未来期间的应收款项，简化了现金流计算
3. **EPI限制**：当endorse_type_code包含15或16时，该policy_no对应的原单和批单都不存在EPI（未来保费现金流）

### 与DD/FO业务的差异
| 特性 | DD/FO业务 | TOT业务 |
|------|-----------|---------|
| 应收概念 | 存在应收与实收差异 | 实收即应收 |
| maxDevNo计算 | 需要比较累计应收vs累计实收 | 直接设置为1 |
| 现金流分配 | 第0期+第1期 | 仅第0期 |
| EPI处理 | 正常计算 | 特殊处理时不存在 |

## 修改的文件清单

### 1. 数据模型修改

#### AtrBussToLrcTUlR.java
- **位置**: `ss-ifrs-actuarial\src\main\java\com\ss\ifrs\actuarial\pojo\ecf\vo\lrc\to\AtrBussToLrcTUlR.java`
- **修改内容**:
  - 添加了`endorseTypeCode`字段用于存储批改类型代码
  - 添加了`specialProcessType`字段用于存储特殊处理类型（0:正常, 1:当月有15/16批单, 2:历史有15/16批单但当月没有）
  - **重要修复**：添加了`preCumlEdPremium`和`preCumlEdNetFee`字段用于存储历史累计已赚数据，解决了特殊处理逻辑的编译错误

### 2. SQL层面修改

#### AtrBussLrcToCustDao.xml
- **位置**: `ss-ifrs-actuarial\src\main\resources\mapper\postgres\custom\ecf\AtrBussLrcToCustDao.xml`
- **修改内容**:
  - 在`partitionBaseDataT`方法中添加了`special_process_check` CTE逻辑
  - 实现了与DD和FO业务相同的special_process_type字段计算
  - 在`listBasePolicyT`方法中添加了endorse_type_code和special_process_type字段的查询

#### special_process_check CTE逻辑
```sql
special_process_check AS (
    SELECT
        policy_no,
        -- 检查是否存在15/16类型的批单在当前评估月
        CASE WHEN MAX(CASE WHEN year_month = #{yearMonth,jdbcType=VARCHAR} 
                           AND EXISTS (
                               SELECT 1 FROM unnest(string_to_array(endorse_type_code, ',')) as endorse_code
                               WHERE trim(endorse_code) IN ('15', '16')
                           ) THEN 1 ELSE 0 END) = 1
             THEN true ELSE false END as has_current_month_1516,
        -- 检查是否存在15/16类型的批单（不限月份）
        CASE WHEN MAX(CASE WHEN EXISTS (
                               SELECT 1 FROM unnest(string_to_array(endorse_type_code, ',')) as endorse_code
                               WHERE trim(endorse_code) IN ('15', '16')
                           ) THEN 1 ELSE 0 END) = 1
             THEN true ELSE false END as has_any_1516
    FROM atr_dap_to_paid_t check_unit
    WHERE check_unit.entity_id = #{entityId,jdbcType=BIGINT}
      AND check_unit.year_month <= #{yearMonth,jdbcType=VARCHAR}
      AND check_unit.endorse_type_code IS NOT NULL
    GROUP BY policy_no
)
```

### 3. Java层面修改

#### AtrBussLrcTotService.java
- **位置**: `ss-ifrs-actuarial\src\main\java\com\ss\ifrs\actuarial\service\impl\AtrBussLrcTotService.java`
- **修改内容**:
  - 在`calcIcp`方法中添加了specialProcessType判断逻辑
  - **重要修复**：添加了历史累计已赚数据的获取和设置逻辑，确保特殊处理能够正确计算
  - 实现了简化的maxDev计算（直接设置为1）
  - 根据specialProcessType=1和2进行不同的已赚保费处理
  - 特殊处理时取消EPI（未来保费现金流）

## 重要修复说明

### 历史累计已赚字段修复

在实现过程中发现并修复了一个关键问题：

**问题**：AtrBussToLrcTUlR数据模型类缺少必要的历史累计已赚字段，导致特殊处理逻辑出现编译错误。

**修复**：
1. 在AtrBussToLrcTUlR.java中添加了以下两个字段：
   - `preCumlEdPremium`（历史累计已赚保费）
   - `preCumlEdNetFee`（历史累计已赚手续费）

2. 在calcIcp方法中添加了历史累计已赚数据的获取和设置逻辑：
```java
// 获取历史累计已赚数据
List<Object> unitKey = createUnitKey(icu);
AtrBussToLrcTUl preToLrcUl = preToLrcUls.one(unitKey);
if (preToLrcUl != null) {
    BigDecimal preCumlEdPremium = nvl(preToLrcUl.getPreCumlEdPremium())
            .add(nvl(preToLrcUl.getCurEdPremium()));
    BigDecimal preCumlEdNetFee = nvl(preToLrcUl.getPreCumlEdNetFee())
            .add(nvl(preToLrcUl.getCurEdNetFee()));
    icu.setPreCumlEdPremium(preCumlEdPremium);
    icu.setPreCumlEdNetFee(preCumlEdNetFee);
} else {
    icu.setPreCumlEdPremium(BigDecimal.ZERO);
    icu.setPreCumlEdNetFee(BigDecimal.ZERO);
}
```

**影响**：修复后确保了specialProcessType=1的特殊处理逻辑能够正确计算已赚金额（签单保费-历史累计已赚）。

## 核心业务逻辑

### 1. 简化的maxDev计算
```java
// 特殊处理：对于endorse_type_code包含15或16的批改类型数据
if (specialProcessType == 1 || specialProcessType == 2) {
    // TOT业务特殊性：实收即应收，直接设置maxDev=1，只生成第0期数据
    maxDev = 1;
    treatyEffectiveMonths = 0; // 不存在EPI（未来保费现金流）
}
```

**关键特点**：
- 不需要比较累计应收与累计实收（因为实收即应收）
- 直接设置maxDev=1，确保只生成第0期数据
- 设置treatyEffectiveMonths=0，取消EPI计算

### 2. 已赚保费计算
```java
if (specialProcessType == 1 && i == 0) {
    // specialProcessType=1：第0期已赚金额 = 签单保费 - 历史累计已赚
    BigDecimal specialEdPremium = icu.getPremium().subtract(nvl(icu.getPreCumlEdPremium()));
    BigDecimal specialEdNetFee = icu.getNetFee().subtract(nvl(icu.getPreCumlEdNetFee()));
    // ... 设置已赚相关字段
} else if (specialProcessType == 2 && i == 0) {
    // specialProcessType=2：第0期已赚金额 = 0
    // ... 设置已赚字段为0
}
```

### 3. 现金流分配
- **第0期**：当期实收保费和净额结算手续费
- **无第1期**：因为TOT业务实收即应收，不存在后续期间的现金流

## 处理逻辑对比

| 处理类型 | 含义 | maxDev | 第0期处理 | EPI处理 | 适用场景 |
|----------|------|--------|-----------|---------|----------|
| specialProcessType=0 | 正常处理 | 正常计算 | 正常计算 | 正常计算 | 无15/16批改类型 |
| specialProcessType=1 | 只计算第0期 | 1 | 已赚=签单保费-历史累计已赚 | 不存在 | 当月有15/16批单 |
| specialProcessType=2 | 不计算发展期 | 1 | 已赚=0 | 不存在 | 历史有15/16批单但当月没有 |

## 与DD/FO业务的一致性

### 相同点
1. SQL层面的special_process_check CTE逻辑完全一致
2. specialProcessType的判断逻辑相同
3. 已赚保费计算原则相同
4. 特殊处理触发条件相同

### 不同点
1. **maxDev计算**：TOT业务直接设置为1，DD/FO业务需要比较累计应收与累计实收
2. **现金流分配**：TOT业务只有第0期，DD/FO业务可能有第0期和第1期
3. **EPI处理**：TOT业务特殊处理时完全取消EPI，DD/FO业务不涉及EPI
4. **数据源表**：TOT业务使用atr_dap_to_paid_t表

## 数据库表结构修改

### 临时表字段添加
需要为`atr_temp_to_t_lrc_u`临时表添加以下字段：

```sql
-- 添加字段到 atr_temp_to_t_lrc_u 临时表
ALTER TABLE atruser.atr_temp_to_t_lrc_u ADD COLUMN IF NOT EXISTS endorse_type_code VARCHAR(100) NULL;
ALTER TABLE atruser.atr_temp_to_t_lrc_u ADD COLUMN IF NOT EXISTS special_process_type INTEGER NULL;
```

相关SQL脚本已创建：`sql\add_endorse_type_code_fields_to_tot_tables.sql`

## 验证要求

### 测试场景
1. **当月有15/16批单**：验证specialProcessType=1的处理逻辑
2. **历史有15/16批单但当月没有**：验证specialProcessType=2的处理逻辑
3. **只生成第0期数据**：验证maxDev=1的正确性
4. **EPI不存在**：验证treatyEffectiveMonths=0的效果
5. **正常业务记录**：确保不受特殊处理影响

### 关键验证点
1. **SQL逻辑验证**：special_process_type在同一policy_no下的一致性
2. **maxDev计算**：确保特殊处理时maxDev=1
3. **已赚计算**：specialProcessType=1时第0期正常计算；specialProcessType=2时为0
4. **EPI处理**：特殊处理时treatyEffectiveMonths=0
5. **现金流分配**：只有第0期数据，无后续期间现金流

## 部署步骤

1. **数据库表结构修改**：
   - 执行`sql\add_endorse_type_code_fields_to_tot_tables.sql`脚本
   - 验证字段添加成功

2. **代码部署**：
   - 部署修改后的Java类和XML配置文件
   - 重启相关服务

3. **测试验证**：
   - 在测试环境充分验证各种场景
   - 数据对比：对比实现前后的计算结果
   - 监控观察：部署后密切关注相关业务指标

4. **回滚准备**：
   - 准备快速回滚方案以应对意外情况
   - 备份相关配置文件

## 实现完整性检查

✅ **数据模型**：AtrBussToLrcTUlR.java添加必要字段  
✅ **SQL逻辑**：AtrBussLrcToCustDao.xml实现special_process_check CTE  
✅ **Java逻辑**：AtrBussLrcTotService.java实现特殊处理逻辑  
✅ **TOT特殊性**：简化maxDev计算，取消EPI处理  
✅ **业务一致性**：与DD和FO业务保持逻辑一致  
⚠️ **数据库表**：需要执行SQL脚本添加临时表字段  

## 总结

成功在TOT业务中实现了endorse_type_code特殊处理逻辑，充分考虑了TOT业务的特殊性：

### 关键特性
- **简化设计**：基于TOT业务实收即应收的特点，简化了maxDev计算逻辑
- **业务一致性**：与DD和FO业务保持处理原则的一致性
- **EPI处理**：特殊处理时正确取消EPI（未来保费现金流）
- **向后兼容性**：不影响现有正常业务记录的处理

### 实现优势
- **逻辑清晰**：TOT业务的特殊处理逻辑更加简洁明了
- **性能优化**：避免了不必要的累计应收与累计实收比较
- **维护性好**：代码结构清晰，易于理解和维护
- **扩展性强**：为后续类似业务的实现提供了良好的参考模式

call acc_pack_commonutils_proc_modify_table_columntype('acc_conf_model_code', 'model_code_code', 'varchar(255)');

call acc_pack_commonutils_proc_modify_table_columntype('acc_conf_model_codehis', 'model_code_code', 'varchar(255)');


call acc_pack_commonutils_proc_modify_table_columntype('acc_conf_model_code', 'model_code_code', 'varchar(255)');

call acc_pack_commonutils_proc_modify_table_columntype('acc_conf_model_codehis', 'model_code_code', 'varchar(255)');

call acc_pack_commonutils_proc_modify_table_columntype('acc_conf_model_code', 'model_column_code', 'varchar(255)');

call acc_pack_commonutils_proc_modify_table_columntype('acc_conf_model_codehis', 'model_column_code', 'varchar(255)');


call acc_pack_commonutils_proc_add_table_column('acc_dap_entry_data', 'border_is', 'VARCHAR(3)', '合同初始确认标识');
call acc_pack_commonutils_proc_add_table_column('acc_duct_entry_data_all', 'border_is', 'VARCHAR(3)', '合同初始确认标识');
call acc_pack_commonutils_proc_add_table_column('acc_duct_entry_data_success', 'border_is', 'VARCHAR(3)', '合同初始确认标识');
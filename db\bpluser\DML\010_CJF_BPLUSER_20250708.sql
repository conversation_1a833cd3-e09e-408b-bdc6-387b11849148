
DELETE from bpluser.bpl_conf_code where upper_code_id = (SELECT code_id from bpluser.bpl_conf_code where code_code = 'BorderIs' and upper_code_id = 0);
DELETE from bpluser.bpl_conf_code where code_code = 'BorderIs';
INSERT INTO bpluser.bpl_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('bpl_seq_conf_code'), 0, 'BorderIs', '合同初始确认标识', '合同初始确认标识', '合同初始确认标识', NULL, NULL, '1', 1, NULL, 1, NULL, NULL);


INSERT INTO bpluser.bpl_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('bpl_seq_conf_code'), (SELECT code_id from bpluser.bpl_conf_code where code_code = 'BorderIs' and upper_code_id = 0), '1', '已确认', '已确认', '已确认', 1, NULL, '1', 1, NULL, 1, NULL, NULL);
INSERT INTO bpluser.bpl_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('bpl_seq_conf_code'), (SELECT code_id from bpluser.bpl_conf_code where code_code = 'BorderIs' and upper_code_id = 0), '0', '不区分', '不区分', '不区分', 2, NULL, '1', 1, NULL, 1, NULL, NULL);


DELETE FROM bpluser.bpl_conf_code where upper_code_id = 1481 and code_code like 'OE%';
INSERT INTO bpluser.bpl_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('bpl_seq_conf_code'), 1481, 'OE0001', '业管费', '业管费', '业管费', '6402010000', 108, '1', 1, '2025-04-11 15:02:09.444', 1, '2025-04-11 15:02:09.444', 1);
INSERT INTO bpluser.bpl_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('bpl_seq_conf_code'), 1481, 'OE0002', '税金及附加', '税金及附加', '税金及附加', '6402020000', 109, '1', 1, '2025-04-11 15:02:09.444', 1, '2025-04-11 15:02:09.444', 1);
INSERT INTO bpluser.bpl_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('bpl_seq_conf_code'), 1481, 'OE0003', '其他业务成本', '其他业务成本', '其他业务成本', '6402030000', 110, '1', 1, '2025-04-11 15:02:09.445', 1, '2025-04-11 15:02:09.445', 1);
INSERT INTO bpluser.bpl_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('bpl_seq_conf_code'), 1481, 'OE0004', '营业外支出', '营业外支出', '营业外支出', '6402040000', 111, '1', 1, '2025-04-11 15:02:09.445', 1, '2025-04-11 15:02:09.445', 1);
INSERT INTO bpluser.bpl_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('bpl_seq_conf_code'), 1481, 'OE0005', '手续费及佣金支出（系统探源不到单的部分）', '手续费及佣金支出（系统探源不到单的部分）', '手续费及佣金支出（系统探源不到单的部分）', '6402050001', 112, '1', 1, '2025-04-11 15:02:09.445', 1, '2025-04-11 15:02:09.445', 1);
INSERT INTO bpluser.bpl_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('bpl_seq_conf_code'), 1481, 'OE0006', '赔款支出', '赔款支出', '赔款支出', '6402050002', 113, '1', 1, '2025-04-11 15:02:09.445', 1, '2025-04-11 15:02:09.445', 1);

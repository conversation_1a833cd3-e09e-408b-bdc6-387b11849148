call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_import_main');
CREATE TABLE atruser.atr_buss_ibnr_import_main (
  ibnr_main_id int8 NOT NULL,
  entity_id int8,
  year_month varchar(6) COLLATE pg_catalog.default,
  ibnr_range varchar(1) COLLATE pg_catalog.default,
	provision_ratio  numeric(32,8),
  use_import_os_is varchar(32) COLLATE pg_catalog.default,
  version_no varchar(32) COLLATE pg_catalog.default,
  confirm_is char(1) COLLATE pg_catalog.default,
  confirm_id int8,
  confirm_time timestamp(6),
  creator_id int8,
  create_time timestamp(6),
  updator_id int8,
  update_time timestamp(6),
  CONSTRAINT atr_buss_ibnr_import_main_pkey PRIMARY KEY (ibnr_main_id)
);
ALTER TABLE atruser.atr_buss_ibnr_import_main OWNER TO atruser;
COMMENT ON COLUMN atruser.atr_buss_ibnr_import_main.entity_id IS 'entity_id|业务单位id';


call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_import_detail');
CREATE TABLE atruser.atr_buss_ibnr_import_detail (
  ibnr_detail_id int8 NOT NULL,
  ibnr_main_id int8 NOT NULL,
  year_month varchar(6) COLLATE pg_catalog.default,
	accident_quarter varchar(6) COLLATE pg_catalog.default,
  business_model varchar(64) COLLATE pg_catalog.default,
  risk_class_code varchar(64) COLLATE pg_catalog.default,
  center_code varchar(64) COLLATE pg_catalog.default,
  ri_dept varchar(32) COLLATE pg_catalog.default,
  treaty_no varchar(32) COLLATE pg_catalog.default,
  ibnr_amount numeric(32,8),
	ri_ibnr_amount  numeric(32,8),
	ri_case_amount  numeric(32,8),
  CONSTRAINT atr_buss_ibnr_import_detail_pkey PRIMARY KEY (ibnr_detail_id)
) ;

ALTER TABLE atruser.atr_buss_ibnr_import_detail 
  OWNER TO atruser;


call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_import_calim');
CREATE TABLE atruser.atr_buss_ibnr_import_calim (
  ibnr_claim_id int8 NOT NULL,
  ibnr_main_id int8 NOT NULL,
  treaty_no varchar(32) COLLATE pg_catalog.default,
  policy_no varchar(64) COLLATE pg_catalog.default,
  kind_code varchar(64) COLLATE pg_catalog.default,
  claim_no varchar(64) COLLATE pg_catalog.default,
  CONSTRAINT atr_buss_ibnr_import_calim_pkey PRIMARY KEY (ibnr_claim_id)
) ;

ALTER TABLE atruser.atr_buss_ibnr_import_calim OWNER TO atruser;
 
 
---------------------------------------------------------------- 
call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_action');
 CREATE TABLE atruser.atr_buss_ibnr_alloc_action (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  business_source_code varchar(2)  NOT NULL,
  status varchar(1)  NOT NULL,
  confirm_is varchar(1)  NOT NULL,
  confirm_user int8,
  confirm_time timestamp(0),
  creator_id int8,
  create_time timestamp(0),
  updator_id int8,
  update_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_action PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_action OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_action ON atruser.atr_buss_ibnr_alloc_action USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.year_month IS '业务年月|评估期';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.business_source_code IS '业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.status IS '执行状态|R-执行中；E-执行异常；S-执行成功';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.confirm_is IS '是否确认|1-是、0-否';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.confirm_user IS '确认人';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.confirm_time IS '确认时间';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.creator_id IS '创建人';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.create_time IS '创建时间';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.updator_id IS '最后修改人';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.update_time IS '最后修改时间';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_action IS '分摊操作表';



call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_dd_result');
CREATE TABLE atruser.atr_buss_ibnr_alloc_dd_result (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  evaluate_approach varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  policy_no varchar(64)  NOT NULL,
	risk_class_code varchar(64),
  kind_code 	varchar(64)  NOT NULL,
  ep_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
  ibnr_amount  numeric(32,8),
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_dd_result PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_dd_result 
  OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_dd_result ON atruser.atr_buss_ibnr_alloc_dd_result USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.year_month IS '业务年月|评估期';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_dd_result IS 'DD分摊结果操作表';


call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_ti_result');
CREATE TABLE atruser.atr_buss_ibnr_alloc_ti_result (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  evaluate_approach varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  ri_dept varchar(32) COLLATE pg_catalog.default,
  treaty_no varchar(32) COLLATE pg_catalog.default,
  risk_class_code varchar(64) COLLATE pg_catalog.default,
  ibnr_amount numeric(32,8),
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_ti_result PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_ti_result 
  OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_ti_result ON atruser.atr_buss_ibnr_alloc_ti_result USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_ti_result.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_ti_result.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_ti_result.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_ti_result.year_month IS '业务年月|评估期';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_ti_result IS 'TI分摊结果操作表';


call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_fo_result');
CREATE TABLE atruser.atr_buss_ibnr_alloc_fo_result (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  evaluate_approach varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  treaty_no varchar(32) COLLATE pg_catalog.default,
	risk_class_code varchar(64),
  policy_no varchar(64)  NOT NULL,
  kind_code 	varchar(64)  NOT NULL,
  ep_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
  ibnr_amount numeric(32,8),
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_fo_result PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_fo_result 
  OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_fo_result ON atruser.atr_buss_ibnr_alloc_fo_result USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_fo_result.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_fo_result.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_fo_result.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_fo_result.year_month IS '业务年月|评估期';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_fo_result IS '临分分出分摊结果操作表';


call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_to_result');
CREATE TABLE atruser.atr_buss_ibnr_alloc_to_result (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  evaluate_approach varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  treaty_no varchar(32) COLLATE pg_catalog.default,
  risk_class_code varchar(64),
  policy_no varchar(64)  NOT NULL,
  kind_code 	varchar(64)  NOT NULL,
  ep_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
  ibnr_amount numeric(32,8),
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_to_result PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_to_result 
  OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_to_result ON atruser.atr_buss_ibnr_alloc_to_result USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result.year_month IS '业务年月|评估期';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_to_result IS '合约分出分摊结果操作表';

call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_to_result_x');
CREATE TABLE atruser.atr_buss_ibnr_alloc_to_result_x (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  evaluate_approach varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  treaty_no varchar(32) COLLATE pg_catalog.default,
	risk_class_code varchar(64),
  policy_no varchar(64)  NOT NULL,
  kind_code 	varchar(64)  NOT NULL,
  claim_no varchar(64)  NOT NULL,
  rl_amount  numeric(32,8),
  rl_ratio  numeric(32,8),
  case_amount numeric(32,8),
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_to_result_x PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_to_result_x 
  OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_to_result_x ON atruser.atr_buss_ibnr_alloc_to_result_x USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result_x.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result_x.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result_x.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result_x.year_month IS '业务年月|评估期';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_to_result_x IS '超配分摊结果操作表';


call atr_pack_commonutils_proc_drop_table('atr_duct_ibnr_alloc_ep');
CREATE TABLE atruser.atr_duct_ibnr_alloc_ep (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
	data_type varchar(6)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  acc_year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  policy_no varchar(64)  NOT NULL,
	risk_class_code varchar(64) COLLATE pg_catalog.default,
  kind_code 	varchar(64)  NOT NULL,
  ep_amount  numeric(32,8),
  ep_sum_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_duct_ibnr_alloc_ep PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_duct_ibnr_alloc_ep 
  OWNER TO atruser;

CREATE INDEX idx_atr_duct_ibnr_alloc_ep ON atruser.atr_duct_ibnr_alloc_ep USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);


call atr_pack_commonutils_proc_drop_table('atr_duct_ibnr_alloc_ep_out');
CREATE TABLE atruser.atr_duct_ibnr_alloc_ep_out (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
	data_type varchar(6)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  acc_year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  treaty_no varchar(64)  NOT NULL,
  policy_no varchar(64)  NOT NULL,
	risk_class_code varchar(64) COLLATE pg_catalog.default,
  kind_code 	varchar(64)  NOT NULL,
  ep_amount  numeric(32,8),
  ep_sum_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_duct_ibnr_alloc_ep_out PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_duct_ibnr_alloc_ep_out 
  OWNER TO atruser;

CREATE INDEX idx_atr_duct_ibnr_alloc_ep_out ON atruser.atr_duct_ibnr_alloc_ep_out USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);


call atr_pack_commonutils_proc_drop_table('atr_duct_ibnr_alloc_ep_out_x');
CREATE TABLE atruser.atr_duct_ibnr_alloc_ep_out_x (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
	data_type varchar(6)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  acc_year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  treaty_no varchar(64)  NOT NULL,
  policy_no varchar(64)  NOT NULL,
  claim_no varchar(64)  NOT NULL,
	risk_class_code varchar(64) COLLATE pg_catalog.default,
  kind_code 	varchar(64)  NOT NULL,
  case_amount  numeric(32,8),
  case_sum_amount  numeric(32,8),
  case_ratio  numeric(32,8),
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT px_atr_duct_ibnr_alloc_ep_out_x PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_duct_ibnr_alloc_ep_out_x 
  OWNER TO atruser;

CREATE INDEX idx_atr_duct_ibnr_alloc_ep_out_x ON atruser.atr_duct_ibnr_alloc_ep_out_x USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);


CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_duct_ibnr_alloc_ep');
create sequence atr_seq_duct_ibnr_alloc_ep;   
CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_duct_ibnr_alloc_ep_out');
create sequence atr_seq_duct_ibnr_alloc_ep_out;   
CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_duct_ibnr_alloc_ep_out_x');
create sequence atr_seq_duct_ibnr_alloc_ep_out_x;   

CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_buss_ibnr_alloc_action');
create sequence atr_seq_buss_ibnr_alloc_action;   

CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_buss_ibnr_alloc_dd_result');
create sequence atr_seq_buss_ibnr_alloc_dd_result;   

CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_buss_ibnr_alloc_ti_result');
create sequence atr_seq_buss_ibnr_alloc_ti_result;   

CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_buss_ibnr_alloc_to_result');
create sequence atr_seq_buss_ibnr_alloc_to_result;   

CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_buss_ibnr_alloc_fo_result');
create sequence atr_seq_buss_ibnr_alloc_fo_result;   

CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_buss_ibnr_alloc_out_result_x');
create sequence atr_seq_buss_ibnr_alloc_out_result_x;   

CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_buss_ibnr_import_calim');
create sequence atr_seq_buss_ibnr_import_calim;   

 




drop table if exists atr_buss_ibnr_import_main;
CREATE TABLE atruser.atr_buss_ibnr_import_main (
  ibnr_main_id int8 NOT NULL,
  entity_id int8,
  year_month varchar(6) COLLATE pg_catalog.default,
  ibnr_range varchar(1) COLLATE pg_catalog.default,
	provision_ratio  numeric(32,8),
  use_import_os_is varchar(32) COLLATE pg_catalog.default,
  version_no varchar(32) COLLATE pg_catalog.default,
  confirm_is char(1) COLLATE pg_catalog.default,
  confirm_id int8,
  confirm_time timestamp(6),
  creator_id int8,
  create_time timestamp(6),
  updator_id int8,
  update_time timestamp(6),
  CONSTRAINT atr_buss_ibnr_import_main_pkey PRIMARY KEY (ibnr_main_id)
);
ALTER TABLE atruser.atr_buss_ibnr_import_main 
  OWNER TO atruser;

COMMENT ON COLUMN atruser.atr_buss_ibnr_import_main.entity_id IS 'entity_id|业务单位id';

 

drop table if exists atr_buss_ibnr_import_detail;
CREATE TABLE atruser.atr_buss_ibnr_import_detail (
  ibnr_detail_id int8 NOT NULL,
  ibnr_main_id int8 NOT NULL,
  year_month varchar(6) COLLATE pg_catalog.default,
	accident_quarter varchar(6) COLLATE pg_catalog.default,
  business_model varchar(64) COLLATE pg_catalog.default,
  risk_class_code varchar(64) COLLATE pg_catalog.default,
  center_code varchar(64) COLLATE pg_catalog.default,
  ri_dept varchar(32) COLLATE pg_catalog.default,
  treaty_no varchar(32) COLLATE pg_catalog.default,
  treaty_name varchar(1000) COLLATE pg_catalog.default,
  ibnr_amount numeric(32,8),
	ri_ibnr_amount  numeric(32,8),
	ri_case_amount  numeric(32,8),
  CONSTRAINT atr_buss_ibnr_import_detail_pkey PRIMARY KEY (ibnr_detail_id)
) ;

ALTER TABLE atruser.atr_buss_ibnr_import_detail 
  OWNER TO atruser;



drop table if EXISTS atr_buss_claim_import_main;
CREATE TABLE atruser.atr_buss_claim_import_main (
  claim_main_id int8 NOT NULL,
  entity_id int8,
  year_month varchar(6) COLLATE pg_catalog.default,
  version_no varchar(32) COLLATE pg_catalog.default,
  confirm_is char(1) COLLATE pg_catalog.default,
  confirm_id int8,
  confirm_time timestamp(6),
  creator_id int8,
  create_time timestamp(6),
  updator_id int8,
  update_time timestamp(6),
  CONSTRAINT atr_buss_claim_import_main_pk PRIMARY KEY (claim_main_id)
) ;
ALTER TABLE atruser.atr_buss_claim_import_main OWNER TO atruser;


drop table if exists atr_buss_ibnr_import_claim;
CREATE TABLE atruser.atr_buss_ibnr_import_claim (
  ibnr_claim_id int8 NOT NULL,
  claim_main_id int8 NOT NULL,
  treaty_no varchar(32) COLLATE pg_catalog.default,
	treaty_name varchar(1000),
  policy_no varchar(64) COLLATE pg_catalog.default,
  kind_code varchar(64) COLLATE pg_catalog.default,
  claim_no varchar(64) COLLATE pg_catalog.default,
  CONSTRAINT atr_buss_ibnr_import_claim_pkey PRIMARY KEY (ibnr_claim_id)
) ;
ALTER TABLE atruser.atr_buss_ibnr_import_claim 
  OWNER TO atruser;
 
CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_buss_ibnr_import_calim');
CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_buss_ibnr_import_claim');
create sequence atr_seq_buss_ibnr_import_claim;   

CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_buss_claim_import_main');
create sequence atr_seq_buss_claim_import_main;   









 
/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-12-23 10:53:35
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import com.ss.platform.core.annotation.SsTranslateCode;
import com.ss.platform.core.constant.SystemConstant;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-12-23 10:53:35<br/>
 * Description: null<br/>
 * Table Name: bbs_conf_quota_def<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 * <AUTHOR>
 */
public class AtrConfQuotaDef implements Serializable {
    /**
     * Database column: bbs_conf_quota_def.quota_def_id
     * Database remarks: quotaDefId|主键
     */
    @ApiModelProperty(value = "quotaDefId|主键", required = true)
    private Long quotaDefId;

    /**
     * Database column: bbs_conf_quota_def.quota_code
     * Database remarks: quotaCode|指标编码
     */
    @ApiModelProperty(value = "quotaCode|指标编码", required = false)
    private String quotaCode;

    /**
     * Database column: bbs_conf_quota_def.quota_c_name
     * Database remarks: quotaCName|指标简体名称
     */
    @ApiModelProperty(value = "quotaCName|指标简体名称", required = false)
    private String quotaCName;

    /**
     * Database column: bbs_conf_quota_def.quota_t_name
     * Database remarks: quotaLName|指标本地语言名称
     */
    @ApiModelProperty(value = "quotaLName|指标本地语言名称", required = false)
    private String quotaLName;

    /**
     * Database column: bbs_conf_quota_def.quota_e_name
     * Database remarks: quotaEName|指标英文名称
     */
    @ApiModelProperty(value = "quotaEName|指标英文名称", required = false)
    private String quotaEName;

    /**
     * Database column: bbs_conf_quota_def.code_type
     * Database remarks: codeType|基础代码类型
     */
    @ApiModelProperty(value = "codeType|基础代码类型", required = false)
    private String codeType;

    /**
     * Database column: bbs_conf_quota_def.quota_class
     * Database remarks: quotaClass|指标分类
     */
    @ApiModelProperty(value = "quotaClass|指标归类", required = false)
    @SsTranslateCode(context = SystemConstant.AtrIdentity.APP_CONTEXT,codeCodeIdx = "QuotaClass")
    private String quotaClass;

    /**
     * Database column: bbs_conf_quota_def.quota_group
     * Database remarks: quotaGroup|指标分组
     */
    @ApiModelProperty(value = "quotaGroup|指标分组", required = false)
    private String quotaGroup;

    /**
     * Database column: bbs_conf_quota_def.quota_type
     * Database remarks: quotaType|指标类型
     */
    @ApiModelProperty(value = "quotaType|指标类型", required = false)
    private String quotaType;

    /**
     * Database column: bbs_conf_quota_def.quota_value_type
     * Database remarks: quotaValueType|指标数据类型
     */
    @ApiModelProperty(value = "quotaValueType|指标数据类型", required = false)
    private String quotaValueType;

    /**
     * Database column: bbs_conf_quota_def.rule_id
     * Database remarks: ruleId|规则ID
     */
    @ApiModelProperty(value = "ruleId|规则ID", required = true)
    private Long ruleId;

    /**
     * Database column: bbs_conf_quota_def.percent_hundred_is
     * Database remarks: percentHundredIs|是否统计百分百
     */
    @ApiModelProperty(value = "percentHundredIs|是否统计百分百", required = false)
    private String percentHundredIs;

    /**
     * Database column: bbs_conf_quota_def.display_no
     * Database remarks: displayNo|排序序号
     */
    @ApiModelProperty(value = "displayNo|排序序号", required = true)
    private Long displayNo;

    /**
     * Database column: bbs_conf_quota_def.version_no
     * Database remarks: serialNo|版本号
     */
    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Integer serialNo;

    /**
     * Database column: bbs_conf_quota_def.valid_is
     * Database remarks: validIs|是否有效
     */
    @ApiModelProperty(value = "validIs|是否有效", required = true)
    private String validIs;

    @ApiModelProperty(value = "quotaEffect|假设值影响", required = false)
    private String quotaEffect;

    /**
     * Database column: bbs_conf_quota_def.audit_state
     * Database remarks: audit_State|审核状态
     */
    @ApiModelProperty(value = "audit_State|审核状态", required = false)
    private String auditState;

    /**
     * Database column: bbs_conf_quota_def.checked_msg
     * Database remarks: checked_msg|审核意见
     */
    @ApiModelProperty(value = "checked_msg|审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: bbs_conf_quota_def.checked_id
     * Database remarks: checked_id|审核人
     */
    @ApiModelProperty(value = "checked_id|审核人", required = false)
    private Long checkedId;

    /**
     * Database column: bbs_conf_quota_def.checked_time
     * Database remarks: checked_time|审核时间
     */
    @ApiModelProperty(value = "checked_time|审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: bbs_conf_quota_def.creator_id
     * Database remarks: creator_id|创建人
     */
    @ApiModelProperty(value = "creator_id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: bbs_conf_quota_def.create_time
     * Database remarks: create_time|创建时间
     */
    @ApiModelProperty(value = "create_time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: bbs_conf_quota_def.updator_id
     * Database remarks: updator_id|最后修改人
     */
    @ApiModelProperty(value = "updator_id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: bbs_conf_quota_def.update_time
     * Database remarks: update_time|最后修改时间
     */
    @ApiModelProperty(value = "update_time|最后修改时间", required = false)
    private Date updateTime;

    /**
     * Database column: bbs_conf_quota_def.dimension
     * Database remarks: dimension|颗粒度
     */
    @ApiModelProperty(value = "dimension|颗粒度", required = false)
    private String dimension;

    /**
     * Database column: bbs_conf_quota_def.remark
     * Database remarks: remark|备注
     */
    @ApiModelProperty(value = "remark|备注", required = false)
    private String remark;

    /**
     * Database column: bbs_conf_quota_def.remark
     * Database remarks: remark|备注
     */
    @ApiModelProperty(value = "auto_calculated_is|是否自动计算", required = false)
    private String autoCalculatedIs;

    private String businessModel;
    private String businessDirection;

    private String businessSourceCode;

    private static final long serialVersionUID = 1L;

    public String getBusinessDirection() {
        return businessDirection;
    }

    public void setBusinessDirection(String businessDirection) {
        this.businessDirection = businessDirection;
    }

    public String getBusinessModel() {
        return businessModel;
    }

    public void setBusinessModel(String businessModel) {
        this.businessModel = businessModel;
    }

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public String getQuotaCName() {
        return quotaCName;
    }

    public void setQuotaCName(String quotaCName) {
        this.quotaCName = quotaCName;
    }

    public String getQuotaLName() {
        return quotaLName;
    }

    public void setQuotaLName(String quotaLName) {
        this.quotaLName = quotaLName;
    }

    public String getQuotaEName() {
        return quotaEName;
    }

    public void setQuotaEName(String quotaEName) {
        this.quotaEName = quotaEName;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public String getQuotaGroup() {
        return quotaGroup;
    }

    public void setQuotaGroup(String quotaGroup) {
        this.quotaGroup = quotaGroup;
    }

    public String getQuotaType() {
        return quotaType;
    }

    public void setQuotaType(String quotaType) {
        this.quotaType = quotaType;
    }

    public String getQuotaValueType() {
        return quotaValueType;
    }

    public void setQuotaValueType(String quotaValueType) {
        this.quotaValueType = quotaValueType;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getPercentHundredIs() {
        return percentHundredIs;
    }

    public void setPercentHundredIs(String percentHundredIs) {
        this.percentHundredIs = percentHundredIs;
    }

    public Long getDisplayNo() {
        return displayNo;
    }

    public void setDisplayNo(Long displayNo) {
        this.displayNo = displayNo;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getQuotaClass() {
        return quotaClass;
    }

    public void setQuotaClass(String quotaClass) {
        this.quotaClass = quotaClass;
    }

    public String getQuotaEffect() {
        return quotaEffect;
    }

    public void setQuotaEffect(String quotaEffect) {
        this.quotaEffect = quotaEffect;
    }

    public String getAutoCalculatedIs() {
        return autoCalculatedIs;
    }

    public void setAutoCalculatedIs(String autoCalculatedIs) {
        this.autoCalculatedIs = autoCalculatedIs;
    }
}
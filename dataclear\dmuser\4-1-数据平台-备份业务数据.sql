--备份ods
create table ods_policy_main_20240403 as select * from ods_policy_main;
create table ods_policy_premium_20240403 as select * from ods_policy_premium;
create table ods_policy_payment_plan_20240403 as select * from ods_policy_payment_plan;
create table ods_reins_outward_20240403 as select * from ods_reins_outward;
create table ods_reins_outward_detail_20240403 as select * from ods_reins_outward_detail;
create table ods_reins_bill_20240403 as select * from ods_reins_bill;
create table ods_reins_bill_detail_20240403 as select * from ods_reins_bill_detail;
create table ods_claim_main_20240403 as select * from ods_claim_main;
create table ods_claim_loss_20240403 as select * from ods_claim_loss;
create table ods_claim_loss_detail_20240403 as select * from ods_claim_loss_detail;
create table ods_claim_outstanding_20240403 as select * from ods_claim_outstanding;
create table ods_acc_payment_20240403 as select * from ods_acc_payment;
create table ods_acc_receivable_20240403 as select * from ods_acc_receivable;
create table ods_fin_article_balance_20240403 as select * from ods_fin_article_balance;
create table ods_fin_ledger_balance_20240403 as select * from ods_fin_ledger_balance;
create table ods_fin_voucher_20240403 as select * from ods_fin_voucher;
create table ods_fin_voucher_detail_20240403 as select * from ods_fin_voucher_detail;
create table ods_base_entity_20240403 as select * from ods_base_entity;
create table ods_base_account_20240403 as select * from ods_base_account;
create table ods_base_currency_20240403 as select * from ods_base_currency;
create table ods_base_currency_rate_20240403 as select * from ods_base_currency_rate;
create table ods_base_product_20240403 as select * from ods_base_product;
create table ods_base_risk_class_20240403 as select * from ods_base_risk_class;
create table ods_base_risk_20240403 as select * from ods_base_risk;
create table ods_base_risk_mapping_20240403 as select * from ods_base_risk_mapping;
create table ods_reins_treaty_class_20240403 as select * from ods_reins_treaty_class;
create table ods_reins_base_treaty_20240403 as select * from ods_reins_base_treaty;
create table ods_reins_treaty_20240403 as select * from ods_reins_treaty;
create table ods_reins_treaty_section_20240403 as select * from ods_reins_treaty_section;
create table ods_reins_treaty_payment_plan_20240403 as select * from ods_reins_treaty_payment_plan;
create table ods_reins_risk_20240403 as select * from ods_reins_risk;
create table ods_reins_reinsurer_20240403 as select * from ods_reins_reinsurer;
create table ods_reins_float_charge_20240403 as select * from ods_reins_float_charge;

--备份dm
create table dm_policy_main_20240403 as select * from dm_policy_main;
create table dm_policy_premium_20240403 as select * from dm_policy_premium;
create table dm_policy_payment_plan_20240403 as select * from dm_policy_payment_plan;
create table dm_reins_outward_20240403 as select * from dm_reins_outward;
create table dm_reins_outward_detail_20240403 as select * from dm_reins_outward_detail;
create table dm_reins_bill_20240403 as select * from dm_reins_bill;
create table dm_reins_bill_detail_20240403 as select * from dm_reins_bill_detail;
create table dm_claim_main_20240403 as select * from dm_claim_main;
create table dm_claim_loss_20240403 as select * from dm_claim_loss;
create table dm_claim_loss_detail_20240403 as select * from dm_claim_loss_detail;
create table dm_claim_outstanding_20240403 as select * from dm_claim_outstanding;
create table dm_acc_payment_20240403 as select * from dm_acc_payment;
create table dm_acc_receivable_20240403 as select * from dm_acc_receivable;
create table dm_fin_article_balance_20240403 as select * from dm_fin_article_balance;
create table dm_fin_ledger_balance_20240403 as select * from dm_fin_ledger_balance;
create table dm_fin_voucher_20240403 as select * from dm_fin_voucher;
create table dm_fin_voucher_detail_20240403 as select * from dm_fin_voucher_detail;
create table dm_base_entity_20240403 as select * from dm_base_entity;
create table dm_base_account_20240403 as select * from dm_base_account;
create table dm_base_currency_20240403 as select * from dm_base_currency;
create table dm_base_currency_rate_20240403 as select * from dm_base_currency_rate;
create table dm_base_product_20240403 as select * from dm_base_product;
create table dm_base_risk_class_20240403 as select * from dm_base_risk_class;
create table dm_base_risk_20240403 as select * from dm_base_risk;
create table dm_base_risk_mapping_20240403 as select * from dm_base_risk_mapping;
create table dm_reins_treaty_class_20240403 as select * from dm_reins_treaty_class;
create table dm_reins_base_treaty_20240403 as select * from dm_reins_base_treaty;
create table dm_reins_treaty_20240403 as select * from dm_reins_treaty;
create table dm_reins_treaty_section_20240403 as select * from dm_reins_treaty_section;
create table dm_reins_treaty_payment_plan_20240403 as select * from dm_reins_treaty_payment_plan;
create table dm_reins_risk_20240403 as select * from dm_reins_risk;
create table dm_reins_reinsurer_20240403 as select * from dm_reins_reinsurer;
create table dm_reins_float_charge_20240403 as select * from dm_reins_float_charge;

--备份计量单元
create table dm_buss_cmunit_direct_20240403 as select * from dm_buss_cmunit_direct;
create table dm_buss_cmunit_fac_outwards_20240403 as select * from dm_buss_cmunit_fac_outwards;
create table dm_buss_cmunit_treaty_20240403 as select * from dm_buss_cmunit_treaty;

--备份信号表
create table ods_data_push_signal_20240403 as select * from ods_data_push_signal;


--备份业务期间
create table dm_conf_bussperiod_20240403 as select * from dm_conf_bussperiod;
create table dm_conf_bussperiod_detail_20240403 as select * from dm_conf_bussperiod_detail;




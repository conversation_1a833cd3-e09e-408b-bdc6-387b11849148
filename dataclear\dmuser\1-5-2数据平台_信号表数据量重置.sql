TRUNCATE table ODS_DATA_PUSH_SIGNAL;
TRUNCATE table ODS_DATA_PUSH_SIGNALhis;

DECLARE
    V_DRAW_TYPE VARCHAR2(10) := '2';
BEGIN
    
--POLICY_MAIN
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'POLICY_MAIN' and task_code in (select distinct task_code from ods_POLICY_MAIN where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'POLICY_MAIN' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_POLICY_MAIN T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--POLICY_PREMIUM
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'POLICY_PREMIUM' and task_code in (select distinct task_code from ods_POLICY_PREMIUM where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'POLICY_PREMIUM' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_POLICY_PREMIUM T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--POLICY_PAYMENT_PLAN
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'POLICY_PAYMENT_PLAN' and task_code in (select distinct task_code from ods_POLICY_PAYMENT_PLAN where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'POLICY_PAYMENT_PLAN' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_POLICY_PAYMENT_PLAN T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_OUTWARD
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_OUTWARD' and task_code in (select distinct task_code from ods_REINS_OUTWARD where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_OUTWARD' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_OUTWARD T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_OUTWARD_DETAIL
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_OUTWARD_DETAIL' and task_code in (select distinct task_code from ods_REINS_OUTWARD_DETAIL where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_OUTWARD_DETAIL' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_OUTWARD_DETAIL T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_BILL
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_BILL' and task_code in (select distinct task_code from ods_REINS_BILL where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_BILL' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_BILL T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_BILL_DETAIL
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_BILL_DETAIL' and task_code in (select distinct task_code from ods_REINS_BILL_DETAIL where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_BILL_DETAIL' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_BILL_DETAIL T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--CLAIM_MAIN
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'CLAIM_MAIN' and task_code in (select distinct task_code from ods_CLAIM_MAIN where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'CLAIM_MAIN' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_CLAIM_MAIN T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--CLAIM_LOSS
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'CLAIM_LOSS' and task_code in (select distinct task_code from ods_CLAIM_LOSS where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'CLAIM_LOSS' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_CLAIM_LOSS T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--CLAIM_LOSS_DETAIL
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'CLAIM_LOSS_DETAIL' and task_code in (select distinct task_code from ods_CLAIM_LOSS_DETAIL where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'CLAIM_LOSS_DETAIL' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_CLAIM_LOSS_DETAIL T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--CLAIM_OUTSTANDING
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'CLAIM_OUTSTANDING' and task_code in (select distinct task_code from ods_CLAIM_OUTSTANDING where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'CLAIM_OUTSTANDING' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_CLAIM_OUTSTANDING T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--ACC_PAYMENT
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'ACC_PAYMENT' and task_code in (select distinct task_code from ods_ACC_PAYMENT where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'ACC_PAYMENT' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_ACC_PAYMENT T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--ACC_RECEIVABLE
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'ACC_RECEIVABLE' and task_code in (select distinct task_code from ods_ACC_RECEIVABLE where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'ACC_RECEIVABLE' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_ACC_RECEIVABLE T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--FIN_ARTICLE_BALANCE
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'FIN_ARTICLE_BALANCE' and task_code in (select distinct task_code from ods_FIN_ARTICLE_BALANCE where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'FIN_ARTICLE_BALANCE' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_FIN_ARTICLE_BALANCE T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--FIN_LEDGER_BALANCE
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'FIN_LEDGER_BALANCE' and task_code in (select distinct task_code from ods_FIN_LEDGER_BALANCE where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'FIN_LEDGER_BALANCE' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_FIN_LEDGER_BALANCE T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--FIN_VOUCHER
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'FIN_VOUCHER' and task_code in (select distinct task_code from ods_FIN_VOUCHER where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'FIN_VOUCHER' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_FIN_VOUCHER T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--FIN_VOUCHER_DETAIL
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'FIN_VOUCHER_DETAIL' and task_code in (select distinct task_code from ods_FIN_VOUCHER_DETAIL where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'FIN_VOUCHER_DETAIL' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_FIN_VOUCHER_DETAIL T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--BASE_ENTITY
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'BASE_ENTITY' and task_code in (select distinct task_code from ods_BASE_ENTITY where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'BASE_ENTITY' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_BASE_ENTITY T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--BASE_ACCOUNT
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'BASE_ACCOUNT' and task_code in (select distinct task_code from ods_BASE_ACCOUNT where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'BASE_ACCOUNT' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_BASE_ACCOUNT T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--BASE_CURRENCY
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'BASE_CURRENCY' and task_code in (select distinct task_code from ods_BASE_CURRENCY where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'BASE_CURRENCY' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_BASE_CURRENCY T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--BASE_CURRENCY_RATE
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'BASE_CURRENCY_RATE' and task_code in (select distinct task_code from ods_BASE_CURRENCY_RATE where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'BASE_CURRENCY_RATE' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_BASE_CURRENCY_RATE T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--BASE_PRODUCT
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'BASE_PRODUCT' and task_code in (select distinct task_code from ods_BASE_PRODUCT where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'BASE_PRODUCT' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_BASE_PRODUCT T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--BASE_RISK_CLASS
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'BASE_RISK_CLASS' and task_code in (select distinct task_code from ods_BASE_RISK_CLASS where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'BASE_RISK_CLASS' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_BASE_RISK_CLASS T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--BASE_RISK
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'BASE_RISK' and task_code in (select distinct task_code from ods_BASE_RISK where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'BASE_RISK' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_BASE_RISK T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--BASE_RISK_MAPPING
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'BASE_RISK_MAPPING' and task_code in (select distinct task_code from ods_BASE_RISK_MAPPING where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'BASE_RISK_MAPPING' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_BASE_RISK_MAPPING T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_TREATY_CLASS
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_TREATY_CLASS' and task_code in (select distinct task_code from ods_REINS_TREATY_CLASS where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_TREATY_CLASS' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_TREATY_CLASS T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_BASE_TREATY
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_BASE_TREATY' and task_code in (select distinct task_code from ods_REINS_BASE_TREATY where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_BASE_TREATY' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_BASE_TREATY T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_TREATY
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_TREATY' and task_code in (select distinct task_code from ods_REINS_TREATY where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_TREATY' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_TREATY T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_TREATY_SECTION
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_TREATY_SECTION' and task_code in (select distinct task_code from ods_REINS_TREATY_SECTION where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_TREATY_SECTION' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_TREATY_SECTION T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_TREATY_PAYMENT_PLAN
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_TREATY_PAYMENT_PLAN' and task_code in (select distinct task_code from ods_REINS_TREATY_PAYMENT_PLAN where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_TREATY_PAYMENT_PLAN' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_TREATY_PAYMENT_PLAN T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_RISK
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_RISK' and task_code in (select distinct task_code from ods_REINS_RISK where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_RISK' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_RISK T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_REINSURER
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_REINSURER' and task_code in (select distinct task_code from ods_REINS_REINSURER where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_REINSURER' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_REINSURER T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


--REINS_FLOAT_CHARGE
    DELETE FROM ODS_DATA_PUSH_SIGNAL WHERE PUSH_MODEL = 'REINS_FLOAT_CHARGE' and task_code in (select distinct task_code from ods_REINS_FLOAT_CHARGE where task_status = '0' and DRAW_TYPE = V_DRAW_TYPE);
    INSERT INTO ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT 'REINS_FLOAT_CHARGE' AS PUSH_MODEL,
                       SUBSTR(T.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       COUNT(1) AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       T.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM ODS_REINS_FLOAT_CHARGE T
                 WHERE T.DRAW_TYPE = V_DRAW_TYPE
         and t.task_status = '0'
                 GROUP BY T.TASK_CODE) T;

    COMMIT;


for cur in (select year_month,min(task_code) as task_code
 from odsuser.ods_data_push_signal group by year_month
 order by year_month) loop
 
   INSERT INTO odsuser.ODS_DATA_PUSH_SIGNAL
        (DATA_PUSH_SIGNAL_ID,
         PUSH_MODEL,
         YEAR_MONTH,
         ROW_COUNT,
         PUSH_TIME,
         TASK_CODE,
         TASK_STATUS,
         DEAL_MSG)
        SELECT odsuser.ODS_SEQ_DATA_PUSH_SIGNAL.NEXTVAL AS DATA_PUSH_SIGNAL_ID, T.*
          FROM (SELECT T.biz_code AS PUSH_MODEL,
                       SUBSTR(cur.TASK_CODE, 8, 6) AS YEAR_MONTH,
                       0 AS ROW_COUNT,
                       sysdate AS PUSH_TIME,
                       cur.TASK_CODE AS TASK_CODE,
                       '0' AS TASK_STATUS,
                       NULL AS DEAL_MSG
                  FROM dmuser.dm_conf_table T
                 WHERE VALID_IS = '1'
         and not exists (
          select 1 from odsuser.ODS_DATA_PUSH_SIGNAL odps 
          where odps.year_month = cur.year_month
          and t.biz_code = odps.push_model
         )
         order by (case when t.type_group = '7' then '0' else t.type_group end),t.display_no
         ) T;
         commit;
  end loop;

END;

/
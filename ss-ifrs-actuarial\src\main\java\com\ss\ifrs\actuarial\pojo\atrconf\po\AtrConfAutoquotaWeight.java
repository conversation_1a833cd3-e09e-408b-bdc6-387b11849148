/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-09-05 16:09:55
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-09-05 16:09:55<br/>
 * Description: 假设值自动计算加权公共配置表<br/>
 * Table Name: atr_conf_autoquota_weight<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "假设值自动计算加权公共配置表")
public class AtrConfAutoquotaWeight implements Serializable {
    /**
     * Database column: atr_conf_autoquota_weight.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_conf_autoquota_weight.loa_code
     * Database remarks: 业务线
     */
    @ApiModelProperty(value = "业务线", required = true)
    private String loaCode;

    /**
     * Database column: atr_conf_autoquota_weight.offset_years
     * Database remarks: 偏移年数
     */
    @ApiModelProperty(value = "偏移年数", required = true)
    private Short offsetYears;

    /**
     * Database column: atr_conf_autoquota_weight.weight_value
     * Database remarks: 加权值
     */
    @ApiModelProperty(value = "加权值", required = false)
    private BigDecimal weightValue;

    /**
     * Database column: atr_conf_autoquota_weight.oper_id
     * Database remarks: 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    private Long operId;

    /**
     * Database column: atr_conf_autoquota_weight.oper_time
     * Database remarks: 操作时间
     */
    @ApiModelProperty(value = "操作时间", required = true)
    private Date operTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public Short getOffsetYears() {
        return offsetYears;
    }

    public void setOffsetYears(Short offsetYears) {
        this.offsetYears = offsetYears;
    }

    public BigDecimal getWeightValue() {
        return weightValue;
    }

    public void setWeightValue(BigDecimal weightValue) {
        this.weightValue = weightValue;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }
}
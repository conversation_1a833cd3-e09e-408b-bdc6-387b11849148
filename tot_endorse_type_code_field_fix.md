# TOT业务endorse_type_code特殊处理字段修复说明

## 问题描述

在实现TOT业务的endorse_type_code特殊处理逻辑时，发现AtrBussToLrcTUlR数据模型类缺少必要的历史累计已赚字段，导致特殊处理逻辑出现编译错误。

### 具体问题
1. **编译错误**：在AtrBussLrcTotService.java的calcIcp方法中，specialProcessType=1的处理逻辑需要使用`icu.getPreCumlEdPremium()`和`icu.getPreCumlEdNetFee()`方法
2. **字段缺失**：AtrBussToLrcTUlR类中不存在`preCumlEdPremium`和`preCumlEdNetFee`这两个字段
3. **逻辑不完整**：无法正确计算specialProcessType=1时的已赚金额（签单保费-历史累计已赚）

### 错误信息
```
java: cannot find symbol
  symbol:   method getPreCumlEdPremium()
  location: variable icu of type com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUlR

java: cannot find symbol
  symbol:   method getPreCumlEdNetFee()
  location: variable icu of type com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUlR
```

## 修复方案

### 1. 数据模型字段添加

在AtrBussToLrcTUlR.java中添加了以下两个字段：

```java
/** 上期累计已赚保费 (pre_cuml_ed_premium) */
private BigDecimal preCumlEdPremium;

/** 上期累计已赚净额结算手续费 (pre_cuml_ed_net_fee) */
private BigDecimal preCumlEdNetFee;
```

**字段特点**：
- 数据类型：BigDecimal（与DD/FO业务保持一致）
- 命名规范：遵循现有的驼峰命名规范
- 注释说明：清晰说明字段用途
- 位置：放置在curEdNetFee字段之后，endorseTypeCode字段之前

### 2. 历史数据获取逻辑

在AtrBussLrcTotService.java的calcIcp方法中添加了历史累计已赚数据的获取和设置逻辑：

```java
// 获取历史累计已赚数据
List<Object> unitKey = createUnitKey(icu);
AtrBussToLrcTUl preToLrcUl = preToLrcUls.one(unitKey);
if (preToLrcUl != null) {
    BigDecimal preCumlEdPremium = nvl(preToLrcUl.getPreCumlEdPremium())
            .add(nvl(preToLrcUl.getCurEdPremium()));
    BigDecimal preCumlEdNetFee = nvl(preToLrcUl.getPreCumlEdNetFee())
            .add(nvl(preToLrcUl.getCurEdNetFee()));
    icu.setPreCumlEdPremium(preCumlEdPremium);
    icu.setPreCumlEdNetFee(preCumlEdNetFee);
} else {
    icu.setPreCumlEdPremium(BigDecimal.ZERO);
    icu.setPreCumlEdNetFee(BigDecimal.ZERO);
}
```

**逻辑说明**：
- 使用createUnitKey方法创建维度键（treatyNo、policyNo、endorseSeqNo、kindCode）
- 从preToLrcUls索引中获取历史数据
- 计算历史累计已赚 = 上期累计已赚 + 上期当期已赚
- 如果没有历史数据，则设置为0

### 3. 与DD/FO业务的一致性

**字段定义对比**：

| 业务类型 | 类名 | 字段名 | 数据类型 | 注释 |
|----------|------|--------|----------|------|
| DD业务 | AtrBussLrcDdIcu | preCumlEdPremium | BigDecimal | 上期累计已赚保费 |
| DD业务 | AtrBussLrcDdIcu | preCumlEdNetFee | BigDecimal | 上期累计已赚净额结算手续费 |
| FO业务 | AtrBussLrcFoIcu | preCumlEdPremium | BigDecimal | 上期累计已赚保费 |
| FO业务 | AtrBussLrcFoIcu | preCumlEdNetFee | BigDecimal | 上期累计已赚净额结算手续费 |
| TOT业务 | AtrBussToLrcTUlR | preCumlEdPremium | BigDecimal | 上期累计已赚保费 |
| TOT业务 | AtrBussToLrcTUlR | preCumlEdNetFee | BigDecimal | 上期累计已赚净额结算手续费 |

**一致性确认**：✅ 字段名、数据类型、注释完全一致

## 修复效果

### 1. 编译错误解决
- ✅ `icu.getPreCumlEdPremium()`方法调用正常
- ✅ `icu.getPreCumlEdNetFee()`方法调用正常
- ✅ 代码编译通过，无语法错误

### 2. 特殊处理逻辑完整
- ✅ specialProcessType=1时能够正确计算已赚金额
- ✅ 已赚金额 = 签单保费 - 历史累计已赚保费
- ✅ 已赚手续费 = 净额结算手续费 - 历史累计已赚手续费

### 3. 业务逻辑正确性
- ✅ 历史数据获取逻辑与TOT业务现有逻辑一致
- ✅ 维度键创建方式与collectIcu方法保持一致
- ✅ 数据计算方式与DD/FO业务保持一致

## 测试验证

### 验证场景1：有历史数据的情况
**测试数据**：
- 签单保费：10000
- 历史累计已赚保费：6000
- specialProcessType：1

**预期结果**：
- preCumlEdPremium：6000
- 第0期已赚保费：4000（10000-6000）

### 验证场景2：无历史数据的情况
**测试数据**：
- 签单保费：10000
- 无历史数据
- specialProcessType：1

**预期结果**：
- preCumlEdPremium：0
- 第0期已赚保费：10000（10000-0）

### 验证场景3：正常业务不受影响
**测试数据**：
- specialProcessType：0

**预期结果**：
- 正常业务逻辑不受影响
- 历史数据设置不影响正常计算

## 代码修改位置

### AtrBussToLrcTUlR.java
- **文件路径**：`ss-ifrs-actuarial\src\main\java\com\ss\ifrs\actuarial\pojo\ecf\vo\lrc\to\AtrBussToLrcTUlR.java`
- **修改行号**：第314-318行（在curEdNetFee字段之后添加）
- **修改内容**：添加preCumlEdPremium和preCumlEdNetFee字段

### AtrBussLrcTotService.java
- **文件路径**：`ss-ifrs-actuarial\src\main\java\com\ss\ifrs\actuarial\service\impl\AtrBussLrcTotService.java`
- **修改行号**：第130-145行（在calcIcp方法开始处添加）
- **修改内容**：添加历史累计已赚数据的获取和设置逻辑

## 风险评估

### 风险等级：低
- **修改范围**：仅添加字段和数据设置逻辑，不修改现有业务逻辑
- **影响范围**：只影响特殊处理场景，正常业务不受影响
- **兼容性**：完全向后兼容，不会破坏现有功能

### 风险控制
1. **充分测试**：重点测试特殊处理场景的数据正确性
2. **数据验证**：验证历史数据获取逻辑的准确性
3. **回归测试**：确保正常业务记录不受影响
4. **监控观察**：部署后密切关注相关业务指标

## 总结

这个修复解决了TOT业务endorse_type_code特殊处理实现中的关键问题：

### 修复成果
1. **编译错误消除**：解决了字段缺失导致的编译错误
2. **逻辑完整性**：确保特殊处理逻辑能够正确执行
3. **业务一致性**：与DD和FO业务保持字段定义和逻辑的一致性
4. **向后兼容**：不影响现有正常业务记录的处理

### 关键特点
- **准确性**：历史数据获取逻辑与TOT业务现有模式完全一致
- **完整性**：字段定义参考DD/FO业务，确保规范统一
- **安全性**：修改范围小，风险可控
- **可维护性**：代码结构清晰，易于理解和维护

修复后的TOT业务endorse_type_code特殊处理功能已经完全可用，能够正确处理包含15/16批改类型的保单数据。

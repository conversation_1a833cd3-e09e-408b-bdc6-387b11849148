
call dm_pack_commonutils.drop_sequence('DM_SEQ_BIZ_TYPE_CONFIG');
call dm_pack_commonutils.drop_sequence('DM_SEQ_BUSS_CMUNIT_DIRECT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_BUSS_CMUNIT_FAC_INWARDS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_BUSS_CMUNIT_FAC_OUTWARD');
call dm_pack_commonutils.drop_sequence('DM_SEQ_BUSS_CMUNIT_TREATY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CFGDRAWTYPEDETAIL_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CMUNITNO');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CODE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CODE_CONFIG');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CODE_TYPE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFCONTRACTQUADEFHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFIGCHECKCOLUMNHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFIGCHECKRULE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFIGCHECKRULEHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFIGCODEDEF');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFIGDRAWTYPEDETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFIGDRAWTYPELIST');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFIGDRAWTYPELIST_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFIGSOURCEDATA');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFIGSOURCEDATADETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONFIGSOURCEDATALIST');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_BUSSPERIOD');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_BUSSPERIOD_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CHECKRULE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CHECKRULEHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CODE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CODE_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_COLUMN_REF');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_CODE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_CODEDEF');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_CODE_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_ICDDEF');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_ICDDEFHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_LOSSDEF');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_PLDDEF');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_PLDDEFHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_RULE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_RULEHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONTRACT_RULE_DEF');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CONT_LOSSDEFHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CON_CODEDEF_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CON_CODEMAT_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CON_EVALDEF');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CON_EVALDEFHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_CON_RISKDEFHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_DRAWTYPELIST');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_DRAWTYPELIST_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_GDRAWTYPEDETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_ICG_LOSS_PLAN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_ICG_LOSS_PLANHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_KT_CODEMATCHDEF');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_KT_RISKDEF_PLAN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_KT_RISKDEF_PLANHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_KT_RULE_DEFHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_TABLE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_TABLEHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_TABLEREF');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_TABLE_COLUMN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_TABLE_COLUMN_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTCFG_CODE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTCFG_CODEDEF_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTCFG_CODEMAT_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTCFG_ICDDEFHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTCFG_PLDDEFHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTCFG_PLDID');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTCONFIGEVALHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTCONFIGQUADEFHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTCONFIGRISKHIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTCONFIG_CODE_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_CONTRACTSHARE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DATA_DRAW_LOG');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DATA_RECORD');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DIRECT_PROFIT_PARAM');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DIRECT_PROFIT_UNIT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DM_DATA_DRAW_LOG');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DM_TGT_ACC_ART');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DUCT_CHECK_LOG');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DUCT_DRAW_LOG');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DUCT_ERROR_LOG');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DUCT_STAT_AMOUNT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_DUCT_STAT_COUNT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_ERROR_LOG');
call dm_pack_commonutils.drop_sequence('DM_SEQ_EXPECT_PAY_RATE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_FAC_EVALUATE_RELATED');
call dm_pack_commonutils.drop_sequence('DM_SEQ_FAC_IN_CMUNITNO');
call dm_pack_commonutils.drop_sequence('DM_SEQ_FAC_OUT_CMUNITNO');
call dm_pack_commonutils.drop_sequence('DM_SEQ_INTEREST_RATE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_LIQUIDITY_PREMIUM');
call dm_pack_commonutils.drop_sequence('DM_SEQ_LOG_DATA_VERIFY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_PAY_MODE_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_PENDING_RISK_JUDGEMENT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_PROFIT_LOSS_FACTOR');
call dm_pack_commonutils.drop_sequence('DM_SEQ_PROFIT_PARAM');
call dm_pack_commonutils.drop_sequence('DM_SEQ_PROFIT_PARAM_VALUE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_PROFIT_UNIT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_RAWTYPEDETAIL_HIS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_REINFINALREINSURER');
call dm_pack_commonutils.drop_sequence('DM_SEQ_REINS_TREATY_PAYMENT_PLAN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_RI_CMUNITNO');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_ACC_ARTICLE_BALANCE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_ACC_LEDGER_BALANCE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_ACC_PAYMENT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_ACC_RECEIVABLE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_ACC_VOUCHER');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_ACC_VOUCHERDETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_ACCOUNT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_ACCOUNTITEM');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_COMPANY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_CURRENCY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_CURRENCY_RATE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_ENTITY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_PRODUCT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_RISK');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_RISK_CLASS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_RISK_MAPPING');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_BASE_TREATY_CLASS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_CLAIM_LOSS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_CLAIM_MAIN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_CLAIM_OUTSTANDING');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_CLAIM_PAYMENT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_CLM_UNSETTLED_LIST');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_FIN_ARTICLE_BALANCE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_FIN_LEDGER_BALANCE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_FIN_PAYMENT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_FIN_RECEIVABLE_ACC');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_FIN_VOUCHER');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_FIN_VOUCHER_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_PAYMENT_PLAN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_POLICY_MAIN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_POLICY_PAYMENT_PLAN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_POLICY_PREMIUM');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_BASE_TREATY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_BILL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_BILL_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_CONF_TREATY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_FAC');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_OUTWARD');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_OUTWARD_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_PRODUCT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_REINSURER');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_RISK');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_TREATY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_TREATY_CLASS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_TREATY_PLAN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_SRC_REINS_TREATY_SEC');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_ACC_PAYMENT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_ACC_RECEIVABLE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_ACC_PAYMENT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_ACC_RECEIVABLE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_CLAIM_LOSS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_CLAIM_LOSS_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_CLAIM_MAIN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_CLAIM_OUTSTANDING');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_FIN_ARTICLE_BALANCE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_FIN_LEDGER_BALANCE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_FIN_RECEIVABLE_ACCOUNT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_FIN_VOUCHER_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_PAYMENT_PLAN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_POLICY_MAIN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_POLICY_PAYMENT_PLAN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_POLICY_PREMIUM');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_REINS_BILL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_REINS_BILL_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_REINS_OUTWARD');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_REINS_OUTWARD_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_REINS_PRODUCT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_REINS_TREATY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_AMOUNT_REINS_TREATY_SECTION');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_BASE_ACCOUNT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_BASE_CURRENCY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_BASE_CURRENCY_RATE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_BASE_ENTITY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_BASE_PRODUCT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_BASE_RISK');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_BASE_RISK_CLASS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_BASE_RISK_MAPPING');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_CLAIM_LOSS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_CLAIM_LOSS_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_CLAIM_MAIN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_CLAIM_OUTSTANDING');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_FIN_ARTICLE_BALANCE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_FIN_LEDGER_BALANCE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_FIN_VOUCHER');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_FIN_VOUCHER_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_POLICY_MAIN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_POLICY_PAYMENT_PLAN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_POLICY_PREMIUM');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_BASE_TREATY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_BILL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_BILL_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_FLOAT_CHARGE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_OUTWARD');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_OUTWARD_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_REINSURER');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_RISK');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_TREATY');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_TREATY_CLASS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_TREATY_PAYMENT_PLAN');
call dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_REINS_TREATY_SECTION');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TEST');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TGT_ACC_ARTICLE_BALANCE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TGT_FIN_PAYMENT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TREATY_EVALUATE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TREATY_IN_CMUNITNO');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TREATY_MAJOR_RISK');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TREATY_MODEL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TREATY_MODEL_DETAIL');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TREATY_OUT_CMUNITNO');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TREATY_PAY_RATE');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TREATY_PROFIT_LOSS');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TREATY_PROFIT_PARAM');
call dm_pack_commonutils.drop_sequence('DM_SEQ_TREATY_PROFIT_UNIT');
call dm_pack_commonutils.drop_sequence('DM_SEQ_UNEXPIRED_PAY_MODE');
call dm_pack_commonutils.drop_sequence('SEQ_DM_CONFIGCHECKCOLUMN');
call dm_pack_commonutils.drop_sequence('SEQ_DM_CONF_CONTRACT_QUADEF');
call dm_pack_commonutils.drop_sequence('SEQ_DM_CONF_CONTRACT_RISKDEF');
call dm_pack_commonutils.drop_sequence('SEQ_DM_CONF_CON_CODEMATCHDEF');
call dm_pack_commonutils.drop_sequence('SEQ_DM_CONF_TABLE_COLUMN');
call dm_pack_commonutils.drop_sequence('SEQ_DM_CONTRACTCFGICDDEF_ICDID');
call dm_pack_commonutils.drop_sequence('SEQ_DM_CONTRACTCFG_EVALID');
call dm_pack_commonutils.drop_sequence('SEQ_DM_CONTRACTCFG_QUAID');
call dm_pack_commonutils.drop_sequence('SEQ_DM_CONTRACTCONFIG_CMD');
call dm_pack_commonutils.drop_sequence('SEQ_DM_CONTRACTCONFIG_RISKDEF');
call dm_pack_commonutils.drop_sequence('SEQ_POC_RULE_SUGGEST_REPORT');
call dm_pack_commonutils.drop_sequence('SEQ_RTA_ALARM_CONFIG');


call dm_pack_commonutils.add_sequence('DM_SEQ_BIZ_TYPE_CONFIG');
call dm_pack_commonutils.add_sequence('DM_SEQ_BUSS_CMUNIT_DIRECT');
call dm_pack_commonutils.add_sequence('DM_SEQ_BUSS_CMUNIT_FAC_INWARDS');
call dm_pack_commonutils.add_sequence('DM_SEQ_BUSS_CMUNIT_FAC_OUTWARD');
call dm_pack_commonutils.add_sequence('DM_SEQ_BUSS_CMUNIT_TREATY');
call dm_pack_commonutils.add_sequence('DM_SEQ_CFGDRAWTYPEDETAIL_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CMUNITNO');
call dm_pack_commonutils.add_sequence('DM_SEQ_CODE');
call dm_pack_commonutils.add_sequence('DM_SEQ_CODE_CONFIG');
call dm_pack_commonutils.add_sequence('DM_SEQ_CODE_TYPE');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFCONTRACTQUADEFHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFIGCHECKCOLUMNHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFIGCHECKRULE');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFIGCHECKRULEHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFIGCODEDEF');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFIGDRAWTYPEDETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFIGDRAWTYPELIST');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFIGDRAWTYPELIST_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFIGSOURCEDATA');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFIGSOURCEDATADETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONFIGSOURCEDATALIST');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_BUSSPERIOD');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_BUSSPERIOD_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CHECKRULE');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CHECKRULEHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CODE');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CODE_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_COLUMN_REF');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_CODE');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_CODEDEF');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_CODE_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_ICDDEF');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_ICDDEFHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_LOSSDEF');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_PLDDEF');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_PLDDEFHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_RULE');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_RULEHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONTRACT_RULE_DEF');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CONT_LOSSDEFHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CON_CODEDEF_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CON_CODEMAT_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CON_EVALDEF');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CON_EVALDEFHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_CON_RISKDEFHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_DRAWTYPELIST');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_DRAWTYPELIST_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_GDRAWTYPEDETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_ICG_LOSS_PLAN');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_ICG_LOSS_PLANHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_KT_CODEMATCHDEF');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_KT_RISKDEF_PLAN');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_KT_RISKDEF_PLANHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_KT_RULE_DEFHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_TABLE');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_TABLEHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_TABLEREF');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_TABLE_COLUMN');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONF_TABLE_COLUMN_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTCFG_CODE');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTCFG_CODEDEF_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTCFG_CODEMAT_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTCFG_ICDDEFHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTCFG_PLDDEFHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTCFG_PLDID');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTCONFIGEVALHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTCONFIGQUADEFHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTCONFIGRISKHIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTCONFIG_CODE_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_CONTRACTSHARE');
call dm_pack_commonutils.add_sequence('DM_SEQ_DATA_DRAW_LOG');
call dm_pack_commonutils.add_sequence('DM_SEQ_DATA_RECORD');
call dm_pack_commonutils.add_sequence('DM_SEQ_DIRECT_PROFIT_PARAM');
call dm_pack_commonutils.add_sequence('DM_SEQ_DIRECT_PROFIT_UNIT');
call dm_pack_commonutils.add_sequence('DM_SEQ_DM_DATA_DRAW_LOG');
call dm_pack_commonutils.add_sequence('DM_SEQ_DM_TGT_ACC_ART');
call dm_pack_commonutils.add_sequence('DM_SEQ_DUCT_CHECK_LOG');
call dm_pack_commonutils.add_sequence('DM_SEQ_DUCT_DRAW_LOG');
call dm_pack_commonutils.add_sequence('DM_SEQ_DUCT_ERROR_LOG');
call dm_pack_commonutils.add_sequence('DM_SEQ_DUCT_STAT_AMOUNT');
call dm_pack_commonutils.add_sequence('DM_SEQ_DUCT_STAT_COUNT');
call dm_pack_commonutils.add_sequence('DM_SEQ_ERROR_LOG');
call dm_pack_commonutils.add_sequence('DM_SEQ_EXPECT_PAY_RATE');
call dm_pack_commonutils.add_sequence('DM_SEQ_FAC_EVALUATE_RELATED');
call dm_pack_commonutils.add_sequence('DM_SEQ_FAC_IN_CMUNITNO');
call dm_pack_commonutils.add_sequence('DM_SEQ_FAC_OUT_CMUNITNO');
call dm_pack_commonutils.add_sequence('DM_SEQ_INTEREST_RATE');
call dm_pack_commonutils.add_sequence('DM_SEQ_LIQUIDITY_PREMIUM');
call dm_pack_commonutils.add_sequence('DM_SEQ_LOG_DATA_VERIFY');
call dm_pack_commonutils.add_sequence('DM_SEQ_PAY_MODE_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_PENDING_RISK_JUDGEMENT');
call dm_pack_commonutils.add_sequence('DM_SEQ_PROFIT_LOSS_FACTOR');
call dm_pack_commonutils.add_sequence('DM_SEQ_PROFIT_PARAM');
call dm_pack_commonutils.add_sequence('DM_SEQ_PROFIT_PARAM_VALUE');
call dm_pack_commonutils.add_sequence('DM_SEQ_PROFIT_UNIT');
call dm_pack_commonutils.add_sequence('DM_SEQ_RAWTYPEDETAIL_HIS');
call dm_pack_commonutils.add_sequence('DM_SEQ_REINFINALREINSURER');
call dm_pack_commonutils.add_sequence('DM_SEQ_REINS_TREATY_PAYMENT_PLAN');
call dm_pack_commonutils.add_sequence('DM_SEQ_RI_CMUNITNO');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_ACC_ARTICLE_BALANCE');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_ACC_LEDGER_BALANCE');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_ACC_PAYMENT');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_ACC_RECEIVABLE');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_ACC_VOUCHER');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_ACC_VOUCHERDETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_ACCOUNT');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_ACCOUNTITEM');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_COMPANY');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_CURRENCY');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_CURRENCY_RATE');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_ENTITY');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_PRODUCT');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_RISK');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_RISK_CLASS');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_RISK_MAPPING');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_BASE_TREATY_CLASS');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_CLAIM_LOSS');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_CLAIM_MAIN');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_CLAIM_OUTSTANDING');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_CLAIM_PAYMENT');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_CLM_UNSETTLED_LIST');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_FIN_ARTICLE_BALANCE');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_FIN_LEDGER_BALANCE');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_FIN_PAYMENT');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_FIN_RECEIVABLE_ACC');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_FIN_VOUCHER');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_FIN_VOUCHER_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_PAYMENT_PLAN');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_POLICY_MAIN');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_POLICY_PAYMENT_PLAN');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_POLICY_PREMIUM');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_BASE_TREATY');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_BILL');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_BILL_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_CONF_TREATY');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_FAC');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_OUTWARD');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_OUTWARD_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_PRODUCT');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_REINSURER');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_RISK');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_TREATY');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_TREATY_CLASS');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_TREATY_PLAN');
call dm_pack_commonutils.add_sequence('DM_SEQ_SRC_REINS_TREATY_SEC');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_ACC_PAYMENT');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_ACC_RECEIVABLE');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_ACC_PAYMENT');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_ACC_RECEIVABLE');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_CLAIM_LOSS');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_CLAIM_LOSS_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_CLAIM_MAIN');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_CLAIM_OUTSTANDING');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_FIN_ARTICLE_BALANCE');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_FIN_LEDGER_BALANCE');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_FIN_RECEIVABLE_ACCOUNT');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_FIN_VOUCHER_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_PAYMENT_PLAN');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_POLICY_MAIN');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_POLICY_PAYMENT_PLAN');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_POLICY_PREMIUM');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_REINS_BILL');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_REINS_BILL_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_REINS_OUTWARD');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_REINS_OUTWARD_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_REINS_PRODUCT');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_REINS_TREATY');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_AMOUNT_REINS_TREATY_SECTION');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_BASE_ACCOUNT');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_BASE_CURRENCY');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_BASE_CURRENCY_RATE');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_BASE_ENTITY');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_BASE_PRODUCT');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_BASE_RISK');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_BASE_RISK_CLASS');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_BASE_RISK_MAPPING');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_CLAIM_LOSS');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_CLAIM_LOSS_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_CLAIM_MAIN');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_CLAIM_OUTSTANDING');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_FIN_ARTICLE_BALANCE');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_FIN_LEDGER_BALANCE');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_FIN_VOUCHER');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_FIN_VOUCHER_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_POLICY_MAIN');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_POLICY_PAYMENT_PLAN');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_POLICY_PREMIUM');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_BASE_TREATY');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_BILL');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_BILL_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_FLOAT_CHARGE');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_OUTWARD');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_OUTWARD_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_REINSURER');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_RISK');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_TREATY');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_TREATY_CLASS');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_TREATY_PAYMENT_PLAN');
call dm_pack_commonutils.add_sequence('DM_SEQ_STAT_REINS_TREATY_SECTION');
call dm_pack_commonutils.add_sequence('DM_SEQ_TEST');
call dm_pack_commonutils.add_sequence('DM_SEQ_TGT_ACC_ARTICLE_BALANCE');
call dm_pack_commonutils.add_sequence('DM_SEQ_TGT_FIN_PAYMENT');
call dm_pack_commonutils.add_sequence('DM_SEQ_TREATY_EVALUATE');
call dm_pack_commonutils.add_sequence('DM_SEQ_TREATY_IN_CMUNITNO');
call dm_pack_commonutils.add_sequence('DM_SEQ_TREATY_MAJOR_RISK');
call dm_pack_commonutils.add_sequence('DM_SEQ_TREATY_MODEL');
call dm_pack_commonutils.add_sequence('DM_SEQ_TREATY_MODEL_DETAIL');
call dm_pack_commonutils.add_sequence('DM_SEQ_TREATY_OUT_CMUNITNO');
call dm_pack_commonutils.add_sequence('DM_SEQ_TREATY_PAY_RATE');
call dm_pack_commonutils.add_sequence('DM_SEQ_TREATY_PROFIT_LOSS');
call dm_pack_commonutils.add_sequence('DM_SEQ_TREATY_PROFIT_PARAM');
call dm_pack_commonutils.add_sequence('DM_SEQ_TREATY_PROFIT_UNIT');
call dm_pack_commonutils.add_sequence('DM_SEQ_UNEXPIRED_PAY_MODE');
call dm_pack_commonutils.add_sequence('SEQ_DM_CONFIGCHECKCOLUMN');
call dm_pack_commonutils.add_sequence('SEQ_DM_CONF_CONTRACT_QUADEF');
call dm_pack_commonutils.add_sequence('SEQ_DM_CONF_CONTRACT_RISKDEF');
call dm_pack_commonutils.add_sequence('SEQ_DM_CONF_CON_CODEMATCHDEF');
call dm_pack_commonutils.add_sequence('SEQ_DM_CONF_TABLE_COLUMN');
call dm_pack_commonutils.add_sequence('SEQ_DM_CONTRACTCFGICDDEF_ICDID');
call dm_pack_commonutils.add_sequence('SEQ_DM_CONTRACTCFG_EVALID');
call dm_pack_commonutils.add_sequence('SEQ_DM_CONTRACTCFG_QUAID');
call dm_pack_commonutils.add_sequence('SEQ_DM_CONTRACTCONFIG_CMD');
call dm_pack_commonutils.add_sequence('SEQ_DM_CONTRACTCONFIG_RISKDEF');
call dm_pack_commonutils.add_sequence('SEQ_POC_RULE_SUGGEST_REPORT');
call dm_pack_commonutils.add_sequence('SEQ_RTA_ALARM_CONFIG');


/
--sequence 
DECLARE v_count NUMBER(15);
BEGIN
  dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_BUSSPERIOD');
  SELECT NVL(max(BUSS_PERIOD_ID),0)+1 INTO v_count FROM dm_conf_bussperiod;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_BUSSPERIOD
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1'; 

dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_BUSSPERIOD_DETAIL');
  SELECT NVL(max(PERIOD_DETAIL_ID),0)+1 INTO v_count FROM dm_conf_bussperiod_detail;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_BUSSPERIOD_DETAIL
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';

dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_TABLE');
  SELECT NVL(max(BIZ_TYPE_ID),0)+1 INTO v_count FROM dm_conf_table;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_TABLE
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';
                 
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_TABLE_COLUMN');
  SELECT NVL(max(COL_ID),0)+1 INTO v_count FROM dm_conf_table_column;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_TABLE_COLUMN
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';          
                 
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_COLUMN_REF');
  SELECT NVL(max(COLUMN_REF_ID),0)+1 INTO v_count FROM dm_conf_table_column_ref;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_COLUMN_REF
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';          
                 
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CHECKRULE');
  SELECT NVL(max(CONFIG_RULE_ID),0)+1 INTO v_count FROM dm_conf_checkrule;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CHECKRULE
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';     
                 
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_GDRAWTYPEDETAIL');
  SELECT NVL(max(DETAIL_ID),0)+1 INTO v_count FROM dm_conf_drawtypedetail;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_GDRAWTYPEDETAIL
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';          
                 
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CONTRACT_CODE');
  SELECT NVL(max(CODE_ID),0)+1 INTO v_count FROM dm_conf_contract_code;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CONTRACT_CODE
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';   
                       
dmuser.dm_pack_commonutils.DROP_SEQUENCE('SEQ_DM_CONF_CON_CODEMATCHDEF');
  SELECT NVL(max(CODE_MATCH_ID),0)+1 INTO v_count FROM dm_conf_contract_codematchdef;
  EXECUTE IMMEDIATE 'create sequence SEQ_DM_CONF_CON_CODEMATCHDEF
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';          
                 
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_KT_RISKDEF_PLAN');
  SELECT NVL(max(RISK_PLAN_ID),0)+1 INTO v_count FROM dm_conf_icg_majorrisk_plan;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_KT_RISKDEF_PLAN
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';     
                 
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CONTRACT_ICDDEF');
  SELECT NVL(max(ICD_ID),0)+1 INTO v_count FROM dm_conf_contract_icddef;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CONTRACT_ICDDEF
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';     
                 
dmuser.dm_pack_commonutils.DROP_SEQUENCE('dm_seq_conf_icg_loss_plan');
  SELECT NVL(max(LOSS_PLAN_ID),0)+1 INTO v_count FROM dm_conf_icg_profitloss_plan;
  EXECUTE IMMEDIATE 'create sequence dm_seq_conf_icg_loss_plan
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';     
                 
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CONTRACT_LOSSDEF');
  SELECT NVL(max(LOSS_ID),0)+1 INTO v_count FROM dm_conf_contract_lossdef;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CONTRACT_LOSSDEF
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';
                 
dmuser.dm_pack_commonutils.DROP_SEQUENCE('dm_seq_profit_loss_factor');
  SELECT NVL(max(BBA_PROFIT_LOSS_FACTOR_ID),0)+1 INTO v_count FROM dm_conf_bba_profit_loss_factor;
  EXECUTE IMMEDIATE 'create sequence dm_seq_profit_loss_factor
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';
        
        
--轨迹表
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CHECKRULEHIS');
  SELECT NVL(max(CONFIG_RULE_HIS_ID),0)+1 INTO v_count FROM dm_conf_checkrulehis;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CHECKRULEHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
        
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CODE_HIS');
  SELECT NVL(max(code_his_id),0)+1 INTO v_count FROM dm_conf_code_his;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CODE_HIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';

dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CONTRACT_CODE_HIS');
  SELECT NVL(max(ITEM_HIS_ID),0)+1 INTO v_count FROM DM_CONF_CONTRACT_CODE_HIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CONTRACT_CODE_HIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
        
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CONTRACT_ICDDEFHIS');
  SELECT NVL(max(ICD_HIS_ID),0)+1 INTO v_count FROM DM_CONF_CONTRACT_ICDDEFHIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CONTRACT_ICDDEFHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';         
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CONTRACT_RULEHIS');
  SELECT NVL(max(CONTRACT_RULE_HIS_ID),0)+1 INTO v_count FROM DM_CONF_CONTRACT_RULEHIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CONTRACT_RULEHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';    
        
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CONT_LOSSDEFHIS');
  SELECT NVL(max(LOSS_HIS_ID),0)+1 INTO v_count FROM DM_CONF_CONTRACT_LOSSDEFHIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CONT_LOSSDEFHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';    
        
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CON_CODEDEF_HIS');
  SELECT NVL(max(ITEM_HIS_ID),0)+1 INTO v_count FROM DM_CONF_CONTRACT_CODEDEF_HIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CON_CODEDEF_HIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
            
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CON_CODEMAT_HIS');
  SELECT NVL(max(ITEM_HIS_ID),0)+1 INTO v_count FROM DM_CONF_CONTRACT_CODEMAT_HIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CON_CODEMAT_HIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
        
          
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CON_EVALDEFHIS');
  SELECT NVL(max(EVAL_HIS_ID),0)+1 INTO v_count FROM DM_CONF_CONTRACT_EVALDEFHIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CON_EVALDEFHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
                
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_CON_RISKDEFHIS');
  SELECT NVL(max(RISK_HIS_ID),0)+1 INTO v_count FROM DM_CONF_CONTRACT_RISKDEFHIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_CON_RISKDEFHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
                    
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_DRAWTYPELIST_HIS');
  SELECT NVL(max(ITEM_HIS_ID),0)+1 INTO v_count FROM DM_CONF_DRAWTYPELIST_HIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_DRAWTYPELIST_HIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
                      
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_ICG_LOSS_PLANHIS');
  SELECT NVL(max(loss_plan_his_id),0)+1 INTO v_count FROM dm_conf_icg_profitloss_planhis;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_ICG_LOSS_PLANHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
                      
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_TABLEHIS');
  SELECT NVL(max(BIZ_TYPE_HIS_ID),0)+1 INTO v_count FROM DM_CONF_TABLEHIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_TABLEHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
        
                      
dmuser.dm_pack_commonutils.DROP_SEQUENCE('DM_SEQ_CONF_TABLE_COLUMN_HIS');
  SELECT NVL(max(COL_HIS_ID),0)+1 INTO v_count FROM DM_CONF_TABLE_COLUMN_HIS;
  EXECUTE IMMEDIATE 'create sequence DM_SEQ_CONF_TABLE_COLUMN_HIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
end;
/

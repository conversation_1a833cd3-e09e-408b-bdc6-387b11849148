<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="IncorrectHttpHeaderInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="customHeaders">
        <set>
          <option value="    &quot;captcha_sign&quot;" />
          <option value="    &quot;client_version&quot;" />
          <option value="    &quot;package_name&quot;" />
        </set>
      </option>
    </inspection_tool>
  </profile>
</component>
/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-09-16 14:50:26
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.ss.platform.core.annotation.SsTranslateCode;
import com.ss.platform.core.constant.SystemConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-09-16 14:50:26<br/>
 * Description: 指标配置表<br/>
 * Table Name: ATR_CONF_QUOTA<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "指标配置表")
public class AtrConfQuotaVo implements Serializable {
    /**
     * Database column: ATR_CONF_QUOTA.QUOTA_ID
     * Database remarks: quotaId|主键
     */
    @ApiModelProperty(value = "quotaId|主键", required = true)
    private Long quotaId;

    /**
     * Database column: ATR_CONF_QUOTA.CENTER_ID
     * Database remarks: entityId|业务单位
     */
    @ApiModelProperty(value = "entityId|业务单位", required = true)
    @NotNull(message = "TheBusiness Unit can't be null|业务单位不能为空|業務單位不能為空")
    private Long entityId;

    /**
     * Database column: ATR_CONF_QUOTA.MODEL_DEF_ID
     * Database remarks: model_def_id|计量模型
     */
    @ApiModelProperty(value = "model_def_id|计量模型", required = true)
    private String businessSourceCode;

    /**
     * Database column: bbs_conf_quota.quota_class
     * Database remarks: quotaClass|指标分类
     */
    @ApiModelProperty(value = "quotaClass|指标归类", required = false)
    @SsTranslateCode(context = SystemConstant.AtrIdentity.APP_CONTEXT,codeCodeIdx = "QuotaClass")
    private String quotaClass;

    private String yearMonth;
    /**
     * Database column: ATR_CONF_QUOTA.LOA_CODE
     * Database remarks: loa_code|业务线编码
     */
    @ApiModelProperty(value = "loa_code|业务线编码", required = true)
    private String loaCode;

    /**
     * Database column: ATR_CONF_QUOTA.QUOTA_DEF_ID
     * Database remarks: quotaDefId|指标定义
     */
    @ApiModelProperty(value = "quotaDefId|指标定义", required = true)
    @NotNull(message = "The quotaDefId can't be null|指标定义不能为空|指標定義不能為空")
    private Long quotaDefId;

    /**
     * Database column: ATR_CONF_QUOTA.QUOTA_VALUE
     * Database remarks: quotaValue|指标值
     */
    @ApiModelProperty(value = "quotaValue|指标值", required = false)
    private String quotaValue;

    /**
     * Database column: ATR_CONF_QUOTA.VALID_IS
     * Database remarks: validIs|是否有效
     */
    @ApiModelProperty(value = "validIs|是否有效", required = true)
    @NotBlank(message = "The Status cannot be empty|状态不能为空|狀態不能為空")
    @Size(max = 1,message = "The Status must be a character|状态只能是1个字符|狀態只能是1個字符")
    private String validIs;

    /**
     * Database column: ATR_CONF_QUOTA.AUDIT_STATE
     * Database remarks: auditState|审核状态
     */
    @ApiModelProperty(value = "auditState|审核状态", required = false)
    private String auditState;

    /**
     * Database column: ATR_CONF_QUOTA.CHECKED_MSG
     * Database remarks: checkedMsg|审核意见
     */
    @ApiModelProperty(value = "checkedMsg|审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: ATR_CONF_QUOTA.CHECKED_ID
     * Database remarks: checkedId|审核热
     */
    @ApiModelProperty(value = "checkedId|审核热", required = false)
    private Long checkedId;

    /**
     * Database column: ATR_CONF_QUOTA.CHECKED_TIME
     * Database remarks: checkedTime|审核时间
     */
    @ApiModelProperty(value = "checkedTime|审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: ATR_CONF_QUOTA.CREATOR_ID
     * Database remarks: creatorId|创建人
     */
    @ApiModelProperty(value = "creatorId|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_CONF_QUOTA.CREATE_TIME
     * Database remarks: createTime|创建时间
     */
    @ApiModelProperty(value = "createTime|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_CONF_QUOTA.UPDATOR_ID
     * Database remarks: updatorId|最后修改人
     */
    @ApiModelProperty(value = "updatorId|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_CONF_QUOTA.UPDATE_TIME
     * Database remarks: updateTime|最后修改时间
     */
    @ApiModelProperty(value = "updateTime|最后修改时间", required = false)
    private Date updateTime;

    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Integer serialNo;

    /**
     * Database column: ATR_CONF_QUOTA_NEW.dimension
     * Database remarks: dimension|假设颗粒度类型( 合同组合、合同组、保单)
     */
    @ApiModelProperty(value = "dimension|假设颗粒度类型( 合同组合、合同组、保单)", required = false)
    private String dimension;

    /**
     * Database column: ATR_CONF_QUOTA_NEW.dimension_value
     * Database remarks: dimension_value|假设颗粒度值
     */
    @ApiModelProperty(value = "dimension_value|假设颗粒度值", required = true)
    private String dimensionValue;

    private String creatorName;

    private String updatorName;

    private String quotaCode;
    private String quotaName;
    private String quotaCName;
    private String quotaLName;
    private String quotaEName;
    private String quotaType;
    private String quotaPeriod;
    private String quotaValueType;
    private String entityCode;
    private String entityEName;
    private String entityCName;
    private String entityLName;

    private String modelCode;
    private String modelCName;
    private String modelLName;
    private String modelEName;

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/15
     * @Description 区分新增和编辑或查看
     *
     */
    private String type;
    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/15
     * @Description 区分新增和编辑或查看
     *
     */
    private String validIsDef;

    private List<AtrConfQuotaDetailVo> atrConfQuotaDetailVoList;

    private String loaCName;
    private String loaLName;
    private String loaEName;
    private String loaName;
    private String orCondition;
    private String perYearMonth;

    private String autoCalculatedIs;

    private String businessSourceCodeName;

    private static final long serialVersionUID = 1L;

    private String riskClassCode;

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getBusinessSourceCodeName() {
        return businessSourceCodeName;
    }

    public void setBusinessSourceCodeName(String businessSourceCodeName) {
        this.businessSourceCodeName = businessSourceCodeName;
    }

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getQuotaClass() {
        return quotaClass;
    }

    public void setQuotaClass(String quotaClass) {
        this.quotaClass = quotaClass;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    public String getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(String quotaValue) {
        this.quotaValue = quotaValue;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }


    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public String getQuotaName() {
        return quotaName;
    }

    public void setQuotaName(String quotaName) {
        this.quotaName = quotaName;
    }

    public String getQuotaCName() {
        return quotaCName;
    }

    public void setQuotaCName(String quotaCName) {
        this.quotaCName = quotaCName;
    }

    public String getQuotaLName() {
        return quotaLName;
    }

    public void setQuotaLName(String quotaLName) {
        this.quotaLName = quotaLName;
    }

    public String getQuotaEName() {
        return quotaEName;
    }

    public void setQuotaEName(String quotaEName) {
        this.quotaEName = quotaEName;
    }

    public String getQuotaType() {
        return quotaType;
    }

    public void setQuotaType(String quotaType) {
        this.quotaType = quotaType;
    }

    public String getQuotaPeriod() {
        return quotaPeriod;
    }

    public void setQuotaPeriod(String quotaPeriod) {
        this.quotaPeriod = quotaPeriod;
    }

    public String getQuotaValueType() {
        return quotaValueType;
    }

    public void setQuotaValueType(String quotaValueType) {
        this.quotaValueType = quotaValueType;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValidIsDef() {
        return validIsDef;
    }

    public void setValidIsDef(String validIsDef) {
        this.validIsDef = validIsDef;
    }

    public List<AtrConfQuotaDetailVo> getAtrConfQuotaDetailVoList() {
        return atrConfQuotaDetailVoList;
    }

    public void setAtrConfQuotaDetailVoList(List<AtrConfQuotaDetailVo> atrConfQuotaDetailVoList) {
        this.atrConfQuotaDetailVoList = atrConfQuotaDetailVoList;
    }

    public String getLoaCName() {
        return loaCName;
    }

    public void setLoaCName(String loaCName) {
        this.loaCName = loaCName;
    }

    public String getLoaLName() {
        return loaLName;
    }

    public void setLoaLName(String loaLName) {
        this.loaLName = loaLName;
    }

    public String getLoaEName() {
        return loaEName;
    }

    public void setLoaEName(String loaEName) {
        this.loaEName = loaEName;
    }

    public String getOrCondition() {
        return orCondition;
    }

    public void setOrCondition(String orCondition) {
        this.orCondition = orCondition;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getModelCName() {
        return modelCName;
    }

    public void setModelCName(String modelCName) {
        this.modelCName = modelCName;
    }

    public String getModelLName() {
        return modelLName;
    }

    public void setModelLName(String modelLName) {
        this.modelLName = modelLName;
    }

    public String getModelEName() {
        return modelEName;
    }

    public void setModelEName(String modelEName) {
        this.modelEName = modelEName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getDimensionValue() {
        return dimensionValue;
    }

    public void setDimensionValue(String dimensionValue) {
        this.dimensionValue = dimensionValue;
    }

    public String getPerYearMonth() {
        return perYearMonth;
    }

    public void setPerYearMonth(String perYearMonth) {
        this.perYearMonth = perYearMonth;
    }

    public String getAutoCalculatedIs() {
        return autoCalculatedIs;
    }

    public void setAutoCalculatedIs(String autoCalculatedIs) {
        this.autoCalculatedIs = autoCalculatedIs;
    }

    public String getLoaName() {
        return loaName;
    }

    public void setLoaName(String loaName) {
        this.loaName = loaName;
    }
}
--清除校验日志
TRUNCATE TABLE dm_log_check_rule;
TRUNCATE TABLE dm_log_data_verify;
TRUNCATE TABLE dm_log_data_verify_detail;
--更新信号表状态为0待处理状态
update ods_data_push_signal
set  TASK_STATUS = '0';
commit;

--全量校验
begin 
   for cur_verify in (
    select ps.task_code as task_code,
           ps.year_month as year_month,
           t.biz_code as biz_code,t.biz_type_id as biz_type_id,
           t.type_group as type_group
     from ods_data_push_signal ps 
       join dm_conf_table t on ps.push_model = t.biz_code 
     where ps.TASK_STATUS = '0'
          order by ps.year_month,
                (case when t.type_group = '7' then '0' else t.type_group end ),t.display_no,ps.task_code) loop
   
         dm_pack_data_verify.proc_data_verify(1,cur_verify.task_code,
                             cur_verify.biz_type_id,1,'2');
   
   end loop;                         
                           
end;
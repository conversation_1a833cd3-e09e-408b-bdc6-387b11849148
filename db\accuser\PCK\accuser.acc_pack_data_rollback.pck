create or replace package acc_pack_data_rollback is

  -- Author  : WUYH
  -- Created : 2023-7-26 14:35:39
  -- Purpose :
  PROCEDURE proc_month_bak(p_entity_id  NUMBER, p_yearmonth VARCHAR2);

  PROCEDURE proc_all_bak(p_yearmonth VARCHAR2);

end acc_pack_data_rollback;
/
create or replace package body acc_pack_data_rollback IS

   PROCEDURE proc_month_bak(p_entity_id  NUMBER, p_yearmonth VARCHAR2) AS
   BEGIN

    --冲销凭证数据
    DELETE from acc_temp_voucher_detail where voucher_id  in (select voucher_id from acc_temp_voucher where entity_id = p_entity_id and book_code='BookI17' and year_month=p_yearmonth  );
    DELETE from acc_temp_voucher where entity_id = p_entity_id and year_month=p_yearmonth ;

    DELETE FROM acc_temp_entry_data_detail T  WHERE t.buss_entry_id IN (
    SELECT buss_entry_id from acc_temp_entry_data where entity_id = p_entity_id and book_code='BookI17' and year_month=p_yearmonth);

    DELETE from acc_temp_entry_data where entity_id = p_entity_id and book_code='BookI17' and year_month=p_yearmonth;
    COMMIT;
    --凭证数据
    DELETE from acc_buss_voucher_detail where voucher_id  in (select voucher_id from acc_buss_voucher where entity_id = p_entity_id and book_code='BookI17' and year_month=p_yearmonth  );
    DELETE from acc_buss_voucher where entity_id = p_entity_id and year_month=p_yearmonth ;
    DELETE from acc_buss_voucherhis where entity_id = p_entity_id and book_code='BookI17' and year_month=p_yearmonth;
   
    --入账过程数据
    DELETE FROM acc_buss_entry_data_detail T  WHERE t.buss_entry_id IN (
           SELECT buss_entry_id from acc_buss_entry_data where entity_id = p_entity_id and book_code='BookI17' and year_month=p_yearmonth);

    DELETE from acc_buss_entry_data where entity_id = p_entity_id and book_code='BookI17' and year_month=p_yearmonth;
    DELETE from acc_duct_contra_data  where entity_id = p_entity_id and book_code='BookI17' and year_month=p_yearmonth;
    
    --入账日志信息
    DELETE FROM BPLUSER.BPL_LOG_ACTION_DETAIL  WHERE act_log_id IN (SELECT act_log_id FROM BPLUSER.BPL_LOG_ACTION WHERE entity_id = p_entity_id and book_code='BookI17' AND year_month=p_yearmonth);
    DELETE FROM BPLUSER.BPL_LOG_ACTION WHERE entity_id = p_entity_id and book_code='BookI17' AND year_month=p_yearmonth;
    COMMIT;

    --多准则对账数据
    DELETE from acc_buss_multi_reconrstdtl where recon_rst_id in (select recon_rst_id from acc_buss_multi_reconrst WHERE entity_id = p_entity_id and book_code='BookI17' AND year_month=p_yearmonth);
    DELETE from acc_buss_multi_reconrst WHERE entity_id = p_entity_id and book_code='BookI17' AND year_month=p_yearmonth;
   
    --余额数据
    DELETE from acc_buss_ledger_balance WHERE entity_id = p_entity_id and book_code='BookI17' AND year_month=p_yearmonth;
    DELETE from acc_buss_article_balance WHERE entity_id = p_entity_id and book_code='BookI17' AND year_month=p_yearmonth;
    DELETE from acc_buss_ledger_balancehis WHERE entity_id = p_entity_id and book_code='BookI17' AND year_month=p_yearmonth;
    DELETE from acc_buss_article_balancehis WHERE entity_id = p_entity_id and book_code='BookI17' AND year_month=p_yearmonth;


    DELETE from acc_dap_entry_data WHERE entity_id = p_entity_id AND year_month=p_yearmonth;
    DELETE from acc_ext_ledger_balance WHERE entity_id = p_entity_id  AND year_month=p_yearmonth;
    DELETE from acc_ext_article_balance WHERE entity_id = p_entity_id  AND year_month=p_yearmonth;
    DELETE from acc_ext_voucher_detail t WHERE EXISTS (
      SELECT 1 FROM acc_ext_voucher t1 WHERE t1.entity_id = p_entity_id  AND t1.year_month=p_yearmonth AND t.voucher_no=t1.voucher_no);
    DELETE from acc_ext_voucher WHERE entity_id = p_entity_id  AND year_month=p_yearmonth;
    COMMIT;

   END proc_month_bak;




   PROCEDURE PROC_ALL_BAK(p_yearmonth VARCHAR2) AS
   BEGIN
    --业务期间详情任务配置
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_conf_accountperiod_detail';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_conf_accountperiod';

    --总账数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_buss_ledger_balance';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_buss_ledger_balancehis';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_buss_article_balance';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_buss_article_balancehis';
    --多准则数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE accuser.acc_buss_multi_reconrstdtl';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE accuser.acc_buss_multi_reconrstdtlhis';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE accuser.acc_buss_multi_reconrst';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE accuser.acc_buss_multi_reconrsthis';
    --凭证数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_buss_voucher_detail';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_buss_voucher';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_buss_voucherhis';

    --入账过程数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE ACC_BUSS_ENTRY_DATA_DETAIL';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_buss_entry_data';

    --外部接入数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_ext_voucher_detail';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_ext_voucher';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_ext_ledger_balance';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_ext_article_balance';
    --待入账数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_dap_entry_data';

    --入账日志信息
    DELETE FROM bpluser.bpl_log_action_detail t where t.act_log_id in (SELECT t.act_log_id FROM bpluser.bpl_log_action t where t.system_code ='ACC');
    delete FROM bpluser.bpl_log_action t where t.system_code ='ACC';
    commit;

    --试对冲
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_temp_voucher_detail';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_temp_voucher';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_temp_entry_data_detail';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_temp_entry_data';

    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_buss_manual_voucher';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE acc_buss_manual_voucher_detail';
    
    --序列回滚
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_ARTICLE_BALANCE',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_BUSS_ENTRY_DATA',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_BUSS_ENTRY_DATA_DTL',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_BUSS_MULTI_RECONRSTDTL',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_BUSS_MUL_RECRSTDTLHIS',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_DAP_ENTRY_DATA',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_EXT_ARTICLE_BALANCE',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_EXT_LEDGER_BALANCE',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_EXT_VOUCHER',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_EXT_VOUCHERDETAIL',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_ITEM_ARTICLE_BALANCE',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_ITEM_ATC_BALANCEHIS',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_ITEM_LEDGER_BALANCE',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_ITEM_LEDGER_BALANCEHIS',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_MULTIRECONCILRESULT',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_MULTIRECONCILRESULTHIS',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_PERIOD',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_buss_voucher',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_buss_voucher_detail',1,1);
     acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_buss_voucherhis',1,1);

    --初始化业务期间
    insert into acc_conf_accountperiod (PERIOD_ID, ENTITY_ID, BOOK_CODE, PERIOD_TYPE, YEAR_MONTH, CURRENCY_CODE, EXECUTION_STATE, VALID_IS, AUDIT_STATE, CHECKED_ID, CHECKED_MSG, CHECKED_TIME, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME, SERIAL_NO)
    values (acc_seq_acc_conf_accountperiod.nextval, 1, 'BookI17', null, p_yearmonth, 'HKD', '0', '1', '1', 1, null, sysdate, 1, sysdate, 1, sysdate, 1);
    commit;

    --1、循环xxx_conf_period
    FOR cur_period IN (SELECT period_id,
                              execution_state
                         FROM acc_conf_accountperiod
                        WHERE valid_is = '1') LOOP
      --2、循环xxx_conf_table
      FOR cur_table IN (SELECT biz_type_id,
                               biz_code,
                               system_code
                          FROM acc_conf_table) LOOP

      INSERT INTO acc_conf_accountperiod_detail
      (period_detail_id,
       period_id,
       biz_type_id,
       task_time,
       exec_result,
       ready_state,
       creator_id,
       create_time,
       updator_id,
       update_time,
       system_code)
      VALUES
      (acc_seq_conf_acc_prd_dtl.NEXTVAL,
           cur_period.period_id,
           cur_table.biz_type_id,
       NULL,
       NULL,
       (CASE WHEN cur_period.execution_state = '1' OR cur_period.execution_state = '3' THEN '1' ELSE '0' END), --业务期间：1-已准备中或3-已完成，则准备状态为1-已准备，其它情况为0-准备中
       1,
       SYSDATE,
       NULL,
       NULL,
       cur_table.system_code);

            --提交事务
          COMMIT;

      END LOOP;
    END LOOP;

   END PROC_ALL_BAK;
end acc_pack_data_rollback;
/

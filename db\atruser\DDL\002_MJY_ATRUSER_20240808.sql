
-- 根据 uat3 20240807 跑数， 调整字段

do language plpgsql
$$
    declare
        rec record;
    begin

        for rec in select format('alter table %s alter column %s type varchar(20);', t.table_name, t.column_name) sql
                   from information_schema.columns t
                   where t.column_name = 'ri_endorse_seq_no'
                     and not t.table_name ~ 'p_\d+$'
                     and t.table_name like 'atr%'
                     and t.character_maximum_length < 20
                   union all
                   select format('alter table %s alter column %s  drop not null;', t.table_name, t.column_name)
                   from information_schema.columns t
                   where t.column_name = 'ri_statement_no'
                     and not t.table_name ~ 'p_\d+$'
                     and t.table_name like 'atr%'
                     and t.is_nullable = 'NO'
                   union all
                   select format('alter table %s alter column %s  drop not null;', t.table_name, t.column_name)
                   from information_schema.columns t
                   where t.column_name = 'cm_unit_dim_code'
                     and not t.table_name ~ 'p_\d+$'
                     and t.table_name like 'atr%'
                     and t.is_nullable = 'NO'
            loop

                execute rec.sql;

            end loop;

    end;
$$;
CREATE OR R<PERSON>LACE PACKAGE acc_pack_common IS

  FUNCTION func_analy_model_mapping(p_table_src IN VARCHAR2,
                                    p_table_map IN VARCHAR2,
                                    p_mcolumn IN VARCHAR2,
                                    p_condition_column IN VARCHAR2,
                                    p_condition_symbol IN VARCHAR2,
                                    p_condition_value IN VARCHAR2,
                                    p_condition_value_type IN VARCHAR2)
    RETURN VARCHAR2;

  FUNCTION func_concat_str(p_str IN VARCHAR2, symbol IN VARCHAR2)
    RETURN VARCHAR2;

  FUNCTION func_get_exch_rate(p_entity_id IN NUMBER,
                              p_exch_date DATE,
                              p_currency IN VARCHAR2,
                              p_exch_currency IN VARCHAR2,
                              p_exch_type IN VARCHAR2) RETURN NUMBER;

  FUNCTION func_correct_voucher_date(p_year_month IN VARCHAR2) RETURN DATE;

  FUNCTION func_get_base_account_id(p_account_id IN NUMBER) RETURN NUMBER;

  FUNCTION func_year_month_dimension(p_year_month IN VARCHAR2,
                                     p_year_month_other IN VARCHAR2,
                                     dimension_type IN VARCHAR2)
    RETURN NUMBER;

END acc_pack_common;
/
CREATE OR REPLACE PACKAGE BODY acc_pack_common IS

  FUNCTION func_analy_model_mapping(p_table_src IN VARCHAR2,
                                    p_table_map IN VARCHAR2,
                                    p_mcolumn IN VARCHAR2,
                                    p_condition_column IN VARCHAR2,
                                    p_condition_symbol IN VARCHAR2,
                                    p_condition_value IN VARCHAR2,
                                    p_condition_value_type IN VARCHAR2)
    RETURN VARCHAR2 IS
    v_sql             VARCHAR2(4000); --执行sql脚本
    v_result          VARCHAR2(500);
    v_condition_value VARCHAR2(500); --计数
  BEGIN
    SELECT (CASE lower(p_condition_value_type)
             WHEN 'number' THEN
              'to_number(' || p_condition_value || ')'
             WHEN 'date' THEN
              'to_date(''' || p_condition_value || ''',yyyy/mm/dd)'
             ELSE
              p_condition_value
           END)
      INTO v_condition_value
      FROM dual;
      
    SELECT
           CASE t.column_rule
            WHEN '1' THEN
              MAX('select ' || REPLACE(t.rule_config, ';', '') || ' from ' || t.source_table || ' t' ||
               ' where ' || p_condition_column || p_condition_symbol ||
               v_condition_value)
            ELSE
              MAX('select ' || t.source_column || ' from ' || t.source_table || ' t' ||
               ' where ' || p_condition_column || p_condition_symbol ||
               v_condition_value)
           END 
      INTO v_sql
      FROM bpluser.bbs_conf_model_mapping t
     WHERE source_table = p_table_src
       AND mapping_table = p_table_map
       AND mapping_column = p_mcolumn
       GROUP BY t.column_rule,t.source_column;      
     
    IF v_sql IS NULL THEN
      RETURN NULL;
    END IF;
  
    BEGIN
      EXECUTE IMMEDIATE v_sql
        INTO v_result;
    EXCEPTION
      WHEN OTHERS THEN
        dbms_output.put_line('**SQLSTATE: ' || to_char(SQLCODE) ||
                             '; **SQLERRM: ' || substr(SQLERRM, 1, 200) ||
                             ',【脚本解析失败，请检查脚本Sql：' || v_sql || '】');
        RETURN NULL;
    END;
    --返回脚本结果
    RETURN v_result;
  END func_analy_model_mapping;

  FUNCTION func_concat_str(p_str IN VARCHAR2, symbol IN VARCHAR2)
    RETURN VARCHAR2 IS
    v_str VARCHAR2(4000) := ''; --执行sql脚本
  BEGIN
    IF p_str IS NOT NULL THEN
      v_str := p_str || symbol;
    END IF;
    --返回脚本结果
    RETURN v_str;
  END func_concat_str;

  FUNCTION func_get_exch_rate(p_entity_id IN NUMBER,
                              p_exch_date DATE,
                              p_currency IN VARCHAR2,
                              p_exch_currency IN VARCHAR2,
                              p_exch_type IN VARCHAR2) RETURN NUMBER IS
    /***********************************************************************
    NAME :ACC_func_get_exch_rate
    DESCRIPTION :获取汇率
    DATE :2020-10-29
    AUTHOR :LEIHUAN
    BUSINESS RULE : 获取小于等于当前日期的最新汇率
    ***********************************************************************/
    v_exchrate NUMBER(32,8) := 1;
  
  BEGIN
    IF p_exch_currency = p_currency THEN
      v_exchrate := 1;
    ELSE
    
      SELECT MAX(a.EXCHANGE_RATE)
        INTO v_exchrate
        FROM bpluser.bbs_conf_currencyrate a
       WHERE entity_id = p_entity_id
         AND CURRENCY_CODE = p_currency
         AND EXCH_CURRENCY_CODE = p_exch_currency
         AND FREQUENCY_CODE = p_exch_type
         AND audit_state = '1'
         AND valid_is = '1'
         AND EFFECTIVE_DATE = (SELECT MAX(b.EFFECTIVE_DATE)
                            FROM bpluser.bbs_conf_currencyrate b
                           WHERE b.entity_id = p_entity_id
                             AND b.CURRENCY_CODE = p_currency
                             AND b.EXCH_CURRENCY_CODE = p_exch_currency
                             AND b.FREQUENCY_CODE = p_exch_type
                             AND b.EFFECTIVE_DATE <= p_exch_date
                             AND b.audit_state = '1'
                             AND b.valid_is = '1');
    
    END IF;
  
    RETURN v_exchrate;
  EXCEPTION
    WHEN OTHERS THEN
      --抛出异常提示信息
      --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
      ---RAISE NOTICE '[EXCEPTION]acc_pack_voucher_func_get_exch_rate：%; %',v_text1,v_text2;
      RETURN NULL;
  END func_get_exch_rate;

  FUNCTION func_correct_voucher_date(p_year_month IN VARCHAR2) RETURN DATE IS
    /***********************************************************************
    NAME :ACC_func_correct_voucher_date
    DESCRIPTION :获取凭证日期-依据会计期间的凭证日期
    DATE :2020-10-29
    AUTHOR :LEIHUAN
    BUSINESS RULE : 若会计期间在当前现实时间月份 取当前日期
                    若会计期间不在当前现实时间月份 取会计期间月最后一天
    ***********************************************************************/
    v_voucher_date DATE := SYSDATE;
  BEGIN
  
    IF to_char(SYSDATE, 'yyyymm') = p_year_month THEN
      v_voucher_date := trunc(SYSDATE);
    ELSE
      --需要指定格式date，否则会默认为日期+时间
      v_voucher_date := trunc(last_day(to_date(p_year_month || '01',
                                               'YYYYMMDD')));
    END IF;
  
    RETURN v_voucher_date;
  EXCEPTION
    WHEN OTHERS THEN
      --抛出异常提示信息
      --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
      --RAISE NOTICE '[EXCEPTION]acc_pack_voucher_func_correct_voucher_date：%; %',v_text1,v_text2;
      dbms_output.put_line('[EXCEPTION]acc_pack_voucher_func_correct_voucher_date：' ||
                           to_char(SQLCODE) || ',' ||
                           substr(SQLERRM, 1, 200));
      RETURN trunc(SYSDATE);
    
  END func_correct_voucher_date;

  FUNCTION func_get_base_account_id(p_account_id IN NUMBER) RETURN NUMBER IS
    /***********************************************************************
      NAME : acc_pack_buss_balance_func_get_base_itemid
      DESCRIPTION : 根据科目id获取顶级科目id
      DATE :2020-12-29
      AUTHOR :YINXH
    ***********************************************************************/
    v_account_id NUMBER; --科目ID
  BEGIN
    SELECT MIN(t.account_id)
      INTO v_account_id
      FROM (WITH tb_result AS (SELECT account_id, upper_account_id, account_level
                                 FROM bpluser.bbs_account c
                                START WITH c.valid_is = '1'
                                       AND account_id = p_account_id
                               CONNECT BY PRIOR upper_account_id = c.account_id)
             SELECT *
               FROM tb_result
              WHERE account_level = 1
                AND rownum = 1) t;
  
  
    RETURN v_account_id;
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line('**异常信息：' || to_char(SQLCODE) || '::' ||
                           substr(SQLERRM, 1, 200));
      RETURN NULL;
  END func_get_base_account_id;

  FUNCTION func_year_month_dimension(p_year_month IN VARCHAR2,
                                     p_year_month_other IN VARCHAR2,
                                     dimension_type IN VARCHAR2)
    RETURN NUMBER IS
    /***********************************************************************
      NAME : func_year_month_dimension
      DESCRIPTION :  会计期间时间是否同一维度判断 ，dimension_type维度类型(1-同月，3-同季度，12-同年度)
      DATE :2020-12-29
      AUTHOR :wyh
    ***********************************************************************/
  
    v_result NUMBER(10) := 0;
  BEGIN
  
    IF dimension_type = '1' THEN
      IF p_year_month = p_year_month_other THEN
        RETURN 1;
      END IF;
    ELSIF dimension_type = '3' THEN
      IF substr(p_year_month, 1, 4) = substr(p_year_month_other, 1, 4)
         AND ceil(CAST(substr(p_year_month, 5) AS NUMBER) / 3) =
         ceil(CAST(substr(p_year_month_other, 5) AS NUMBER) / 3) THEN
        RETURN 1;
      END IF;
    ELSIF dimension_type = '12' THEN
      IF substr(p_year_month, 1, 4) = substr(p_year_month_other, 1, 4) THEN
        RETURN 1;
      END IF;
    END IF;
    RETURN v_result;
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line('**异常信息：' || to_char(SQLCODE) || '::' ||
                           substr(SQLERRM, 1, 200));
      RETURN 0;
  END func_year_month_dimension;

END acc_pack_common;
/

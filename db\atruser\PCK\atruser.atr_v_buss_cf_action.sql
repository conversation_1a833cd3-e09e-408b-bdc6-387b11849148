CREATE OR REPLACE VIEW ATR_V_BUSS_CF_ACTION AS
SELECT 'Prem' AS cf_calc_type,
    t.action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    t.business_source_code,
    ddicg.loa_code
   FROM atr_buss_lrc_action t
     LEFT JOIN atr_buss_dd_lrc_icg_calc ddicg ON t.action_no = ddicg.action_no
  WHERE t.confirm_is = '1' AND t.business_source_code = 'DD' AND ddicg.loa_code IS NOT NULL
  GROUP BY t.action_no, t.entity_id, t.year_month, t.currency_code, t.business_source_code, ddicg.loa_code
UNION
 SELECT 'Prem' AS cf_calc_type,
    t.action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    t.business_source_code,
    foicg.loa_code
   FROM atr_buss_lrc_action t
     LEFT JOIN atr_buss_fo_lrc_icg_calc foicg ON t.action_no = foicg.action_no
  WHERE t.confirm_is = '1' AND t.business_source_code = 'FO' AND foicg.loa_code IS NOT NULL
  GROUP BY t.action_no, t.entity_id, t.year_month, t.currency_code, t.business_source_code, foicg.loa_code
UNION
 SELECT 'Prem' AS cf_calc_type,
    t.action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    t.business_source_code,
    tiicg.loa_code
   FROM atr_buss_lrc_action t
     LEFT JOIN atr_buss_ti_lrc_icg_calc tiicg ON t.action_no = tiicg.action_no
  WHERE t.confirm_is = '1' AND t.business_source_code = 'TI' AND tiicg.loa_code IS NOT NULL
  GROUP BY t.action_no, t.entity_id, t.year_month, t.currency_code, t.business_source_code, tiicg.loa_code
UNION
 SELECT 'Prem' AS cf_calc_type,
    t.action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    t.business_source_code,
    toicg.loa_code
   FROM atr_buss_lrc_action t
     LEFT JOIN atr_buss_to_lrc_icg_calc toicg ON t.action_no = toicg.action_no
  WHERE t.confirm_is = '1' AND t.business_source_code = 'TO' AND toicg.loa_code IS NOT NULL
  GROUP BY t.action_no, t.entity_id, t.year_month, t.currency_code, t.business_source_code, toicg.loa_code
UNION
 SELECT 'Claim' AS cf_calc_type,
    t.action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    t.business_source_code,
    ddicg.loa_code
   FROM atr_buss_lic_action t
     LEFT JOIN atr_buss_dd_lic_icg_calc ddicg ON t.action_no = ddicg.action_no
  WHERE t.confirm_is = '1' AND t.business_source_code = 'DD' AND ddicg.loa_code IS NOT NULL
  GROUP BY t.action_no, t.entity_id, t.year_month, t.currency_code, t.business_source_code, ddicg.loa_code
UNION
 SELECT 'Claim' AS cf_calc_type,
    t.action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    t.business_source_code,
    foicg.loa_code
   FROM atr_buss_lic_action t
     LEFT JOIN atr_buss_fo_lic_icg_calc foicg ON t.action_no = foicg.action_no
  WHERE t.confirm_is = '1' AND t.business_source_code = 'FO' AND foicg.loa_code IS NOT NULL
  GROUP BY t.action_no, t.entity_id, t.year_month, t.currency_code, t.business_source_code, foicg.loa_code
UNION
 SELECT 'Claim' AS cf_calc_type,
    t.action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    t.business_source_code,
    tiicg.loa_code
   FROM atr_buss_lic_action t
     LEFT JOIN atr_buss_ti_lic_icg_calc tiicg ON t.action_no = tiicg.action_no
  WHERE t.confirm_is = '1' AND t.business_source_code = 'TI' AND tiicg.loa_code IS NOT NULL
  GROUP BY t.action_no, t.entity_id, t.year_month, t.currency_code, t.business_source_code, tiicg.loa_code
UNION
 SELECT 'Claim' AS cf_calc_type,
    t.action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    t.business_source_code,
    toicg.loa_code
   FROM atr_buss_lic_action t
     LEFT JOIN atr_buss_to_lic_icg_calc toicg ON t.action_no = toicg.action_no
  WHERE t.confirm_is = '1' AND t.business_source_code = 'TO' AND toicg.loa_code IS NOT NULL
  GROUP BY t.action_no, t.entity_id, t.year_month, t.currency_code, t.business_source_code, toicg.loa_code;
--总账数据
TRUNCATE TABLE acc_buss_ledger_balance;
TRUNCATE TABLE acc_buss_ledger_balancehis;
TRUNCATE TABLE acc_buss_article_balance;
TRUNCATE TABLE acc_buss_article_balancehis;
--多准则数据
TRUNCATE TABLE accuser.acc_buss_multi_reconrstdtl;
TRUNCATE TABLE accuser.acc_buss_multi_reconrstdtlhis;
TRUNCATE TABLE accuser.acc_buss_multi_reconrst;
TRUNCATE TABLE accuser.acc_buss_multi_reconrsthis;
--凭证数据
TRUNCATE TABLE acc_buss_voucher_detail;
TRUNCATE TABLE acc_buss_voucher;
TRUNCATE TABLE acc_buss_voucherhis;

--入账过程数据
TRUNCATE TABLE ACC_BUSS_ENTRY_DATA_DETAIL;
TRUNCATE TABLE acc_buss_entry_data;

--外部接入数据
TRUNCATE TABLE acc_ext_voucher_detail;
TRUNCATE TABLE acc_ext_voucher;
TRUNCATE TABLE acc_ext_ledger_balance;
TRUNCATE TABLE acc_ext_article_balance;
--待入账数据
TRUNCATE TABLE acc_dap_entry_data;

--入账日志信息
DELETE FROM bpluser.bpl_log_action_detail t where t.act_log_id in (SELECT t.act_log_id FROM bpluser.bpl_log_action t where t.system_code ='ACC');
delete FROM bpluser.bpl_log_action t where t.system_code ='ACC';
UPDATE ACC_CONF_KEYNO SET KEY_NO=1 WHERE 1=1;
commit;

--试对冲
TRUNCATE TABLE acc_temp_voucher_detail;
TRUNCATE TABLE acc_temp_voucher;
TRUNCATE TABLE acc_temp_entry_data_detail;
TRUNCATE TABLE acc_temp_entry_data;

--业务期间详情任务配置
TRUNCATE TABLE acc_conf_accountperiod_detail;
TRUNCATE TABLE acc_conf_accountperiod;

--年结期间详情任务配置
TRUNCATE TABLE acc_conf_annual_period;

--序列回滚
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_acc_conf_accountperiod',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_conf_acc_prd_dtl',1,1);


--初始化业务期间
insert into acc_conf_accountperiod (PERIOD_ID, ENTITY_ID, BOOK_CODE, PERIOD_TYPE, YEAR_MONTH, CURRENCY_CODE, EXECUTION_STATE, VALID_IS, AUDIT_STATE, CHECKED_ID, CHECKED_MSG, CHECKED_TIME, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME, SERIAL_NO)
values (acc_seq_acc_conf_accountperiod.nextval, 1, 'BookI17', null, '202401', 'CNY', '0', '1', '1', 1, null, sysdate, 1, sysdate, 1, sysdate, 1);

commit;

--插入业务期间详情任务配置
DECLARE
BEGIN
  --1、循环xxx_conf_period
  FOR cur_period IN (SELECT period_id,
                            execution_state
                       FROM acc_conf_accountperiod
                      WHERE valid_is = '1') LOOP
    --2、循环xxx_conf_table
    FOR cur_table IN (SELECT biz_type_id,
                             biz_code,
               system_code
                        FROM acc_conf_table) LOOP
    
    
    INSERT INTO acc_conf_accountperiod_detail
    (period_detail_id,
     period_id,
     biz_type_id,
     task_time,
     exec_result,
     ready_state,
     creator_id,
     create_time,
     updator_id,
     update_time,
     system_code)
    VALUES
    (acc_seq_conf_acc_prd_dtl.NEXTVAL,
         cur_period.period_id,
         cur_table.biz_type_id,
     NULL,
     NULL,
     (CASE WHEN cur_period.execution_state = '1' OR cur_period.execution_state = '3' THEN '1' ELSE '0' END), --业务期间：1-已准备中或3-已完成，则准备状态为1-已准备，其它情况为0-准备中
     1,
     SYSDATE,
     NULL,
     NULL,
     cur_table.system_code);

          --提交事务
        COMMIT;
    
    END LOOP;
  END LOOP;

END;

/
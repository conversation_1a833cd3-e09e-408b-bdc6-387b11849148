
update acc_conf_account_article t set record_flag = '2' where t.account_code in ('************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'************',
'651105210100',
'651105210200',
'651105210300',
'651105219000',
'651105220000',
'651105230000',
'651105240100',
'651105240200',
'651105240300',
'651105240400',
'651105249000',
'651105250000',
'651105260100',
'651105260200',
'651105260300',
'651105260400',
'651105260500',
'651105260600',
'651105260700',
'651105260800',
'651105269000',
'651105270100',
'651105270200',
'651105270300',
'651105270400',
'651105270500',
'651105270600',
'651105270700',
'651105270800',
'651105270900',
'651105271000',
'651105271100',
'651105271200',
'651105271300',
'651105271400',
'651105271500',
'651105271600',
'651105279000',
'651105280000',
'651105290000',
'651105300000',
'651105310100',
'651105310200',
'651105310300',
'651105310400',
'651105310500',
'651105310600',
'651105320000',
'651105330100',
'651105330200',
'651105339000',
'651105340000',
'651105350000',
'651105360000',
'651105370000',
'651105380000',
'651105390000',
'651105400000',
'651105410000',
'651105420000',
'651105430000',
'651105440000',
'651105450000',
'651105460000',
'651105470000',
'651105470100',
'651105470200',
'651105470300',
'651105470400',
'651105470500',
'651105479000',
'651105480000',
'651105490000',
'651105500000',
'651105510100',
'651105510200',
'651105520000',
'651105530100',
'651105530200',
'651105900000',
'660101010000',
'660101020000',
'660102010100',
'660102010200',
'660102020000',
'660102030000',
'660102900000',
'660103010000',
'660103020000',
'660103030000',
'660103040000',
'660103050000',
'660103900000',
'660104010000',
'660104020000',
'660105010000',
'660105020100',
'660105020200',
'660106010000',
'660106020000',
'660106030000',
'660106040000',
'660106050000',
'660106900000',
'660107010000',
'660107020000',
'660107900000',
'660108000000',
'660108010000',
'660108020000',
'660109000000',
'660110010000',
'660110020000',
'660110900000',
'660111000000',
'660112000000',
'660113000000',
'660114000000',
'660115000000',
'660116000000',
'660117000000',
'660118000000',
'660119000000',
'660120010100',
'660120010200',
'660120010300',
'660120020000',
'660120030000',
'660120040000',
'660120050000',
'660120060000',
'660120070100',
'660120070200',
'660120080000',
'660120900000',
'660121010000',
'660121020000',
'660121030000',
'660121040000',
'660121050000',
'660121900000',
'660122010000',
'660122020000',
'660122030000',
'660122900000',
'660123010000',
'660123020000',
'660123030000',
'660123040000',
'660123050000',
'660123060000',
'660123900000',
'660124010000',
'660124020000',
'660124030000',
'660124040000',
'660124050000',
'660124060000',
'660124070000',
'660124900000',
'660125000000',
'660125000100',
'660126010000',
'660126900000',
'660127000000',
'660128010000',
'660128020000',
'660128030000',
'660128900000',
'660129010000',
'660129010100',
'660129900000',
'660130010100',
'660130010200',
'660130010300',
'660130020000',
'660130030000',
'660130040000',
'660130050000',
'660130060000',
'660130070000',
'660130080000',
'660130900000',
'660131010000',
'660131020000',
'660131030000',
'660131040000',
'660131900000',
'660132010000',
'660132020000',
'660132030000',
'660132900000',
'660133000000',
'660134010000',
'660134020000',
'660134900000',
'660135010000',
'660135020000',
'660135030000',
'660135040000',
'660135040100',
'660135040200',
'660135040300',
'660135050000',
'660135060000',
'660135070000',
'660135080000',
'660135090000',
'660135100000',
'660135110000',
'660135120000',
'660135130000',
'660135140000',
'660135150000',
'660135160000',
'660135170000',
'660135180000',
'660135190000',
'660135200000',
'660135200100',
'660135900000',
'660136010100',
'660136010200',
'660136010300',
'660136010400',
'660136010500',
'660136010600',
'660136010700',
'660136010800',
'660136010900',
'660136011000',
'660136011100',
'660136011200',
'660136011300',
'660136011400',
'660136011500',
'660136011600',
'660136019000',
'660136020100',
'660136020200',
'660136020300',
'660136020400',
'660136020500',
'660136020600',
'660136020700',
'660136020800',
'660136020900',
'660136021000',
'660136021100',
'660136021200',
'660136021300',
'660136021400',
'660136021500',
'660136021600',
'660136021700',
'660136029000',
'660136030100',
'660136030200',
'660136030300',
'660136030400',
'660136030500',
'660136030600',
'660136040100',
'660136040200',
'660136040300',
'660136040400',
'660136040500',
'660136040600',
'660136050100',
'660136050200',
'660136060100',
'660136060200',
'660136060300',
'660136060400',
'660136070100',
'660136070200',
'660136070300',
'660136080100',
'660136080200',
'660136090100',
'660136090200',
'660136100100',
'660136100200',
'660136110100',
'660136110200',
'660136120100',
'660136120200',
'660136130000',
'660136130100',
'660136130200',
'660136130300',
'660136130400',
'660136130500',
'660136130600',
'660136130700',
'660136130800',
'660136130900',
'660136131000',
'660136131100',
'660136131200',
'660136131300',
'660136140100',
'660136140200',
'660136150100',
'660136150200',
'660136160100',
'660136160200',
'660136170100',
'660136170200',
'660136900100',
'660136900200',
'660137010000',
'660137020000',
'660138010000',
'660138020000',
'660139000000',
'660140000000',
'660141010000',
'660141020000',
'660141030000',
'660141040000',
'660141050000',
'660141900000',
'660142010000',
'660142020000',
'660142030000',
'660143000000',
'660144010000',
'660144020000',
'660145000000',
'660146000000',
'660147000000',
'660148000000',
'660149000000',
'660150000000',
'660151000000',
'660152000000',
'660153010000',
'660153020000',
'660153030000',
'660153040000',
'660153050000',
'660153900000',
'660154010000',
'660154020000',
'660154020100',
'660154020200',
'660154020300',
'660154020400',
'660154020500',
'660154020600',
'660154020700',
'660154020800',
'660154030000',
'660154900000',
'660155000000',
'660155000100',
'660156000100',
'660156000200',
'660157000100',
'660157000200',
'660190000000',
'660190000100',
'660190000200',
'271109030000',
'654307010000',
'272107010000',
'660100000000',
'640300000000',
'640200000000',
'642100000000',
'654408010000',
'660199000000',
'640399000000',
'640299000000',
'671199000000',
'671100000000',
'654510000000'
) and article_code = 'L7';
grant SELECT on ODS_SEQ_DATA_PUSH_SIGNAL to DMUSER;
grant SELECT on ODS_SEQ_DATA_PUSH_SIGNALHIS to DMUSER;
grant DELETE on ODS_REINS_FLOAT_CHARGE to DMUSER;
grant INSERT on ODS_REINS_FLOAT_CHARGE to DMUSE<PERSON>;
grant SELECT on ODS_REINS_FLOAT_CHARGE to DMUSER;
grant UPDATE on ODS_REINS_FLOAT_CHARGE to DMUSER;
grant DELETE on ODS_ACC_PAYMENT to DMUSER;
grant INSERT on ODS_ACC_PAYMENT to DMUSER;
grant SELECT on ODS_ACC_PAYMENT to DMUSER;
grant UPDATE on ODS_ACC_PAYMENT to DMUSER;
grant DELETE on ODS_ACC_RECEIVABLE to DMUSER;
grant INSERT on ODS_ACC_RECEIVABLE to DMUSER;
grant SELECT on ODS_ACC_RECEIVABLE to DMUSER;
grant UPDATE on ODS_ACC_RECEIVABLE to DMUSER;
grant DELETE on ODS_BASE_ACCOUNT to DMU<PERSON><PERSON>;
grant INSERT on ODS_BASE_ACCOUNT to DMUSE<PERSON>;
grant SELECT on ODS_BASE_ACCOUNT to DMUSER;
grant UPDATE on ODS_BASE_ACCOUNT to DMUSER;
grant DELETE on ODS_BASE_CURRENCY to DMUSER;
grant INSERT on ODS_BASE_CURRENCY to DMUSER;
grant SELECT on ODS_BASE_CURRENCY to DMUSER;
grant UPDATE on ODS_BASE_CURRENCY to DMUSER;
grant DELETE on ODS_BASE_CURRENCY_RATE to DMUSER;
grant INSERT on ODS_BASE_CURRENCY_RATE to DMUSER;
grant SELECT on ODS_BASE_CURRENCY_RATE to DMUSER;
grant UPDATE on ODS_BASE_CURRENCY_RATE to DMUSER;
grant DELETE on ODS_BASE_ENTITY to DMUSER;
grant INSERT on ODS_BASE_ENTITY to DMUSER;
grant SELECT on ODS_BASE_ENTITY to DMUSER;
grant UPDATE on ODS_BASE_ENTITY to DMUSER;
grant DELETE on ODS_BASE_PRODUCT to DMUSER;
grant INSERT on ODS_BASE_PRODUCT to DMUSER;
grant SELECT on ODS_BASE_PRODUCT to DMUSER;
grant UPDATE on ODS_BASE_PRODUCT to DMUSER;
grant DELETE on ODS_BASE_RISK to DMUSER;
grant INSERT on ODS_BASE_RISK to DMUSER;
grant SELECT on ODS_BASE_RISK to DMUSER;
grant UPDATE on ODS_BASE_RISK to DMUSER;
grant DELETE on ODS_BASE_RISK_CLASS to DMUSER;
grant INSERT on ODS_BASE_RISK_CLASS to DMUSER;
grant SELECT on ODS_BASE_RISK_CLASS to DMUSER;
grant UPDATE on ODS_BASE_RISK_CLASS to DMUSER;
grant DELETE on ODS_BASE_RISK_MAPPING to DMUSER;
grant INSERT on ODS_BASE_RISK_MAPPING to DMUSER;
grant SELECT on ODS_BASE_RISK_MAPPING to DMUSER;
grant UPDATE on ODS_BASE_RISK_MAPPING to DMUSER;
grant DELETE on ODS_CLAIM_LOSS to DMUSER;
grant INSERT on ODS_CLAIM_LOSS to DMUSER;
grant SELECT on ODS_CLAIM_LOSS to DMUSER;
grant UPDATE on ODS_CLAIM_LOSS to DMUSER;
grant DELETE on ODS_CLAIM_LOSS_DETAIL to DMUSER;
grant INSERT on ODS_CLAIM_LOSS_DETAIL to DMUSER;
grant SELECT on ODS_CLAIM_LOSS_DETAIL to DMUSER;
grant UPDATE on ODS_CLAIM_LOSS_DETAIL to DMUSER;
grant DELETE on ODS_CLAIM_MAIN to DMUSER;
grant INSERT on ODS_CLAIM_MAIN to DMUSER;
grant SELECT on ODS_CLAIM_MAIN to DMUSER;
grant UPDATE on ODS_CLAIM_MAIN to DMUSER;
grant DELETE on ODS_CLAIM_OUTSTANDING to DMUSER;
grant INSERT on ODS_CLAIM_OUTSTANDING to DMUSER;
grant SELECT on ODS_CLAIM_OUTSTANDING to DMUSER;
grant UPDATE on ODS_CLAIM_OUTSTANDING to DMUSER;
grant DELETE on ODS_DATA_PUSH_SIGNAL to DMUSER;
grant INSERT on ODS_DATA_PUSH_SIGNAL to DMUSER;
grant SELECT on ODS_DATA_PUSH_SIGNAL to DMUSER;
grant UPDATE on ODS_DATA_PUSH_SIGNAL to DMUSER;
grant DELETE on ODS_DATA_PUSH_SIGNALHIS to DMUSER;
grant INSERT on ODS_DATA_PUSH_SIGNALHIS to DMUSER;
grant SELECT on ODS_DATA_PUSH_SIGNALHIS to DMUSER;
grant UPDATE on ODS_DATA_PUSH_SIGNALHIS to DMUSER;
grant DELETE on ODS_FIN_ARTICLE_BALANCE to DMUSER;
grant INSERT on ODS_FIN_ARTICLE_BALANCE to DMUSER;
grant SELECT on ODS_FIN_ARTICLE_BALANCE to DMUSER;
grant UPDATE on ODS_FIN_ARTICLE_BALANCE to DMUSER;
grant DELETE on ODS_FIN_LEDGER_BALANCE to DMUSER;
grant INSERT on ODS_FIN_LEDGER_BALANCE to DMUSER;
grant SELECT on ODS_FIN_LEDGER_BALANCE to DMUSER;
grant UPDATE on ODS_FIN_LEDGER_BALANCE to DMUSER;
grant DELETE on ODS_FIN_VOUCHER to DMUSER;
grant INSERT on ODS_FIN_VOUCHER to DMUSER;
grant SELECT on ODS_FIN_VOUCHER to DMUSER;
grant UPDATE on ODS_FIN_VOUCHER to DMUSER;
grant DELETE on ODS_FIN_VOUCHER_DETAIL to DMUSER;
grant INSERT on ODS_FIN_VOUCHER_DETAIL to DMUSER;
grant SELECT on ODS_FIN_VOUCHER_DETAIL to DMUSER;
grant UPDATE on ODS_FIN_VOUCHER_DETAIL to DMUSER;
grant DELETE on ODS_POLICY_MAIN to DMUSER;
grant INSERT on ODS_POLICY_MAIN to DMUSER;
grant SELECT on ODS_POLICY_MAIN to DMUSER;
grant UPDATE on ODS_POLICY_MAIN to DMUSER;
grant DELETE on ODS_POLICY_PAYMENT_PLAN to DMUSER;
grant INSERT on ODS_POLICY_PAYMENT_PLAN to DMUSER;
grant SELECT on ODS_POLICY_PAYMENT_PLAN to DMUSER;
grant UPDATE on ODS_POLICY_PAYMENT_PLAN to DMUSER;
grant DELETE on ODS_POLICY_PREMIUM to DMUSER;
grant INSERT on ODS_POLICY_PREMIUM to DMUSER;
grant SELECT on ODS_POLICY_PREMIUM to DMUSER;
grant UPDATE on ODS_POLICY_PREMIUM to DMUSER;
grant DELETE on ODS_REINS_BASE_TREATY to DMUSER;
grant INSERT on ODS_REINS_BASE_TREATY to DMUSER;
grant SELECT on ODS_REINS_BASE_TREATY to DMUSER;
grant UPDATE on ODS_REINS_BASE_TREATY to DMUSER;
grant DELETE on ODS_REINS_BILL to DMUSER;
grant INSERT on ODS_REINS_BILL to DMUSER;
grant SELECT on ODS_REINS_BILL to DMUSER;
grant UPDATE on ODS_REINS_BILL to DMUSER;
grant DELETE on ODS_REINS_BILL_DETAIL to DMUSER;
grant INSERT on ODS_REINS_BILL_DETAIL to DMUSER;
grant SELECT on ODS_REINS_BILL_DETAIL to DMUSER;
grant UPDATE on ODS_REINS_BILL_DETAIL to DMUSER;
grant DELETE on ODS_REINS_OUTWARD to DMUSER;
grant INSERT on ODS_REINS_OUTWARD to DMUSER;
grant SELECT on ODS_REINS_OUTWARD to DMUSER;
grant UPDATE on ODS_REINS_OUTWARD to DMUSER;
grant DELETE on ODS_REINS_OUTWARD_DETAIL to DMUSER;
grant INSERT on ODS_REINS_OUTWARD_DETAIL to DMUSER;
grant SELECT on ODS_REINS_OUTWARD_DETAIL to DMUSER;
grant UPDATE on ODS_REINS_OUTWARD_DETAIL to DMUSER;
grant DELETE on ODS_REINS_REINSURER to DMUSER;
grant INSERT on ODS_REINS_REINSURER to DMUSER;
grant SELECT on ODS_REINS_REINSURER to DMUSER;
grant UPDATE on ODS_REINS_REINSURER to DMUSER;
grant DELETE on ODS_REINS_RISK to DMUSER;
grant INSERT on ODS_REINS_RISK to DMUSER;
grant SELECT on ODS_REINS_RISK to DMUSER;
grant UPDATE on ODS_REINS_RISK to DMUSER;
grant DELETE on ODS_REINS_TREATY to DMUSER;
grant INSERT on ODS_REINS_TREATY to DMUSER;
grant SELECT on ODS_REINS_TREATY to DMUSER;
grant UPDATE on ODS_REINS_TREATY to DMUSER;
grant DELETE on ODS_REINS_TREATY_CLASS to DMUSER;
grant INSERT on ODS_REINS_TREATY_CLASS to DMUSER;
grant SELECT on ODS_REINS_TREATY_CLASS to DMUSER;
grant UPDATE on ODS_REINS_TREATY_CLASS to DMUSER;
grant DELETE on ODS_REINS_TREATY_PAYMENT_PLAN to DMUSER;
grant INSERT on ODS_REINS_TREATY_PAYMENT_PLAN to DMUSER;
grant SELECT on ODS_REINS_TREATY_PAYMENT_PLAN to DMUSER;
grant UPDATE on ODS_REINS_TREATY_PAYMENT_PLAN to DMUSER;
grant DELETE on ODS_REINS_TREATY_SECTION to DMUSER;
grant INSERT on ODS_REINS_TREATY_SECTION to DMUSER;
grant SELECT on ODS_REINS_TREATY_SECTION to DMUSER;
grant UPDATE on ODS_REINS_TREATY_SECTION to DMUSER;
grant EXECUTE on ODS_PACK_COMMONUTILS to DMUSER;
grant SELECT on ODS_V_ACC_PAYMENT to DMUSER;



grant SELECT on ODS_DATA_PUSH_SIGNAL to BPLUSER;
grant SELECT on ODS_V_ACC_PAYMENT to DMUSER;
grant EXECUTE on ODS_PACK_COMMONUTILS to DMUSER;
grant DELETE on ODS_FIN_ARTICLE_BALANCE to DMUSER;
grant INSERT on ODS_FIN_ARTICLE_BALANCE to DMUSER;
grant SELECT on ODS_FIN_ARTICLE_BALANCE to DMUSER;
grant UPDATE on ODS_FIN_ARTICLE_BALANCE to DMUSER;
grant DELETE on ODS_BASE_PRODUCT to DMUSER;
grant INSERT on ODS_BASE_PRODUCT to DMUSER;
grant SELECT on ODS_BASE_PRODUCT to DMUSER;
grant UPDATE on ODS_BASE_PRODUCT to DMUSER;
grant DELETE on ODS_REINS_TREATY to DMUSER;
grant INSERT on ODS_REINS_TREATY to DMUSER;
grant SELECT on ODS_REINS_TREATY to DMUSER;
grant UPDATE on ODS_REINS_TREATY to DMUSER;
grant DELETE on ODS_CLAIM_LOSS_DETAIL to DMUSER;
grant INSERT on ODS_CLAIM_LOSS_DETAIL to DMUSER;
grant SELECT on ODS_CLAIM_LOSS_DETAIL to DMUSER;
grant UPDATE on ODS_CLAIM_LOSS_DETAIL to DMUSER;
grant DELETE on ODS_REINS_FLOAT_CHARGE to DMUSER;
grant INSERT on ODS_REINS_FLOAT_CHARGE to DMUSER;
grant SELECT on ODS_REINS_FLOAT_CHARGE to DMUSER;
grant UPDATE on ODS_REINS_FLOAT_CHARGE to DMUSER;
grant DELETE on ODS_REINS_TREATY_PAYMENT_PLAN to DMUSER;
grant INSERT on ODS_REINS_TREATY_PAYMENT_PLAN to DMUSER;
grant SELECT on ODS_REINS_TREATY_PAYMENT_PLAN to DMUSER;
grant UPDATE on ODS_REINS_TREATY_PAYMENT_PLAN to DMUSER;
grant DELETE on ODS_REINS_OUTWARD to DMUSER;
grant INSERT on ODS_REINS_OUTWARD to DMUSER;
grant SELECT on ODS_REINS_OUTWARD to DMUSER;
grant UPDATE on ODS_REINS_OUTWARD to DMUSER;
grant DELETE on ODS_REINS_BASE_TREATY to DMUSER;
grant INSERT on ODS_REINS_BASE_TREATY to DMUSER;
grant SELECT on ODS_REINS_BASE_TREATY to DMUSER;
grant UPDATE on ODS_REINS_BASE_TREATY to DMUSER;
grant DELETE on ODS_ACC_RECEIVABLE to DMUSER;
grant INSERT on ODS_ACC_RECEIVABLE to DMUSER;
grant SELECT on ODS_ACC_RECEIVABLE to DMUSER;
grant UPDATE on ODS_ACC_RECEIVABLE to DMUSER;
grant DELETE on ODS_REINS_RISK to DMUSER;
grant INSERT on ODS_REINS_RISK to DMUSER;
grant SELECT on ODS_REINS_RISK to DMUSER;
grant UPDATE on ODS_REINS_RISK to DMUSER;
grant DELETE on ODS_POLICY_PREMIUM to DMUSER;
grant INSERT on ODS_POLICY_PREMIUM to DMUSER;
grant SELECT on ODS_POLICY_PREMIUM to DMUSER;
grant UPDATE on ODS_POLICY_PREMIUM to DMUSER;
grant DELETE on ODS_BASE_ENTITY to DMUSER;
grant INSERT on ODS_BASE_ENTITY to DMUSER;
grant SELECT on ODS_BASE_ENTITY to DMUSER;
grant UPDATE on ODS_BASE_ENTITY to DMUSER;
grant DELETE on ODS_FIN_LEDGER_BALANCE to DMUSER;
grant INSERT on ODS_FIN_LEDGER_BALANCE to DMUSER;
grant SELECT on ODS_FIN_LEDGER_BALANCE to DMUSER;
grant UPDATE on ODS_FIN_LEDGER_BALANCE to DMUSER;
grant DELETE on ODS_POLICY_MAIN to DMUSER;
grant INSERT on ODS_POLICY_MAIN to DMUSER;
grant SELECT on ODS_POLICY_MAIN to DMUSER;
grant UPDATE on ODS_POLICY_MAIN to DMUSER;
grant DELETE on ODS_POLICY_PAYMENT_PLAN to DMUSER;
grant INSERT on ODS_POLICY_PAYMENT_PLAN to DMUSER;
grant SELECT on ODS_POLICY_PAYMENT_PLAN to DMUSER;
grant UPDATE on ODS_POLICY_PAYMENT_PLAN to DMUSER;
grant DELETE on ODS_BASE_RISK_MAPPING to DMUSER;
grant INSERT on ODS_BASE_RISK_MAPPING to DMUSER;
grant SELECT on ODS_BASE_RISK_MAPPING to DMUSER;
grant UPDATE on ODS_BASE_RISK_MAPPING to DMUSER;
grant DELETE on ODS_REINS_TREATY_CLASS to DMUSER;
grant INSERT on ODS_REINS_TREATY_CLASS to DMUSER;
grant SELECT on ODS_REINS_TREATY_CLASS to DMUSER;
grant UPDATE on ODS_REINS_TREATY_CLASS to DMUSER;
grant DELETE on ODS_FIN_VOUCHER_DETAIL to DMUSER;
grant INSERT on ODS_FIN_VOUCHER_DETAIL to DMUSER;
grant SELECT on ODS_FIN_VOUCHER_DETAIL to DMUSER;
grant UPDATE on ODS_FIN_VOUCHER_DETAIL to DMUSER;
grant DELETE on ODS_DATA_PUSH_SIGNALHIS to DMUSER;
grant INSERT on ODS_DATA_PUSH_SIGNALHIS to DMUSER;
grant SELECT on ODS_DATA_PUSH_SIGNALHIS to DMUSER;
grant UPDATE on ODS_DATA_PUSH_SIGNALHIS to DMUSER;
grant DELETE on ODS_CLAIM_LOSS to DMUSER;
grant INSERT on ODS_CLAIM_LOSS to DMUSER;
grant SELECT on ODS_CLAIM_LOSS to DMUSER;
grant UPDATE on ODS_CLAIM_LOSS to DMUSER;
grant DELETE on ODS_BASE_RISK_CLASS to DMUSER;
grant INSERT on ODS_BASE_RISK_CLASS to DMUSER;
grant SELECT on ODS_BASE_RISK_CLASS to DMUSER;
grant UPDATE on ODS_BASE_RISK_CLASS to DMUSER;
grant DELETE on ODS_CLAIM_OUTSTANDING to DMUSER;
grant INSERT on ODS_CLAIM_OUTSTANDING to DMUSER;
grant SELECT on ODS_CLAIM_OUTSTANDING to DMUSER;
grant UPDATE on ODS_CLAIM_OUTSTANDING to DMUSER;
grant DELETE on ODS_FIN_VOUCHER to DMUSER;
grant INSERT on ODS_FIN_VOUCHER to DMUSER;
grant SELECT on ODS_FIN_VOUCHER to DMUSER;
grant UPDATE on ODS_FIN_VOUCHER to DMUSER;
grant DELETE on ODS_CLAIM_MAIN to DMUSER;
grant INSERT on ODS_CLAIM_MAIN to DMUSER;
grant SELECT on ODS_CLAIM_MAIN to DMUSER;
grant UPDATE on ODS_CLAIM_MAIN to DMUSER;
grant DELETE on ODS_BASE_ACCOUNT to DMUSER;
grant INSERT on ODS_BASE_ACCOUNT to DMUSER;
grant SELECT on ODS_BASE_ACCOUNT to DMUSER;
grant UPDATE on ODS_BASE_ACCOUNT to DMUSER;
grant DELETE on ODS_BASE_CURRENCY_RATE to DMUSER;
grant INSERT on ODS_BASE_CURRENCY_RATE to DMUSER;
grant SELECT on ODS_BASE_CURRENCY_RATE to DMUSER;
grant UPDATE on ODS_BASE_CURRENCY_RATE to DMUSER;
grant DELETE on ODS_REINS_BILL to DMUSER;
grant INSERT on ODS_REINS_BILL to DMUSER;
grant SELECT on ODS_REINS_BILL to DMUSER;
grant UPDATE on ODS_REINS_BILL to DMUSER;
grant DELETE on ODS_REINS_REINSURER to DMUSER;
grant INSERT on ODS_REINS_REINSURER to DMUSER;
grant SELECT on ODS_REINS_REINSURER to DMUSER;
grant UPDATE on ODS_REINS_REINSURER to DMUSER;
grant DELETE on ODS_ACC_PAYMENT to DMUSER;
grant INSERT on ODS_ACC_PAYMENT to DMUSER;
grant SELECT on ODS_ACC_PAYMENT to DMUSER;
grant UPDATE on ODS_ACC_PAYMENT to DMUSER;
grant DELETE on ODS_REINS_OUTWARD_DETAIL to DMUSER;
grant INSERT on ODS_REINS_OUTWARD_DETAIL to DMUSER;
grant SELECT on ODS_REINS_OUTWARD_DETAIL to DMUSER;
grant UPDATE on ODS_REINS_OUTWARD_DETAIL to DMUSER;
grant DELETE on ODS_BASE_RISK to DMUSER;
grant INSERT on ODS_BASE_RISK to DMUSER;
grant SELECT on ODS_BASE_RISK to DMUSER;
grant UPDATE on ODS_BASE_RISK to DMUSER;
grant DELETE on ODS_DATA_PUSH_SIGNAL to DMUSER;
grant INSERT on ODS_DATA_PUSH_SIGNAL to DMUSER;
grant SELECT on ODS_DATA_PUSH_SIGNAL to DMUSER;
grant UPDATE on ODS_DATA_PUSH_SIGNAL to DMUSER;
grant DELETE on ODS_BASE_CURRENCY to DMUSER;
grant INSERT on ODS_BASE_CURRENCY to DMUSER;
grant SELECT on ODS_BASE_CURRENCY to DMUSER;
grant UPDATE on ODS_BASE_CURRENCY to DMUSER;
grant DELETE on ODS_REINS_BILL_DETAIL to DMUSER;
grant INSERT on ODS_REINS_BILL_DETAIL to DMUSER;
grant SELECT on ODS_REINS_BILL_DETAIL to DMUSER;
grant UPDATE on ODS_REINS_BILL_DETAIL to DMUSER;
grant DELETE on ODS_REINS_TREATY_SECTION to DMUSER;
grant INSERT on ODS_REINS_TREATY_SECTION to DMUSER;
grant SELECT on ODS_REINS_TREATY_SECTION to DMUSER;
grant UPDATE on ODS_REINS_TREATY_SECTION to DMUSER;
grant SELECT on ODS_SEQ_DATA_PUSH_SIGNALHIS to DMUSER;
grant SELECT on ODS_SEQ_ACC_PAYMENT to DMUSER;
grant SELECT on ODS_SEQ_DATA_PUSH_SIGNAL to DMUSER;
grant SELECT on ODS_SEQ_ACC_RECEIVABLE to DMUSER;
grant SELECT on ODS_SEQ_BASE_ACCOUNT to DMUSER;
grant SELECT on ODS_SEQ_BASE_CURRENCY to DMUSER;
grant SELECT on ODS_SEQ_BASE_CURRENCY_RATE to DMUSER;
grant SELECT on ODS_SEQ_BASE_ENTITY to DMUSER;
grant SELECT on ODS_SEQ_BASE_PRODUCT to DMUSER;
grant SELECT on ODS_SEQ_BASE_RISK to DMUSER;
grant SELECT on ODS_SEQ_BASE_RISK_CLASS to DMUSER;
grant SELECT on ODS_SEQ_BASE_RISK_MAPPING to DMUSER;
grant SELECT on ODS_SEQ_CLAIM_LOSS to DMUSER;
grant SELECT on ODS_SEQ_CLAIM_LOSS_DETAIL to DMUSER;
grant SELECT on ODS_SEQ_CLAIM_MAIN to DMUSER;
grant SELECT on ODS_SEQ_CLAIM_OUTSTANDING to DMUSER;
grant SELECT on ODS_SEQ_FIN_ARTICLE_BALANCE to DMUSER;
grant SELECT on ODS_SEQ_FIN_LEDGER_BALANCE to DMUSER;
grant SELECT on ODS_SEQ_FIN_VOUCHER to DMUSER;
grant SELECT on ODS_SEQ_FIN_VOUCHER_DETAIL to DMUSER;
grant SELECT on ODS_SEQ_POLICY_MAIN to DMUSER;
grant SELECT on ODS_SEQ_POLICY_PAYMENT_PLAN to DMUSER;
grant SELECT on ODS_SEQ_POLICY_PREMIUM to DMUSER;
grant SELECT on ODS_SEQ_REINS_BASE_TREATY to DMUSER;
grant SELECT on ODS_SEQ_REINS_BILL to DMUSER;
grant SELECT on ODS_SEQ_REINS_BILL_DETAIL to DMUSER;
grant SELECT on ODS_SEQ_REINS_FLOAT_CHARGE to DMUSER;
grant SELECT on ODS_SEQ_REINS_OUTWARD to DMUSER;
grant SELECT on ODS_SEQ_REINS_OUTWARD_DETAIL to DMUSER;
grant SELECT on ODS_SEQ_REINS_REINSURER to DMUSER;
grant SELECT on ODS_SEQ_REINS_RISK to DMUSER;
grant SELECT on ODS_SEQ_REINS_TREATY to DMUSER;
grant SELECT on ODS_SEQ_REINS_TREATY_CLASS to DMUSER;
grant SELECT on ODS_SEQ_REINS_TREATY_PAYMENT_PLAN to DMUSER;
grant SELECT on ODS_SEQ_REINS_TREATY_SECTION to DMUSER;


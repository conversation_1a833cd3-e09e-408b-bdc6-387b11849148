--非配置数据序列回滚
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_ARTICLE_BALANCE',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_BUSS_ENTRY_DATA',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_BUSS_ENTRY_DATA_DTL',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_BUSS_MULTI_RECONRSTDTL',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_BUSS_MUL_RECRSTDTLHIS',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_DAP_ENTRY_DATA',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_EXT_ARTICLE_BALANCE',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_EXT_LEDGER_BALANCE',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_EXT_VOUCHER',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_EXT_VOUCHERDETAIL',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_ITEM_ARTICLE_BALANCE',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_ITEM_ATC_BALANCEHIS',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_ITEM_LEDGER_BALANCE',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_ITEM_LEDGER_BALANCEHIS',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_MULTIRECONCILRESULT',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_MULTIRECONCILRESULTHIS',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_POST',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_POSTDTL',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('ACC_SEQ_VOUCHERHIS',1,1);


call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_acc_conf_acc_prd_his',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_conf_annual_periodhis',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_conf_code_his',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_entryrulehis',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_entryrule_detail_his',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_confmodelcodehis',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_multicriteriareconhis',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_conf_multi_scenehis',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_conf_scenehis',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_confscenefactorrefhis',1,1);
call acc_pack_commonutils.CREATE_SEQUENCE('acc_seq_conf_scene_rulerefhis',1,1);


--配置表回滚
/
DECLARE v_count NUMBER(15);
BEGIN
  
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_acc_conf_accountperiod');
SELECT NVL(MAX(t.period_id),0)+1 INTO v_count FROM ACC_CONF_ACCOUNTPERIOD t;
EXECUTE IMMEDIATE 'create sequence acc_seq_acc_conf_accountperiod
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';
       
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_conf_acc_prd_dtl');
SELECT NVL(MAX(t.period_detail_id),0)+1 INTO v_count FROM ACC_CONF_ACCOUNTPERIOD_DETAIL t;
EXECUTE IMMEDIATE 'create sequence acc_seq_conf_acc_prd_dtl
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';
              
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_conf_annual_period');
SELECT NVL(MAX(t.ANNUAL_PERIOD_ID),0)+1 INTO v_count FROM acc_conf_annual_period t;
EXECUTE IMMEDIATE 'create sequence acc_seq_conf_annual_period
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';
              
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_conf_code');
SELECT NVL(MAX(t.code_id),0)+1 INTO v_count FROM ACC_CONF_CODE t;
EXECUTE IMMEDIATE 'create sequence acc_seq_conf_code
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
              
              
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_entryrule');
SELECT NVL(MAX(t.entry_rule_id),0)+1 INTO v_count FROM ACC_CONF_ENTRYRULE t;
EXECUTE IMMEDIATE 'create sequence acc_seq_entryrule
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';     
              
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_entryrule_detail');
SELECT NVL(MAX(t.entry_rule_dtl_id),0)+1 INTO v_count FROM ACC_CONF_ENTRYRULE_DETAIL t;
EXECUTE IMMEDIATE 'create sequence acc_seq_entryrule_detail
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';   
              
              
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_conf_model_code');
SELECT NVL(MAX(t.Model_Code_Id),0)+1 INTO v_count FROM ACC_CONF_MODEL_CODE t;
EXECUTE IMMEDIATE 'create sequence acc_seq_conf_model_code
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';   
              
              
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_multicriteriarecon');
SELECT NVL(MAX(t.RECON_ID),0)+1 INTO v_count FROM ACC_CONF_MULTICRITERIARECON t;
EXECUTE IMMEDIATE 'create sequence acc_seq_multicriteriarecon
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';   
              
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_conf_multi_scene');
SELECT NVL(MAX(t.multi_scenario_id),0)+1 INTO v_count FROM ACC_CONF_MULTI_SCENE t;
EXECUTE IMMEDIATE 'create sequence acc_seq_conf_multi_scene
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';   
              
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_conf_scene_ruleref');
SELECT NVL(MAX(t.scene_ruleref_id),0)+1 INTO v_count FROM ACC_CONF_MULTI_SCENE_RULEREF t;
EXECUTE IMMEDIATE 'create sequence acc_seq_conf_scene_ruleref
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';   
     
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_conf_scene_ruleref');
SELECT NVL(MAX(t.scene_ruleref_id),0)+1 INTO v_count FROM ACC_CONF_MULTI_SCENE_RULEREF t;
EXECUTE IMMEDIATE 'create sequence acc_seq_conf_scene_ruleref
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';   
              
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_configscene');
SELECT NVL(MAX(t.scenario_id),0)+1 INTO v_count FROM ACC_CONF_SCENARIO t;
EXECUTE IMMEDIATE 'create sequence acc_seq_configscene
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';   
              
accuser.acc_pack_commonutils.DROP_SEQUENCE('acc_seq_confscenefactorref');
SELECT NVL(MAX(t.scenario_modelref_id),0)+1 INTO v_count FROM ACC_CONF_SCENARIO_MODELREF t;
EXECUTE IMMEDIATE 'create sequence acc_seq_confscenefactorref
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';  
                
END ;
/

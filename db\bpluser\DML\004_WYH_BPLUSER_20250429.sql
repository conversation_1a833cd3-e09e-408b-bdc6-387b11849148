
delete
from bpl_saa_menu_task
where menu_id = (select menu_id from bpl_saa_menu where menu_e_name = 'EPI/Earned Import');

delete
from bpl_saa_task
where task_id in (select task_id
                  from bpl_saa_menu_task
                  where menu_id = (select menu_id from bpl_saa_menu where menu_e_name = 'EPI/Earned Import'));

delete
from bpluser.bpl_saa_menu
where menu_e_name = 'EPI/Earned Import';
INSERT INTO bpluser.bpl_saa_menu (menu_id, upper_id, menu_level, system_code, menu_c_name, menu_l_name, menu_e_name,
                                  target_router, action_url, task_code, type, code, icon, display_no, valid_ind, remark,
                                  creator_id, create_time, updator_id, update_time, mapping_url)
VALUES (nextval('bpl_seq_saa_menu'), (select menu_id
                                      from bpl_saa_menu
                                      where menu_c_name = 'BECF-计算'), 4, 'QTC', 'EPI/已赚导入', 'EPI/已赚导入',
        'EPI/Earned Import',
        '/atr/expectedCashFlow/data_import',
        '/atr/expectedCashFlow', null, 'M', null, '', 9, '1', null, 1, '2025-04-28 17:31:37.000000', 1,
        '2025-04-29 15:02:58.089000', null);

INSERT INTO bpluser.bpl_saa_task (task_id, task_code, group_name, task_c_name, task_l_name, task_e_name, task_url,
                                  valid_is, creator_id, create_time, updator_id, update_time, task_type)
VALUES (nextval('bpl_seq_saa_task'), 'delete_import_data', '', '刪除EPI或已赚数据', '刪除EPI或已賺數據',
        'Delete Import Data',
        '/get_import_detail', '1', 1, '2025-04-29 15:26:16.000000', 1, '2025-04-29 15:30:02.366000', '1');
INSERT INTO bpluser.bpl_saa_task (task_id, task_code, group_name, task_c_name, task_l_name, task_e_name, task_url,
                                  valid_is, creator_id, create_time, updator_id, update_time, task_type)
VALUES (nextval('bpl_seq_saa_task'), 'earnedImport', '', '已赚导入', '已賺導入', 'Earned Import', '/earned/import', '1',
        1,
        '2025-04-29 15:26:16.000000', 1, '2025-04-29 15:30:02.359000', '1');
INSERT INTO bpluser.bpl_saa_task (task_id, task_code, group_name, task_c_name, task_l_name, task_e_name, task_url,
                                  valid_is, creator_id, create_time, updator_id, update_time, task_type)
VALUES (nextval('bpl_seq_saa_task'), 'get_import_data_list', '', '获取导入的EPI或已赚数据', '獲取導入的EPI或已賺數據',
        'Getting Imported EPI or Earned Data', '/get_import_data_list', '1', 1, '2025-04-29 15:12:48.000000', 1,
        '2025-04-29 15:30:02.333000', '1');
INSERT INTO bpluser.bpl_saa_task (task_id, task_code, group_name, task_c_name, task_l_name, task_e_name, task_url,
                                  valid_is, creator_id, create_time, updator_id, update_time, task_type)
VALUES (nextval('bpl_seq_saa_task'), 'get_import_detail', '', '获取导入的EPI或已赚明细', '获取导入的EPI或已赚明細',
        'Getting Imported EPI or Earned Detail Data', '/get_import_detail', '1', 1, '2025-04-29 15:26:16.000000', 1,
        '2025-04-29 15:30:02.377000', '1');
INSERT INTO bpluser.bpl_saa_task (task_id, task_code, group_name, task_c_name, task_l_name, task_e_name, task_url,
                                  valid_is, creator_id, create_time, updator_id, update_time, task_type)
VALUES (nextval('bpl_seq_saa_task'), 'epiImport', '', 'EPI导入', 'EPI導入', 'EPI Import', '/epi/import', '1', 1,
        '2025-04-29 15:26:16.000000', 1, '2025-04-29 15:30:02.349000', '1');


insert into bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time,
                                      task_type)
values (nextval('bpl_seq_saa_menu_task')
           , (select bpl_saa_menu.menu_id from bpluser.bpl_saa_menu where menu_e_name = 'EPI/Earned Import'),
        (select bpl_saa_task.task_id from bpluser.bpl_saa_task where task_code = 'delete_import_data'), 1,
        localtimestamp, null, null);

insert into bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time,
                                      task_type)
values (nextval('bpl_seq_saa_menu_task')
           , (select bpl_saa_menu.menu_id from bpluser.bpl_saa_menu where menu_e_name = 'EPI/Earned Import'),
        (select bpl_saa_task.task_id from bpluser.bpl_saa_task where task_code = 'earnedImport'), 1,
        localtimestamp, null, null);


insert into bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time,
                                      task_type)
values (nextval('bpl_seq_saa_menu_task')
           , (select bpl_saa_menu.menu_id from bpluser.bpl_saa_menu where menu_e_name = 'EPI/Earned Import'),
        (select bpl_saa_task.task_id from bpluser.bpl_saa_task where task_code = 'get_import_data_list'), 1,
        localtimestamp, null, null);


insert into bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time,
                                      task_type)
values (nextval('bpl_seq_saa_menu_task')
           , (select bpl_saa_menu.menu_id from bpluser.bpl_saa_menu where menu_e_name = 'EPI/Earned Import'),
        (select bpl_saa_task.task_id from bpluser.bpl_saa_task where task_code = 'get_import_detail'), 1,
        localtimestamp, null, null);


insert into bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time,
                                      task_type)
values (nextval('bpl_seq_saa_menu_task')
           , (select bpl_saa_menu.menu_id from bpluser.bpl_saa_menu where menu_e_name = 'EPI/Earned Import'),
        (select bpl_saa_task.task_id from bpluser.bpl_saa_task where task_code = 'epiImport'), 1,
        localtimestamp, null, null);
# TOT业务endorse_type_code特殊处理测试场景

## 测试场景1：specialProcessType=1（当月有15/16批单）

### 测试数据
- 保单号：TOT_TEST_POLICY_001
- endorse_type_code：01,15,16
- year_month：202501（当前评估期）
- specialProcessType：1

### 金额数据
- 签单保费(premium)：10000
- 净额结算手续费(net_fee)：1000
- 历史累计已赚保费(pre_cuml_ed_premium)：6000
- 历史累计已赚手续费(pre_cuml_ed_net_fee)：600
- 当期实收保费(cur_paid_premium)：2000
- 当期实收手续费(cur_net_fee)：200

### 预期结果
**maxDev = 1**（TOT业务特殊处理直接设置为1）
**treatyEffectiveMonths = 0**（不存在EPI）

**第0期(dev_no=0)**：
- edPremium：4000（10000-6000）
- edRate：0.4（4000/10000）
- edNetFee：400（1000-600）
- recvPremium：2000（当期实收保费）
- netFee：200（当期实收手续费）

**无第1期**：因为maxDev=1，循环只执行一次

## 测试场景2：specialProcessType=2（历史有15/16批单但当月没有）

### 测试数据
- 保单号：TOT_TEST_POLICY_002
- endorse_type_code：01,15,16
- year_month：202412（历史评估期）
- specialProcessType：2

### 金额数据
- 签单保费(premium)：10000
- 净额结算手续费(net_fee)：1000
- 当期实收保费(cur_paid_premium)：2000
- 当期实收手续费(cur_net_fee)：200

### 预期结果
**maxDev = 1**（TOT业务特殊处理直接设置为1）
**treatyEffectiveMonths = 0**（不存在EPI）

**第0期(dev_no=0)**：
- edPremium：0（specialProcessType=2时已赚为0）
- edRate：0
- edNetFee：0
- recvPremium：2000（当期实收保费）
- netFee：200（当期实收手续费）

**无第1期**：因为maxDev=1，循环只执行一次

## 测试场景3：正常业务记录（不受影响）

### 测试数据
- 保单号：TOT_NORMAL_POLICY_001
- endorse_type_code：01,02,03（不包含15和16）
- specialProcessType：0

### 预期结果
- 按照原有逻辑正常处理
- maxDev根据正常业务逻辑计算
- treatyEffectiveMonths正常计算
- EPI正常处理
- 不受特殊处理逻辑影响

## SQL逻辑测试场景

### SQL场景1：当月有15/16批单
**数据**：
- 保单号：TOT_SQL_TEST_001
- 原单：year_month=202412, endorse_type_code=01
- 批单：year_month=202501, endorse_type_code=15
- 评估期：202501

**预期SQL结果**：
- 原单记录：special_process_type=1
- 批单记录：special_process_type=1

### SQL场景2：历史有15/16批单，当月没有
**数据**：
- 保单号：TOT_SQL_TEST_002
- 原单：year_month=202412, endorse_type_code=01
- 批单：year_month=202412, endorse_type_code=16
- 评估期：202501

**预期SQL结果**：
- 原单记录：special_process_type=2
- 批单记录：special_process_type=2

### SQL场景3：无15/16批单
**数据**：
- 保单号：TOT_SQL_TEST_003
- 原单：year_month=202412, endorse_type_code=01
- 批单：year_month=202501, endorse_type_code=02
- 评估期：202501

**预期SQL结果**：
- 原单记录：special_process_type=0
- 批单记录：special_process_type=0

## TOT业务特殊性验证

### 验证场景1：实收即应收特性
**验证内容**：
- 确认TOT业务不存在应收与实收的差异
- 验证所有现金流都在当期确认
- 确保不存在未来期间的应收款项

### 验证场景2：maxDev简化计算
**验证内容**：
- 特殊处理时maxDev直接设置为1
- 不需要进行累计应收与累计实收的比较
- 循环只执行一次（i=0）

### 验证场景3：EPI处理
**验证内容**：
- 特殊处理时treatyEffectiveMonths=0
- 不存在EPI（未来保费现金流）
- 验证EPI相关计算被正确跳过

### 验证场景4：发展期数据限制
**验证内容**：
- 特殊处理的记录只生成devNo=0的发展期数据
- 确保没有生成第1期及后续期间的数据
- 验证数据结构的正确性

## 关键验证点

### 1. TOT业务特殊性验证
- **实收即应收**：确认不存在应收与实收差异
- **maxDev简化**：验证直接设置为1的逻辑
- **EPI取消**：确认treatyEffectiveMonths=0的效果
- **单期数据**：验证只生成第0期数据

### 2. SQL逻辑验证
- **special_process_type一致性**：同一policy_no下所有记录的special_process_type一致
- **CTE逻辑正确性**：special_process_check CTE的计算逻辑
- **数据源正确性**：从atr_dap_to_paid_t表获取数据

### 3. Java逻辑验证
- **specialProcessType判断**：正确识别特殊处理类型
- **已赚计算**：specialProcessType=1和2的不同处理
- **现金流分配**：第0期数据的正确设置
- **循环控制**：确保循环只执行一次

### 4. 业务一致性验证
- **与DD/FO对比**：处理原则的一致性
- **特殊性体现**：TOT业务特点的正确体现
- **向后兼容**：正常业务记录不受影响

## 与DD/FO业务对比验证

### 相同的验证点
1. SQL层面的special_process_check CTE逻辑
2. specialProcessType的判断逻辑
3. 已赚保费计算原则
4. 特殊处理触发条件

### TOT业务特有的验证点
1. **maxDev计算**：直接设置为1，不需要比较累计应收与累计实收
2. **现金流简化**：只有第0期，无第1期
3. **EPI处理**：特殊处理时完全取消
4. **数据源**：确保从atr_dap_to_paid_t表获取数据

## 测试执行建议

### 1. 单元测试
- 针对每个测试场景编写单元测试
- 重点测试TOT业务的特殊性处理
- 验证maxDev=1的逻辑正确性

### 2. 集成测试
- 在测试环境验证完整的业务流程
- 测试SQL和Java层面的协同工作
- 验证数据的端到端流转

### 3. 数据对比
- 对比实现前后的计算结果
- 重点关注特殊处理场景的数据变化
- 确保正常业务记录不受影响

### 4. 边界测试
- 测试各种边界条件和异常情况
- 验证endorse_type_code的各种组合
- 测试不同year_month的场景

### 5. 性能测试
- 验证大数据量情况下的性能表现
- 关注SQL查询的执行效率
- 监控内存和CPU使用情况

## 预期测试结果

所有测试场景应该体现TOT业务的特殊性，同时与DD和FO业务保持处理原则的一致性。特别是要确保：

1. **特殊处理逻辑**：只对包含15/16批改类型的保单生效
2. **TOT特殊性**：正确体现实收即应收、maxDev=1、EPI取消等特点
3. **正常业务**：不受任何影响，保持原有处理逻辑
4. **数据一致性**：SQL层面的特殊处理类型判断准确无误
5. **业务逻辑**：已赚保费和现金流分配逻辑正确

## 风险控制

### 1. 回归测试
- 重点测试正常业务记录不受影响
- 验证现有功能的完整性
- 确保没有引入新的问题

### 2. 数据校验
- 验证特殊处理数据的准确性
- 检查数据结构的完整性
- 确认计算结果的合理性

### 3. 监控观察
- 部署后密切关注业务指标
- 监控系统性能和稳定性
- 及时发现和处理异常情况

### 4. 应急预案
- 准备快速回滚方案
- 建立问题处理流程
- 确保业务连续性

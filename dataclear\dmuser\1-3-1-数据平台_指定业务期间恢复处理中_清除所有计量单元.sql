--适用于计量单元数据清除

--指定月份恢复处理中
update dm_conf_bussperiod
set execution_state = '1'
where execution_state >= '2' and execution_state <> '0'
and year_month > '202101';

update dm_conf_bussperiod
set execution_state = '3'
where year_month < '202101';

UPDATE dm_conf_bussperiod
set execution_state = '2'
where year_month = '202101';
update dm_conf_bussperiod_detail
set ready_state = '0'
where direction = '0'
and buss_period_id  in (select BUSS_PERIOD_ID from dm_conf_bussperiod where year_month >= '202101');


--3、清除计量单元表数据
TRUNCATE TABLE dmuser.dm_buss_cmunit_direct;
TRUNCATE TABLE dmuser.dm_buss_cmunit_fac_outwards;
TRUNCATE TABLE dmuser.dm_buss_cmunit_treaty;

--恢复未生成状态
update dm_policy_main
set task_status = '4';

update dm_policy_premium
set task_status = '4';

update dm_reins_outward
set task_status = '4';

update dm_reins_treaty
set task_status = '4';
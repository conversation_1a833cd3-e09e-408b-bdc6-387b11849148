--恢复不确认的状态
update dmuser.dm_buss_cmunit_direct
set year_month = null 
where  year_month = '202201';

update dmuser.dm_buss_cmunit_fac_outwards
set year_month = null 
where  year_month = '202201';

update dmuser.dm_buss_cmunit_treaty
set year_month = null 
where  year_month = '202201';

--恢复业务期间不确认的状态
update dmuser.dm_conf_bussperiod_detail 
set ready_state = '0'
where direction = '0' 
 and buss_period_id = (SELECT buss_period_id from dmuser.dm_conf_bussperiod where year_month = '202201')
 and BIZ_TYPE_ID in (select BIZ_TYPE_ID from dm_conf_table_output 
					where biz_code in (
					'BUSS_CMUNIT_DIRECT',
					'BUSS_CMUNIT_FAC_OUT',
					'BUSS_CMUNIT_TREATY_IN',
					'BUSS_CMUNIT_TREATY_OUT'));
 
--业务期间需要根据实际情况更改dmuser.dm_conf_bussperiod
-- AtrBussLrcTotService 优化前后数据比较SQL
-- 新版本 action_no: 20250729_124218_DC1XT (优化后)
-- 旧版本 action_no: 20250729_095134_LS6VB (优化前)

-- =====================================================
-- 1. 主表数据比较 (atr_buss_to_lrc_t_ul_r)
-- =====================================================

-- 1.1 统计两个版本的记录数量
SELECT 
    '记录数量比较' as comparison_type,
    old_count,
    new_count,
    (new_count - old_count) as diff_count
FROM (
    SELECT 
        (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul_r WHERE action_no = '20250729_095134_LS6VB') as old_count,
        (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul_r WHERE action_no = '20250729_124218_DC1XT') as new_count
) t;

-- 1.2 比较主表关键字段的差异
WITH old_data AS (
    SELECT 
        treaty_no, policy_no, endorse_seq_no, kind_code, sectiono_code, reinsurer_code,
        premium, net_fee, fee_rate, ri_ceding_rate,
        cur_ed_premium, cur_ed_net_fee, pre_cuml_ed_premium, pre_cuml_ed_net_fee
    FROM atr_buss_to_lrc_t_ul_r 
    WHERE action_no = '20250729_095134_LS6VB'
),
new_data AS (
    SELECT 
        treaty_no, policy_no, endorse_seq_no, kind_code, sectiono_code, reinsurer_code,
        premium, net_fee, fee_rate, ri_ceding_rate,
        cur_ed_premium, cur_ed_net_fee, pre_cuml_ed_premium, pre_cuml_ed_net_fee
    FROM atr_buss_to_lrc_t_ul_r 
    WHERE action_no = '20250729_124218_DC1XT'
)
SELECT 
    '主表字段差异' as comparison_type,
    COALESCE(o.treaty_no, n.treaty_no) as treaty_no,
    COALESCE(o.policy_no, n.policy_no) as policy_no,
    COALESCE(o.endorse_seq_no, n.endorse_seq_no) as endorse_seq_no,
    COALESCE(o.kind_code, n.kind_code) as kind_code,
    COALESCE(o.sectiono_code, n.sectiono_code) as sectiono_code,
    COALESCE(o.reinsurer_code, n.reinsurer_code) as reinsurer_code,
    -- 保费比较
    o.premium as old_premium,
    n.premium as new_premium,
    CASE WHEN ABS(COALESCE(o.premium,0) - COALESCE(n.premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as premium_status,
    -- 手续费比较
    o.net_fee as old_net_fee,
    n.net_fee as new_net_fee,
    CASE WHEN ABS(COALESCE(o.net_fee,0) - COALESCE(n.net_fee,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as net_fee_status,
    -- 分出比例比较
    o.ri_ceding_rate as old_ri_ceding_rate,
    n.ri_ceding_rate as new_ri_ceding_rate,
    CASE WHEN ABS(COALESCE(o.ri_ceding_rate,0) - COALESCE(n.ri_ceding_rate,0)) > 0.000001 THEN 'DIFF' ELSE 'SAME' END as ri_ceding_rate_status,
    -- 当期已赚保费比较
    o.cur_ed_premium as old_cur_ed_premium,
    n.cur_ed_premium as new_cur_ed_premium,
    CASE WHEN ABS(COALESCE(o.cur_ed_premium,0) - COALESCE(n.cur_ed_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as cur_ed_premium_status,
    -- 历史累计已赚保费比较
    o.pre_cuml_ed_premium as old_pre_cuml_ed_premium,
    n.pre_cuml_ed_premium as new_pre_cuml_ed_premium,
    CASE WHEN ABS(COALESCE(o.pre_cuml_ed_premium,0) - COALESCE(n.pre_cuml_ed_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as pre_cuml_ed_premium_status
FROM old_data o
FULL OUTER JOIN new_data n 
    ON o.treaty_no = n.treaty_no 
    AND o.policy_no = n.policy_no 
    AND o.endorse_seq_no = n.endorse_seq_no 
    AND o.kind_code = n.kind_code
    AND o.sectiono_code = n.sectiono_code
    AND o.reinsurer_code = n.reinsurer_code
WHERE 
    o.treaty_no IS NULL OR n.treaty_no IS NULL  -- 记录缺失
    OR ABS(COALESCE(o.premium,0) - COALESCE(n.premium,0)) > 0.0001  -- 保费差异
    OR ABS(COALESCE(o.net_fee,0) - COALESCE(n.net_fee,0)) > 0.0001  -- 手续费差异
    OR ABS(COALESCE(o.ri_ceding_rate,0) - COALESCE(n.ri_ceding_rate,0)) > 0.000001  -- 分出比例差异
    OR ABS(COALESCE(o.cur_ed_premium,0) - COALESCE(n.cur_ed_premium,0)) > 0.0001  -- 当期已赚保费差异
    OR ABS(COALESCE(o.pre_cuml_ed_premium,0) - COALESCE(n.pre_cuml_ed_premium,0)) > 0.0001  -- 历史累计已赚保费差异
ORDER BY treaty_no, policy_no, endorse_seq_no, kind_code, sectiono_code, reinsurer_code;

-- =====================================================
-- 2. 发展期表数据比较 (atr_buss_to_lrc_t_ul_r_dev)
-- =====================================================

-- 2.1 发展期表记录数量比较
SELECT 
    '发展期表记录数量比较' as comparison_type,
    old_count,
    new_count,
    (new_count - old_count) as diff_count
FROM (
    SELECT 
        (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul_r_dev d 
         JOIN atr_buss_to_lrc_t_ul_r m ON d.main_id = m.id 
         WHERE m.action_no = '20250729_095134_LS6VB') as old_count,
        (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul_r_dev d 
         JOIN atr_buss_to_lrc_t_ul_r m ON d.main_id = m.id 
         WHERE m.action_no = '20250729_124218_DC1XT') as new_count
) t;

-- 2.2 发展期表关键字段差异
WITH old_dev_data AS (
    SELECT 
        m.treaty_no, m.policy_no, m.endorse_seq_no, m.kind_code, m.sectiono_code, m.reinsurer_code,
        d.dev_no, d.ed_premium, d.recv_premium, d.ed_net_fee, d.net_fee
    FROM atr_buss_to_lrc_t_ul_r_dev d
    JOIN atr_buss_to_lrc_t_ul_r m ON d.main_id = m.id
    WHERE m.action_no = '20250729_095134_LS6VB'
),
new_dev_data AS (
    SELECT 
        m.treaty_no, m.policy_no, m.endorse_seq_no, m.kind_code, m.sectiono_code, m.reinsurer_code,
        d.dev_no, d.ed_premium, d.recv_premium, d.ed_net_fee, d.net_fee
    FROM atr_buss_to_lrc_t_ul_r_dev d
    JOIN atr_buss_to_lrc_t_ul_r m ON d.main_id = m.id
    WHERE m.action_no = '20250729_124218_DC1XT'
)
SELECT 
    '发展期表字段差异' as comparison_type,
    COALESCE(o.treaty_no, n.treaty_no) as treaty_no,
    COALESCE(o.policy_no, n.policy_no) as policy_no,
    COALESCE(o.endorse_seq_no, n.endorse_seq_no) as endorse_seq_no,
    COALESCE(o.kind_code, n.kind_code) as kind_code,
    COALESCE(o.sectiono_code, n.sectiono_code) as sectiono_code,
    COALESCE(o.reinsurer_code, n.reinsurer_code) as reinsurer_code,
    COALESCE(o.dev_no, n.dev_no) as dev_no,
    -- 已赚保费比较
    o.ed_premium as old_ed_premium,
    n.ed_premium as new_ed_premium,
    CASE WHEN ABS(COALESCE(o.ed_premium,0) - COALESCE(n.ed_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as ed_premium_status,
    -- 应收保费比较
    o.recv_premium as old_recv_premium,
    n.recv_premium as new_recv_premium,
    CASE WHEN ABS(COALESCE(o.recv_premium,0) - COALESCE(n.recv_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as recv_premium_status
FROM old_dev_data o
FULL OUTER JOIN new_dev_data n 
    ON o.treaty_no = n.treaty_no 
    AND o.policy_no = n.policy_no 
    AND o.endorse_seq_no = n.endorse_seq_no 
    AND o.kind_code = n.kind_code
    AND o.sectiono_code = n.sectiono_code
    AND o.reinsurer_code = n.reinsurer_code
    AND o.dev_no = n.dev_no
WHERE 
    o.treaty_no IS NULL OR n.treaty_no IS NULL  -- 记录缺失
    OR ABS(COALESCE(o.ed_premium,0) - COALESCE(n.ed_premium,0)) > 0.0001  -- 已赚保费差异
    OR ABS(COALESCE(o.recv_premium,0) - COALESCE(n.recv_premium,0)) > 0.0001  -- 应收保费差异
ORDER BY treaty_no, policy_no, endorse_seq_no, kind_code, sectiono_code, reinsurer_code, dev_no;

-- =====================================================
-- 3. 4维度主表数据比较 (atr_buss_to_lrc_t_ul)
-- =====================================================

-- 3.1 4维度主表记录数量比较
SELECT
    '4维度主表记录数量比较' as comparison_type,
    old_count,
    new_count,
    (new_count - old_count) as diff_count
FROM (
    SELECT
        (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul WHERE action_no = '20250729_095134_LS6VB') as old_count,
        (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul WHERE action_no = '20250729_124218_DC1XT') as new_count
) t;

-- 3.2 4维度主表关键字段差异
WITH old_ul_data AS (
    SELECT
        treaty_no, policy_no, endorse_seq_no, kind_code,
        cur_premium, cur_net_fee, pre_cuml_paid_premium, pre_cuml_paid_net_fee,
        cur_ed_premium, cur_ed_net_fee, pre_cuml_ed_premium, pre_cuml_ed_net_fee,
        premium, inv_amount
    FROM atr_buss_to_lrc_t_ul
    WHERE action_no = '20250729_095134_LS6VB'
),
new_ul_data AS (
    SELECT
        treaty_no, policy_no, endorse_seq_no, kind_code,
        cur_premium, cur_net_fee, pre_cuml_paid_premium, pre_cuml_paid_net_fee,
        cur_ed_premium, cur_ed_net_fee, pre_cuml_ed_premium, pre_cuml_ed_net_fee,
        premium, inv_amount
    FROM atr_buss_to_lrc_t_ul
    WHERE action_no = '20250729_124218_DC1XT'
)
SELECT
    '4维度主表字段差异' as comparison_type,
    COALESCE(o.treaty_no, n.treaty_no) as treaty_no,
    COALESCE(o.policy_no, n.policy_no) as policy_no,
    COALESCE(o.endorse_seq_no, n.endorse_seq_no) as endorse_seq_no,
    COALESCE(o.kind_code, n.kind_code) as kind_code,
    -- 当期保费比较
    o.cur_premium as old_cur_premium,
    n.cur_premium as new_cur_premium,
    CASE WHEN ABS(COALESCE(o.cur_premium,0) - COALESCE(n.cur_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as cur_premium_status,
    -- 当期手续费比较
    o.cur_net_fee as old_cur_net_fee,
    n.cur_net_fee as new_cur_net_fee,
    CASE WHEN ABS(COALESCE(o.cur_net_fee,0) - COALESCE(n.cur_net_fee,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as cur_net_fee_status,
    -- 上期累计实收保费比较
    o.pre_cuml_paid_premium as old_pre_cuml_paid_premium,
    n.pre_cuml_paid_premium as new_pre_cuml_paid_premium,
    CASE WHEN ABS(COALESCE(o.pre_cuml_paid_premium,0) - COALESCE(n.pre_cuml_paid_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as pre_cuml_paid_premium_status,
    -- 当期已赚保费比较
    o.cur_ed_premium as old_cur_ed_premium,
    n.cur_ed_premium as new_cur_ed_premium,
    CASE WHEN ABS(COALESCE(o.cur_ed_premium,0) - COALESCE(n.cur_ed_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as cur_ed_premium_status,
    -- 上期累计已赚保费比较
    o.pre_cuml_ed_premium as old_pre_cuml_ed_premium,
    n.pre_cuml_ed_premium as new_pre_cuml_ed_premium,
    CASE WHEN ABS(COALESCE(o.pre_cuml_ed_premium,0) - COALESCE(n.pre_cuml_ed_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as pre_cuml_ed_premium_status,
    -- 总保费比较
    o.premium as old_total_premium,
    n.premium as new_total_premium,
    CASE WHEN ABS(COALESCE(o.premium,0) - COALESCE(n.premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as total_premium_status,
    -- 投资成分比较
    o.inv_amount as old_inv_amount,
    n.inv_amount as new_inv_amount,
    CASE WHEN ABS(COALESCE(o.inv_amount,0) - COALESCE(n.inv_amount,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as inv_amount_status
FROM old_ul_data o
FULL OUTER JOIN new_ul_data n
    ON o.treaty_no = n.treaty_no
    AND o.policy_no = n.policy_no
    AND o.endorse_seq_no = n.endorse_seq_no
    AND o.kind_code = n.kind_code
WHERE
    o.treaty_no IS NULL OR n.treaty_no IS NULL  -- 记录缺失
    OR ABS(COALESCE(o.cur_premium,0) - COALESCE(n.cur_premium,0)) > 0.0001  -- 当期保费差异
    OR ABS(COALESCE(o.cur_net_fee,0) - COALESCE(n.cur_net_fee,0)) > 0.0001  -- 当期手续费差异
    OR ABS(COALESCE(o.pre_cuml_paid_premium,0) - COALESCE(n.pre_cuml_paid_premium,0)) > 0.0001  -- 上期累计实收保费差异
    OR ABS(COALESCE(o.cur_ed_premium,0) - COALESCE(n.cur_ed_premium,0)) > 0.0001  -- 当期已赚保费差异
    OR ABS(COALESCE(o.pre_cuml_ed_premium,0) - COALESCE(n.pre_cuml_ed_premium,0)) > 0.0001  -- 上期累计已赚保费差异
    OR ABS(COALESCE(o.premium,0) - COALESCE(n.premium,0)) > 0.0001  -- 总保费差异
    OR ABS(COALESCE(o.inv_amount,0) - COALESCE(n.inv_amount,0)) > 0.0001  -- 投资成分差异
ORDER BY treaty_no, policy_no, endorse_seq_no, kind_code;

-- =====================================================
-- 4. 4维度发展期表数据比较 (atr_buss_to_lrc_t_ul_dev)
-- =====================================================

-- 4.1 4维度发展期表记录数量比较
SELECT
    '4维度发展期表记录数量比较' as comparison_type,
    old_count,
    new_count,
    (new_count - old_count) as diff_count
FROM (
    SELECT
        (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul_dev d
         JOIN atr_buss_to_lrc_t_ul m ON d.main_id = m.id
         WHERE m.action_no = '20250729_095134_LS6VB') as old_count,
        (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul_dev d
         JOIN atr_buss_to_lrc_t_ul m ON d.main_id = m.id
         WHERE m.action_no = '20250729_124218_DC1XT') as new_count
) t;

-- 4.2 4维度发展期表关键字段差异
WITH old_ul_dev_data AS (
    SELECT
        m.treaty_no, m.policy_no, m.endorse_seq_no, m.kind_code,
        d.dev_no, d.paid_ed_premium, d.ed_premium, d.recv_premium,
        d.paid_net_fee, d.ed_net_fee, d.net_fee
    FROM atr_buss_to_lrc_t_ul_dev d
    JOIN atr_buss_to_lrc_t_ul m ON d.main_id = m.id
    WHERE m.action_no = '20250729_095134_LS6VB'
),
new_ul_dev_data AS (
    SELECT
        m.treaty_no, m.policy_no, m.endorse_seq_no, m.kind_code,
        d.dev_no, d.paid_ed_premium, d.ed_premium, d.recv_premium,
        d.paid_net_fee, d.ed_net_fee, d.net_fee
    FROM atr_buss_to_lrc_t_ul_dev d
    JOIN atr_buss_to_lrc_t_ul m ON d.main_id = m.id
    WHERE m.action_no = '20250729_124218_DC1XT'
)
SELECT
    '4维度发展期表字段差异' as comparison_type,
    COALESCE(o.treaty_no, n.treaty_no) as treaty_no,
    COALESCE(o.policy_no, n.policy_no) as policy_no,
    COALESCE(o.endorse_seq_no, n.endorse_seq_no) as endorse_seq_no,
    COALESCE(o.kind_code, n.kind_code) as kind_code,
    COALESCE(o.dev_no, n.dev_no) as dev_no,
    -- 已写入已赚保费比较
    o.paid_ed_premium as old_paid_ed_premium,
    n.paid_ed_premium as new_paid_ed_premium,
    CASE WHEN ABS(COALESCE(o.paid_ed_premium,0) - COALESCE(n.paid_ed_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as paid_ed_premium_status,
    -- 已赚保费比较
    o.ed_premium as old_ed_premium,
    n.ed_premium as new_ed_premium,
    CASE WHEN ABS(COALESCE(o.ed_premium,0) - COALESCE(n.ed_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as ed_premium_status,
    -- 应收保费比较
    o.recv_premium as old_recv_premium,
    n.recv_premium as new_recv_premium,
    CASE WHEN ABS(COALESCE(o.recv_premium,0) - COALESCE(n.recv_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as recv_premium_status,
    -- 已赚手续费比较
    o.ed_net_fee as old_ed_net_fee,
    n.ed_net_fee as new_ed_net_fee,
    CASE WHEN ABS(COALESCE(o.ed_net_fee,0) - COALESCE(n.ed_net_fee,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as ed_net_fee_status,
    -- 手续费现金流比较
    o.net_fee as old_net_fee,
    n.net_fee as new_net_fee,
    CASE WHEN ABS(COALESCE(o.net_fee,0) - COALESCE(n.net_fee,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as net_fee_status
FROM old_ul_dev_data o
FULL OUTER JOIN new_ul_dev_data n
    ON o.treaty_no = n.treaty_no
    AND o.policy_no = n.policy_no
    AND o.endorse_seq_no = n.endorse_seq_no
    AND o.kind_code = n.kind_code
    AND o.dev_no = n.dev_no
WHERE
    o.treaty_no IS NULL OR n.treaty_no IS NULL  -- 记录缺失
    OR ABS(COALESCE(o.paid_ed_premium,0) - COALESCE(n.paid_ed_premium,0)) > 0.0001  -- 已写入已赚保费差异
    OR ABS(COALESCE(o.ed_premium,0) - COALESCE(n.ed_premium,0)) > 0.0001  -- 已赚保费差异
    OR ABS(COALESCE(o.recv_premium,0) - COALESCE(n.recv_premium,0)) > 0.0001  -- 应收保费差异
    OR ABS(COALESCE(o.ed_net_fee,0) - COALESCE(n.ed_net_fee,0)) > 0.0001  -- 已赚手续费差异
    OR ABS(COALESCE(o.net_fee,0) - COALESCE(n.net_fee,0)) > 0.0001  -- 手续费现金流差异
ORDER BY treaty_no, policy_no, endorse_seq_no, kind_code, dev_no;

-- =====================================================
-- 5. ICG表数据比较 (atr_buss_to_lrc_g)
-- =====================================================

-- 5.1 ICG表记录数量比较
SELECT 
    'ICG表记录数量比较' as comparison_type,
    old_count,
    new_count,
    (new_count - old_count) as diff_count
FROM (
    SELECT 
        (SELECT COUNT(*) FROM atr_buss_to_lrc_g WHERE action_no = '20250729_095134_LS6VB') as old_count,
        (SELECT COUNT(*) FROM atr_buss_to_lrc_g WHERE action_no = '20250729_124218_DC1XT') as new_count
) t;

-- 5.2 ICG表关键字段差异
WITH old_icg_data AS (
    SELECT 
        portfolio_no, icg_no, risk_class_code,
        total_premium, total_net_fee, remaining_months
    FROM atr_buss_to_lrc_g 
    WHERE action_no = '20250729_095134_LS6VB'
),
new_icg_data AS (
    SELECT 
        portfolio_no, icg_no, risk_class_code,
        total_premium, total_net_fee, remaining_months
    FROM atr_buss_to_lrc_g 
    WHERE action_no = '20250729_124218_DC1XT'
)
SELECT 
    'ICG表字段差异' as comparison_type,
    COALESCE(o.portfolio_no, n.portfolio_no) as portfolio_no,
    COALESCE(o.icg_no, n.icg_no) as icg_no,
    COALESCE(o.risk_class_code, n.risk_class_code) as risk_class_code,
    -- 总保费比较
    o.total_premium as old_total_premium,
    n.total_premium as new_total_premium,
    CASE WHEN ABS(COALESCE(o.total_premium,0) - COALESCE(n.total_premium,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as total_premium_status,
    -- 总手续费比较
    o.total_net_fee as old_total_net_fee,
    n.total_net_fee as new_total_net_fee,
    CASE WHEN ABS(COALESCE(o.total_net_fee,0) - COALESCE(n.total_net_fee,0)) > 0.0001 THEN 'DIFF' ELSE 'SAME' END as total_net_fee_status,
    -- 剩余月数比较
    o.remaining_months as old_remaining_months,
    n.remaining_months as new_remaining_months,
    CASE WHEN COALESCE(o.remaining_months,0) != COALESCE(n.remaining_months,0) THEN 'DIFF' ELSE 'SAME' END as remaining_months_status
FROM old_icg_data o
FULL OUTER JOIN new_icg_data n 
    ON o.portfolio_no = n.portfolio_no 
    AND o.icg_no = n.icg_no 
    AND o.risk_class_code = n.risk_class_code
WHERE 
    o.portfolio_no IS NULL OR n.portfolio_no IS NULL  -- 记录缺失
    OR ABS(COALESCE(o.total_premium,0) - COALESCE(n.total_premium,0)) > 0.0001  -- 总保费差异
    OR ABS(COALESCE(o.total_net_fee,0) - COALESCE(n.total_net_fee,0)) > 0.0001  -- 总手续费差异
    OR COALESCE(o.remaining_months,0) != COALESCE(n.remaining_months,0)  -- 剩余月数差异
ORDER BY portfolio_no, icg_no, risk_class_code;

-- =====================================================
-- 6. 汇总统计
-- =====================================================

-- 6.1 按合约汇总比较 - 6维度表
SELECT 
    '按合约汇总比较' as comparison_type,
    treaty_no,
    old_total_premium,
    new_total_premium,
    (new_total_premium - old_total_premium) as premium_diff,
    old_total_net_fee,
    new_total_net_fee,
    (new_total_net_fee - old_total_net_fee) as net_fee_diff,
    old_record_count,
    new_record_count,
    (new_record_count - old_record_count) as record_count_diff
FROM (
    SELECT 
        COALESCE(o.treaty_no, n.treaty_no) as treaty_no,
        COALESCE(o.total_premium, 0) as old_total_premium,
        COALESCE(n.total_premium, 0) as new_total_premium,
        COALESCE(o.total_net_fee, 0) as old_total_net_fee,
        COALESCE(n.total_net_fee, 0) as new_total_net_fee,
        COALESCE(o.record_count, 0) as old_record_count,
        COALESCE(n.record_count, 0) as new_record_count
    FROM (
        SELECT 
            treaty_no,
            SUM(premium) as total_premium,
            SUM(net_fee) as total_net_fee,
            COUNT(*) as record_count
        FROM atr_buss_to_lrc_t_ul_r 
        WHERE action_no = '20250729_095134_LS6VB'
        GROUP BY treaty_no
    ) o
    FULL OUTER JOIN (
        SELECT 
            treaty_no,
            SUM(premium) as total_premium,
            SUM(net_fee) as total_net_fee,
            COUNT(*) as record_count
        FROM atr_buss_to_lrc_t_ul_r 
        WHERE action_no = '20250729_124218_DC1XT'
        GROUP BY treaty_no
    ) n ON o.treaty_no = n.treaty_no
) t
WHERE 
    ABS(new_total_premium - old_total_premium) > 0.01
    OR ABS(new_total_net_fee - old_total_net_fee) > 0.01
    OR new_record_count != old_record_count
ORDER BY treaty_no;

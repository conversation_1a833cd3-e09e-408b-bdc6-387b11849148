CREATE OR REPLACE PACKAGE acc_pack_voucher IS

    FUNCTION func_generate_voucher_no(p_entity_id         IN NUMBER,
                                      p_book_code         IN VARCHAR2,
                                      p_year_month        IN VARCHAR2,
                                      p_posting_type_code IN VARCHAR2,
                                      p_key_type          IN VARCHAR2,
                                      p_increment         IN NUMBER)
        RETURN VARCHAR2;

    PROCEDURE proc_correct_voucher_key(p_entity_id         IN NUMBER,
                                       p_book_code         IN VARCHAR2,
                                       p_year_month        IN VARCHAR2,
                                       p_posting_type_code IN VARCHAR2,
                                       p_key_type          IN VARCHAR2);

    PROCEDURE proc_entry_entrance(p_entity_id  IN NUMBER,
                                  p_book_code  IN VARCHAR2,
                                  p_year_month IN VARCHAR2,
                                  p_user_id    IN NUMBER);

    PROCEDURE proc_entry_scenario_match(p_entity_id  IN NUMBER,
                                        p_book_code  IN VARCHAR2,
                                        p_year_month IN VARCHAR2,
                                        p_task_code  IN VARCHAR2,
        --p_proc_id NUMBER,
                                        p_user_id IN NUMBER);

    PROCEDURE proc_entry_data_check(p_entity_id  IN NUMBER,
                                    p_book_code  IN VARCHAR2,
                                    p_year_month IN VARCHAR2,
                                    p_proc_id    IN NUMBER,
                                    p_task_code  IN VARCHAR2,
                                    p_user_id    IN NUMBER);

    PROCEDURE proc_entry_voucher(p_entity_id  IN NUMBER,
                                 p_book_code  IN VARCHAR2,
                                 p_year_month IN VARCHAR2,
                                 p_proc_id    IN NUMBER,
                                 p_task_code  IN VARCHAR2,
                                 p_user_id    IN NUMBER);

    PROCEDURE proc_entry_voucher_profit_loss(p_entity_id  IN NUMBER,
                                             p_book_code  IN VARCHAR2,
                                             p_year_month IN VARCHAR2,
                                             p_user_id    IN NUMBER);

    PROCEDURE proc_voucher_revoke_voucher_no(p_entity_id      IN NUMBER,
                                             p_book_code      IN VARCHAR2,
                                             p_year_month     IN VARCHAR2,
                                             p_voucher_no     IN VARCHAR2,
                                             p_revoke_message IN VARCHAR2,
                                             p_task_code      IN VARCHAR2,
                                             p_user_id        IN NUMBER,
                                             p_new_voucher_no IN OUT VARCHAR2);

    PROCEDURE proc_voucher_revoke_post_type(p_entity_id         IN NUMBER,
                                            p_book_code         IN VARCHAR2,
                                            p_year_month        IN VARCHAR2,
                                            p_posting_type_code IN VARCHAR2,
                                            p_user_id           IN NUMBER,
                                            p_massage           IN OUT VARCHAR2);

    PROCEDURE proc_entry_deal_actionlog(p_entity_id  IN NUMBER,
                                        p_book_code  IN VARCHAR2,
                                        p_year_month IN VARCHAR2,
                                        p_proc_id    IN NUMBER,
                                        p_task_code  IN VARCHAR2,
                                        p_user_id    IN NUMBER);

    FUNCTION func_entry_reset_actionlog(p_entity_id  IN NUMBER,
                                        p_book_code  IN VARCHAR2,
                                        p_year_month IN VARCHAR2,
                                        p_proc_id    IN NUMBER,
                                        p_task_code  IN VARCHAR2,
                                        p_user_id    IN NUMBER) RETURN VARCHAR2;

    PROCEDURE proc_account_entry_log(p_entity_id    NUMBER,
                                     p_year_month   VARCHAR2,
                                     p_task_code    VARCHAR2,
                                     p_user_id      NUMBER,
                                     p_proc_id      NUMBER,
                                     p_trace_code   VARCHAR2,
                                     p_trace_status VARCHAR2,
                                     p_trace_msg    VARCHAR2);

    --凭证对冲 start
    --1 对冲功能入口
    PROCEDURE proc_voucher_revoke(p_entity_id      IN NUMBER,
                                  p_book_code      IN VARCHAR2,
                                  p_year_month     IN VARCHAR2,
                                  p_voucher_no     IN VARCHAR2,
                                  p_revoke_message IN VARCHAR2,
                                  p_user_id        IN NUMBER,
                                  p_out_message    OUT VARCHAR2,
                                  p_remark         OUT VARCHAR2);
    --2 试对冲功能-冲销凭证
    PROCEDURE proc_voucher_trial_revoke_voucher_no(p_entity_id      IN NUMBER,
                                                   p_book_code      IN VARCHAR2,
                                                   p_year_month     IN VARCHAR2,
                                                   p_voucher_no     IN VARCHAR2,
                                                   p_user_id        IN NUMBER,
                                                   p_task_code      IN VARCHAR2,
                                                   p_new_voucher_no IN OUT VARCHAR2);
    --3 试对冲功能-生成凭证
    PROCEDURE proc_voucher_trial_reentry_voucher_no(p_entity_id      IN NUMBER,
                                                    p_book_code      IN VARCHAR2,
                                                    p_year_month     IN VARCHAR2,
                                                    p_voucher_no     IN VARCHAR2,
                                                    p_user_id        IN NUMBER,
                                                    p_new_voucher_no IN OUT VARCHAR2,
                                                    p_task_code      IN VARCHAR2);
    --4 试对冲功能-重新匹配场景
    PROCEDURE proc_entry_temp_scenario_match(p_entity_id      IN NUMBER,
                                             p_book_code      IN VARCHAR2,
                                             p_year_month     IN VARCHAR2,
                                             p_task_code      IN VARCHAR2,
                                             p_old_voucher_id IN NUMBER,
                                             p_user_id        IN NUMBER);
    --5 对冲功能-重新匹配场景
    PROCEDURE proc_entry_re_scenario_match(p_entity_id      IN NUMBER,
                                           p_book_code      IN VARCHAR2,
                                           p_year_month     IN VARCHAR2,
                                           p_task_code      IN VARCHAR2,
                                           p_old_voucher_id IN NUMBER,
                                           p_user_id        IN NUMBER);
    --6 对冲功能-数据检查
    PROCEDURE proc_entry_re_data_check(p_entity_id  IN NUMBER,
                                       p_book_code  IN VARCHAR2,
                                       p_year_month IN VARCHAR2,
                                       p_proc_id    IN NUMBER,
                                       p_task_code  IN VARCHAR2,
                                       p_user_id    IN NUMBER);

    PROCEDURE proc_entry_re_voucher(p_entity_id  IN NUMBER,
                                    p_book_code  IN VARCHAR2,
                                    p_year_month IN VARCHAR2,
                                    p_proc_id    IN NUMBER,
                                    p_task_code  IN VARCHAR2,
                                    p_user_id    IN NUMBER);

    PROCEDURE proc_entry_deal_cmunit(p_entity_id  IN NUMBER,
                                     p_book_code  IN VARCHAR2,
                                     p_year_month IN VARCHAR2,
                                     p_user_id    IN NUMBER);
    --凭证对冲 end
END acc_pack_voucher;
/
CREATE OR REPLACE PACKAGE BODY acc_pack_voucher IS

    FUNCTION func_generate_voucher_no(p_entity_id         IN NUMBER,
                                      p_book_code         IN VARCHAR2,
                                      p_year_month        IN VARCHAR2,
                                      p_posting_type_code IN VARCHAR2,
                                      p_key_type          IN VARCHAR2,
                                      p_increment         IN NUMBER)
        RETURN VARCHAR2 IS
        /***********************************************************************
      NAME :ACC_ func_generate_voucher_no
      DESCRIPTION :获取凭证号码
      DATE :2023-03-01
      AUTHOR :LY
      BUSINESS RULE : 凭证号码按照下面方式进行生成
      2位凭证类型 || 4位核算单位编码 || 2位账套末位 || 6位会计期间 || 4位流水号码
    ***********************************************************************/
        v_keyno        NUMBER(11);
        v_voucher_no   VARCHAR2(32);
        v_digit        NUMBER(8) := 4; --流水号默认4位
        v_company_code VARCHAR2(32);
    BEGIN
        IF p_key_type IS NULL OR p_posting_type_code IS NULL THEN
            RETURN NULL;
        END IF;

        SELECT MAX(a.key_no), nvl(MAX(a.digit), 4)
        INTO v_keyno, v_digit
        FROM acc_conf_keyno a
        WHERE a.entity_id = p_entity_id
          AND a.year_month = p_year_month
          AND a.book_code = p_book_code
          AND a.posting_type_code = p_posting_type_code
          AND a.key_type = p_key_type;

        SELECT company_code
        INTO v_company_code
        FROM bpluser.bpl_company c
        WHERE c.company_id = p_entity_id
          AND c.valid_is = '1';

        --分段处理，方便问题跟踪
        v_voucher_no := p_posting_type_code || p_key_type;
        v_voucher_no := v_voucher_no ||
                        substr(lpad(v_company_code, 4, '0'), 1, 4);
        v_voucher_no := v_voucher_no || substr('00' || p_book_code, -2);
        v_voucher_no := v_voucher_no || p_year_month;
        v_voucher_no := v_voucher_no ||
                        lpad(CAST((nvl(v_keyno, 0) + nvl(p_increment, 1)) AS
                                 VARCHAR),
                             v_digit,
                             '0');
        --打印凭证号
        --raise notice 'v_voucher_no: %;',v_voucher_no;
        RETURN v_voucher_no;

    EXCEPTION
        WHEN OTHERS THEN
            --抛出异常提示信息
            --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
            dbms_output.put_line(to_char(SQLCODE) || '::' ||
                                 substr(SQLERRM, 1, 200));
            --RAISE NOTICE '[EXCEPTION]acc_pack_voucher_ func_generate_voucher_no：%; %',v_text1,v_text2;

            RETURN NULL;

    END func_generate_voucher_no;

    PROCEDURE proc_correct_voucher_key(p_entity_id         IN NUMBER,
                                       p_book_code         IN VARCHAR2,
                                       p_year_month        IN VARCHAR2,
                                       p_posting_type_code IN VARCHAR2,
                                       p_key_type          IN VARCHAR2) IS
        /***********************************************************************
      NAME :ACC_proc_correct_voucher_key
      DESCRIPTION :校正凭证号码
      DATE :2023-03-01
      AUTHOR :LY
      BUSINESS RULE : 凭证号码按照下面方式进行生成
      2位凭证类型 || 4位核算单位编码 || 2位账套末位 || 6位会计期间 || 4位流水号码
    ***********************************************************************/
        v_vou_max number(11);
        v_m_max   number(11);
        v_max     number(11);
    BEGIN
        SELECT coalesce(to_number(substr(max(v.voucher_no), -4)), 0)
        into v_vou_max
        FROM acc_buss_voucher v
        where v.entity_id = p_entity_id
          AND v.book_code = p_book_code
          AND v.year_month = p_year_month
          AND v.posting_type_code = p_posting_type_code
          AND v.proc_id = p_key_type;

        SELECT coalesce(to_number(substr(max(v.voucher_no), -4)), 0)
        into v_m_max
        FROM acc_buss_manual_voucher v
        where v.entity_id = p_entity_id
          AND v.book_code = p_book_code
          AND v.year_month = p_year_month
          AND v.posting_type_code = p_posting_type_code
          AND v.proc_id = p_key_type;

        select greatest(v_m_max, v_vou_max) into v_max from dual;

        MERGE INTO acc_conf_keyno t
        USING (SELECT t1.entity_id,
                      t1.book_code,
                      t1.year_month,
                      t1.posting_type_code,
                      t1.key_type,
                      nvl(sum(t2.key_no), 0) key_no
               FROM (SELECT p_entity_id         entity_id,
                            p_book_code         book_code,
                            p_year_month        year_month,
                            p_posting_type_code posting_type_code,
                            p_key_type          key_type
                     FROM dual) t1
                        LEFT JOIN (SELECT a.entity_id,
                                          a.book_code,
                                          a.year_month,
                                          a.posting_type_code,
                                          a.proc_id key_type,
                                          COUNT(a.voucher_id) key_no
                                   FROM acc_buss_voucher a
                                   WHERE a.entity_id = p_entity_id
                                     AND a.book_code = p_book_code
                                     AND a.year_month = p_year_month
                                     AND a.posting_type_code = p_posting_type_code
                                     AND a.proc_id = p_key_type
                                   GROUP BY a.entity_id,
                                            a.book_code,
                                            a.year_month,
                                            a.posting_type_code,
                                            a.proc_id) t2
                                  ON t2.entity_id = t1.entity_id
                                      AND t2.book_code = t1.book_code
                                      AND t2.year_month = t1.year_month
                                      AND t2.posting_type_code = t1.posting_type_code
                                      AND t2.key_type = t1.key_type
               group by t1.entity_id,
                        t1.book_code,
                        t1.year_month,
                        t1.posting_type_code,
                        t1.key_type) c
        ON (t.entity_id = c.entity_id AND t.book_code = c.book_code AND t.year_month = c.year_month AND t.posting_type_code = c.posting_type_code AND t.key_type = c.key_type)
        WHEN MATCHED THEN
            UPDATE
            SET t.key_no =
                    (case
                         when p_posting_type_code = '07' then
                             greatest(v_max, c.key_no)
                         else
                             c.key_no
                        end)
            WHERE t.entity_id = p_entity_id
              AND t.book_code = p_book_code
              AND t.year_month = p_year_month
              AND t.posting_type_code = p_posting_type_code
              AND t.key_type = p_key_type

        WHEN NOT MATCHED THEN INSERT(entity_id, book_code, year_month, posting_type_code, key_type, digit, key_no, flag, remark) VALUES(p_entity_id, p_book_code, p_year_month, p_posting_type_code, p_key_type, 4, c.key_no, NULL, NULL);
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN

            --抛出异常提示信息
            --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
            --RAISE NOTICE '[EXCEPTION]acc_pack_voucher_proc_correct_voucher_key：%; %',v_text1,v_text2;
            dbms_output.put_line(to_char(SQLCODE) || '::' ||
                                 substr(SQLERRM, 1, 200));

    END proc_correct_voucher_key;

    PROCEDURE proc_entry_entrance(p_entity_id  IN NUMBER,
                                  p_book_code  IN VARCHAR2,
                                  p_year_month IN VARCHAR2,
                                  p_user_id    IN NUMBER) IS
        /***********************************************************************
      NAME :acc_pack_voucher_proc_entry_entrance
      DESCRIPTION : 入账总入口 [核算单位、账套、会计期间、执行人ID必传]
      DATE :2020-10-31
      AUTHOR :LEIHUAN
      -------
      MODIFY LOG
      UPDATE DATE : 2023-03-01
      UPDATE BY : LY
      UPDATE DESC :
    ***********************************************************************/
        v_task_code        VARCHAR(32);
        v_result           VARCHAR(10);
        v_proc_id          NUMERIC;
        v_currency_cu_code VARCHAR(3);
        v_exch_date        DATE;
        --rec_proc record;
        v_year_month VARCHAR(6);
        v_tran_ym    VARCHAR(6);
    BEGIN

        --1 、获取会计引擎的入账任务ID
        v_task_code := bpluser.bpl_pack_common.func_get_taskcode('ACC',
                                                                 'A',
                                                                 'PY-Entry');

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               v_task_code,
                               p_user_id,
                               NULL,
                               'proc_entry_entrance',
                               '1',
                               'Start');

        SELECT MIN(year_month), MIN(currency_code)
        INTO v_year_month, v_currency_cu_code
        FROM acc_conf_accountperiod
        WHERE entity_id = p_entity_id
          AND book_code = p_book_code
          AND year_month = p_year_month
          AND valid_is = '1'
          AND audit_state = '1'
          AND execution_state IN ('0', '2');

        IF v_year_month IS NULL THEN
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   '',
                                   p_user_id,
                                   NULL,
                                   'proc_entry_entrance',
                                   '2',
                                   'year_month is invalid');
            RETURN;
        END IF;

        --v_error_msg := '入账任务ID：' || v_task_code; --RAISE NOTICE '入账任务ID：%',v_task_code;
        --dbms_output.put_line('入账任务ID：'||v_task_code);
        v_exch_date := acc_pack_common.func_correct_voucher_date(p_year_month); --兑换日期
        v_proc_id   := bpluser.bpl_pack_common.func_get_procid('ACC_ACCOUNTENTRY');

        -- 删除临时表，防止前一次任务出错，表数据未删除
        execute immediate ' truncate table acc_buss_entry_data_all ';
        execute immediate ' truncate table acc_buss_entry_data_err ';
        execute immediate ' truncate table acc_buss_entry_data_success ';

        --判断是否是过渡期当月 ********
        begin
            SELECT t.code_code
            into v_tran_ym
            FROM acc_conf_code t
            where t.upper_code_id =
                  (SELECT a.code_id
                   FROM acc_v_conf_code a
                   where a.code_code_idx = 'TransitionPeriod')
              and t.valid_is = '1';

        EXCEPTION
            WHEN OTHERS THEN
                dbms_output.put_line('[EXCEPTION]proc_entry_entrance TransitionPeriod：' ||
                                     to_char(SQLCODE) || ';' ||
                                     substr(SQLERRM, 1, 200));
        end;

        if v_tran_ym is not null and p_year_month < v_tran_ym then
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   v_task_code,
                                   p_user_id,
                                   NULL,
                                   'proc_entry_entrance',
                                   '1',
                                   'Step1 delete acc_temp_dap_entry_data');
            /* delete from acc_temp_dap_entry_data T
       where ENTITY_ID = p_entity_id
         AND year_month = p_year_month
         AND EXISTS (select *
                from ACC_DAP_ENTRY_DATA A
               WHERE A.ENTITY_ID = T.ENTITY_ID
                 AND A.YEAR_MONTH = T.YEAR_MONTH
                 AND A.ENTRY_DATA_ID = T.ENTRY_DATA_ID);
      commit;

    proc_account_entry_log(p_entity_id,
                           p_year_month,
                           v_task_code,
                           p_user_id,
                           NULL,
                           'proc_entry_entrance',
                           '1',
                           'Step1 insert acc_temp_dap_entry_data');
      insert into acc_temp_dap_entry_data
        select *
          from (select \*+INDEX(a IDX_ACC_DAP_ENTRY_DATA_QUERY )*\ a.*
                  from acc_dap_entry_data a
                 where a.entity_id = p_entity_id
                   and a.year_month = p_year_month
                   and a.posting_type_code = '02'
                   and exists (select \*+INDEX(t IDX_ACC_DAP_ENTRY_DATA_QUERY )*\  t.bu_voucher_no
                          from acc_dap_entry_data t
                         where t.entity_id = p_entity_id
                         and t.year_month = p_year_month
                         and t.expenses_type_code = 'cashflow'
                           and a.bu_voucher_no = t.bu_voucher_no)
                   and not exists
                 (select \*+INDEX(t IDX_ACC_DAP_ENTRY_DATA_QUERY )*\  t.bu_voucher_no
                          from acc_dap_entry_data t
                         where t.entity_id = p_entity_id
                         and t.year_month = p_year_month
                         and t.expenses_type_code != 'cashflow'
                           and a.bu_voucher_no = t.bu_voucher_no)
                union
                select \*+INDEX(a IDX_ACC_DAP_ENTRY_DATA_QUERY )*\  a.*
                  from acc_dap_entry_data a
                 where a.entity_id = p_entity_id
                   and a.year_month = p_year_month
                   and a.posting_type_code = '02'
                   and a.bu_voucher_no in
                       (select \*+INDEX(t IDX_ACC_DAP_ENTRY_DATA_QUERY )*\ t.bu_voucher_no
                          from acc_dap_entry_data t
                         where t.entity_id = p_entity_id
                           and t.year_month = p_year_month
                           and t.posting_type_code = '02'
                           and t.bu_voucher_no not in
                         (select \*+INDEX(a IDX_ACC_DAP_ENTRY_DATA_QUERY )*\ a.bu_voucher_no
                                  from acc_dap_entry_data a
                                 where a.entity_id = p_entity_id
                                   and a.year_month = p_year_month
                                   and a.posting_type_code = '02'
                                   --and a.bu_voucher_no = t.bu_voucher_no
                                   and a.expenses_type_code = 'IAC')
                         group by t.bu_voucher_no
                        having count(t.bu_voucher_no) = 1));

      commit;*/

            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   v_task_code,
                                   p_user_id,
                                   NULL,
                                   'proc_entry_entrance',
                                   '1',
                                   'Step1 delete acc_dap_entry_data');
            /*  delete \*+INDEX(a PK_ACC_DAP_ENTRY_DATA_ID )*\  from acc_dap_entry_data a
       where a.entity_id = p_entity_id
       and a.year_month = p_year_month
       and a.entry_data_id in
             (select \*+INDEX(t IDX_ACC_TEMP_DAP_ENTRY_DATA_MAIN )*\ t.entry_data_id
                from acc_temp_dap_entry_data t
               where t.entity_id = p_entity_id
                 and t.year_month = p_year_month);*/
            /* merge into acc_dap_entry_data a
      using (select \*+INDEX(t IDX_ACC_TEMP_DAP_ENTRY_DATA_MAIN )*\
              t.entry_data_id
               from acc_temp_dap_entry_data t
              where t.entity_id = p_entity_id
                 and t.year_month = p_year_month) c
      on (a.entry_data_id = c.entry_data_id)
      when matched then
        update set a.cashflow_article = 'd ' || a.cashflow_article
        delete  where 1 = 1;*/

            delete from acc_dap_entry_data where entry_data_id in ( select /*+INDEX(a IDX_ACC_DAP_ENTRY_DATA_QUERY )*/ a.entry_data_id
                                                                    from acc_dap_entry_data a
                                                                    where a.entity_id = p_entity_id
                                                                      and a.year_month = p_year_month
                                                                      and a.posting_type_code = '02'
                                                                      and exists (select /*+INDEX(t IDX_ACC_DAP_ENTRY_DATA_QUERY )*/  t.bu_voucher_no
                                                                                  from acc_dap_entry_data t
                                                                                  where t.entity_id = p_entity_id
                                                                                    and t.year_month = p_year_month
                                                                                    and t.expenses_type_code = 'cashflow'
                                                                                    and a.bu_voucher_no = t.bu_voucher_no)
                                                                      and not exists
                                                                        (select /*+INDEX(t IDX_ACC_DAP_ENTRY_DATA_QUERY )*/  t.bu_voucher_no
                                                                         from acc_dap_entry_data t
                                                                         where t.entity_id = p_entity_id
                                                                           and t.year_month = p_year_month
                                                                           and t.expenses_type_code != 'cashflow'
                                                                           and a.bu_voucher_no = t.bu_voucher_no)
                                                                    union
                                                                    select /*+INDEX(a IDX_ACC_DAP_ENTRY_DATA_QUERY )*/  a.entry_data_id
                                                                    from acc_dap_entry_data a
                                                                    where a.entity_id = p_entity_id
                                                                      and a.year_month =p_year_month
                                                                      and a.posting_type_code = '02'
                                                                      and a.bu_voucher_no in
                                                                          (select /*+INDEX(t IDX_ACC_DAP_ENTRY_DATA_QUERY )*/ t.bu_voucher_no
                                                                           from acc_dap_entry_data t
                                                                           where t.entity_id = p_entity_id
                                                                             and t.year_month = p_year_month
                                                                             and t.posting_type_code = '02'
                                                                             and t.bu_voucher_no not in
                                                                                 (select /*+INDEX(a IDX_ACC_DAP_ENTRY_DATA_QUERY )*/ a.bu_voucher_no
                                                                                  from acc_dap_entry_data a
                                                                                  where a.entity_id = p_entity_id
                                                                                    and a.year_month =p_year_month
                                                                                    and a.posting_type_code = '02'
                                                                                    --and a.bu_voucher_no = t.bu_voucher_no
                                                                                    and a.expenses_type_code = 'IAC')
                                                                           group by t.bu_voucher_no
                                                                           having count(t.bu_voucher_no) = 1));

            commit;
        end if;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               v_task_code,
                               p_user_id,
                               NULL,
                               'proc_entry_entrance',
                               '1',
                               'Step1 delete acc_buss_entry_data');
        -- 删除上一次失败数据
        /*  delete \*+INDEX(t IDX_ACC_TEMP_DAP_ENTRY_DATA_MAIN )*\  from acc_buss_entry_data a
     where a.entity_id = p_entity_id
       and a.year_month = p_year_month
       and a.book_code = p_book_code
       and a.voucher_id IS NULL
       AND a.entry_state <> 1
       and exists (select 1
              from acc_dap_entry_data t
             where t.entity_id = p_entity_id
               AND t.year_month = p_year_month
               and t.entry_data_id = a.entry_data_id);*/
        merge into acc_buss_entry_data a
        using (select /*+INDEX(t IDX_ACC_TEMP_DAP_ENTRY_DATA_MAIN )*/
                   t.entry_data_id
               from acc_dap_entry_data t
               where t.entity_id = p_entity_id
                 AND t.year_month = p_year_month
        )c
        on (c.entry_data_id = a.entry_data_id and a.voucher_id IS NULL and a.entry_state <> 1 )
        when matched then
            update set a.create_time = sysdate
            delete where 1=1;
        commit;
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               v_task_code,
                               p_user_id,
                               NULL,
                               'proc_entry_entrance',
                               '1',
                               'Step2');
        -- 插入新增数据
        INSERT /*+append*/
        into acc_buss_entry_data_all
        (buss_entry_id,
         entry_data_id,
         entity_id,
         book_code,
         year_month,
         task_code,
         proc_id,
         posting_type_code,
         currency_cu_code,
         currency_code,
         exchange_rate,
         entry_state,
         scenario_type,
         change_time,
         creator_id,
         create_time,
         bu_voucher_no,
         RI_ARRANGEMENT_CODE,
         BUSINESS_SOURCE_CODE,
         RI_DIRECTION_CODE,
         TREATY_TYPE_CODE,
         EVALUATE_APPROACH,
         EXPENSES_TYPE_CODE,
         ACCIDENT_DATE_TIME,
         CURRENT_PREVIOUS_IS,
         EXTEND_COLUMN1,
         EXTEND_COLUMN2,
         EXTEND_COLUMN3,
         EXTEND_COLUMN4,
         EXTEND_COLUMN5,
         EXTEND_COLUMN6,
         EXTEND_COLUMN7,
         EXTEND_COLUMN8,
         EXTEND_COLUMN9,
         EXTEND_COLUMN10,
         EXTEND_COLUMN11,
         EXTEND_COLUMN12)
        select acc_seq_buss_entry_data.nextval,
               t.entry_data_id,
               t.entity_id,
               p_book_code,
               p_year_month,
               v_task_code,
               t.proc_id,
               t.posting_type_code,
               v_currency_cu_code AS currency_cu_code,
               t.currency_code AS currency_code,
               acc_pack_common.func_get_exch_rate(t.entity_id,
                                                  v_exch_date,
                                                  t.currency_code,
                                                  v_currency_cu_code,
                                                  '2') as exchange_rate,
               0 as entry_state,
               (CASE
                    WHEN t.account_entry_code IN ('D', 'C') AND
                         t.posting_type_code != '05' THEN
                        'CF'
                    ELSE
                        'AP'
                   END) scenario_type, --费用分摊提取科目，赋值借贷方向
               1,
               p_user_id,
               localtimestamp,
               nvl(t.bu_voucher_no, t.icg_no) as bu_voucher_no,
               t.RI_ARRANGEMENT_CODE,
               t.BUSINESS_SOURCE_CODE,
               t.RI_DIRECTION_CODE,
               t.TREATY_TYPE_CODE,
               t.EVALUATE_APPROACH,
               t.EXPENSES_TYPE_CODE,
               t.ACCIDENT_DATE_TIME,
               t.CURRENT_PREVIOUS_IS,
               EXTEND_COLUMN1,
               EXTEND_COLUMN2,
               EXTEND_COLUMN3,
               EXTEND_COLUMN4,
               EXTEND_COLUMN5,
               EXTEND_COLUMN6,
               EXTEND_COLUMN7,
               EXTEND_COLUMN8,
               EXTEND_COLUMN9,
               EXTEND_COLUMN10,
               EXTEND_COLUMN11,
               EXTEND_COLUMN12
        FROM acc_dap_entry_data t
        WHERE t.entity_id = p_entity_id
          AND t.year_month = p_year_month
          and not exists
            (select 1
             from acc_buss_entry_data a
             where a.entity_id = t.entity_id
               and a.book_code = p_book_code
               and a.year_month = t.year_month
               and t.entry_data_id = a.entry_data_id);

        COMMIT;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               v_task_code,
                               p_user_id,
                               NULL,
                               'proc_entry_entrance',
                               '1',
                               'Step3');

        proc_entry_scenario_match(p_entity_id,
                                  p_book_code,
                                  p_year_month,
                                  v_task_code,
                                  p_user_id);

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               v_task_code,
                               p_user_id,
                               NULL,
                               'proc_entry_entrance',
                               '1',
                               'Step4');

        -- 【节点循环】：根据 核算单位ID 和 账套编码，获取当前节点所有父节点及其所有子节点
        FOR rec_proc IN (SELECT bp1.proc_id, bp1.parent_proc_id
                         FROM bpluser.bpl_act_re_procdef bp1
                         WHERE bp1.valid_is = '1'
                           AND bp1.parent_proc_id = v_proc_id
                           AND substr(scenerio_code, 2, 1) = '1'
                         -- and bp1.proc_id not in ( 50,51)
                         ORDER BY bp1.proc_id) LOOP

                proc_account_entry_log(p_entity_id,
                                       p_year_month,
                                       v_task_code,
                                       p_user_id,
                                       rec_proc.proc_id,
                                       'proc_entry_entrance',
                                       '1',
                                       'Step4');

                v_result := func_entry_reset_actionlog(p_entity_id,
                                                       p_book_code,
                                                       p_year_month,
                                                       rec_proc.proc_id,
                                                       v_task_code,
                                                       p_user_id);
                IF v_result = 'Y' THEN
                    --3 、入账数据检查
                    --RAISE NOTICE '入账数据检查: [%,%]',rec_proc.proc_id,p_book_code;
                    --v_error_msg := '入账数据检查：' || rec_proc.proc_id || ',' || p_book_code; --RAISE NOTICE '入账任务ID：%',v_task_code;
                    --dbms_output.put_line('入账数据检查：' || rec_proc.proc_id || ',' || p_book_code);

                    proc_entry_data_check(p_entity_id,
                                          p_book_code,
                                          p_year_month,
                                          rec_proc.proc_id,
                                          v_task_code,
                                          p_user_id);

                    --4 、入账
                    --v_error_msg := '入账开始:' || p_entity_id || ',' || p_year_month;
                    proc_entry_voucher(p_entity_id,
                                       p_book_code,
                                       p_year_month,
                                       rec_proc.proc_id,
                                       v_task_code,
                                       p_user_id);

                    --5、记录成功生成凭证的流程日志信息
                    proc_entry_deal_actionlog(p_entity_id,
                                              p_book_code,
                                              p_year_month,
                                              rec_proc.proc_id,
                                              v_task_code,
                                              p_user_id);

                END IF;

            END LOOP;

        insert into acc_buss_entry_data
        SELECT * from acc_buss_entry_data_err;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               v_task_code,
                               p_user_id,
                               NULL,
                               'proc_entry_entrance',
                               '1',
                               'End');
    EXCEPTION
        WHEN OTHERS THEN
            --提示异常信息
            --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2=PG_EXCEPTION_CONTEXT;
            --RAISE EXCEPTION '[EXCEPTION]acc_pack_voucher_proc_entry_entrance：%; %',v_text1,v_text2;
            dbms_output.put_line('[EXCEPTION]proc_entry_entrance：' ||
                                 to_char(SQLCODE) || ';' ||
                                 substr(SQLERRM, 1, 200));
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   v_task_code,
                                   p_user_id,
                                   NULL,
                                   'proc_entry_entrance',
                                   '2',
                                   'End' ||to_char(SQLCODE) || ';' ||
                                   substr(SQLERRM, 1, 200));
    END proc_entry_entrance;

    PROCEDURE proc_entry_scenario_match(p_entity_id  IN NUMBER,
                                        p_book_code  IN VARCHAR2,
                                        p_year_month IN VARCHAR2,
                                        p_task_code  IN VARCHAR2,
        --p_proc_id NUMBER,
                                        p_user_id IN NUMBER) IS
        /***********************************************************************
      NAME :ACC_SCENE_MATCH
      DESCRIPTION :入账场景匹配
      DATE :2020-12-28
      AUTHOR :LEIHUAN SCENE
      -------
      MODIFY LOG
      UPDATE DATE : 2023-03-01
      UPDATE BY : LY
      UPDATE DESC :
    ***********************************************************************/
        v_index                       NUMBER(10) := 0;
        v_count                       NUMBER(10);
        v_cf_scenario_id              NUMBER(11);
        acc_accountentry_comp_stl_acr NUMBER(11);
    BEGIN

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               null,
                               'proc_entry_scenario_match',
                               '1',
                               'Start');
        --获取需要匹配入账场景的业务数据
        select proc_id
        into acc_accountentry_comp_stl_acr
        from bpl_act_re_procdef
        where proc_code = 'ACC_ACCOUNTENTRY_COMP_STL_ACR';
        FOR rec_scenario IN (SELECT a.scenario_id,
                                    a.scenario_condition,
                                    a.serial_no,
                                    COUNT(b.model_code_id) model_count,
                                 /*' UPDATE
                                \*+INDEX(a IDX_ACC_BUSS_ENTRY_DATA_MAIN )*\
                                acc_buss_entry_data a' ||
                                ' SET scenario_id = ' || a.scenario_id ||
                                ',scen_serial_no = ' || coalesce(a.serial_no,1) ||
                                ' WHERE A.entity_id = ' || p_entity_id ||
                                ' AND A.book_code = ''' || p_book_code || '''' ||
                                ' AND A.year_month = ''' || p_year_month || '''' ||
                                ' AND A.task_code = ''' || p_task_code || '''' ||
                                ' AND A.entry_state = 0' ||
                                ' AND A.scenario_type = ''AP''' ||
                                ' AND A.scenario_id IS NULL' ||
                                ' AND A.entry_data_id IN( select \*+INDEX(b IDX_ACC_DAP_ENTRY_DATA_QUERY )*\ B.entry_data_id from acc_dap_entry_data B' ||
                                ' WHERE B.entity_id = ' || p_entity_id ||' and  B.year_month = ''' || p_year_month || ''''  ||
                                ' AND ' || a.scenario_condition || ')' scenario_sql,
                                */
                                    ' insert /*+append */ into acc_buss_entry_data_success(buss_entry_id,entry_data_id,entity_id,book_code,year_month,' ||
                                    ' task_code,proc_id,posting_type_code,currency_cu_code,currency_code,exchange_rate,entry_state,' ||
                                    ' scenario_type,change_time,creator_id,create_time,bu_voucher_no,scenario_id,scen_serial_no)' ||
                                    '  select b.buss_entry_id,b.entry_data_id, b.entity_id,b.book_code,b.year_month,b.task_code,b.proc_id,' ||
                                    ' b.posting_type_code,b.currency_cu_code, b.currency_code,b.exchange_rate,b.entry_state,' ||
                                    ' b.scenario_type,b.change_time,b.creator_id,b.create_time,b.bu_voucher_no,' ||
                                    a.scenario_id || ' , ' ||
                                    coalesce(a.serial_no, 1) ||
                                    ' from acc_buss_entry_data_all b ' ||
                                    ' where ' ||
                                        -- ' and b.entity_id = ' || p_entity_id ||
                                        -- ' AND b.book_code = ''' || p_book_code || '''' ||
                                        --  ' AND b.year_month = ''' || p_year_month || '''' ||
                                    ' b.task_code = ''' || p_task_code || '''' ||
                                    ' and not exists (select 1 from acc_buss_entry_data_success s where s.buss_entry_id = b.buss_entry_id)' ||
                                        -- ' AND b.proc_id = '||p_proc_id||
                                    ' AND b.entry_state = 0' ||
                                    ' AND b.scenario_type = ''AP''' || ' and  ' ||
                                    a.scenario_condition as scenario_sql
                             FROM acc_conf_scenario a
                                      LEFT JOIN acc_conf_scenario_modelref b
                                                ON a.scenario_id = b.scenario_id
                             WHERE a.valid_is = '1'
                               AND a.audit_state = '1'
                             --and substr(a.scenario_code,1,3) not in ('GMM','PAA','EA0')
                             -- and (case when acc_accountentry_comp_stl_acr = p_proc_id then p_proc_id else coalesce(a.proc_id,0) end) = p_proc_id
                             HAVING COUNT(b.model_code_id) > 0
                             GROUP BY a.scenario_id,
                                      a.scenario_condition,
                                      a.serial_no
                             ORDER BY model_count DESC, scenario_id) LOOP
                BEGIN
                    --dbms_output.put_line('proc_entry_scenario_match：' || rec_scenario.scenario_sql);
                    EXECUTE IMMEDIATE rec_scenario.scenario_sql;
                    commit;
                    /*proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
        p_user_id, p_proc_id, 'proc_entry_scenario_match', '1',
        'Step21-'||rec_scenario.scenario_id);*/
                    v_index := v_index + 1;
                    IF MOD(v_index, 100) = 0 THEN
                        SELECT COUNT(1)
                        INTO v_count
                        FROM acc_buss_entry_data_all t
                        WHERE t.task_code = p_task_code
                          AND t.entry_state = 0
                          and t.scenario_type = 'AP'
                          and not exists （select 1 from
                            acc_buss_entry_data_success s
                        where t.buss_entry_id = s.buss_entry_id）;
                        IF v_count = 0 THEN
                            EXIT;
                        END IF;
                    END IF;

                EXCEPTION
                    WHEN OTHERS THEN
                        --抛出异常提示信息
                        proc_account_entry_log(p_entity_id,
                                               p_year_month,
                                               p_task_code,
                                               p_user_id,
                                               rec_scenario.scenario_id,
                                               'proc_entry_scenario_match',
                                               '2',
                                               SQLERRM);
                END;
            END LOOP;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               null,
                               'proc_entry_scenario_match',
                               '1',
                               'Step2');

        --收付方式更新现金流场景：借贷方向标志不为空时，默认更新为现金流入账场景 入账场景编码 CF00001-现金场景
        SELECT max(a.scenario_id)
        into v_cf_scenario_id
        FROM acc_conf_scenario a
        WHERE a.scenario_code LIKE 'CF%'
          AND a.valid_is = '1'
          AND a.audit_state = '1'
          AND rownum = 1;
        if v_cf_scenario_id is not null then
            insert /*+append */
            into acc_buss_entry_data_success
            (buss_entry_id,
             entry_data_id,
             entity_id,
             book_code,
             year_month,
             task_code,
             proc_id,
             posting_type_code,
             currency_cu_code,
             currency_code,
             exchange_rate,
             entry_state,
             scenario_type,
             change_time,
             creator_id,
             create_time,
             bu_voucher_no,
             scenario_id)
            select buss_entry_id,
                   entry_data_id,
                   entity_id,
                   book_code,
                   year_month,
                   task_code,
                   proc_id,
                   posting_type_code,
                   currency_cu_code,
                   currency_code,
                   exchange_rate,
                   entry_state,
                   scenario_type,
                   change_time,
                   creator_id,
                   create_time,
                   bu_voucher_no,
                   v_cf_scenario_id
            from acc_buss_entry_data_all a
            WHERE a.task_code = p_task_code
              AND a.entry_state = 0
              AND a.scenario_type = 'CF';
            --and a.proc_id = p_proc_id
            -- AND a.scenario_id IS NULL

        end if;

        COMMIT;

        -- 处理匹配场景失败的数据进入acc_buss_entry_data_err表
        insert /*+append*/
        into acc_buss_entry_data_err
        (buss_entry_id,
         entry_data_id,
         entity_id,
         book_code,
         year_month,
         task_code,
         proc_id,
         posting_type_code,
         currency_cu_code,
         currency_code,
         exchange_rate,
         scenario_type,
         change_time,
         creator_id,
         create_time,
         bu_voucher_no,
         scenario_id,
         entry_msg,
         entry_state)
        select buss_entry_id,
               entry_data_id,
               entity_id,
               book_code,
               year_month,
               task_code,
               proc_id,
               posting_type_code,
               currency_cu_code,
               currency_code,
               exchange_rate,
               scenario_type,
               change_time,
               creator_id,
               create_time,
               bu_voucher_no,
               scenario_id,
               '010',
               2
        from acc_buss_entry_data_all a
        where a.task_code = p_task_code
          --and a.proc_id = p_proc_id
          and not exists
            (select 1
             from acc_buss_entry_data_success s
             where s.buss_entry_id = a.buss_entry_id);

        commit;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               null,
                               'proc_entry_scenario_match',
                               '1',
                               'End');

    EXCEPTION
        WHEN OTHERS THEN
            --抛出异常提示信息
            --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
            ---RAISE EXCEPTION '[EXCEPTION]acc_pack_voucher_proc_scenario_match：%; %',v_text1,v_text2;
            dbms_output.put_line('[EXCEPTION]proc_entry_scenario_match：' ||
                                 to_char(SQLCODE) || ';' ||
                                 substr(SQLERRM, 1, 200));
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   null,
                                   'proc_entry_scenario_match',
                                   '2',
                                   SQLERRM);
    END proc_entry_scenario_match;

    PROCEDURE proc_entry_data_check(p_entity_id  IN NUMBER,
                                    p_book_code  IN VARCHAR2,
                                    p_year_month IN VARCHAR2,
                                    p_proc_id    IN NUMBER,
                                    p_task_code  IN VARCHAR2,
                                    p_user_id    IN NUMBER) IS

        /***********************************************************************
      NAME :acc_pack_voucher_proc_entry_data_check
      DESCRIPTION : 入账处理数据检查
      DATE :2021-04-25
      AUTHOR :YINXH
    ***********************************************************************/
        v_log_id         NUMERIC;
        v_parent_proc_id NUMERIC;
        --REC_BUSINESS record;
        --v_error_msg VARCHAR2(100);

        v_tran_ym             VARCHAR2(6); --过渡期年月
        v_account_posting_qtp NUMBER(11);
        v_account_posting_exp NUMBER(11);
        v_err_count           NUMBER(10);
        v_suc_count           NUMBER(10);
    BEGIN

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_data_check',
                               '1',
                               'Start');

        SELECT b.parent_proc_id
        INTO v_parent_proc_id
        FROM bpluser.bpl_act_re_procdef b
        WHERE proc_id = p_proc_id;

        SELECT MAX(a.act_log_id)
        INTO v_log_id
        FROM bpluser.bpl_log_action a
        WHERE a.task_code = p_task_code
          AND a.system_code = 'ACC'
          AND a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.parent_proc_id = v_parent_proc_id
          AND a.proc_id = p_proc_id; --入账节点ID

        /* proc_account_entry_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_proc_id,
                           'proc_entry_data_check',
                           '1',
                           'Step1 获取核算单位、账套、会计期间对应账期的本位币');*/
        --获取核算单位、账套、会计期间对应账期的本位币

        INSERT /*+append*/
        INTO ACC_BUSS_ENTRY_DATA_ERR
        SELECT
            BUSS_ENTRY_ID,
            ENTRY_DATA_ID,
            ENTITY_ID,
            BOOK_CODE,
            YEAR_MONTH,
            TASK_CODE,
            PROC_ID,
            SCENARIO_ID,
            ACCOUNT_ID_DR,
            ACCOUNT_ID_CR,
            POSTING_TYPE_CODE,
            SCENARIO_TYPE,
            VOUCHER_ID,
            CHANGE_TIME,
            CURRENCY_CU_CODE,
            CURRENCY_CODE,
            EXCHANGE_RATE,
            2 AS ENTRY_STATE,
            '006' AS ENTRY_MSG,
            CREATE_TIME,
            CREATOR_ID,
            UPDATE_TIME,
            UPDATOR_ID,
            BU_VOUCHER_NO,
            ENTRY_DATE,
            SCEN_SERIAL_NO
        FROM ACC_BUSS_ENTRY_DATA_SUCCESS a
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.task_code = p_task_code
          AND a.entry_state = 0
          AND a.proc_id = p_proc_id
          AND a.currency_cu_code IS NULL
          AND NOT EXISTS
            (SELECT t.BUSS_ENTRY_ID
             FROM ACC_BUSS_ENTRY_DATA_ERR t
             WHERE t.BUSS_ENTRY_ID = a.BUSS_ENTRY_ID);
        COMMIT;

        /*proc_account_entry_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_proc_id,
                           'proc_entry_data_check',
                           '1',
                           'Step1 获取汇率异常');*/
        --获取汇率异常
        INSERT /*+append*/
        INTO ACC_BUSS_ENTRY_DATA_ERR
        SELECT BUSS_ENTRY_ID,
               ENTRY_DATA_ID,
               ENTITY_ID,
               BOOK_CODE,
               YEAR_MONTH,
               TASK_CODE,
               PROC_ID,
               SCENARIO_ID,
               ACCOUNT_ID_DR,
               ACCOUNT_ID_CR,
               POSTING_TYPE_CODE,
               SCENARIO_TYPE,
               VOUCHER_ID,
               CHANGE_TIME,
               CURRENCY_CU_CODE,
               CURRENCY_CODE,
               EXCHANGE_RATE,
               2 AS ENTRY_STATE,
               '006' AS ENTRY_MSG,
               CREATE_TIME,
               CREATOR_ID,
               UPDATE_TIME,
               UPDATOR_ID,
               BU_VOUCHER_NO,
               ENTRY_DATE,
               SCEN_SERIAL_NO
        FROM ACC_BUSS_ENTRY_DATA_SUCCESS a
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.proc_id = p_proc_id
          AND a.task_code = p_task_code
          AND a.entry_state = 0
          AND a.exchange_rate IS NULL
          AND NOT EXISTS
            (SELECT t.BUSS_ENTRY_ID
             FROM ACC_BUSS_ENTRY_DATA_ERR t
             WHERE t.BUSS_ENTRY_ID = a.BUSS_ENTRY_ID);
        COMMIT;

        --匹配入账场景
        --     INSERT /*+append*/ INTO ACC_BUSS_ENTRY_DATA_ERR
        --  SELECT BUSS_ENTRY_ID,
        --    ENTRY_DATA_ID,
        --    ENTITY_ID,
        --    BOOK_CODE,
        --    YEAR_MONTH,
        --    TASK_CODE,
        --    PROC_ID,
        --    SCENARIO_ID,
        --    ACCOUNT_ID_DR,
        --    ACCOUNT_ID_CR,
        --    POSTING_TYPE_CODE,
        --    SCENARIO_TYPE,
        --    VOUCHER_ID,
        --    CHANGE_TIME,
        --    CURRENCY_CU_CODE,
        --    CURRENCY_CODE,
        --    EXCHANGE_RATE,
        --    2 AS ENTRY_STATE,
        --    '010' AS ENTRY_MSG,
        --    CREATE_TIME,
        --    CREATOR_ID,
        --    UPDATE_TIME,
        --    UPDATOR_ID,
        --    BU_VOUCHER_NO,
        --    ENTRY_DATE,
        --    SCEN_SERIAL_NO
        --  FROM
        --    ACC_BUSS_ENTRY_DATA_SUCCESS a
        --  WHERE
        --    a.entity_id = p_entity_id
        --    AND a.book_code = p_book_code
        --    AND a.year_month = p_year_month
        --    AND a.task_code = p_task_code
        --    AND a.entry_state = 0
        --    AND a.proc_id = p_proc_id
        --    AND scenario_id IS NULL
        --    AND NOT EXISTS ( SELECT t.BUSS_ENTRY_ID FROM ACC_BUSS_ENTRY_DATA_ERR t WHERE t.BUSS_ENTRY_ID = a.BUSS_ENTRY_ID );
        --     COMMIT;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_data_check',
                               '1',
                               'Step1 删除过程表ACC_BUSS_ENTRY_DATA_SUCCESS中存在的失败数据');
        --删除过程表ACC_BUSS_ENTRY_DATA_SUCCESS中存在的失败数据
        DELETE FROM ACC_BUSS_ENTRY_DATA_SUCCESS S
        WHERE EXISTS (SELECT E.BUSS_ENTRY_ID
                      FROM ACC_BUSS_ENTRY_DATA_ERR E
                      WHERE E.BUSS_ENTRY_ID = S.BUSS_ENTRY_ID);
        COMMIT;

        -- 删除历史入账规则,并用最新的入账规则入账
        /*DELETE FROM acc_buss_entry_data_detail d
     WHERE EXISTS (SELECT
            \*+INDEX(a IDX_ACC_BUSS_ENTRY_DATA_MAIN )*\
             a.buss_entry_id
              FROM acc_buss_entry_data a --这里提前
             WHERE a.buss_entry_id = d.buss_entry_id
               AND a.entity_id = p_entity_id
               AND a.book_code = p_book_code
               AND a.year_month = p_year_month
               AND a.task_code = p_task_code
               AND a.proc_id = p_proc_id
               AND a.voucher_id IS NULL
               AND a.entry_state <> 1);
    COMMIT;
  */
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_data_check',
                               '1',
                               'Step2 data check');

        INSERT /*+append*/
        INTO acc_buss_entry_data_detail d
        (buss_entry_dtl_id,
         buss_entry_id,
         display_no,
         account_id_dr,
         account_id_cr)
        SELECT acc_seq_buss_entry_data_dtl.nextval,
               a.buss_entry_id,
               rownum, --一个凭证有多条现金流序号
               (CASE dap.account_entry_code
                    WHEN 'D' THEN
                        item.base_account_id
                    ELSE
                        NULL
                   END),
               (CASE dap.account_entry_code
                    WHEN 'C' THEN
                        item.base_account_id
                    ELSE
                        NULL
                   END)
        FROM acc_buss_entry_data_success a
                 LEFT JOIN acc_dap_entry_data dap
                           ON a.entry_data_id = dap.entry_data_id
                               AND dap.account_id IS NOT NULL
                 LEFT JOIN bpluser.bbs_conf_account_mapping item
                           ON item.other_account_id = dap.account_id
        WHERE a.task_code = p_task_code
          AND a.proc_id = p_proc_id
          AND a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          -- AND a.entry_state = 0
          AND a.scenario_type = 'CF'
          AND dap.account_id IS NOT NULL;
        COMMIT;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_data_check',
                               '1',
                               'Step3 --insert CF detail');
        --判断是否是过渡期当月 ********
        --计量特殊处理，转为实际现金流处理 ********
        begin
            SELECT t.code_code
            into v_tran_ym
            FROM acc_conf_code t
            where t.upper_code_id =
                  (SELECT a.code_id
                   FROM acc_v_conf_code a
                   where a.code_code_idx = 'TransitionPeriod')
              and t.valid_is = '1';

            select proc_id
            into v_account_posting_qtp
            from bpl_act_re_procdef
            where proc_code = 'ACC_ACCOUNTENTRY_QTP_BCR';
            select proc_id
            into v_account_posting_exp
            from bpl_act_re_procdef
            where proc_code = 'ACC_ACCOUNTENTRY_EXP_BCR';
        EXCEPTION
            WHEN OTHERS THEN
                --抛出异常提示信息
                --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
                --RAISE EXCEPTION '[EXCEPTION]测试异常：%, %',v_text1,v_text2;
                dbms_output.put_line('[EXCEPTION]proc_entry_data_check：' ||
                                     to_char(SQLCODE) || ';' ||
                                     substr(SQLERRM, 1, 200));
                proc_account_entry_log(p_entity_id,
                                       p_year_month,
                                       p_task_code,
                                       p_user_id,
                                       p_proc_id,
                                       'proc_entry_data_check',
                                       '2',
                                       SQLERRM);
        end;

        --匹配入账科目编码
        INSERT /*+append*/
        INTO acc_buss_entry_data_detail
        (buss_entry_dtl_id,
         buss_entry_id,
         display_no,
         account_id_dr,
         account_id_cr,
         entry_serial_no)
        SELECT acc_seq_buss_entry_data_dtl.nextval,
               a.buss_entry_id,
               c.display_no,
               c.account_id_dr,
               c.account_id_cr,
               b.serial_no
        FROM acc_buss_entry_data_success a
                 LEFT JOIN acc_conf_entryrule b
                           ON a.scenario_id = b.scenario_id
                               AND a.entity_id = b.entity_id
                               AND a.book_code = b.book_code
                               AND b.audit_state = '1'
                               AND b.valid_is = '1'
                 LEFT JOIN acc_conf_entryrule_detail c
                           ON c.entry_rule_id = b.entry_rule_id
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.task_code = p_task_code
          AND a.proc_id = p_proc_id
          AND a.scenario_type = 'AP';
        --AND a.entry_state = 0;
        COMMIT;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_data_check',
                               '1',
                               'Step4 --insert AP detail');
        --入账场景匹配入账规则校验
        INSERT /*+append*/
        INTO ACC_BUSS_ENTRY_DATA_ERR
        SELECT BUSS_ENTRY_ID,
               ENTRY_DATA_ID,
               ENTITY_ID,
               BOOK_CODE,
               YEAR_MONTH,
               TASK_CODE,
               PROC_ID,
               SCENARIO_ID,
               ACCOUNT_ID_DR,
               ACCOUNT_ID_CR,
               POSTING_TYPE_CODE,
               SCENARIO_TYPE,
               VOUCHER_ID,
               CHANGE_TIME,
               CURRENCY_CU_CODE,
               CURRENCY_CODE,
               EXCHANGE_RATE,
               2 AS ENTRY_STATE,
               (CASE
                    WHEN a.scenario_type = 'CF' THEN
                        '008' --现金流
                    ELSE
                        '002'
                   END) AS ENTRY_MSG,
               CREATE_TIME,
               CREATOR_ID,
               UPDATE_TIME,
               UPDATOR_ID,
               BU_VOUCHER_NO,
               ENTRY_DATE,
               SCEN_SERIAL_NO
        FROM ACC_BUSS_ENTRY_DATA_SUCCESS a
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.task_code = p_task_code
          AND a.proc_id = p_proc_id
          AND a.entry_state = 0
          AND NOT EXISTS ( --在明细表没有对应记录
            SELECT /*+INDEX(t IDX_ACC_BUSS_ENTRY_DATA_DETAIL_ID )*/
                t.buss_entry_id
            FROM acc_buss_entry_data_detail t
            WHERE t.buss_entry_id = a.buss_entry_id)
          AND NOT EXISTS
            (SELECT t.BUSS_ENTRY_ID
             FROM ACC_BUSS_ENTRY_DATA_ERR t
             WHERE t.BUSS_ENTRY_ID = a.BUSS_ENTRY_ID);
        COMMIT;

        --入账场景匹配入账规则校验
        INSERT /*+append*/
        INTO ACC_BUSS_ENTRY_DATA_ERR
        SELECT BUSS_ENTRY_ID,
               ENTRY_DATA_ID,
               ENTITY_ID,
               BOOK_CODE,
               YEAR_MONTH,
               TASK_CODE,
               PROC_ID,
               SCENARIO_ID,
               ACCOUNT_ID_DR,
               ACCOUNT_ID_CR,
               POSTING_TYPE_CODE,
               SCENARIO_TYPE,
               VOUCHER_ID,
               CHANGE_TIME,
               CURRENCY_CU_CODE,
               CURRENCY_CODE,
               EXCHANGE_RATE,
               2 AS ENTRY_STATE,
               (CASE
                    WHEN a.scenario_type = 'CF' THEN
                        '008' --现金流
                    ELSE
                        '002'
                   END) AS ENTRY_MSG,
               CREATE_TIME,
               CREATOR_ID,
               UPDATE_TIME,
               UPDATOR_ID,
               BU_VOUCHER_NO,
               ENTRY_DATE,
               SCEN_SERIAL_NO
        FROM ACC_BUSS_ENTRY_DATA_SUCCESS a
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.task_code = p_task_code
          AND a.proc_id = p_proc_id
          AND a.entry_state = 0
          AND EXISTS ( --在明细表存在对应记录，但借、贷科目都为空
            SELECT /*+INDEX(t IDX_ACC_BUSS_ENTRY_DATA_DETAIL_ID )*/
                t.buss_entry_id
            FROM acc_buss_entry_data_detail t
            WHERE t.buss_entry_id = a.buss_entry_id
              AND t.account_id_dr IS NULL
              AND t.account_id_cr IS NULL)
          AND NOT EXISTS
            (SELECT t.BUSS_ENTRY_ID
             FROM ACC_BUSS_ENTRY_DATA_ERR t
             WHERE t.BUSS_ENTRY_ID = a.BUSS_ENTRY_ID);
        COMMIT;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_data_check',
                               '1',
                               'Step5--item id is null');
        --校验是否借贷不平
        -- 正常处理借贷不平的数据
        INSERT /*+append*/
        INTO ACC_BUSS_ENTRY_DATA_ERR
        SELECT BUSS_ENTRY_ID,
               ENTRY_DATA_ID,
               ENTITY_ID,
               BOOK_CODE,
               YEAR_MONTH,
               TASK_CODE,
               PROC_ID,
               SCENARIO_ID,
               ACCOUNT_ID_DR,
               ACCOUNT_ID_CR,
               POSTING_TYPE_CODE,
               SCENARIO_TYPE,
               VOUCHER_ID,
               CHANGE_TIME,
               CURRENCY_CU_CODE,
               CURRENCY_CODE,
               EXCHANGE_RATE,
               2 AS ENTRY_STATE,
               '007' AS ENTRY_MSG,
               CREATE_TIME,
               CREATOR_ID,
               UPDATE_TIME,
               UPDATOR_ID,
               BU_VOUCHER_NO,
               ENTRY_DATE,
               SCEN_SERIAL_NO
        FROM ACC_BUSS_ENTRY_DATA_SUCCESS s
        WHERE s.entity_id = p_entity_id
          AND s.book_code = p_book_code
          AND s.year_month = p_year_month
          AND s.task_code = p_task_code
          AND s.proc_id = p_proc_id
          AND NOT EXISTS (SELECT t.BUSS_ENTRY_ID
                          FROM ACC_BUSS_ENTRY_DATA_ERR t
                          WHERE t.BUSS_ENTRY_ID = s.BUSS_ENTRY_ID)
          AND EXISTS
            (SELECT 1
             FROM acc_buss_entry_data_success ed
                      LEFT JOIN acc_dap_entry_data b
                                ON ed.entry_data_id = b.entry_data_id
                      LEFT JOIN acc_buss_entry_data_detail c
                                ON c.buss_entry_id = ed.buss_entry_id
             WHERE ed.entity_id = p_entity_id
               AND ed.book_code = p_book_code
               AND ed.year_month = p_year_month
               AND ed.task_code = p_task_code
               AND ed.proc_id = p_proc_id
               and ed.bu_voucher_no = s.bu_voucher_no
             GROUP BY ed.entity_id,
                      ed.book_code,
                      ed.year_month,
                      ed.proc_id,
                      ed.task_code,
                      ed.posting_type_code,
                      ed.currency_cu_code,
                      ed.bu_voucher_no
             HAVING SUM((CASE
                             WHEN c.account_id_dr IS NOT NULL AND
                                  b.amount >= 0 THEN
                                     b.amount * ed.exchange_rate
                             ELSE
                                 0
                 END) + (CASE
                             WHEN c.account_id_cr IS NOT NULL AND b.amount < 0 THEN
                                 abs(b.amount * ed.exchange_rate)
                             ELSE
                                 0
                 END)) <> SUM((CASE
                                   WHEN c.account_id_cr IS NOT NULL AND
                                        b.amount >= 0 THEN
                                           b.amount * ed.exchange_rate
                                   ELSE
                                       0
                 END) + (CASE
                             WHEN c.account_id_dr IS NOT NULL AND b.amount < 0 THEN
                                 abs(b.amount * ed.exchange_rate)
                             ELSE
                                 0
                 END)));

        if (v_tran_ym is not null and p_year_month < v_tran_ym) and
           p_proc_id not in (v_account_posting_qtp, v_account_posting_exp) then
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_data_check',
                                   '1',
                                   'Step5--处理脏数据，修改源数据金额');
            -- 处理脏数据，修改源数据金额
            MERGE INTO acc_dap_entry_data t
            USING (SELECT ed.entity_id,
                          ed.book_code,
                          ed.year_month,
                          ed.proc_id,
                          ed.task_code,
                          ed.posting_type_code,
                          ed.currency_cu_code,
                          b.bu_voucher_no,
                          SUM((CASE
                                   WHEN c.account_id_dr IS NOT NULL AND b.amount >= 0 THEN
                                           b.amount * ed.exchange_rate
                                   ELSE
                                       0
                              END) + (CASE
                                          WHEN c.account_id_cr IS NOT NULL AND b.amount < 0 THEN
                                              abs(b.amount * ed.exchange_rate)
                                          ELSE
                                              0
                              END)) dr_amount,
                          SUM((CASE
                                   WHEN c.account_id_cr IS NOT NULL AND b.amount >= 0 THEN
                                           b.amount * ed.exchange_rate
                                   ELSE
                                       0
                              END) + (CASE
                                          WHEN c.account_id_dr IS NOT NULL AND b.amount < 0 THEN
                                              abs(b.amount * ed.exchange_rate)
                                          ELSE
                                              0
                              END)) cr_amount
                   FROM ACC_BUSS_ENTRY_DATA_ERR ed
                            LEFT JOIN acc_dap_entry_data b
                                      ON ed.entry_data_id = b.entry_data_id
                            LEFT JOIN acc_buss_entry_data_detail c
                                      ON c.buss_entry_id = ed.buss_entry_id
                   WHERE
                       /*ed.entity_id = p_entity_id
             AND ed.book_code = p_book_code
             AND ed.year_month = p_year_month
             AND ed.task_code = p_task_code
             AND */
                           ed.ENTRY_MSG = '007'
                     and ed.proc_id = p_proc_id
                   GROUP BY ed.entity_id,
                            ed.book_code,
                            ed.year_month,
                            ed.proc_id,
                            ed.task_code,
                            ed.posting_type_code,
                            ed.currency_cu_code,
                            b.bu_voucher_no) c
            ON (c.entity_id = t.entity_id AND c.year_month = t.year_month AND c.posting_type_code = t.posting_type_code AND c.proc_id = t.proc_id AND c.bu_voucher_no = t.bu_voucher_no and c.posting_type_code = '02')
            WHEN MATCHED THEN
                update
                set t.amount          =
                        (case
                             when t.account_entry_code = 'D' then
                                     t.amount - (c.dr_amount - c.cr_amount)
                             else
                                     t.amount - (c.cr_amount - c.dr_amount)
                            end),
                    t.cashflow_article = 'update ' || t.amount,
                    T.ENTRY_DATE       = SYSDATE
                where t.entity_id = p_entity_id
                  and t.year_month = p_year_month
                  AND T.ENTRY_DATA_ID =
                      (select DAP.ENTRY_DATA_ID
                       from ACC_DAP_ENTRY_DATA DAP
                                left join bpluser.bbs_account v
                                          on v.account_id = DAP.account_id
                                              and v.ACCOUNT_CATEGORY_CODE in ('1', '2', '4')
                       WHERE DAP.entity_id = p_entity_id
                         and DAP.year_month = p_year_month
                         and DAP.posting_type_code = '02'
                         and DAP.expenses_type_code = 'cashflow'
                         AND DAP.BU_VOUCHER_NO = C.bu_voucher_no
                         and v.account_id is not null
                       order by decode(v.account_category_code, '1', 1, '4', 2, '2', 3) fetch next 1 rows only);
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_data_check',
                                   '1',
                                   'Step5--如果这个条件的数据依旧借贷不平，直接删除');

            /*INSERT \*+append*\
      INTO ACC_TEMP_DAP_ENTRY_DATA
        select \*+INDEX(a IDX_ACC_DAP_ENTRY_DATA_QUERY )*\  a.*
          from ACC_DAP_ENTRY_DATA A
         WHERE A.ENTITY_ID = p_entity_id
           AND A.YEAR_MONTH = p_year_month
           AND exists
         (SELECT 1
                  FROM ACC_BUSS_ENTRY_DATA_ERR ed
                  LEFT JOIN acc_dap_entry_data b
                    ON ed.entry_data_id = b.entry_data_id
                  LEFT JOIN acc_buss_entry_data_detail c
                    ON c.buss_entry_id = ed.buss_entry_id
                 WHERE ed.entity_id = p_entity_id
                   AND ed.book_code = p_book_code
                   AND ed.year_month = p_year_month
                   AND ed.task_code = p_task_code
                   AND ed.ENTRY_MSG = '007'
                   AND ed.proc_id = p_proc_id
                   and b.bu_voucher_no = A.BU_VOUCHER_NO
                 GROUP BY ed.entity_id,
                          ed.book_code,
                          ed.year_month,
                          ed.proc_id,
                          ed.task_code,
                          ed.posting_type_code,
                          ed.currency_cu_code,
                          b.bu_voucher_no
                HAVING SUM((CASE
                  WHEN c.account_id_dr IS NOT NULL AND
                       b.amount >= 0 THEN
                   b.amount * ed.exchange_rate
                  ELSE
                   0
                END) + (CASE
                  WHEN c.account_id_cr IS NOT NULL AND b.amount < 0 THEN
                   abs(b.amount * ed.exchange_rate)
                  ELSE
                   0
                END)) <> SUM((CASE
                  WHEN c.account_id_cr IS NOT NULL AND
                       b.amount >= 0 THEN
                   b.amount * ed.exchange_rate
                  ELSE
                   0
                END) + (CASE
                  WHEN c.account_id_dr IS NOT NULL AND b.amount < 0 THEN
                   abs(b.amount * ed.exchange_rate)
                  ELSE
                   0
                END)));*/
            /* AND A.BU_VOUCHER_NO IN (
      SELECT b.bu_voucher_no
              FROM acc_buss_entry_data_success ed
              LEFT JOIN acc_dap_entry_data b
                ON ed.entry_data_id = b.entry_data_id
              LEFT JOIN acc_buss_entry_data_detail c
                ON c.buss_entry_id = ed.buss_entry_id
             WHERE ed.entity_id = p_entity_id
               AND ed.book_code = p_book_code
               AND ed.year_month = p_year_month
               AND ed.task_code = p_task_code
               AND ed.proc_id = p_proc_id
             GROUP BY ed.entity_id,
                      ed.book_code,
                      ed.year_month,
                      ed.proc_id,
                      ed.task_code,
                      ed.posting_type_code,
                      ed.currency_cu_code,
                      b.bu_voucher_no
            HAVING SUM((CASE
              WHEN c.account_id_dr IS NOT NULL
                   AND b.amount >= 0 THEN
               b.amount * ed.exchange_rate
              ELSE
               0
            END) + (CASE
              WHEN c.account_id_cr IS NOT NULL
                   AND b.amount < 0 THEN
               abs(b.amount * ed.exchange_rate)
              ELSE
               0
            END)) <> SUM((CASE
              WHEN c.account_id_cr IS NOT NULL
                   AND b.amount >= 0 THEN
               b.amount * ed.exchange_rate
              ELSE
               0
            END) + (CASE
              WHEN c.account_id_dr IS NOT NULL
                   AND b.amount < 0 THEN
               abs(b.amount * ed.exchange_rate)
              ELSE
               0
            END)));*/

            delete from ACC_BUSS_ENTRY_DATA_ERR  a
            where exists(SELECT 1
                         FROM ACC_BUSS_ENTRY_DATA_ERR ed
                                  LEFT JOIN acc_dap_entry_data b
                                            ON ed.entry_data_id = b.entry_data_id
                                  LEFT JOIN acc_buss_entry_data_detail c
                                            ON c.buss_entry_id = ed.buss_entry_id
                         WHERE ed.entity_id = p_entity_id
                           AND ed.book_code = p_book_code
                           AND ed.year_month = p_year_month
                           AND ed.task_code = p_task_code
                           AND ed.ENTRY_MSG = '007'
                           AND ed.proc_id = p_proc_id
                           and b.bu_voucher_no = A.BU_VOUCHER_NO
                         GROUP BY ed.entity_id,
                                  ed.book_code,
                                  ed.year_month,
                                  ed.proc_id,
                                  ed.task_code,
                                  ed.posting_type_code,
                                  ed.currency_cu_code,
                                  b.bu_voucher_no
                         HAVING SUM((CASE
                                         WHEN c.account_id_dr IS NOT NULL AND
                                              b.amount >= 0 THEN
                                                 b.amount * ed.exchange_rate
                                         ELSE
                                             0
                             END) + (CASE
                                         WHEN c.account_id_cr IS NOT NULL AND b.amount < 0 THEN
                                             abs(b.amount * ed.exchange_rate)
                                         ELSE
                                             0
                             END)) = SUM((CASE
                                              WHEN c.account_id_cr IS NOT NULL AND
                                                   b.amount >= 0 THEN
                                                      b.amount * ed.exchange_rate
                                              ELSE
                                                  0
                             END) + (CASE
                                         WHEN c.account_id_dr IS NOT NULL AND b.amount < 0 THEN
                                             abs(b.amount * ed.exchange_rate)
                                         ELSE
                                             0
                             END)));


            COMMIT;
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_data_check',
                                   '1',
                                   'Step5--ACC_BUSS_ENTRY_DATA_DETAIL 如果这个条件的数据依旧借贷不平，直接删除');
            DELETE FROM ACC_BUSS_ENTRY_DATA_DETAIL A
            WHERE exists (select /*+INDEX(t IDX_ACC_BUSS_ENTRY_DATA_S_MAIN )*/  t.buss_entry_id
                          from acc_buss_entry_data_success T,
                               ACC_BUSS_ENTRY_DATA_ERR     B
                          WHERE B.ENTITY_ID = p_entity_id
                            AND B.YEAR_MONTH = p_year_month
                            AND T.ENTRY_DATA_ID = B.ENTRY_DATA_ID
                            and T.BUSS_ENTRY_ID = A.BUSS_ENTRY_ID
                            and b.entry_msg = '007');
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_data_check',
                                   '1',
                                   'Step5--Acc_Buss_Entry_Data_All 如果这个条件的数据依旧借贷不平，直接删除');
            /*      DELETE FROM Acc_Buss_Entry_Data_All A
       WHERE a.entry_data_id in (select \*+INDEX(b IDX_ACC_TEMP_DAP_ENTRY_DATA_MAIN )*\ b.entry_data_id
                from ACC_TEMP_DAP_ENTRY_DATA B
               WHERE B.ENTITY_ID = p_entity_id
                 AND B.YEAR_MONTH = p_year_month
                 --and b.ENTRY_DATA_ID = A.ENTRY_DATA_ID
                 );*/
            /* merge into Acc_Buss_Entry_Data_All a
                using (select \*+INDEX(b IDX_ACC_TEMP_DAP_ENTRY_DATA_MAIN )*\
                        b.entry_data_id
                         from ACC_TEMP_DAP_ENTRY_DATA B
                        WHERE B.ENTITY_ID = p_entity_id
                          AND B.YEAR_MONTH = p_year_month) c
                on (c.entry_data_id = a.entry_data_id)
                when matched then
                  update set a.update_time = sysdate
                  delete where 1 = 1;*/
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_data_check',
                                   '1',
                                   'Step5--Acc_Buss_Entry_Data_Success 如果这个条件的数据依旧借贷不平，直接删除');
            /*  DELETE  \*+INDEX(a IDX_ACC_BUSS_ENTRY_DATA_S_MAIN )*\ FROM Acc_Buss_Entry_Data_Success A
       WHERE a.entity_id = p_entity_id
       and a.book_code = p_book_code
       and a.year_month = p_year_month
       and  A.ENTRY_DATA_ID in (select \*+INDEX(b IDX_ACC_TEMP_DAP_ENTRY_DATA_MAIN )*\ b.entry_data_id
                from ACC_TEMP_DAP_ENTRY_DATA B
               WHERE B.ENTITY_ID = p_entity_id
                 AND B.YEAR_MONTH = p_year_month
                 --and b.ENTRY_DATA_ID = A.ENTRY_DATA_ID
                 );*/
            merge into Acc_Buss_Entry_Data_Success a
            using(
                select /*+INDEX(b IDX_ACC_TEMP_DAP_ENTRY_DATA_MAIN )*/ b.entry_data_id
                from ACC_BUSS_ENTRY_DATA_ERR B
                WHERE B.ENTITY_ID = p_entity_id
                  AND B.YEAR_MONTH =p_year_month
                  and b.entry_msg = '007'
                --and b.ENTRY_DATA_ID = A.ENTRY_DATA_ID
            )c
            on (a.entry_data_id = c.entry_data_id)
            when matched then
                update set a.update_time = sysdate
                delete where 1=1;
            /* DELETE FROM Acc_Buss_Entry_Data_Err A WHERE exists (
       select 1 from ACC_TEMP_DAP_ENTRY_DATA B  WHERE B.ENTITY_ID = p_entity_id
       AND B.YEAR_MONTH = p_year_month and b.ENTRY_DATA_ID = A.ENTRY_DATA_ID
      );*/
            commit;

            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_data_check',
                                   '1',
                                   'Step5--ACC_DAP_ENTRY_DATA 如果这个条件的数据依旧借贷不平，直接删除');
            -- 删除源数据
            /*DELETE \*+INDEX(a IDX_ACC_DAP_ENTRY_DATA_QUERY )*\ FROM ACC_DAP_ENTRY_DATA A
       WHERE a.entity_id =p_entity_id
       and a.year_month = p_year_month
       and a.entry_data_id in (select \*+INDEX(b IDX_ACC_TEMP_DAP_ENTRY_DATA_MAIN )*\ b.entry_data_id
                from ACC_TEMP_DAP_ENTRY_DATA B
               WHERE B.ENTITY_ID = p_entity_id
                 AND B.YEAR_MONTH = p_year_month
                 --and b.ENTRY_DATA_ID = A.ENTRY_DATA_ID
                 );*/
            merge into ACC_DAP_ENTRY_DATA A
            using (select /*+INDEX(b IDX_ACC_TEMP_DAP_ENTRY_DATA_MAIN )*/ b.entry_data_id
                   from ACC_BUSS_ENTRY_DATA_ERR B
                   WHERE B.ENTITY_ID = p_entity_id
                     AND B.YEAR_MONTH = p_year_month
                     and b.ENTRY_MSG = '007'
                --and b.ENTRY_DATA_ID = A.ENTRY_DATA_ID
            )c
            on (a.entry_data_id = c.entry_data_id)
            when matched then
                update set a.cashflow_article = 'd ' || a.cashflow_article
                delete where 1=1;

            /* DELETE FROM ACC_BUSS_ENTRY_DATA_DETAIL A WHERE A.BUSS_ENTRY_ID IN (
       select T.BUSS_ENTRY_ID from acc_buss_entry_data_success T ,ACC_TEMP_DAP_ENTRY_DATA B WHERE
       B.ENTITY_ID = p_entity_id AND B.YEAR_MONTH = p_year_month
       AND T.ENTRY_DATA_ID = B.ENTRY_DATA_ID);

      DELETE FROM Acc_Buss_Entry_Data_All A WHERE A.ENTRY_DATA_ID IN (
       select ENTRY_DATA_ID from ACC_TEMP_DAP_ENTRY_DATA B  WHERE B.ENTITY_ID = p_entity_id AND B.YEAR_MONTH = p_year_month
      );
       DELETE FROM Acc_Buss_Entry_Data_Success A WHERE A.ENTRY_DATA_ID IN (
       select ENTRY_DATA_ID from ACC_TEMP_DAP_ENTRY_DATA B  WHERE B.ENTITY_ID = p_entity_id AND B.YEAR_MONTH = p_year_month
      );
       DELETE FROM Acc_Buss_Entry_Data_Err A WHERE A.ENTRY_DATA_ID IN (
       select ENTRY_DATA_ID from ACC_TEMP_DAP_ENTRY_DATA B  WHERE B.ENTITY_ID = p_entity_id AND B.YEAR_MONTH = p_year_month
      );

      -- 删除源数据
      DELETE FROM ACC_DAP_ENTRY_DATA A WHERE A.ENTRY_DATA_ID IN (
       select ENTRY_DATA_ID from ACC_TEMP_DAP_ENTRY_DATA B  WHERE B.ENTITY_ID = p_entity_id AND B.YEAR_MONTH = p_year_month
      );*/
            COMMIT;
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_data_check',
                                   '1',
                                   'Step5--Acc_Buss_Entry_Data_Err 如果这个条件的数据依旧借贷不平，直接删除');
            --
            DELETE FROM Acc_Buss_Entry_Data_Err
            WHERE ENTRY_MSG = '007'
              AND PROC_ID = p_proc_id;
            --
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_data_check',
                                   '1',
                                   'Step5 End--如果这个条件的数据依旧借贷不平，直接删除');
        end if;

        -- INSERT /*+append*/ INTO ACC_BUSS_ENTRY_DATA_ERR
        /*SELECT BUSS_ENTRY_ID,
      ENTRY_DATA_ID,
      ENTITY_ID,
      BOOK_CODE,
      YEAR_MONTH,
      TASK_CODE,
      PROC_ID,
      SCENARIO_ID,
      ACCOUNT_ID_DR,
      ACCOUNT_ID_CR,
      POSTING_TYPE_CODE,
      SCENARIO_TYPE,
      VOUCHER_ID,
      CHANGE_TIME,
      CURRENCY_CU_CODE,
      CURRENCY_CODE,
      EXCHANGE_RATE,
      2 AS ENTRY_STATE,
      '007' AS ENTRY_MSG,
      CREATE_TIME,
      CREATOR_ID,
      UPDATE_TIME,
      UPDATOR_ID,
      BU_VOUCHER_NO,
      ENTRY_DATE,
      SCEN_SERIAL_NO
    FROM
      ACC_BUSS_ENTRY_DATA_SUCCESS s
    WHERE
      s.entity_id = p_entity_id
      AND s.book_code = p_book_code
      AND s.year_month = p_year_month
      AND s.task_code = p_task_code
      AND s.proc_id = p_proc_id
      AND NOT EXISTS ( SELECT t.BUSS_ENTRY_ID FROM ACC_BUSS_ENTRY_DATA_ERR t WHERE t.BUSS_ENTRY_ID = s.BUSS_ENTRY_ID )
      AND EXISTS (SELECT 1
               FROM acc_buss_entry_data_success ed
               LEFT JOIN acc_dap_entry_data b
                 ON ed.entry_data_id = b.entry_data_id
               LEFT JOIN acc_buss_entry_data_detail c
                 ON c.buss_entry_id = ed.buss_entry_id
              WHERE ed.entity_id = p_entity_id
                AND ed.book_code = p_book_code
                AND ed.year_month = p_year_month
                AND ed.task_code = p_task_code
                AND ed.proc_id = p_proc_id
              GROUP BY ed.entity_id,
                       ed.book_code,
                       ed.year_month,
                       ed.proc_id,
                       ed.task_code,
                       ed.posting_type_code,
                       ed.currency_cu_code,
                       b.bu_voucher_no
             HAVING SUM((CASE
               WHEN c.account_id_dr IS NOT NULL
                    AND b.amount >= 0 THEN
                b.amount * ed.exchange_rate
               ELSE
                0
             END) + (CASE
               WHEN c.account_id_cr IS NOT NULL
                    AND b.amount < 0 THEN
                abs(b.amount * ed.exchange_rate)
               ELSE
                0
             END)) <> SUM((CASE
               WHEN c.account_id_cr IS NOT NULL
                    AND b.amount >= 0 THEN
                b.amount * ed.exchange_rate
               ELSE
                0
             END) + (CASE
               WHEN c.account_id_dr IS NOT NULL
                    AND b.amount < 0 THEN
                abs(b.amount * ed.exchange_rate)
               ELSE
                0
             END)));*/

        COMMIT;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_data_check',
                               '1',
                               'Step6-drcr');

        --删除过程表ACC_BUSS_ENTRY_DATA_SUCCESS中存在的失败数据
        DELETE FROM ACC_BUSS_ENTRY_DATA_SUCCESS S
        WHERE EXISTS (SELECT E.BUSS_ENTRY_ID
                      FROM ACC_BUSS_ENTRY_DATA_ERR E
                      WHERE E.BUSS_ENTRY_ID = S.BUSS_ENTRY_ID);
        COMMIT;

        FOR rec_business IN (SELECT a.entry_data_id, a.entry_msg
                             FROM (SELECT a.entry_data_id,
                                          a.entry_msg,
                                          row_number() over(PARTITION BY a.entry_msg ORDER BY a.entry_msg) rn
                                   FROM acc_buss_entry_data_err a
                                            LEFT JOIN acc_dap_entry_data b
                                                      ON a.entry_data_id = b.entry_data_id
                                   WHERE a.entity_id = p_entity_id
                                     AND a.book_code = p_book_code
                                     --AND a.year_month <= p_year_month--此处不限制下限是为了处理上次匹配失败的数据能再次捞取进行处理
                                     AND a.year_month = p_year_month
                                     --AND A.ENTRY_SCENERIO_ID IS NULL
                                     --AND A.DCIND IS NULL
                                     AND a.proc_id = p_proc_id
                                     AND a.entry_state = 2) a
                             WHERE rn <= 50) LOOP

                --记录业务日志明细表信息
                bpluser.bpl_pack_action_log.proc_add_actionlogdetails(v_log_id,
                                                                      NULL,
                    --校验规则编码
                                                                      '2',
                                                                      NULL,
                    --校验脚本
                                                                      '0',
                    --0-失败
                                                                      rec_business.entry_msg,
                                                                      '' ||
                                                                      rec_business.entry_data_id,
                    --异常业务主键
                                                                      p_user_id
                    --创建人员
                    );

            END LOOP;

        bpluser.bpl_pack_action_log.proc_update_logst(v_log_id, p_user_id);

        SELECT COUNT(1) INTO v_err_count from ACC_BUSS_ENTRY_DATA_ERR;

        SELECT COUNT(1) INTO v_suc_count from ACC_BUSS_ENTRY_DATA_SUCCESS;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_data_check',
                               '1',
                               'End,ERR=' || v_err_count || ',SUCCESS=' ||
                               v_suc_count);
    EXCEPTION
        WHEN OTHERS THEN
            --抛出异常提示信息
            --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
            --RAISE EXCEPTION '[EXCEPTION]测试异常：%, %',v_text1,v_text2;
            dbms_output.put_line('[EXCEPTION]proc_entry_data_check：' ||
                                 to_char(SQLCODE) || ';' ||
                                 substr(SQLERRM, 1, 200));
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_data_check',
                                   '2',
                                   SQLERRM);
    END proc_entry_data_check;

    PROCEDURE proc_entry_voucher(p_entity_id  IN NUMBER,
                                 p_book_code  IN VARCHAR2,
                                 p_year_month IN VARCHAR2,
                                 p_proc_id    IN NUMBER,
                                 p_task_code  IN VARCHAR2,
                                 p_user_id    IN NUMBER) IS
        /***********************************************************************
      NAME :proc_entry_voucher
      DESCRIPTION :生成凭证数据处理(目前仅入账时调用)
      DATE :2022-8-23
      AUTHOR :LEIHUAN
      -------
      MODIFY LOG
      UPDATE DATE : 2023-03-01
      UPDATE BY : LY
      UPDATE DESC :
    ***********************************************************************/
        v_voucher_date DATE;
        v_message      VARCHAR2(200);
        v_log_id       NUMBER(8);
        --rec_buss_entry_data record;
        v_sql_d        VARCHAR2(4000);
        v_sql_detail   VARCHAR2(4000);
        v_sql_art      VARCHAR2(4000);
        v_source_table VARCHAR2(100);
    BEGIN

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_voucher',
                               '1',
                               'Start');
        SELECT MAX(a.act_log_id)
        INTO v_log_id
        FROM bpluser.bpl_log_action a
        WHERE a.task_code = p_task_code
          AND a.system_code = 'ACC'
          AND a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.proc_id = p_proc_id;

        --获取凭证日期
        v_voucher_date := acc_pack_common.func_correct_voucher_date(p_year_month);

        --按照现行业务系统规则入账
        /*FOR cur_buss_voucher IN (SELECT
    \*+INDEX(a IDX_ACC_BUSS_ENTRY_DATA_MAIN )*\
    a.entity_id,
                                  a.book_code,
                                  a.year_month,
                                  a.posting_type_code,
                                  a.proc_id,
                                  CAST(a.proc_id AS VARCHAR2(32)) AS key_type,
                                  count(1) as data_count
                             FROM acc_buss_entry_data a
                            WHERE a.entity_id = p_entity_id
                              AND a.book_code = p_book_code
                              AND a.year_month = p_year_month
                              AND a.task_code = p_task_code
                              AND a.entry_state = 0
                              AND a.proc_id = p_proc_id
                            GROUP BY a.entity_id,
                                     a.book_code,
                                     a.year_month,
                                     a.posting_type_code,
                                     a.proc_id) LOOP*/
        FOR cur_buss_voucher IN (SELECT
                                     /*+INDEX(a IDX_ACC_BUSS_ENTRY_DATA_MAIN )*/
                                     a.entity_id,
                                     a.book_code,
                                     a.year_month,
                                     a.posting_type_code,
                                     a.proc_id,
                                     CAST(a.proc_id AS VARCHAR2(32)) AS key_type,
                                     count(1) as data_count
                                 FROM acc_buss_entry_data_success a
                                 WHERE a.entity_id = p_entity_id
                                   AND a.book_code = p_book_code
                                   AND a.year_month = p_year_month
                                   AND a.task_code = p_task_code
                                   AND a.entry_state = 0
                                   AND a.proc_id = p_proc_id
                                 GROUP BY a.entity_id,
                                          a.book_code,
                                          a.year_month,
                                          a.posting_type_code,
                                          a.proc_id) LOOP
                BEGIN

                    --校正凭证序列
                    proc_correct_voucher_key(p_entity_id,
                                             p_book_code,
                                             p_year_month,
                                             cur_buss_voucher.posting_type_code,
                                             cur_buss_voucher.key_type);

                    proc_account_entry_log(p_entity_id,
                                           p_year_month,
                                           p_task_code,
                                           p_user_id,
                                           p_proc_id,
                                           'proc_entry_voucher INSERT acc_buss_voucher',
                                           '1',
                                           'Start data_count: ' ||
                                           cur_buss_voucher.data_count);
                    --按凭证类型 生成 凭证信息
                    INSERT /*+append */
                    INTO acc_buss_voucher
                    (voucher_id,
                     entity_id,
                     book_code,
                     year_month,
                     posting_type_code,
                     proc_id,
                     voucher_no,
                     effective_date,
                     ext_voucher_no,
                     state,
                     remark,
                     valid_is,
                     audit_state,
                     task_code,
                     create_time,
                     creator_id) --存放的是用户表主键非用户账户
                    SELECT acc_seq_buss_voucher.nextval,
                           entity_id,
                           book_code,
                           year_month,
                           posting_type_code,
                           proc_id,
                           func_generate_voucher_no(entity_id,
                                                    book_code,
                                                    year_month,
                                                    posting_type_code,
                                                    proc_id || '',
                                                    rownum),
                           v_voucher_date,
                           bu_voucher_no,
                           '1' AS state, --1正常 2 被冲  3 冲销
                           NULL AS remark,
                           '1' AS valid_is, --1 有效
                           '1' AS audit_state,
                           p_task_code AS task_code,
                           localtimestamp,
                           p_user_id
                    FROM (SELECT
                              /*+INDEX(a IDX_ACC_BUSS_ENTRY_DATA_MAIN )*/
                              a.entity_id,
                              a.book_code,
                              a.year_month,
                              a.bu_voucher_no,
                              a.posting_type_code,
                              a.proc_id
                          FROM acc_buss_entry_data_success a
                          WHERE a.entity_id = p_entity_id
                            AND a.book_code = p_book_code
                            AND a.year_month = p_year_month
                            AND a.task_code = p_task_code
                            AND a.posting_type_code =
                                cur_buss_voucher.posting_type_code
                            AND a.proc_id = p_proc_id
                            AND a.entry_state = 0
                          GROUP BY a.entity_id,
                                   a.book_code,
                                   a.year_month,
                                   a.bu_voucher_no,
                                   a.posting_type_code,
                                   a.proc_id) t;
                    COMMIT;

                    execute immediate ' truncate table ACC_TEMP_V_VOUCHERDETAIL ';
                    execute immediate ' truncate table ACC_TEMP_ARTICLE ';

                    --凭证分录处理
                    --借方科目
                    proc_account_entry_log(p_entity_id,
                                           p_year_month,
                                           p_task_code,
                                           p_user_id,
                                           p_proc_id,
                                           'proc_entry_voucher INSERT acc_buss_voucher_detail 借方科目 ',
                                           '1',
                                           'Start data_count: ' ||
                                           cur_buss_voucher.data_count);

                    INSERT /*+append */
                    INTO acc_temp_v_voucherdetail
                    (voucher_dtl_id,
                     voucher_id,
                     account_id,
                     currency_code,
                     currency_cu_code,
                     account_entry_code,
                     amount,
                     amount_cu,
                     exchange_rate,
                     remark,
                     entry_data_id,
                     ENTITY_ID,
                     BOOK_CODE,
                     YEAR_MONTH,
                     PROC_ID,
                     TASK_CODE,
                     EXT_VOUCHER_NO,
                     POSTING_TYPE_CODE)
                    /*SELECT acc_seq_buss_voucher_detail.nextval,
         voucher_id, --凭证主表ID
         account_id,
         currency_code,
         currency_cu_code,
         account_entry_code,
         amount,
         amount_cu,
         exchange_rate,
         NULL remark,
         entry_data_id,
         ENTITY_ID   ,
         BOOK_CODE   ,
         YEAR_MONTH    ,
         PROC_ID   ,
         TASK_CODE  ,
         EXT_VOUCHER_NO,
         POSTING_TYPE_CODE
    FROM (select v.voucher_id,
               v.EXT_VOUCHER_NO,
               e.* from acc_buss_voucher v LEFT JOIN (select c.account_id_dr AS account_id, d.* from acc_buss_entry_data_detail c left JOIN (select a.currency_code, --原币
                 a.currency_cu_code, --本位币
                 (CASE
                    WHEN b.amount >= 0 THEN
                     'D'
                    ELSE
                     'C'
                  END) AS account_entry_code,
                 abs(b.amount) AS amount, --原币金额
                 round(abs(b.amount) * a.exchange_rate, 2) AS amount_cu, --本位币金额,
                 a.exchange_rate AS exchange_rate,
                 b.entry_data_id,
                 a.task_code,
                 a.buss_entry_id as BUSS_ENTRY_ID,
                 a.ENTITY_ID as ENTITY_ID,
                 a.BOOK_CODE as BOOK_CODE,
                 a.YEAR_MONTH as YEAR_MONTH,
                 a.posting_type_code as POSTING_TYPE_CODE,
                 a.proc_id as PROC_ID,
                 a.bu_voucher_no as BU_VOUCHER_NO
                 from acc_buss_entry_data_success a
                 LEFT JOIN acc_dap_entry_data b on a.ENTITY_ID = b.ENTITY_ID
                 and  a.entry_data_id = b.entry_data_id
                 where a.ENTITY_ID = p_entity_id
                 AND a.task_code =p_task_code) d
                 ON c.buss_entry_id = d.buss_entry_id ) e
             on v.ENTITY_ID = e.ENTITY_ID  AND v.book_code = e.book_code
             AND v.year_month = e.year_month
             AND v.posting_type_code = e.posting_type_code
             AND v.proc_id = e.proc_id
             AND v.ext_voucher_no = e.bu_voucher_no
            where  v.entity_id = p_entity_id
             AND v.book_code = p_book_code
             AND v.year_month = p_year_month
             AND v.proc_id = p_proc_id
             AND v.posting_type_code = cur_buss_voucher.posting_type_code ) t where coalesce(t.account_id,0)>0;
             */
                    SELECT /*+INDEX(a IDX_ACC_BUSS_ENTRY_DATA_MAIN )*/
                        acc_seq_buss_voucher_detail.nextval,
                        v.voucher_id,
                        c.account_id_dr, --贷方科目编码
                        a.currency_code, --原币
                        a.currency_cu_code, --本位币
                        (CASE
                             WHEN b.amount >= 0 THEN
                                 'D'
                             ELSE
                                 'C'
                            END) AS account_entry_code, --借贷方科目编码(被冲借贷相反)
                        abs(b.amount) AS amount, --原币金额
                        round(abs(b.amount) * a.exchange_rate, 2) AS amount_cu, --本位币金额
                        a.exchange_rate AS exchange_rate,
                        NULL remark,
                        b.entry_data_id,
                        v.ENTITY_ID,
                        v.BOOK_CODE,
                        v.YEAR_MONTH,
                        v.PROC_ID,
                        a.task_code,
                        v.EXT_VOUCHER_NO,
                        v.POSTING_TYPE_CODE
                    FROM acc_buss_voucher v
                             LEFT JOIN acc_buss_entry_data_success a
                                       ON v.entity_id = a.entity_id
                                           AND v.book_code = a.book_code
                                           AND v.year_month = a.year_month
                                           AND v.posting_type_code = a.posting_type_code
                                           AND v.proc_id = a.proc_id
                                           AND (v.ext_voucher_no = a.bu_voucher_no)
                                           AND a.task_code = p_task_code
                                           AND a.entry_state = 0
                             LEFT JOIN acc_buss_entry_data_detail c
                                       ON c.buss_entry_id = a.buss_entry_id
                             LEFT JOIN acc_dap_entry_data b
                                       ON a.entry_data_id = b.entry_data_id
                    WHERE v.entity_id = p_entity_id
                      AND v.book_code = p_book_code
                      AND v.year_month = p_year_month
                      AND v.proc_id = p_proc_id
                      AND v.posting_type_code = cur_buss_voucher.posting_type_code
                      AND v.task_code = p_task_code
                      AND coalesce(c.account_id_dr, 0) > 0;
                    COMMIT;

                    proc_account_entry_log(p_entity_id,
                                           p_year_month,
                                           p_task_code,
                                           p_user_id,
                                           p_proc_id,
                                           'proc_entry_voucher INSERT acc_buss_voucher_detail 贷方科目 ',
                                           '1',
                                           'Start data_count: ' ||
                                           cur_buss_voucher.data_count);

                    --贷方科目
                    INSERT /*+append */
                    INTO acc_temp_v_voucherdetail
                    (voucher_dtl_id,
                     voucher_id,
                     account_id,
                     currency_code,
                     currency_cu_code,
                     account_entry_code,
                     amount,
                     amount_cu,
                     exchange_rate,
                     remark,
                     entry_data_id,
                     ENTITY_ID,
                     BOOK_CODE,
                     YEAR_MONTH,
                     PROC_ID,
                     TASK_CODE,
                     EXT_VOUCHER_NO,
                     POSTING_TYPE_CODE)
                    SELECT /*+INDEX(a IDX_ACC_BUSS_ENTRY_DATA_MAIN )*/
                        acc_seq_buss_voucher_detail.nextval,
                        v.voucher_id,
                        c.account_id_cr, --贷方科目编码
                        a.currency_code, --原币
                        a.currency_cu_code, --本位币
                        (CASE
                             WHEN b.amount >= 0 THEN
                                 'C'
                             ELSE
                                 'D'
                            END) AS account_entry_code, --借贷方科目编码(被冲借贷相反)
                        abs(b.amount) AS amount, --原币金额
                        round(abs(b.amount) * a.exchange_rate, 2) AS amount_cu, --本位币金额
                        a.exchange_rate AS exchange_rate,
                        NULL remark,
                        b.entry_data_id,
                        v.ENTITY_ID,
                        v.BOOK_CODE,
                        v.YEAR_MONTH,
                        v.PROC_ID,
                        a.task_code,
                        v.EXT_VOUCHER_NO,
                        v.POSTING_TYPE_CODE
                    FROM acc_buss_voucher v
                             LEFT JOIN acc_buss_entry_data_success a
                                       ON v.entity_id = a.entity_id
                                           AND v.book_code = a.book_code
                                           AND v.year_month = a.year_month
                                           AND v.posting_type_code = a.posting_type_code
                                           AND v.proc_id = a.proc_id
                                           AND (v.ext_voucher_no = a.bu_voucher_no)
                                           AND a.task_code = p_task_code
                                           AND a.entry_state = 0
                             LEFT JOIN acc_buss_entry_data_detail c
                                       ON c.buss_entry_id = a.buss_entry_id
                             LEFT JOIN acc_dap_entry_data b
                                       ON a.entry_data_id = b.entry_data_id
                    WHERE v.entity_id = p_entity_id
                      AND v.book_code = p_book_code
                      AND v.year_month = p_year_month
                      AND v.proc_id = p_proc_id
                      AND v.posting_type_code = cur_buss_voucher.posting_type_code
                      AND v.task_code = p_task_code
                      AND coalesce(c.account_id_cr, 0) > 0;

                    COMMIT;

                    --更新专项
                    /*proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_voucher update acc_buss_voucher_detail ',
                               '1',
                               'Start data_count: ' ||
                               cur_buss_voucher.data_count);*/

                    --begin

                    /* SELECT listagg(distinct 'dap.' || upper(t.source_column) || ' as ' ||
                       upper(t.mapping_column),
                       ',') within group(order by t.mapping_column, t.source_column, source_table),
               source_table
          into v_sql_art, v_source_table
          FROM bpluser.bbs_conf_model_mapping t
         WHERE source_table = 'ACC_DAP_ENTRY_DATA'
           AND mapping_table = 'ACC_BUSS_VOUCHER_DETAIL'
         group by source_table;

        if instr(v_sql_art, 'ARTICLE17') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE17';
        end if;
        if instr(v_sql_art, 'ARTICLE16') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE16';
        end if;
        if instr(v_sql_art, 'ARTICLE15') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE15';
        end if;
        if instr(v_sql_art, 'ARTICLE14') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE14';
        end if;
        if instr(v_sql_art, 'ARTICLE13') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE13';
        end if;
        if instr(v_sql_art, 'ARTICLE12') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE12';
        end if;
        if instr(v_sql_art, 'ARTICLE11') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE11';
        end if;
        if instr(v_sql_art, 'ARTICLE10') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE10';
        end if;
        if instr(v_sql_art, 'ARTICLE9') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE9';
        end if;
        if instr(v_sql_art, 'ARTICLE8') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE8';
        end if;
        if instr(v_sql_art, 'ARTICLE7') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE7';
        end if;
        if instr(v_sql_art, 'ARTICLE6') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE6';
        end if;
        if instr(v_sql_art, 'ARTICLE5') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE5';
        end if;
        if instr(v_sql_art, 'ARTICLE4') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE4';
        end if;
        if instr(v_sql_art, 'ARTICLE3') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE3';
        end if;
        if instr(v_sql_art, 'ARTICLE2') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE2';
        end if;
        if instr(v_sql_art, 'ARTICLE1') < 1 then
          v_sql_art := v_sql_art || ', NULL AS ARTICLE1';
        end if;
      */
                    /**-- update john 20231122 begin  **/
                    -- v_sql_art := 'select /*+INDEX(dap IDX_ACC_DAP_ENTRY_DATA_QUERY )*/' ||trim(leading ',' from v_sql_art) || ' ,dap.entry_data_id,v.voucher_id,d.VOUCHER_DTL_ID ';
                    -- v_sql_art := 'select /*+INDEX(dap IDX_ACC_DAP_ENTRY_DATA_QUERY )*/' ||trim(leading ',' from v_sql_art) || ' ,dap.entry_data_id,v.voucher_id,d.VOUCHER_DTL_ID ';

                    /* v_sql_detail := ' insert  into ACC_TEMP_ARTICLE '||
        '  '|| v_sql_art ||
        ' from acc_buss_voucher v RIGHT JOIN ACC_TEMP_V_VOUCHERDETAIL d on d.voucher_id = v.voucher_id '||
        ' left join acc_buss_entry_data a on a.entity_id = v.entity_id '||
        ' and a.book_code = v.book_code and a.year_month = v.year_month '||
        ' and a.bu_voucher_no = v.ext_voucher_no  and a.task_code = '''||p_task_code||''''||
        ' left join ACC_DAP_ENTRY_DATA dap on dap.entry_data_id = a.entry_data_id '||
        ' and dap.entity_id = a.entity_id AND dap.year_month = a.year_month '||
        ' AND dap.posting_type_code = a.posting_type_code AND dap.proc_id = a.proc_id '||
        ' where v.entity_id = '||p_entity_id||
        ' and v.book_code = '''||p_book_code||''''||
        ' and v.year_month = '''||p_year_month||''''||
        ' and v.proc_id = '||p_proc_id||
        ' and v.remark = '''||p_task_code||''''||
        ' and v.posting_type_code = '''||cur_buss_voucher.posting_type_code ||'''' ;*/

                    -- execute immediate v_sql_detail;

                    proc_account_entry_log(p_entity_id,
                                           p_year_month,
                                           p_task_code,
                                           p_user_id,
                                           p_proc_id,
                                           'proc_entry_voucher insert acc_temp_article ',
                                           '1',
                                           'Start data_count: ' ||
                                           cur_buss_voucher.data_count);

                    insert /*+append */
                    into ACC_TEMP_ARTICLE
                    select dap.RISK_CODE         as ARTICLE1,
                           dap.OFFSHORE_IS       as ARTICLE12,
                           dap.POSTING_TYPE_CODE as ARTICLE13,
                           dap.EXTEND_COLUMN8    as ARTICLE14,
                           dap.DEPT_CODE         as ARTICLE2,
                           dap.EVALUATE_APPROACH as ARTICLE3,
                           dap.EXTEND_COLUMN4    as ARTICLE4,
                           dap.EXTEND_COLUMN6    as ARTICLE6,
                           dap.CASHFLOW_ARTICLE  as ARTICLE7,
                           dap.PORTFOLIO_NO      as ARTICLE8,
                           dap.ICG_NO            as ARTICLE9,
                           NULL                  AS ARTICLE17,
                           NULL                  AS ARTICLE16,
                           NULL                  AS ARTICLE15,
                           NULL                  AS ARTICLE11,
                           NULL                  AS ARTICLE10,
                           NULL                  AS ARTICLE5,
                           dap.entry_data_id,
                           d.voucher_id,
                           d.VOUCHER_DTL_ID
                    from ACC_DAP_ENTRY_DATA dap
                             left join ACC_TEMP_V_VOUCHERDETAIL d
                                       on dap.entry_data_id = d.entry_data_id
                                           and dap.entity_id = d.entity_id
                                           AND dap.year_month = d.year_month
                                           AND dap.posting_type_code = d.posting_type_code
                                           AND dap.proc_id = d.proc_id
                                           and d.book_code = p_book_code
                    where dap.entity_id = p_entity_id
                      and dap.year_month = p_year_month
                      and dap.proc_id = p_proc_id
                      --and d.task_code = p_task_code
                      and dap.posting_type_code = cur_buss_voucher.posting_type_code
                      and coalesce(d.voucher_dtl_id,0) >0;

                    commit;

                    proc_account_entry_log(p_entity_id,
                                           p_year_month,
                                           p_task_code,
                                           p_user_id,
                                           p_proc_id,
                                           'proc_entry_voucher insert acc_buss_voucher_detail ',
                                           '1',
                                           'Start data_count: ' ||
                                           cur_buss_voucher.data_count);

                    insert /*+append */
                    into acc_buss_voucher_detail
                    select c.VOUCHER_DTL_ID,
                           c.VOUCHER_ID,
                           c.ACCOUNT_ID,
                           c.CURRENCY_CODE,
                           c.CURRENCY_CU_CODE,
                           c.ACCOUNT_ENTRY_CODE,
                           c.AMOUNT,
                           c.AMOUNT_CU,
                           c.EXCHANGE_RATE,
                           c.REMARK,
                           t.ARTICLE1,
                           t.ARTICLE2,
                           t.ARTICLE3,
                           t.ARTICLE4,
                           t.ARTICLE5,
                           t.ARTICLE6,
                           t.ARTICLE7,
                           t.ARTICLE8,
                           t.ARTICLE9,
                           t.ARTICLE10,
                           t.ARTICLE11,
                           t.ARTICLE12,
                           t.ARTICLE13,
                           t.ARTICLE14,
                           t.ARTICLE15,
                           t.ARTICLE16,
                           t.ARTICLE17,
                           c.CREATE_TIME,
                           c.CREATOR_ID,
                           c.UPDATE_TIME,
                           c.UPDATOR_ID,
                           c.ENTRY_DATA_ID,
                           nvl2(t.article1, t.article1 || '/', '') ||
                           nvl2(t.article2, t.article2 || '/', '') ||
                           nvl2(t.article3, t.article3 || '/', '') ||
                           nvl2(t.article4, t.article4 || '/', '') ||
                           nvl2(t.article5, t.article5 || '/', '') ||
                           nvl2(t.article6, t.article6 || '/', '') ||
                           nvl2(t.article7, t.article7 || '/', '') ||
                           nvl2(t.article8, t.article8 || '/', '') ||
                           nvl2(t.article9, t.article9 || '/', '') ||
                           nvl2(t.article10, t.article10 || '/', '') ||
                           nvl2(t.article11, t.article11 || '/', '') ||
                           nvl2(t.article12, t.article12 || '/', '') ||
                           nvl2(t.article13, t.article13 || '/', '') ||
                           nvl2(t.article14, t.article14 || '/', '') ||
                           nvl2(t.article15, t.article15 || '/', '') ||
                           nvl2(t.article16, t.article16 || '/', '') ||
                           nvl2(t.article17, t.article17 || '/', '') as ARTICLE
                    from acc_temp_v_voucherdetail c, acc_temp_ARTICLE t
                    where c.VOUCHER_DTL_ID = t.VOUCHER_DTL_ID;

                    execute immediate ' truncate table ACC_TEMP_V_VOUCHERDETAIL ';
                    execute immediate ' truncate table ACC_TEMP_ARTICLE ';
                    /**-- update john 20231122 end   **/

                    -- v_sql_detail := 'merge /*+ parallel(a, 4) */ into acc_buss_voucher_detail t '||
                    /* ' using ('|| v_sql_art ||
        ' from acc_buss_voucher v left join acc_buss_voucher_detail d on d.voucher_id = v.voucher_id '||
        ' left join acc_buss_entry_data a on a.entity_id = v.entity_id '||
        ' and a.book_code = v.book_code and a.year_month = v.year_month '||
        ' and a.bu_voucher_no = v.ext_voucher_no  and a.task_code = '''||p_task_code||''''||
        ' left join ACC_DAP_ENTRY_DATA dap on dap.entry_data_id = a.entry_data_id '||
        ' and dap.entity_id = a.entity_id AND dap.year_month = a.year_month '||
        ' AND dap.posting_type_code = a.posting_type_code AND dap.proc_id = a.proc_id '||
        ' where v.entity_id = '||p_entity_id||
        ' and v.book_code = '''||p_book_code||''''||
        ' and v.year_month = '''||p_year_month||''''||
        '  and v.proc_id = '||p_proc_id||
        ' and v.remark = '''||p_task_code||''''||
        '  and v.posting_type_code = '''||cur_buss_voucher.posting_type_code||''') c'||

        ' on ( c.VOUCHER_DTL_ID = t.VOUCHER_DTL_ID  )'||
        ' when matched then '||
        ' update set ARTICLE1 = c.ARTICLE1, '||
        ' ARTICLE2 = c.ARTICLE2, '||
        ' ARTICLE3 = c.ARTICLE3, '||
        ' ARTICLE4 = c.ARTICLE4, '||
        ' ARTICLE5 = c.ARTICLE5, '||
        ' ARTICLE6 = c.ARTICLE6, '||
        ' ARTICLE7 = c.ARTICLE7, '||
        ' ARTICLE8 = c.ARTICLE8, '||
        ' ARTICLE9 = c.ARTICLE9, '||
        ' ARTICLE10 = c.ARTICLE10, '||
        ' ARTICLE11 = c.ARTICLE11, '||
        ' ARTICLE12 = c.ARTICLE12, '||
        ' ARTICLE13 = c.ARTICLE13, '||
        ' ARTICLE14 = c.ARTICLE14, '||
        ' ARTICLE15 = c.ARTICLE15, '||
        ' ARTICLE16 = c.ARTICLE16, '||
        ' ARTICLE17 = c.ARTICLE17, '||
        ' ARTICLE = nvl2(c.article1, c.article1||''/'', '''') ||' ||
        ' nvl2(c.article2, c.article2||''/'', '''') ||' ||
        ' nvl2(c.article3, c.article3||''/'', '''') ||' ||
        ' nvl2(c.article4, c.article4||''/'', '''') ||' ||
        ' nvl2(c.article5, c.article5||''/'', '''') ||' ||
        ' nvl2(c.article6, c.article6||''/'', '''') ||' ||
        ' nvl2(c.article7, c.article7||''/'', '''') ||' ||
        ' nvl2(c.article8, c.article8||''/'', '''') ||' ||
        ' nvl2(c.article9, c.article9||''/'', '''') ||' ||
        ' nvl2(c.article10, c.article10||''/'', '''') ||' ||
        ' nvl2(c.article11, c.article11||''/'', '''') ||' ||
        ' nvl2(c.article12, c.article12||''/'', '''') ||' ||
        ' nvl2(c.article13, c.article13||''/'', '''') ||' ||
        ' nvl2(c.article14, c.article14||''/'', '''') ||' ||
        ' nvl2(c.article15, c.article15||''/'', '''') ||' ||
        ' nvl2(c.article16, c.article16||''/'', '''') ||' ||
        ' nvl2(c.article17, c.article17||''/'', '''') '  ;*/

                    --dbms_output.put_line('v_sql_detail: '||length(v_sql_detail)||v_sql_detail );

                    --  execute immediate v_sql_detail;

                    commit;

                    /*exception
                when others then
                  proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
                                   p_user_id, p_proc_id, 'proc_entry_voucher update acc_buss_voucher_detail ', '1',
                                   'Start data_count: '|| cur_buss_voucher.data_count||',excp:'||substr(v_sql_detail,1,3000));
        -        end;*/

                    --更新待入账表信息
                    /*MERGE INTO acc_buss_entry_data t
        USING (SELECT v.voucher_id,
                      v.entity_id,
                      v.book_code,
                      v.year_month,
                      v.posting_type_code,
                      v.proc_id,
                      v.ext_voucher_no
                 FROM acc_buss_voucher v
                WHERE v.entity_id = p_entity_id
                  AND v.book_code = p_book_code
                  AND v.year_month = p_year_month
                  AND v.posting_type_code = cur_buss_voucher.posting_type_code
                  AND v.proc_id = p_proc_id
                  AND v.state = '1'
                  AND v.remark = p_task_code) c
        ON (c.entity_id = t.entity_id AND c.book_code = t.book_code AND c.year_month = t.year_month AND c.posting_type_code = t.posting_type_code AND c.proc_id = t.proc_id AND (c.ext_voucher_no = t.bu_voucher_no ))
        WHEN MATCHED THEN
          UPDATE
             SET t.voucher_id = c.voucher_id, entry_state = 1
           WHERE t.entity_id = p_entity_id
             AND t.book_code = p_book_code
             AND t.year_month = p_year_month
             AND t.posting_type_code = cur_buss_voucher.posting_type_code
             AND t.proc_id = p_proc_id
             AND t.task_code = p_task_code
             AND t.entry_state = 0;
        COMMIT;*/

                    --         proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
                    --                            p_user_id, p_proc_id, 'proc_entry_voucher update acc_buss_entry_data ', '1',
                    --                            'end data_count: '|| cur_buss_voucher.data_count);
                    --计量冲销上个月凭证
                    IF cur_buss_voucher.posting_type_code = '04' THEN
                        proc_account_entry_log(p_entity_id,
                                               p_year_month,
                                               p_task_code,
                                               p_user_id,
                                               p_proc_id,
                                               'proc_entry_voucher  posting_type_code ',
                                               '1',
                                               'start data_count: ' ||
                                               cur_buss_voucher.data_count);
                        proc_voucher_revoke_post_type(p_entity_id,
                                                      p_book_code,
                                                      p_year_month,
                                                      cur_buss_voucher.posting_type_code,
                                                      p_user_id,
                                                      v_message);
                        proc_account_entry_log(p_entity_id,
                                               p_year_month,
                                               p_task_code,
                                               p_user_id,
                                               p_proc_id,
                                               'proc_entry_voucher  posting_type_code ',
                                               '1',
                                               'end data_count: ' ||
                                               cur_buss_voucher.data_count);
                    END IF;
                    proc_account_entry_log(p_entity_id,
                                           p_year_month,
                                           p_task_code,
                                           p_user_id,
                                           p_proc_id,
                                           'proc_entry_voucher truncate temp table end',
                                           '1',
                                           'end data_count: ' ||
                                           cur_buss_voucher.data_count);
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;

                        --删除借贷异常的凭证信息
                        DELETE FROM acc_buss_voucher_detail d
                        WHERE EXISTS (SELECT 1
                                      FROM acc_buss_voucher v
                                      WHERE v.voucher_id = d.voucher_id
                                        AND v.entity_id = p_entity_id
                                        AND v.book_code = p_book_code
                                        AND v.year_month = p_year_month
                                        AND v.remark = p_task_code
                                        AND v.posting_type_code =
                                            cur_buss_voucher.posting_type_code
                                        AND v.proc_id = p_proc_id);
                        COMMIT;

                        --更新待入账表信息
                        /*UPDATE acc_buss_entry_data a
            SET voucher_id = NULL, entry_state = 2
          WHERE a.entity_id = p_entity_id
            AND a.book_code = p_book_code
            AND a.year_month = p_year_month
            AND a.task_code = p_task_code
            AND a.posting_type_code = cur_buss_voucher.posting_type_code
            AND a.proc_id = p_proc_id
            AND a.entry_state = 0
            AND EXISTS
          (SELECT 1
                   FROM acc_buss_voucher v
                  WHERE v.entity_id = p_entity_id
                    AND v.book_code = p_book_code
                    AND v.year_month = p_year_month
                    AND v.remark = p_task_code
                    AND v.posting_type_code = cur_buss_voucher.posting_type_code
                    AND v.proc_id = p_proc_id
                    AND (v.ext_voucher_no = a.bu_voucher_no OR
                        (v.ext_voucher_no IS NULL AND
                        a.bu_voucher_no IS NULL))
                    AND v.voucher_id = a.voucher_id);*/
                        insert into acc_buss_entry_data_err
                        SELECT a.buss_entry_id,
                               a.entry_data_id,
                               a.entity_id,
                               a.book_code,
                               a.year_month,
                               a.task_code,
                               a.proc_id,
                               a.scenario_id,
                               a.account_id_dr,
                               a.account_id_cr,
                               a.posting_type_code,
                               a.scenario_type,
                               null as voucher_id,
                               a.change_time,
                               a.currency_cu_code,
                               a.currency_code,
                               a.exchange_rate,
                               '2' as entry_state,
                               a.entry_msg,
                               a.create_time,
                               a.creator_id,
                               a.update_time,
                               a.updator_id,
                               a.bu_voucher_no,
                               a.entry_date,
                               a.scen_serial_no
                        from acc_buss_entry_data_success a
                        WHERE a.entity_id = p_entity_id
                          AND a.book_code = p_book_code
                          AND a.year_month = p_year_month
                          AND a.task_code = p_task_code
                          AND a.posting_type_code = cur_buss_voucher.posting_type_code
                          AND a.proc_id = p_proc_id
                          AND a.entry_state = 0
                          AND EXISTS (SELECT 1
                                      FROM acc_buss_voucher v
                                      WHERE v.entity_id = p_entity_id
                                        AND v.book_code = p_book_code
                                        AND v.year_month = p_year_month
                                        AND v.remark = p_task_code
                                        AND v.posting_type_code =
                                            cur_buss_voucher.posting_type_code
                                        AND v.proc_id = p_proc_id
                                        AND (v.ext_voucher_no = a.bu_voucher_no OR
                                             (v.ext_voucher_no IS NULL AND
                                              a.bu_voucher_no IS NULL))
                                        AND v.voucher_id = a.voucher_id);
                        COMMIT;

                        DELETE FROM acc_buss_entry_data_success a
                        WHERE a.entity_id = p_entity_id
                          AND a.book_code = p_book_code
                          AND a.year_month = p_year_month
                          AND a.task_code = p_task_code
                          AND a.posting_type_code = cur_buss_voucher.posting_type_code
                          AND a.proc_id = p_proc_id
                          AND a.entry_state = 0
                          AND EXISTS (SELECT 1
                                      FROM acc_buss_voucher v
                                      WHERE v.entity_id = p_entity_id
                                        AND v.book_code = p_book_code
                                        AND v.year_month = p_year_month
                                        AND v.remark = p_task_code
                                        AND v.posting_type_code =
                                            cur_buss_voucher.posting_type_code
                                        AND v.proc_id = p_proc_id
                                        AND (v.ext_voucher_no = a.bu_voucher_no OR
                                             (v.ext_voucher_no IS NULL AND
                                              a.bu_voucher_no IS NULL))
                                        AND v.voucher_id = a.voucher_id);
                        commit;

                        DELETE FROM acc_buss_voucher v
                        WHERE v.entity_id = p_entity_id
                          AND v.book_code = p_book_code
                          AND v.year_month = p_year_month
                          AND v.remark = p_task_code
                          AND v.posting_type_code = cur_buss_voucher.posting_type_code
                          AND v.proc_id = p_proc_id;
                        COMMIT;

                        --插入失败记录子信息 按照凭证号码写入
                        bpluser.bpl_pack_action_log.proc_add_actionlogdetails(v_log_id,
                            --日志主表主键ID
                                                                              NULL,
                            --校验规则编码
                                                                              '2',
                                                                              NULL,
                            --校验脚本
                                                                              '0',
                            --状态 0 失败 1 成功
                                                                              SQLERRM,
                            --异常原因  入账成功.
                                                                              '' ||
                                                                              p_proc_id ||
                                                                              cur_buss_voucher.posting_type_code,
                            --异常业务主键
                                                                              p_user_id
                            --创建人员
                            );
                        --提示异常信息
                        proc_account_entry_log(p_entity_id,
                                               p_year_month,
                                               p_task_code,
                                               p_user_id,
                                               p_proc_id,
                                               'proc_entry_voucher',
                                               '2',
                                               SQLERRM);

                END;

                --校正凭证序列
                proc_correct_voucher_key(p_entity_id,
                                         p_book_code,
                                         p_year_month,
                                         cur_buss_voucher.posting_type_code,
                                         cur_buss_voucher.key_type);

                proc_account_entry_log(p_entity_id,
                                       p_year_month,
                                       p_task_code,
                                       p_user_id,
                                       p_proc_id,
                                       'proc_entry_voucher insert acc_buss_entry_data ',
                                       '1',
                                       'end data_count: ' ||
                                       cur_buss_voucher.data_count);

                insert /*+append */
                into acc_buss_entry_data
                SELECT c.buss_entry_id,
                       c.entry_data_id,
                       c.entity_id,
                       c.book_code,
                       c.year_month,
                       c.task_code,
                       c.proc_id,
                       c.scenario_id,
                       c.account_id_dr,
                       c.account_id_cr,
                       c.posting_type_code,
                       c.scenario_type,
                       v.voucher_id,
                       c.change_time,
                       c.currency_cu_code,
                       c.currency_code,
                       c.exchange_rate,
                       '1' as entry_state,
                       c.entry_msg,
                       c.create_time,
                       c.creator_id,
                       c.update_time,
                       c.updator_id,
                       c.bu_voucher_no,
                       c.entry_date,
                       c.scen_serial_no
                /*from acc_buss_voucher v , acc_buss_entry_data_success c
    where
    c.entity_id = v.entity_id
    AND c.book_code = v.book_code
    AND c.year_month = v.year_month
    AND c.posting_type_code = v.posting_type_code
    AND c.proc_id = v.proc_id
    AND c.bu_voucher_no = v.ext_voucher_no
    and c.task_code = p_task_code
    and c.entry_state = '0'
    and v.entity_id = p_entity_id
    AND v.book_code = p_book_code
    AND v.year_month = p_year_month
    AND v.posting_type_code = cur_buss_voucher.posting_type_code
    AND v.proc_id = p_proc_id
    AND v.state = '1'
    AND v.task_code = p_task_code;*/
                FROM acc_buss_entry_data_success c
                         LEFT JOIN acc_buss_voucher v
                                   ON c.task_code = v.task_code
                                       --AND c.posting_type_code = v.posting_type_code
                                       --AND c.proc_id = v.proc_id
                                       AND c.bu_voucher_no = v.ext_voucher_no
                                       AND v.state = '1'
                where c.task_code = p_task_code
                  AND c.posting_type_code = cur_buss_voucher.posting_type_code
                  AND c.proc_id = p_proc_id;

                commit;

            END LOOP;

        --execute immediate ' truncate table acc_buss_entry_data_all ';
        --execute immediate ' truncate table acc_buss_entry_data_success ';
        --execute immediate ' truncate table acc_buss_entry_data_err ';

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_voucher',
                               '1',
                               'End');
    EXCEPTION
        WHEN OTHERS THEN
            --提示异常信息
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_voucher',
                                   '2',
                                   SQLERRM);
    END proc_entry_voucher;

    PROCEDURE proc_entry_voucher_profit_loss(p_entity_id  IN NUMBER,
                                             p_book_code  IN VARCHAR2,
                                             p_year_month IN VARCHAR2,
                                             p_user_id    IN NUMBER) IS

        /***********************************************************************
      NAME :proc_entry_voucher_profit_loss
      DESCRIPTION :生成兑换损益凭证处理
      DATE :2022-4-8
      AUTHOR :CHENJUNFENG
      -------
      MODIFY LOG
      UPDATE DATE : 2023-03-01
      UPDATE BY : LY
      UPDATE DESC :
    ***********************************************************************/
        v_voucher_no        VARCHAR(32);
        v_voucher_date      DATE;
        v_voucher_id        NUMERIC;
        v_exch_date         DATE;
        v_debit_amount_cu   VARCHAR(3);
        v_credit_amount_cu  VARCHAR(3);
        v_posting_type_code VARCHAR(32) := '99'; --凭证类型
        v_proc_id           NUMBER; --数据类型，生成凭证号需要

        v_account_id            NUMBER; --默认兑换损益科目
        v_revoke_voucher_no     VARCHAR(32);
        v_revoke_new_voucher_no VARCHAR(400);

    BEGIN
        v_proc_id := bpluser.bpl_pack_common.func_get_procid('ACC_ACCOUNTENTRY_GAIN_LOSS');

        SELECT account_id
        INTO v_account_id
        FROM bpluser.bbs_v_account item
        WHERE account_code = '4303/02/' --TODO 汇兑相关科目不能硬编码
          AND entity_id = p_entity_id
          AND book_code = p_book_code;

        v_exch_date := last_day(to_date(p_year_month || '01', 'YYYYMMDD'));

        SELECT MAX(voucher_no)
        INTO v_revoke_voucher_no
        FROM acc_buss_voucher
        WHERE entity_id = p_entity_id
          AND book_code = p_book_code
          AND year_month = p_year_month
          AND posting_type_code = v_posting_type_code
          AND proc_id = v_proc_id
          AND state = '1';

        --存在正常的汇兑损益凭证，需要做冲销
        IF v_revoke_voucher_no IS NOT NULL THEN
            --兑换损益凭证冲销处理
            proc_voucher_revoke_voucher_no(p_entity_id,
                                           p_book_code,
                                           p_year_month,
                                           v_revoke_voucher_no,
                                           '',
                                           '',
                                           p_user_id,
                                           v_revoke_new_voucher_no);
            IF v_revoke_new_voucher_no IS NULL THEN

                --RAISE NOTICE '凭证冲销失败，v_revoke_voucher_no:%,v_massage:%',v_revoke_voucher_no,v_massage;
                dbms_output.put_line('凭证冲销失败：v_revoke_voucher_no:' ||
                                     v_revoke_voucher_no || ';');
                RETURN;
            END IF;
        END IF;

        --判断是否需要制证
        SELECT round(SUM(t.debit_amount *
                         acc_pack_common.func_get_exch_rate(t.entity_id,
                                                            v_exch_date,
                                                            t.currency_code,
                                                            currency_cu_code,
                                                            '2')),
                     2) - SUM(t.debit_amount_cu),
               round(SUM(t.credit_amount *
                         acc_pack_common.func_get_exch_rate(t.entity_id,
                                                            v_exch_date,
                                                            t.currency_code,
                                                            currency_cu_code,
                                                            '2')),
                     2) - SUM(credit_amount_cu)
        INTO v_debit_amount_cu, v_credit_amount_cu
        FROM acc_buss_ledger_balance t
        WHERE t.book_code = p_book_code
          AND t.entity_id = p_entity_id
          AND t.year_month = p_year_month;

        --RAISE NOTICE '条件，v_debit_amount_cu: %,v_credit_amount_cu:%',v_debit_amount_cu,v_credit_amount_cu;
        IF (v_debit_amount_cu != 0 OR v_credit_amount_cu != 0) AND
           v_debit_amount_cu <> v_credit_amount_cu THEN

            --获取凭证日期
            v_voucher_date := acc_pack_common.func_correct_voucher_date(p_year_month);

            --凭证新主键ID
            v_voucher_id := acc_seq_buss_voucher.nextval;
            --RAISE NOTICE '生成凭证id，VOUCHER_ID: %,v_voucher_no:%',v_voucher_id,v_voucher_no;

            --校正凭证序列
            proc_correct_voucher_key(p_entity_id,
                                     p_book_code,
                                     p_year_month,
                                     v_posting_type_code,
                                     v_proc_id || '');
            --获取凭证号码
            v_voucher_no := func_generate_voucher_no(p_entity_id,
                                                     p_book_code,
                                                     p_year_month,
                                                     v_posting_type_code,
                                                     v_proc_id || '',
                                                     1);

            INSERT INTO acc_buss_voucher
            (voucher_id,
             entity_id,
             book_code,
             year_month,
             posting_type_code,
             proc_id,
             voucher_no,
             effective_date,
             state,
             remark,
             valid_is,
             audit_state,
             create_time,
             creator_id)
            SELECT v_voucher_id,
                   p_entity_id,
                   p_book_code,
                   p_year_month,
                   v_posting_type_code, --凭证类型
                   v_proc_id, --数据类型
                   v_voucher_no,
                   v_voucher_date,
                   '1' AS state, --1正常 2 被冲  3 冲销
                   NULL AS remark,
                   '1' AS valid_is, --1 有效
                   '1' AS audit_state,
                   localtimestamp,
                   p_user_id
            FROM dual;
            COMMIT;

            BEGIN
                --凭证分录处理
                --RAISE NOTICE '插入凭证明细表资产负债借方数据成功，acc_buss_voucher_detail: %','1. 资产负债借方';

                --1. 资产负债借方
                INSERT INTO acc_buss_voucher_detail
                (voucher_dtl_id,
                 voucher_id,
                 account_id,
                 currency_code,
                 currency_cu_code,
                 account_entry_code,
                 amount,
                 amount_cu,
                 exchange_rate,
                 remark,
                 article,
                 article1,
                 article2,
                 article3,
                 article4,
                 article5,
                 article6,
                 article7,
                 article8,
                 article9,
                 article10,
                 article11,
                 article12,
                 article13,
                 article14,
                 article15,
                 article16,
                 create_time,
                 creator_id,
                 update_time,
                 updator_id)
                SELECT acc_seq_buss_voucher_detail.nextval,
                       voucher_id,
                       account_id,
                       currency_code,
                       currency_cu_code,
                       account_entry_code,
                       amount,
                       amount_cu,
                       exchange_rate,
                       remark,
                       nvl2(article1, article1 || '/', '') ||
                       nvl2(article2, article2 || '/', '') ||
                       nvl2(article3, article3 || '/', '') ||
                       nvl2(article4, article4 || '/', '') ||
                       nvl2(article5, article5 || '/', '') ||
                       nvl2(article6, article6 || '/', '') ||
                       nvl2(article7, article7 || '/', '') ||
                       nvl2(article8, article8 || '/', '') ||
                       nvl2(article9, article9 || '/', '') ||
                       nvl2(article10, article10 || '/', '') ||
                       nvl2(article11, article11 || '/', '') ||
                       nvl2(article12, article12 || '/', '') ||
                       nvl2(article13, article13 || '/', '') ||
                       nvl2(article14, article14 || '/', '') ||
                       nvl2(article15, article15 || '/', '') ||
                       nvl2(article16, article16 || '/', '') ||
                       nvl2(article17, article17 || '/', '') AS article,
                       article1,
                       article2,
                       article3,
                       article4,
                       article5,
                       article6,
                       article7,
                       article8,
                       article9,
                       article10,
                       article11,
                       article12,
                       article13,
                       article14,
                       article15,
                       article16,
                       localtimestamp create_time,
                       p_user_id creator_id,
                       NULL update_time,
                       NULL updator_id
                FROM (SELECT v_voucher_id voucher_id, --新凭证主表ID
                             a.account_id, --借方科目编码
                             a.currency_code, --原币
                             a.currency_cu_code, --本位币
                             (CASE
                                  WHEN (round(SUM(a.debit_amount *
                                                  acc_pack_common.func_get_exch_rate(a.entity_id,
                                                                                     v_exch_date,
                                                                                     a.currency_code,
                                                                                     a.currency_cu_code,
                                                                                     '2')),
                                              2) - SUM(a.debit_amount_cu)) >= 0 THEN
                                      'D'
                                  ELSE
                                      'C'
                                 END) AS account_entry_code,
                             0.00 AS amount, --原币金额
                             abs(round(SUM(a.debit_amount *
                                           acc_pack_common.func_get_exch_rate(a.entity_id,
                                                                              v_exch_date,
                                                                              a.currency_code,
                                                                              a.currency_cu_code,
                                                                              '2')),
                                       2) - SUM(a.debit_amount_cu)) AS amount_cu, --本位币金额
                             acc_pack_common.func_get_exch_rate(a.entity_id,
                                                                v_exch_date,
                                                                a.currency_code,
                                                                a.currency_cu_code,
                                                                '2') AS exchange_rate,
                             NULL remark,
                             NULL article1,
                             NULL article2,
                             NULL article3,
                             NULL article4,
                             NULL article5,
                             NULL article6,
                             NULL article7,
                             NULL article8,
                             NULL article9,
                             NULL article10,
                             NULL article11,
                             NULL article12,
                             NULL article13,
                             NULL article14,
                             NULL article15,
                             NULL article16,
                             NULL article17
                      FROM acc_buss_ledger_balance a
                               LEFT JOIN bpluser.bbs_v_account b
                                         ON a.account_id = b.account_id
                      WHERE a.entity_id = p_entity_id
                        AND a.book_code = p_book_code
                        AND a.year_month = p_year_month
                        AND (b.account_code LIKE '1%' OR
                             b.account_code LIKE '2%') --资产、负债类
                        AND b.final_level_is = '1'
                        AND b.book_code = a.book_code
                        AND a.debit_amount > 0
                        AND a.currency_code != a.currency_cu_code --原币和折币不一样
                      GROUP BY a.entity_id,
                               a.account_id,
                               a.currency_code,
                               a.currency_cu_code) t;

                --RAISE NOTICE '插入凭证明细表资产负债贷方数据成功，acc_buss_voucher_detail: %','2. 资产负债贷方';
                --2. 资产负债贷方
                INSERT INTO acc_buss_voucher_detail
                (voucher_dtl_id,
                 voucher_id,
                 account_id,
                 currency_code,
                 currency_cu_code,
                 account_entry_code,
                 amount,
                 amount_cu,
                 exchange_rate,
                 remark,
                 article,
                 article1,
                 article2,
                 article3,
                 article4,
                 article5,
                 article6,
                 article7,
                 article8,
                 article9,
                 article10,
                 article11,
                 article12,
                 article13,
                 article14,
                 article15,
                 article16,
                 article17,
                 create_time,
                 creator_id,
                 update_time,
                 updator_id)
                SELECT acc_seq_buss_voucher_detail.nextval,
                       voucher_id,
                       account_id,
                       currency_code,
                       currency_cu_code,
                       account_entry_code,
                       amount,
                       amount_cu,
                       exchange_rate,
                       remark,
                       nvl2(article1, article1 || '/', '') ||
                       nvl2(article2, article2 || '/', '') ||
                       nvl2(article3, article3 || '/', '') ||
                       nvl2(article4, article4 || '/', '') ||
                       nvl2(article5, article5 || '/', '') ||
                       nvl2(article6, article6 || '/', '') ||
                       nvl2(article7, article7 || '/', '') ||
                       nvl2(article8, article8 || '/', '') ||
                       nvl2(article9, article9 || '/', '') ||
                       nvl2(article10, article10 || '/', '') ||
                       nvl2(article11, article11 || '/', '') ||
                       nvl2(article12, article12 || '/', '') ||
                       nvl2(article13, article13 || '/', '') ||
                       nvl2(article14, article14 || '/', '') ||
                       nvl2(article15, article15 || '/', '') ||
                       nvl2(article16, article16 || '/', '') ||
                       nvl2(article17, article17 || '/', '') AS article,
                       article1,
                       article2,
                       article3,
                       article4,
                       article5,
                       article6,
                       article7,
                       article8,
                       article9,
                       article10,
                       article11,
                       article12,
                       article13,
                       article14,
                       article15,
                       article16,
                       article17,
                       localtimestamp create_time,
                       p_user_id creator_id,
                       NULL update_time,
                       NULL updator_id
                FROM (SELECT v_voucher_id voucher_id, --新凭证主表ID
                             a.account_id, --借方科目编码
                             a.currency_code, --原币
                             a.currency_cu_code, --本位币
                             (CASE
                                  WHEN (round(SUM(a.credit_amount *
                                                  acc_pack_common.func_get_exch_rate(a.entity_id,
                                                                                     v_exch_date,
                                                                                     a.currency_code,
                                                                                     a.currency_cu_code,
                                                                                     '2')),
                                              2) - SUM(a.credit_amount_cu)) >= 0 THEN
                                      'C'
                                  ELSE
                                      'D'
                                 END) AS account_entry_code,
                             0.00 AS amount, --原币金额
                             abs(round(SUM(a.credit_amount *
                                           acc_pack_common.func_get_exch_rate(a.entity_id,
                                                                              v_exch_date,
                                                                              a.currency_code,
                                                                              a.currency_cu_code,
                                                                              '2')),
                                       2) - SUM(a.credit_amount_cu)) AS amount_cu, --本位币金额
                             acc_pack_common.func_get_exch_rate(a.entity_id,
                                                                v_exch_date,
                                                                a.currency_code,
                                                                a.currency_cu_code,
                                                                '2') AS exchange_rate,
                             NULL remark,
                             NULL article1,
                             NULL article2,
                             NULL article3,
                             NULL article4,
                             NULL article5,
                             NULL article6,
                             NULL article7,
                             NULL article8,
                             NULL article9,
                             NULL article10,
                             NULL article11,
                             NULL article12,
                             NULL article13,
                             NULL article14,
                             NULL article15,
                             NULL article16,
                             NULL article17
                      FROM acc_buss_ledger_balance a
                               LEFT JOIN bpluser.bbs_v_account b
                                         ON a.account_id = b.account_id
                      WHERE a.entity_id = p_entity_id
                        AND a.book_code = p_book_code
                        AND a.year_month = p_year_month
                        AND (b.account_code LIKE '1%' OR
                             b.account_code LIKE '2%') --资产、负债类
                        AND b.final_level_is = '1'
                        AND b.book_code = a.book_code
                        AND a.credit_amount > 0
                        AND a.currency_code != a.currency_cu_code --原币和折币不一样
                      GROUP BY a.account_id,
                               a.currency_code,
                               a.currency_cu_code,
                               a.entity_id) t;

                --RAISE NOTICE '插入凭证明细表损益科目数据成功，acc_buss_voucher_detail: %','3. 损益科目';
                --3. 损益科目
                INSERT INTO acc_buss_voucher_detail
                (voucher_dtl_id,
                 voucher_id,
                 account_id,
                 currency_code,
                 currency_cu_code,
                 account_entry_code,
                 amount,
                 amount_cu,
                 exchange_rate,
                 remark,
                    --PORTFOLIO_NO,
                    --ICG_NO,
                 article,
                 article1,
                 article2,
                 article3,
                 article4,
                 article5,
                 article6,
                 article7,
                 article8,
                 article9,
                 article10,
                 article11,
                 article12,
                 article13,
                 article14,
                 article15,
                 article16,
                 article17,
                 create_time,
                 creator_id,
                 update_time,
                 updator_id)
                SELECT acc_seq_buss_voucher_detail.nextval,
                       voucher_id,
                       account_id,
                       currency_code,
                       currency_cu_code,
                       account_entry_code,
                       amount,
                       amount_cu,
                       exchange_rate,
                       remark,
                       --PORTFOLIO_NO,
                       --ICG_NO,
                       nvl2(article1, article1 || '/', '') ||
                       nvl2(article2, article2 || '/', '') ||
                       nvl2(article3, article3 || '/', '') ||
                       nvl2(article4, article4 || '/', '') ||
                       nvl2(article5, article5 || '/', '') ||
                       nvl2(article6, article6 || '/', '') ||
                       nvl2(article7, article7 || '/', '') ||
                       nvl2(article8, article8 || '/', '') ||
                       nvl2(article9, article9 || '/', '') ||
                       nvl2(article10, article10 || '/', '') ||
                       nvl2(article11, article11 || '/', '') ||
                       nvl2(article12, article12 || '/', '') ||
                       nvl2(article13, article13 || '/', '') ||
                       nvl2(article14, article14 || '/', '') ||
                       nvl2(article15, article15 || '/', '') ||
                       nvl2(article16, article16 || '/', '') ||
                       nvl2(article17, article17 || '/', '') AS article,
                       article1,
                       article2,
                       article3,
                       article4,
                       article5,
                       article6,
                       article7,
                       article8,
                       article9,
                       article10,
                       article11,
                       article12,
                       article13,
                       article14,
                       article15,
                       article16,
                       article17,
                       localtimestamp create_time,
                       p_user_id creator_id,
                       NULL update_time,
                       NULL updator_id
                FROM (SELECT v_voucher_id voucher_id, --新凭证主表ID
                             v_account_id AS account_id, --借方科目编码
                             a.currency_cu_code AS currency_code, --原币
                             a.currency_cu_code, --本位币
                             (CASE
                                  WHEN SUM(CASE
                                               WHEN a.account_entry_code = 'D' THEN
                                                   a.amount_cu
                                               ELSE
                                                   0
                                      END) - SUM(CASE
                                                     WHEN a.account_entry_code = 'C' THEN
                                                         a.amount_cu
                                                     ELSE
                                                         0
                                      END) <= 0 THEN
                                      'D'
                                  ELSE
                                      'C'
                                 END) AS account_entry_code, --两层sum有具体数据再确定要哪一层
                             0.00 AS amount, --原币金额
                             abs(SUM(CASE
                                         WHEN a.account_entry_code = 'D' THEN
                                             a.amount_cu
                                         ELSE
                                             0
                                 END) - SUM(CASE
                                                WHEN a.account_entry_code = 'C' THEN
                                                    a.amount_cu
                                                ELSE
                                                    0
                                 END)) AS amount_cu, --本位币金额
                             1.00 AS exchange_rate,
                             NULL remark,
                             --null PORTFOLIO_NO,
                             -- null ICG_NO,
                             NULL article1,
                             NULL article2,
                             NULL article3,
                             NULL article4,
                             NULL article5,
                             NULL article6,
                             NULL article7,
                             NULL article8,
                             NULL article9,
                             NULL article10,
                             NULL article11,
                             NULL article12,
                             NULL article13,
                             NULL article14,
                             NULL article15,
                             NULL article16,
                             NULL article17
                      FROM acc_buss_voucher_detail a
                      WHERE 1 = 1
                        AND a.voucher_id = v_voucher_id
                      GROUP BY a.currency_cu_code);

                COMMIT;

            EXCEPTION
                WHEN OTHERS THEN

                    --删除借贷异常的凭证信息
                    DELETE FROM acc_buss_voucher_detail
                    WHERE voucher_id = v_voucher_id;
                    DELETE FROM acc_buss_voucher WHERE voucher_id = v_voucher_id;
                    COMMIT;

            END;

            --校正凭证序列
            proc_correct_voucher_key(p_entity_id,
                                     p_book_code,
                                     p_year_month,
                                     v_posting_type_code,
                                     v_proc_id || '');
        ELSE
            --RAISE NOTICE '无凭证数据需要做兑换损益凭证，v_debit_amount_cu: %,v_credit_amount_cu:%',v_debit_amount_cu , v_credit_amount_cu;
            dbms_output.put_line('无凭证数据需要做兑换损益凭证');
        END IF;

    EXCEPTION
        WHEN OTHERS THEN
            --提示异常信息
            --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
            --RAISE EXCEPTION '[EXCEPTION]acc_pack_voucher_proc_entry_voucher_profit_loss：%; %',v_text1,v_text2;
            dbms_output.put_line('[EXCEPTION]proc_entry_voucher_profit_loss：' ||
                                 to_char(SQLCODE) || ';' ||
                                 substr(SQLERRM, 1, 200));
    END proc_entry_voucher_profit_loss;

    PROCEDURE proc_voucher_revoke_voucher_no(p_entity_id      IN NUMBER,
                                             p_book_code      IN VARCHAR2,
                                             p_year_month     IN VARCHAR2,
                                             p_voucher_no     IN VARCHAR2,
                                             p_revoke_message IN VARCHAR2,
                                             p_task_code      IN VARCHAR2,
                                             p_user_id        IN NUMBER,
                                             p_new_voucher_no IN OUT VARCHAR2) IS
        /***********************************************************************
    NAME : proc_voucher_revoke_voucher_no
    DESCRIPTION :凭证冲销处理
    DATE :2022-4-12
    AUTHOR :CHENJUNFENG
    -------
    MODIFY LOG
    UPDATE DATE : 2023-03-01
    UPDATE BY : LY
    UPDATE DESC :
    ***********************************************************************/
        v_voucher_id NUMBER(10);

        v_posting_type_code VARCHAR(32); --凭证类型
        v_proc_id           NUMBER;
    BEGIN

        --凭证新主键ID
        v_voucher_id := acc_seq_buss_voucher.nextval;

        --检查凭证信息
        SELECT MAX(a.posting_type_code), MAX(a.proc_id)
        INTO v_posting_type_code, v_proc_id
        FROM acc_buss_voucher a
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.voucher_no = p_voucher_no
          AND a.state = '1';

        --校正凭证序列
        proc_correct_voucher_key(p_entity_id,
                                 p_book_code,
                                 p_year_month,
                                 v_posting_type_code,
                                 v_proc_id || '');

        --获取凭证号码
        p_new_voucher_no := func_generate_voucher_no(p_entity_id,
                                                     p_book_code,
                                                     p_year_month,
                                                     v_posting_type_code,
                                                     v_proc_id || '',
                                                     1);
        --冲销凭证主表插入
        INSERT INTO acc_buss_voucher
        (voucher_id,
         entity_id,
         book_code,
         posting_type_code,
         proc_id,
         voucher_no,
         year_month,
         effective_date,
         state,
         remark,
         valid_is,
         audit_state,
         task_code,
         create_time,
         creator_id,
         ext_voucher_no)
        SELECT v_voucher_id,
               entity_id,
               book_code,
               posting_type_code,
               proc_id,
               p_new_voucher_no,
               p_year_month,
               acc_pack_common.func_correct_voucher_date(p_year_month),
               '3', --1正常 2 被冲  3 冲销
               NULL, --此值后续需要要到
               '1' AS valid_is, --1 有效
               '1' AS audit_state,
               p_task_code AS task_code, --此值后续需要要到
               localtimestamp,
               p_user_id,
               a.ext_voucher_no
        FROM acc_buss_voucher a
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.voucher_no = p_voucher_no
          AND a.state = '1';
        --COMMIT;

        --新凭证分录信息采集并存入凭证分录表 - 3-冲销状态
        INSERT INTO acc_buss_voucher_detail
        (voucher_dtl_id,
         voucher_id,
         account_id,
         currency_code,
         currency_cu_code,
         account_entry_code,
         amount,
         amount_cu,
         exchange_rate,
         remark,
         article,
         article1,
         article2,
         article3,
         article4,
         article5,
         article6,
         article7,
         article8,
         article9,
         article10,
         article11,
         article12,
         article13,
         article14,
         article15,
         article16,
         article17,
         entry_data_id)
        SELECT acc_seq_buss_voucher_detail.nextval,
               c.voucher_id, --新凭证主表ID
               a.account_id,
               a.currency_code,
               a.currency_cu_code,
               (CASE
                    WHEN a.account_entry_code = 'D' THEN
                        'C'
                    ELSE
                        'D'
                   END) account_entry_code,
               a.amount,
               a.amount_cu,
               a.exchange_rate,
               a.remark,
               a.article,
               a.article1,
               a.article2,
               a.article3,
               a.article4,
               a.article5,
               a.article6,
               a.article7,
               a.article8,
               a.article9,
               a.article10,
               a.article11,
               a.article12,
               a.article13,
               a.article14,
               a.article15,
               a.article16,
               a.article17,
               a.entry_data_id
        FROM acc_buss_voucher_detail a
                 LEFT JOIN acc_buss_voucher b
                           ON b.voucher_id = a.voucher_id
                 LEFT JOIN acc_buss_voucher c
                           ON c.entity_id = b.entity_id
                               AND c.book_code = b.book_code
                               AND c.year_month = p_year_month
                               AND c.posting_type_code = b.posting_type_code
                               AND c.proc_id = b.proc_id
                               AND c.voucher_no = p_new_voucher_no
                               AND c.state = '3'
        WHERE b.entity_id = p_entity_id
          AND b.book_code = p_book_code
          --AND b.year_month = p_year_month
          AND b.voucher_no = p_voucher_no
          AND b.state = '1';
        --COMMIT;

        --记录冲销凭证
        INSERT INTO acc_buss_voucherhis
        (his_id,
         entity_id,
         book_code,
         year_month,
         old_voucher_no,
         re_voucher_no,
         voucher_no,
         oper_message,
         create_time,
         creator_id)
        SELECT acc_seq_buss_voucherhis.nextval,
               a.entity_id,
               a.book_code,
               a.year_month,
               a.voucher_no,
               c.voucher_no,
               '', --此处依然是冲销凭证，后面生成新凭证需要更新
               p_revoke_message,
               localtimestamp,
               p_user_id
        FROM acc_buss_voucher a
                 LEFT JOIN acc_buss_voucher c
                           ON c.entity_id = a.entity_id
                               AND c.book_code = a.book_code
                               AND c.year_month = p_year_month
                               AND c.posting_type_code = a.posting_type_code
                               AND c.proc_id = a.proc_id
                               AND (c.ext_voucher_no = a.ext_voucher_no OR
                                    (c.ext_voucher_no IS NULL AND a.ext_voucher_no IS NULL))
                               AND c.state = '3'
                               and c.task_code = p_task_code
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.voucher_no = p_voucher_no
          AND a.state = '1';

        --更新正常状态为2-被冲销状态
        UPDATE acc_buss_voucher a
        SET state = '2', remark = p_revoke_message
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.voucher_no = p_voucher_no
          AND a.state = '1';
        --COMMIT;

        --RAISE NOTICE '凭证冲销，冲销凭证:%,凭证ID:%,新凭证:%',p_voucher_no,v_voucher_id,p_new_voucher_no;
        dbms_output.put_line('凭证冲销，冲销凭证:' || p_voucher_no || ',凭证ID:' ||
                             v_voucher_id || ',新凭证:' || p_new_voucher_no);

        --校正凭证序列
        proc_correct_voucher_key(p_entity_id,
                                 p_book_code,
                                 p_year_month,
                                 v_posting_type_code,
                                 v_proc_id || '');
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK; --对冲要整个流程正常跑完才能commit，如果异常，中途所有产生的数据都需要回滚
            p_new_voucher_no := NULL;

            --抛出异常提示信息
            --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;

            --RAISE EXCEPTION '[EXCEPTION]acc_pack_voucher_proc_voucher_revoke_voucher_no：%; %',v_text1,v_text2;
            dbms_output.put_line('[EXCEPTION]acc_pack_voucher_proc_voucher_revoke_voucher_no：' ||
                                 to_char(SQLCODE) || ';' ||
                                 substr(SQLERRM, 1, 200));

    END proc_voucher_revoke_voucher_no;

    PROCEDURE proc_voucher_revoke_post_type(p_entity_id         IN NUMBER,
                                            p_book_code         IN VARCHAR2,
                                            p_year_month        IN VARCHAR2,
                                            p_posting_type_code IN VARCHAR2,
                                            p_user_id           IN NUMBER,
                                            p_massage           IN OUT VARCHAR2) IS
        /***********************************************************************
      NAME :proc_voucher_revoke_posting_type_code
      DESCRIPTION :凭证冲销处理
      DATE :2022-09-15
      AUTHOR :CHENJUNFENG
      BUSINESS RULE : 根据传递的凭证类型、会计年月进行冲销,生成冲销凭证,并记录操作信息
      p_posting_type_code:  凭证类型
      p_year_month: 当前会计期间
      p_book_code:  账号号
      p_user_id:    用户ID
      p_revoke_message: 冲销结果  1-成功  非1 输出异常原因
      -------
      MODIFY LOG
      UPDATE DATE : 2023-03-01
      UPDATE BY : LY
      UPDATE DESC :
    ***********************************************************************/

        v_taskcode       VARCHAR2(32);
        v_log_id         NUMBER(10);
        v_proc_id        NUMBER(10);
        v_parent_proc_id NUMBER(10);
        v_pre_year_month VARCHAR2(6);
        v_voucher_date   DATE;

    BEGIN

        IF p_posting_type_code = '07' THEN
            --手工凭证不做冲正
            p_massage := '109'; --'ACC-HEG-009';
            RETURN;
        END IF;

        --
        IF p_posting_type_code = '04' and substr(p_year_month, 5, 2) = '01' THEN
            --计量凭证不冲上一年数据，如202201 不冲正202112的凭证
            p_massage := '109'; --'ACC-HEG-009';
            RETURN;
        END IF;

        --获取凭证冲销子父节点
        SELECT proc_id, parent_proc_id
        INTO v_proc_id, v_parent_proc_id
        FROM bpluser.bpl_act_re_procdef
        WHERE proc_code = 'ACC_ACCOUNTCONTRAENTRY';

        v_taskcode := bpl_pack_common.func_get_taskcode('ACC', 'A', 'PY-Contra');
        --获取凭证日期
        v_voucher_date   := acc_pack_common.func_correct_voucher_date(p_year_month);
        v_pre_year_month := to_char(add_months(to_date(p_year_month, 'YYYYMM'),
                                               -1),
                                    'YYYYMM');

        v_log_id := bpluser.bpl_seq_log_action.nextval;

        --校正凭证序列
        proc_correct_voucher_key(p_entity_id,
                                 p_book_code,
                                 p_year_month,
                                 p_posting_type_code,
                                 v_proc_id || '');

        --将入账的凭证冲销节点数据插入日志主表
        bpluser.bpl_pack_action_log.proc_add_actionlog(v_log_id,
                                                       v_taskcode,
                                                       'ACC',
                                                       p_entity_id,
                                                       p_book_code,
                                                       p_year_month,
                                                       v_proc_id,
            --对冲子节点
                                                       v_parent_proc_id,
            --5 对冲归宿入账子节点功能下
                                                       '1',
            --默认成功,后续出现异常进行 UPDATE 0
                                                       p_user_id
            --创建人员
            );
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               v_taskcode,
                               p_user_id,
                               NULL,
                               'proc_voucher_revoke_post_type',
                               '1',
                               'acc_buss_voucher');

        --生成凭证
        INSERT INTO acc_buss_voucher
        (voucher_id,
         entity_id,
         book_code,
         posting_type_code,
         proc_id,
         voucher_no,
         year_month,
         effective_date,
         state,
         remark,
         valid_is,
         audit_state,
         task_code,
         create_time,
         creator_id,
         EXT_VOUCHER_NO)
        SELECT acc_seq_buss_voucher.nextval,
               a.entity_id,
               a.book_code,
               a.posting_type_code,
               a.proc_id,
               func_generate_voucher_no(entity_id,
                                        book_code,
                                        p_year_month,
                                        posting_type_code,
                                        v_proc_id || '',
                                        rownum),
               p_year_month,
               v_voucher_date,
               '3', --1 正常  2 被冲   3 冲销
               a.voucher_no,
               '1' AS valid_is, --1 有效
               '1' AS audit_state,
               v_taskcode AS task_code, --a.voucher_no,
               localtimestamp,
               p_user_id,
               a.voucher_no
        FROM acc_buss_voucher a
        WHERE a.entity_id = p_entity_id
          AND a.year_month = v_pre_year_month
          AND a.book_code = p_book_code
          AND a.posting_type_code = p_posting_type_code
          AND a.state = '1'
          and not exists
            (
                --排除已冲销
                SELECT 1
                FROM acc_buss_voucher t
                where t.entity_id = p_entity_id
                  and t.year_month = p_year_month
                  and t.book_code = p_book_code

                  and t.posting_type_code = p_posting_type_code
                  and t.state = '3'
                  and t.ext_voucher_no = a.voucher_no)
          and not exists (
            --计量凭证PAA模型Lrc不做重销 20230710
            SELECT /*+INDEX(vd IDX_ACC_BUSS_VOUCHER_DETAIL_ID)*/
                t.voucher_id
            FROM acc_buss_voucher t
                     left join acc_buss_voucher_detail vd
                               on vd.voucher_id = t.voucher_id
            --left join acc_dap_entry_data ed ON vd.entry_data_id = ed.entry_data_id
            where t.voucher_id = a.voucher_id
              and t.posting_type_code = '04' --计量
                /*and (vd.article3 != 'PAA' OR \*ed.EXTEND_COLUMN7 ='Lic'*\
                 substr(t.ext_voucher_no, -3) = 'Lic')*/
              and (vd.article3 = 'PAA' and /*ed.EXTEND_COLUMN7 ='Lic'*/
                   substr(t.ext_voucher_no, -3) = 'Lrc')

        );

        COMMIT;

        --校正凭证序列
        proc_correct_voucher_key(p_entity_id,
                                 p_book_code,
                                 p_year_month,
                                 p_posting_type_code,
                                 v_proc_id || '');

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               v_taskcode,
                               p_user_id,
                               v_proc_id,
                               'proc_voucher_revoke_post_type',
                               '1',
                               'start acc_buss_voucher_detail');
        --新凭证分录信息采集并存入凭证分录表 - 1-冲销状态
        INSERT INTO acc_buss_voucher_detail
        (voucher_dtl_id,
         voucher_id,
         account_id,
         currency_code,
         currency_cu_code,
         account_entry_code,
         amount,
         amount_cu,
         exchange_rate,
         remark,
         article,
         article1,
         article2,
         article3,
         article4,
         article5,
         article6,
         article7,
         article8,
         article9,
         article10,
         article11,
         article12,
         article13,
         article14,
         article15,
         article16,
         article17,
         entry_data_id)
        SELECT
            --/*+INDEX(c IDX_ACC_BUSS_VOUCHER_TASK_CODE)*/
            acc_seq_buss_voucher_detail.nextval,
            c.voucher_id, --新凭证主表ID
            a.account_id,
            a.currency_code,
            a.currency_cu_code,
            (CASE
                 WHEN a.account_entry_code = 'D' THEN
                     'C'
                 ELSE
                     'D'
                END) account_entry_code,
            a.amount,
            a.amount_cu,
            a.exchange_rate,
            a.remark,
            a.article,
            a.article1,
            a.article2,
            a.article3,
            a.article4,
            a.article5,
            a.article6,
            a.article7,
            a.article8,
            a.article9,
            a.article10,
            a.article11,
            a.article12,
            a.article13,
            a.article14,
            a.article15,
            a.article16,
            a.article17,
            a.entry_data_id
        /*FROM acc_buss_voucher_detail a
                 LEFT JOIN acc_buss_voucher b
                           ON b.voucher_id = a.voucher_id
                 LEFT JOIN acc_buss_voucher c
                           ON c.entity_id = b.entity_id
                               AND c.year_month = p_year_month
                               AND c.book_code = b.book_code
                               AND c.posting_type_code = b.posting_type_code
                               AND c.proc_id = b.proc_id
                               AND (c.ext_voucher_no = b.voucher_no OR
                                    (c.ext_voucher_no IS NULL AND b.voucher_no IS NULL))
                               AND c.state = '3'
        WHERE b.entity_id = p_entity_id
          AND b.year_month = v_pre_year_month
          AND b.book_code = p_book_code
          AND b.posting_type_code = p_posting_type_code
          AND b.state = '1'
          and c.task_code = v_taskcode;*/
        FROM acc_buss_voucher c
                 LEFT JOIN acc_buss_voucher b
                           ON  b.entity_id = c.entity_id
                               AND b.year_month = v_pre_year_month
                               AND b.book_code = c.book_code
                               AND b.posting_type_code = c.posting_type_code
                               AND b.proc_id = c.proc_id
                               AND b.state = '1'
                 LEFT JOIN  acc_buss_voucher_detail a
                            ON b.voucher_id = a.voucher_id
        WHERE c.task_code = v_taskcode
          and  c.entity_id = p_entity_id
          AND c.year_month = p_year_month
          AND c.book_code =p_book_code
          AND c.posting_type_code = p_posting_type_code
          AND (c.ext_voucher_no = b.voucher_no OR
               (c.ext_voucher_no IS NULL AND b.voucher_no IS NULL))
          AND c.state = '3'
          and coalesce(a.voucher_id,0) > 0;

        COMMIT;
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               v_taskcode,
                               p_user_id,
                               v_proc_id,
                               'proc_voucher_revoke_post_type',
                               '1',
                               'end acc_buss_voucher_detail');
        --主表remark原来存放被冲销凭证号，因为借用存放taskcode，所以恢复存放
        /*update acc_buss_voucher
       set remark = ext_voucher_no
     where task_code = v_taskcode;*/
        --记录业务日志明细信息
        bpluser.bpl_pack_action_log.proc_add_actionlogdetails(v_log_id,
            --日志主表主键ID
                                                              NULL,
            --校验规则编码
                                                              '2',
                                                              NULL,
            --校验脚本
                                                              '1',
            --状态 0 失败 1 成功
                                                              NULL,
                                                              p_posting_type_code ||
                                                              ' 类型凭证冲销',
                                                              p_user_id
            --创建人员
            );

    EXCEPTION
        WHEN OTHERS THEN
            --抛出异常提示信息
            dbms_output.put_line('[EXCEPTION]acc_pack_voucher_proc_voucher_revoke_posting_type_code：' ||
                                 to_char(SQLCODE) || '; ' ||
                                 substr(SQLERRM, 1, 200));
            --插入异常记录子信息
            bpluser.bpl_pack_action_log.proc_add_actionlogdetails(v_log_id,
                --日志主表主键ID
                                                                  NULL,
                                                                  '2',
                --校验规则编码
                                                                  NULL,
                --校验脚本
                                                                  '0',
                --状态 0 失败 1 成功
                                                                  '104',
                                                                  p_posting_type_code || '-' ||
                                                                  to_char(SQLCODE) || '-' ||
                                                                  substr(SQLERRM,
                                                                         1,
                                                                         200),
                                                                  p_user_id
                --创建人员
                );
            --更改主日志状态
            UPDATE bpluser.bpl_log_action
            SET state = '0'
            WHERE act_log_id = v_log_id;

    END proc_voucher_revoke_post_type;

    PROCEDURE proc_entry_deal_actionlog(p_entity_id  IN NUMBER,
                                        p_book_code  IN VARCHAR2,
                                        p_year_month IN VARCHAR2,
                                        p_proc_id    IN NUMBER,
                                        p_task_code  IN VARCHAR2,
                                        p_user_id    IN NUMBER) IS
        /***********************************************************************
    NAME :ACC_MAIN_LOG
    DESCRIPTION :日志主信息处理【入账场景匹配时已添加过日志处理，】
    DATE :2020-12-22
    AUTHOR :LEIHUAN
    ***********************************************************************/
        v_log_id NUMBER;
    BEGIN
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_deal_actionlog',
                               '1',
                               'Start');
        SELECT MAX(A.ACT_LOG_ID)
        INTO V_LOG_ID
        FROM BPLUSER.BPL_LOG_ACTION A
        WHERE A.SYSTEM_CODE = 'ACC'
          AND A.entity_id = p_entity_id
          AND A.BOOK_CODE = P_BOOK_CODE
          AND A.YEAR_MONTH = P_YEAR_MONTH
          AND A.PROC_ID = P_PROC_ID;

        FOR rec_voucher IN (SELECT v.proc_id, v.voucher_no
                            FROM acc_buss_entry_data_success a
                                     LEFT JOIN acc_buss_voucher v
                                               ON a.voucher_id = v.voucher_id
                            WHERE v.entity_id = p_entity_id
                              AND v.book_code = p_book_code
                              AND v.year_month = p_year_month
                              AND a.proc_id = p_proc_id
                              AND a.task_code = p_task_code
                              AND a.entry_state = '1'
                            GROUP BY v.voucher_no, v.proc_id) LOOP
                --插入成功记录子信息 按照凭证号码写入
                BPLUSER.BPL_PACK_ACTION_LOG.PROC_ADD_ACTIONLOGDETAILS(V_LOG_ID,
                                                                      NULL,
                                                                      '2',
                                                                      NULL,
                                                                      '1', --状态 0 失败 1 成功
                                                                      '001', --入账成功.
                                                                      rec_voucher.voucher_no, --业务主键
                                                                      p_user_id);
            END LOOP;
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               p_task_code,
                               p_user_id,
                               p_proc_id,
                               'proc_entry_deal_actionlog',
                               '1',
                               'End');
    EXCEPTION
        WHEN OTHERS THEN
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   p_proc_id,
                                   'proc_entry_deal_actionlog',
                                   '2',
                                   substr(SQLERRM, 1, 200));
    END proc_entry_deal_actionlog;

    FUNCTION func_entry_reset_actionlog(p_entity_id  IN NUMBER,
                                        p_book_code  IN VARCHAR2,
                                        p_year_month IN VARCHAR2,
                                        p_proc_id    IN NUMBER,
                                        p_task_code  IN VARCHAR2,
                                        p_user_id    IN NUMBER) RETURN VARCHAR2 IS
        /***********************************************************************
    NAME : func_entry_reset_actionlog
    DESCRIPTION :
    DATE :2021-04-22
    AUTHOR :YINXH
    -------
    MODIFY LOG
    UPDATE DATE :
    UPDATE BY :
    UPDATE DESC :
    ***********************************************************************/

        --v_text1 text;
        --v_text2 text;
        v_number         NUMBER(8) := 0;
        v_log_id         NUMBER(8);
        v_log_dst        NUMBER(8);
        v_parent_proc_id NUMBER(8);

    BEGIN
        SELECT b.parent_proc_id
        INTO v_parent_proc_id
        FROM bpl_act_re_procdef b
        WHERE proc_id = p_proc_id;

        v_log_id := NULL;
        --待入账数据已经记录业务日志主表，获取当前入账子节点的业务日主表主键ID
        SELECT MAX(a.act_log_id)
        INTO v_log_id
        FROM bpluser.bpl_log_action a
        WHERE a.task_code <> p_task_code
          AND a.system_code = 'ACC'
          AND a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.parent_proc_id = v_parent_proc_id
          AND a.proc_id = p_proc_id; --入账节点ID
        --若当前子节点未记录业务日志表，则新记录
        IF v_log_id IS NULL THEN
            v_log_id := bpluser.bpl_seq_log_action.nextval;
            bpluser.bpl_pack_action_log.proc_add_actionlog(v_log_id,
                                                           p_task_code,
                                                           'ACC',
                                                           p_entity_id,
                                                           p_book_code,
                                                           p_year_month,
                                                           p_proc_id,
                --入账子节点ID
                                                           v_parent_proc_id,
                --入账节点ID
                                                           '1',
                --0-失败
                                                           p_user_id
                --创建人员
                );
        ELSE
            bpluser.bpl_pack_action_log.proc_add_loghis(p_entity_id,
                                                        p_book_code,
                                                        p_year_month,
                                                        p_proc_id);
            UPDATE bpluser.bpl_log_action a
            SET state = '1', task_code = p_task_code
            WHERE act_log_id = v_log_id;
            DELETE FROM bpluser.bpl_log_action_detail a
            WHERE act_log_id = v_log_id
              AND state <> '1';
            COMMIT;
        END IF;

        --判断当前核算单位、会计期间是否有待入账数据
        SELECT COUNT(buss_entry_id)
        INTO v_number
        FROM acc_buss_entry_data_all a
                 LEFT JOIN acc_dap_entry_data b
                           ON a.entry_data_id = b.entry_data_id
        WHERE a.task_code = p_task_code
          AND a.entity_id = p_entity_id
          AND a.year_month = p_year_month
          AND a.book_code = p_book_code
          AND a.voucher_id IS NULL
          AND b.proc_id = p_proc_id; --过滤未匹配入账场景ID的数据

        v_log_id := NULL;
        --待入账数据已经记录业务日志主表，获取当前入账子节点的业务日主表主键ID
        SELECT MIN(a.act_log_id)
        INTO v_log_id
        FROM bpluser.bpl_log_action a
        WHERE a.task_code = p_task_code
          AND a.system_code = 'ACC'
          AND a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.parent_proc_id = v_parent_proc_id
          AND a.proc_id = p_proc_id; --入账节点ID
        IF v_number = 0 THEN

            SELECT MIN(act_log_id)
            INTO v_log_dst
            FROM bpluser.bpl_log_action_detail b
            WHERE b.act_log_id = v_log_id;

            --若当前子节点未记录业务日志表，则新记录
            IF v_log_dst IS NULL THEN
                --记录业务日志明细表信息
                bpluser.bpl_pack_action_log.proc_add_actionlogdetails(v_log_id,
                                                                      NULL,
                    --校验规则编码
                                                                      '2',
                                                                      NULL,
                    --校验脚本
                                                                      '4',
                    --4-不存在检查数据信息
                                                                      '005',
                    --'ACC-ENT-005' --异常原因:不存在待入账数据信息.
                                                                      '',
                    --异常业务主键
                                                                      p_user_id
                    --创建人员
                    );
                UPDATE bpluser.bpl_log_action
                SET state = '4'
                WHERE act_log_id = v_log_id;
            END IF;
            --RAISE NOTICE '无待入账数据信息：%',p_proc_id;
            dbms_output.put_line('无待入账数据信息：' || p_proc_id);

            RETURN 'N';
        END IF;

        RETURN 'Y';

    EXCEPTION
        WHEN OTHERS THEN
            RETURN 'N';
            --抛出异常提示信息
            --RAISE EXCEPTION '[EXCEPTION]入账数据状态重置失败: % ',SQLERRM;
            dbms_output.put_line(to_char(SQLCODE) || '::' ||
                                 substr(SQLERRM, 1, 200));

    END func_entry_reset_actionlog;

    PROCEDURE proc_account_entry_log(p_entity_id    NUMBER,
                                     p_year_month   VARCHAR2,
                                     p_task_code    VARCHAR2,
                                     p_user_id      NUMBER,
                                     p_proc_id      NUMBER,
                                     p_trace_code   VARCHAR2,
                                     p_trace_status VARCHAR2,
                                     p_trace_msg    VARCHAR2) IS
    BEGIN
        INSERT INTO acc_log_account_entry
        (entity_id,
         year_month,
         task_code,
         user_id,
         proc_id,
         trace_code,
         trace_status,
         trace_msg,
         create_time)
        VALUES
            (p_entity_id,
             p_year_month,
             p_task_code,
             p_user_id,
             p_proc_id,
             p_trace_code,
             p_trace_status,
             p_trace_msg,
             SYSDATE);
        COMMIT;

    EXCEPTION
        --意外处理
        WHEN OTHERS THEN
            NULL;
    END proc_account_entry_log;

    --凭证对冲 start
    --1 对冲功能入口
    PROCEDURE proc_voucher_revoke(p_entity_id      IN NUMBER,
                                  p_book_code      IN VARCHAR2,
                                  p_year_month     IN VARCHAR2,
                                  p_voucher_no     IN VARCHAR2,
                                  p_revoke_message IN VARCHAR2,
                                  p_user_id        IN NUMBER,
                                  p_out_message    OUT VARCHAR2,
                                  p_remark         OUT VARCHAR2) IS
        /***********************************************************************
      NAME :acc_pack_voucher_proc_voucher_revoke
      DESCRIPTION :凭证对冲处理(包含对冲和试对冲)
      DATE :2023-03-06
      AUTHOR :LY
      BUSINESS RULE : 根据传递的凭证号码进行冲销,生成新的凭证,并记录操作信息

      p_revoke_message: 被冲销原因 若录入冲销原因，默认是使用冲正功能，反之则默认试对冲
      p_out_message: 冲销结果  1-成功  非1 输出异常原因
    ***********************************************************************/
        v_year_month        VARCHAR2(6);
        v_taskcode          VARCHAR2(32);
        v_proc_id           NUMBER(10);
        v_parent_proc_id    NUMBER(10);
        v_posting_type_code VARCHAR2(32);
        v_state             VARCHAR2(32);
        v_new_voucher_no    VARCHAR2(32);
        v_old_voucher_id    NUMBER(11);
        v_result            VARCHAR2(6);
        v_entry_data_sum    NUMBER(10);
    BEGIN
        --检查凭证是否有效
        SELECT MAX(a.posting_type_code), MAX(a.state), max(a.voucher_id)
        INTO v_posting_type_code, v_state, v_old_voucher_id
        FROM acc_buss_voucher a
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.voucher_no = p_voucher_no;

        IF v_posting_type_code IS NULL THEN
            --冲销凭证号码不存在
            p_out_message := '005';
            dbms_output.put_line('被冲销凭证被冲销凭证号码不存在：p_out_message: ' ||
                                 p_out_message);
            RETURN;
        ELSIF v_posting_type_code = '07' THEN
            --手工凭证不做冲正
            p_out_message := '009';
            dbms_output.put_line('被冲销凭证类型为手工：p_out_message: ' || p_out_message);
            RETURN;
        END IF;

        --2、验证凭证状态是否未正常状态,冲销和被冲销凭证不允许再次冲正
        IF v_state IN ('2', '3') THEN
            --1 正常 2 被冲  3 冲销
            p_out_message := '006';
            --抛出异常，中断事务
            --RAISE EXCEPTION '被冲销凭证状态不正常：V_ERRORCODE: %',V_ERRORCODE;
            dbms_output.put_line('被冲销凭证状态不正常：V_ERRORCODE: ' || p_out_message);
            RETURN;
        END IF;

        --获取当前允许操作的会计期间
        SELECT MIN(year_month)
        INTO v_year_month
        FROM acc_conf_accountperiod c
        WHERE c.entity_id = p_entity_id
          AND c.book_code = p_book_code
          AND c.year_month = p_year_month
          AND c.execution_state = '2';

        IF v_year_month IS NULL THEN
            p_out_message := '108';
            dbms_output.put_line('会计期间不可执行：v_year_month: ' || v_year_month);
            RETURN;
        END IF;

        SELECT COUNT(1)
        INTO v_entry_data_sum
        FROM ACC_DAP_ENTRY_DATA A
        WHERE ENTRY_DATA_ID IN
              (SELECT B.ENTRY_DATA_ID
               FROM ACC_BUSS_VOUCHER_DETAIL B
                        LEFT JOIN ACC_BUSS_VOUCHER C
                                  ON B.VOUCHER_ID = C.VOUCHER_ID
               WHERE C.VOUCHER_NO = P_VOUCHER_NO);
        IF (v_entry_data_sum IS NULL OR v_entry_data_sum = 0) THEN
            p_out_message := '010';
            dbms_output.put_line('入账业务数据缺失：v_year_month: ' || p_out_message);
            RETURN;
        END IF;

        --冲正操作：获取任务ID
        v_taskcode := bpl_pack_common.func_get_taskcode('ACC', 'A', 'PY-Contra');
        --RAISE NOTICE '冲正任务ID：%',v_taskcode;
        dbms_output.put_line('冲正任务ID：' || v_taskcode);

        --删除凭证冲销临时表数据
        --DELETE FROM acc_temp_voucontradtl WHERE 1 = 1;

        --获取凭证冲销子父节点
        SELECT proc_id, parent_proc_id
        INTO v_proc_id, v_parent_proc_id
        FROM bpluser.bpl_act_re_procdef
        WHERE proc_code = 'ACC_ACCOUNTCONTRAENTRY';

        --根据录入的凭证号获取被冲凭证主信息
        IF p_revoke_message IS NOT NULL THEN
            acc_pack_voucher.proc_voucher_revoke_voucher_no(p_entity_id,
                                                            p_book_code,
                                                            p_year_month,
                                                            p_voucher_no,
                                                            p_revoke_message,
                                                            v_taskcode,
                                                            p_user_id,
                                                            v_new_voucher_no);
            --重新匹配场景
            proc_entry_re_scenario_match(p_entity_id,
                                         p_book_code,
                                         p_year_month,
                                         v_taskcode,
                                         v_old_voucher_id,
                                         p_user_id);
            --3 、入账数据检查
            proc_entry_re_data_check(p_entity_id,
                                     p_book_code,
                                     p_year_month,
                                     v_proc_id,
                                     v_taskcode,
                                     p_user_id);
            --4 、入账
            proc_entry_re_voucher(p_entity_id,
                                  p_book_code,
                                  p_year_month,
                                  v_proc_id,
                                  v_taskcode,
                                  p_user_id);

        ELSE
            proc_voucher_trial_revoke_voucher_no(p_entity_id,
                                                 p_book_code,
                                                 p_year_month,
                                                 p_voucher_no,
                                                 p_user_id,
                                                 v_taskcode,
                                                 v_new_voucher_no);

            proc_voucher_trial_reentry_voucher_no(p_entity_id,
                                                  p_book_code,
                                                  p_year_month,
                                                  p_voucher_no,
                                                  p_user_id,
                                                  v_new_voucher_no,
                                                  v_taskcode);

            p_remark := p_voucher_no || '-' || v_taskcode;
        END IF;
        p_out_message := '1';
    EXCEPTION
        WHEN OTHERS THEN
            dbms_output.put_line('**出错行数: ' ||
                                 dbms_utility.format_error_backtrace());
            --抛出异常提示信息
            dbms_output.put_line('[EXCEPTION]acc_pack_voucher_proc_voucher_revoke：' ||
                                 to_char(SQLCODE) || '; ' ||
                                 substr(SQLERRM, 1, 200));
        --最后增加日志

    END proc_voucher_revoke;

    --2 试对冲功能-冲销凭证
    PROCEDURE proc_voucher_trial_revoke_voucher_no(p_entity_id      IN NUMBER,
                                                   p_book_code      IN VARCHAR2,
                                                   p_year_month     IN VARCHAR2,
                                                   p_voucher_no     IN VARCHAR2,
                                                   p_user_id        IN NUMBER,
                                                   p_task_code      IN VARCHAR2,
                                                   p_new_voucher_no IN OUT VARCHAR2) IS
        /***********************************************************************
    NAME : proc_voucher_trial_revoke_voucher_no
    DESCRIPTION :凭证试冲销处理
    DATE :2023-3-6
    AUTHOR :LY
    ***********************************************************************/
        v_voucher_id NUMBER(20);
        v_proc_id    NUMBER;
    BEGIN

        --试对冲不生成凭证号
        --TODO问题:不返回凭证号，前端如何查询试对冲的结果
        p_new_voucher_no := NULL;
        --凭证新主键ID
        v_voucher_id := to_char(SYSTIMESTAMP, 'yyMMddHHMIssff3') + 0;
        --v_voucher_id :=  TO_NUMBER(sysdate - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 1000 ;
        --检查凭证信息
        SELECT MAX(a.proc_id)
        INTO v_proc_id
        FROM acc_buss_voucher a
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.voucher_no = p_voucher_no
          AND a.state = '1';

        --删除试对冲凭证历史数据
        DELETE FROM acc_temp_voucher_detail
        WHERE voucher_id IN
              (SELECT voucher_id
               FROM acc_temp_voucher
               WHERE remark like p_voucher_no || '%');

        DELETE FROM acc_temp_voucher WHERE remark like p_voucher_no || '%';

        --冲销凭证主表插入
        INSERT INTO acc_temp_voucher
        (voucher_id,
         entity_id,
         book_code,
         posting_type_code,
         proc_id,
         voucher_no,
         year_month,
         effective_date,
         state,
         remark,
         valid_is,
         create_time,
         creator_id,
         ext_voucher_no)
        SELECT v_voucher_id,
               entity_id,
               book_code,
               posting_type_code,
               proc_id,
               p_new_voucher_no voucher_no,
               p_year_month,
               acc_pack_common.func_correct_voucher_date(p_year_month),
               '3', --1正常 2 被冲  3 冲销
               p_voucher_no || '-' || p_task_code AS remark,
               '1', --1 有效
               localtimestamp,
               p_user_id,
               a.ext_voucher_no
        FROM acc_buss_voucher a
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.voucher_no = p_voucher_no
          AND a.state = '1';
        --COMMIT;

        --新凭证分录信息采集并存入凭证分录表 - 3-冲销状态
        INSERT INTO acc_temp_voucher_detail
        (voucher_dtl_id,
         voucher_id,
         account_id,
         currency_code,
         currency_cu_code,
         account_entry_code,
         amount,
         amount_cu,
         exchange_rate,
         remark,
         article,
         article1,
         article2,
         article3,
         article4,
         article5,
         article6,
         article7,
         article8,
         article9,
         article10,
         article11,
         article12,
         article13,
         article14,
         article15,
         article16,
         article17,
         entry_data_id)
        SELECT v_voucher_id * 1000 + rownum,
               v_voucher_id, --新凭证主表ID
               a.account_id,
               a.currency_code,
               a.currency_cu_code,
               (CASE
                    WHEN a.account_entry_code = 'D' THEN
                        'C'
                    ELSE
                        'D'
                   END) account_entry_code,
               a.amount,
               a.amount_cu,
               a.exchange_rate,
               a.remark,
               a.article,
               a.article1,
               a.article2,
               a.article3,
               a.article4,
               a.article5,
               a.article6,
               a.article7,
               a.article8,
               a.article9,
               a.article10,
               a.article11,
               a.article12,
               a.article13,
               a.article14,
               a.article15,
               a.article16,
               a.article17,
               a.entry_data_id
        FROM acc_buss_voucher_detail a
                 LEFT JOIN acc_buss_voucher b
                           ON b.voucher_id = a.voucher_id
        WHERE b.entity_id = p_entity_id
          AND b.book_code = p_book_code
          --AND b.year_month = p_year_month
          AND b.voucher_no = p_voucher_no
          AND b.state = '1';
        --COMMIT;

        --RAISE NOTICE '凭证冲销，冲销凭证:%,凭证ID:%,新凭证:%',p_voucher_no,v_voucher_id,p_new_voucher_no;
        dbms_output.put_line('凭证冲销，冲销凭证:' || p_voucher_no || ',凭证ID:' ||
                             v_voucher_id || ',新凭证:' || p_new_voucher_no);

    EXCEPTION
        WHEN OTHERS THEN
            rollback;
            p_new_voucher_no := NULL;

            --抛出异常提示信息
            --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;

            --RAISE EXCEPTION '[EXCEPTION]acc_pack_voucher_proc_voucher_revoke_voucher_no：%; %',v_text1,v_text2;
            dbms_output.put_line('[EXCEPTION]acc_pack_voucher_proc_voucher_trial_revoke_voucher_no：' ||
                                 to_char(SQLCODE) || ';' ||
                                 substr(SQLERRM, 1, 200));

    END proc_voucher_trial_revoke_voucher_no;

    --3 试对冲功能-生成凭证
    PROCEDURE proc_voucher_trial_reentry_voucher_no(p_entity_id      IN NUMBER,
                                                    p_book_code      IN VARCHAR2,
                                                    p_year_month     IN VARCHAR2,
                                                    p_voucher_no     IN VARCHAR2,
                                                    p_user_id        IN NUMBER,
                                                    p_new_voucher_no IN OUT VARCHAR2,
                                                    p_task_code      IN VARCHAR2) IS
        /***********************************************************************
    NAME : proc_voucher_trial_reentry_voucher_no
    DESCRIPTION :凭证试入账处理
    DATE :2023-3-6
    AUTHOR :LY
    ***********************************************************************/
        v_voucher_id     NUMBER(20);
        v_voucher_date   DATE;
        v_old_voucher_id NUMBER;
    BEGIN

        --试对冲不生成凭证号
        --TODO问题:不返回凭证号，前端如何查询试对冲的结果
        p_new_voucher_no := NULL;

        --获取凭证日期
        v_voucher_date := acc_pack_common.func_correct_voucher_date(p_year_month);

        --取凭证相关的入账数据
        SELECT MAX(v.voucher_id)
        INTO v_old_voucher_id
        FROM acc_buss_voucher v
        WHERE v.entity_id = p_entity_id
          AND v.book_code = p_book_code
          AND v.voucher_no = p_voucher_no
          AND v.state = '1';

        --被冲销凭证
        INSERT INTO acc_temp_voucher
        (voucher_id,
         entity_id,
         book_code,
         year_month,
         posting_type_code,
         proc_id,
         voucher_no,
         effective_date,
         ext_voucher_no,
         state,
         remark,
         valid_is,
         create_time,
         creator_id) --存放的是用户表主键非用户账户
        SELECT voucher_id,
               entity_id,
               book_code,
               year_month,
               posting_type_code,
               proc_id,
               voucher_no,
               effective_date,
               ext_voucher_no,
               '2' as state, --1正常 2 被冲  3 冲销
               p_voucher_no || '-' || p_task_code,
               valid_is,
               create_time,
               creator_id
        from acc_buss_voucher t
        where t.voucher_id = v_old_voucher_id;

        insert into acc_temp_voucher_detail
        SELECT T.*
        FROM acc_buss_voucher_detail t
        where t.voucher_id = v_old_voucher_id;
        --写被冲销凭证结束

        --TODO 检查最新的入账规则与场景，更新场景ID后再入账
        proc_entry_temp_scenario_match(p_entity_id,
                                       p_book_code,
                                       p_year_month,
                                       p_task_code,
                                       v_old_voucher_id,
                                       p_user_id);

        --试对冲ID取随机数
        v_voucher_id := to_char(SYSTIMESTAMP, 'yyMMddHHMIssff3') + 0;

        --删除新增凭证信息
        /*DELETE FROM  acc_temp_voucher_detail t
      where t.voucher_id in
            (SELECT a.voucher_id FROM acc_temp_voucher a where a.state = '1' and a.remark = p_task_code);
     DELETE FROM acc_temp_voucher a where a.state = '1' and a.remark = p_task_code;
    */
        --按凭证类型 生成 凭证信息
        INSERT INTO acc_temp_voucher
        (voucher_id,
         entity_id,
         book_code,
         year_month,
         posting_type_code,
         proc_id,
         voucher_no,
         effective_date,
         ext_voucher_no,
         state,
         remark,
         valid_is,
         create_time,
         creator_id) --存放的是用户表主键非用户账户
        SELECT v_voucher_id,
               entity_id,
               book_code,
               year_month,
               posting_type_code,
               proc_id,
               p_new_voucher_no,
               v_voucher_date,
               bu_voucher_no,
               '1', --1正常 2 被冲  3 冲销
               p_voucher_no || '-' || p_task_code,
               '1', --1 有效
               localtimestamp,
               p_user_id
        FROM (SELECT a.entity_id,
                     a.book_code,
                     a.year_month,
                     a.bu_voucher_no,
                     a.posting_type_code,
                     a.proc_id
              FROM acc_buss_entry_data a
              WHERE a.entity_id = p_entity_id
                AND a.book_code = p_book_code
                AND a.year_month = p_year_month
                AND a.voucher_id =
                    (SELECT voucher_id
                     FROM acc_buss_voucher
                     WHERE voucher_no = p_voucher_no)
                AND a.entry_state = 1 --只能冲销正常入账状态的数据
              GROUP BY a.entity_id,
                       a.book_code,
                       a.year_month,
                       a.bu_voucher_no,
                       a.posting_type_code,
                       a.proc_id) t;
        --COMMIT;

        --凭证分录处理
        --借方科目
        INSERT INTO acc_temp_voucher_detail
        (voucher_dtl_id,
         voucher_id,
         account_id,
         currency_code,
         currency_cu_code,
         account_entry_code,
         amount,
         amount_cu,
         exchange_rate,
         remark,
         article,
         article1,
         article2,
         article3,
         article4,
         article5,
         article6,
         article7,
         article8,
         article9,
         article10,
         article11,
         article12,
         article13,
         article14,
         article15,
         article16,
         article17,
         entry_data_id)
        SELECT voucher_id * 1000 + rownum,
               voucher_id, --凭证主表ID
               account_id,
               currency_code,
               currency_cu_code,
               account_entry_code,
               account,
               amount_cu,
               exchange_rate,
               NULL remark,
               nvl2(article1, article1 || '/', '') ||
               nvl2(article2, article2 || '/', '') ||
               nvl2(article3, article3 || '/', '') ||
               nvl2(article4, article4 || '/', '') ||
               nvl2(article5, article5 || '/', '') ||
               nvl2(article6, article6 || '/', '') ||
               nvl2(article7, article7 || '/', '') ||
               nvl2(article8, article8 || '/', '') ||
               nvl2(article9, article9 || '/', '') ||
               nvl2(article10, article10 || '/', '') ||
               nvl2(article11, article11 || '/', '') ||
               nvl2(article12, article12 || '/', '') ||
               nvl2(article13, article13 || '/', '') ||
               nvl2(article14, article14 || '/', '') ||
               nvl2(article15, article15 || '/', '') ||
               nvl2(article16, article16 || '/', '') ||
               nvl2(article17, article17 || '/', '') AS article,
               article1,
               article2,
               article3,
               article4,
               article5,
               article6,
               article7,
               article8,
               article9,
               article10,
               article11,
               article12,
               article13,
               article14,
               article15,
               article16,
               article17,
               entry_data_id
        FROM (SELECT v_voucher_id voucher_id,
                     c.account_id_dr AS account_id, --借方科目编码
                     a.currency_code, --原币
                     a.currency_cu_code, --本位币
                     (CASE
                          WHEN SUM(b.amount) >= 0 THEN
                              'D'
                          ELSE
                              'C'
                         END) AS account_entry_code, --借贷方科目编码(被冲借贷相反)
                     abs(SUM(b.amount)) AS account, --原币金额
                     round(abs(SUM(b.amount)) * a.exchange_rate, 2) AS amount_cu, --本位币金额
                     a.exchange_rate AS exchange_rate,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE1',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article1,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE2',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article2,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE3',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article3,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE4',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article4,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE5',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article5,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE6',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article6,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE7',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article7,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE8',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article8,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE9',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article9,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE10',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article10,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE11',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article11,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE12',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article12,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE13',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article13,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE14',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article14,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE15',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article15,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE16',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article16,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE17',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article17,
                     b.entry_data_id
              FROM acc_buss_entry_data a
                       LEFT JOIN acc_dap_entry_data b
                                 ON a.entry_data_id = b.entry_data_id
                       LEFT JOIN acc_temp_entry_data_detail c
                                 ON c.buss_entry_id = a.buss_entry_id
              WHERE a.entity_id = p_entity_id
                AND a.book_code = p_book_code
                AND a.year_month = p_year_month
                --AND a.task_code = p_task_code
                --AND a.entry_data_id = v_entry_data_id
                and a.voucher_id = v_old_voucher_id
                AND a.entry_state = 1
                AND c.account_id_dr IS NOT NULL
              GROUP BY c.account_id_dr, --借方科目编码
                       a.currency_code, --原币
                       a.currency_cu_code, --本位币
                       a.exchange_rate,
                       b.entry_data_id);
        --COMMIT;

        --贷方科目
        INSERT INTO acc_temp_voucher_detail
        (voucher_dtl_id,
         voucher_id,
         account_id,
         currency_code,
         currency_cu_code,
         account_entry_code,
         amount,
         amount_cu,
         exchange_rate,
         remark,
         article,
         article1,
         article2,
         article3,
         article4,
         article5,
         article6,
         article7,
         article8,
         article9,
         article10,
         article11,
         article12,
         article13,
         article14,
         article15,
         article16,
         article17,
         entry_data_id)
        SELECT voucher_id * 1000 + rownum,
               voucher_id, --凭证主表ID
               account_id,
               currency_code,
               currency_cu_code,
               account_entry_code,
               account,
               amount_cu,
               exchange_rate,
               NULL remark,
               nvl2(article1, article1 || '/', '') ||
               nvl2(article2, article2 || '/', '') ||
               nvl2(article3, article3 || '/', '') ||
               nvl2(article4, article4 || '/', '') ||
               nvl2(article5, article5 || '/', '') ||
               nvl2(article6, article6 || '/', '') ||
               nvl2(article7, article7 || '/', '') ||
               nvl2(article8, article8 || '/', '') ||
               nvl2(article9, article9 || '/', '') ||
               nvl2(article10, article10 || '/', '') ||
               nvl2(article11, article11 || '/', '') ||
               nvl2(article12, article12 || '/', '') ||
               nvl2(article13, article13 || '/', '') ||
               nvl2(article14, article14 || '/', '') ||
               nvl2(article15, article15 || '/', '') ||
               nvl2(article16, article16 || '/', '') ||
               nvl2(article17, article17 || '/', '') AS article,
               article1,
               article2,
               article3,
               article4,
               article5,
               article6,
               article7,
               article8,
               article9,
               article10,
               article11,
               article12,
               article13,
               article14,
               article15,
               article16,
               article17,
               entry_data_id
        FROM (SELECT v_voucher_id voucher_id,
                     c.account_id_cr account_id, --贷方科目编码
                     b.currency_code, --原币
                     a.currency_cu_code, --本位币
                     (CASE
                          WHEN SUM(b.amount) >= 0 THEN
                              'C'
                          ELSE
                              'D'
                         END) AS account_entry_code, --借贷方科目编码(被冲借贷相反)
                     abs(SUM(b.amount)) AS account, --原币金额
                     round(abs(SUM(b.amount)) * a.exchange_rate, 2) AS amount_cu, --本位币金额
                     a.exchange_rate AS exchange_rate,

                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE1',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article1,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE2',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article2,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE3',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article3,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE4',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article4,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE5',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article5,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE6',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article6,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE7',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article7,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE8',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article8,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE9',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article9,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE10',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article10,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE11',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article11,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE12',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article12,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE13',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article13,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE14',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article14,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE15',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article15,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE16',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article16,
                     acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                              'ACC_BUSS_VOUCHER_DETAIL',
                                                              'ARTICLE17',
                                                              'entry_data_id',
                                                              '=',
                                                              b.entry_data_id || '',
                                                              'number') AS article17,
                     b.entry_data_id
              FROM acc_buss_entry_data a
                       LEFT JOIN acc_dap_entry_data b
                                 ON a.entry_data_id = b.entry_data_id
                       LEFT JOIN acc_temp_entry_data_detail c
                                 ON c.buss_entry_id = a.buss_entry_id
              WHERE a.entity_id = p_entity_id
                AND a.book_code = p_book_code
                AND a.year_month = p_year_month
                --AND a.entry_data_id = v_entry_data_id
                and a.voucher_id = v_old_voucher_id
                AND a.entry_state = 1
                AND c.account_id_cr IS NOT NULL
              GROUP BY c.account_id_cr, --贷方科目编码
                       b.currency_code, --原币
                       a.currency_cu_code, --本位币
                       a.exchange_rate,
                       b.entry_data_id);
        --COMMIT;

    EXCEPTION
        WHEN OTHERS THEN
            rollback;
            p_new_voucher_no := NULL;

            --抛出异常提示信息
            --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;

            --RAISE EXCEPTION '[EXCEPTION]acc_pack_voucher_proc_voucher_revoke_voucher_no：%; %',v_text1,v_text2;
            dbms_output.put_line('[EXCEPTION]acc_pack_voucher_proc_voucher_trial_reentry_voucher_no：' ||
                                 to_char(SQLCODE) || ';' ||
                                 substr(SQLERRM, 1, 200));

    END proc_voucher_trial_reentry_voucher_no;

    --4 试对冲功能-重新匹配场景
    PROCEDURE proc_entry_temp_scenario_match(p_entity_id      IN NUMBER,
                                             p_book_code      IN VARCHAR2,
                                             p_year_month     IN VARCHAR2,
                                             p_task_code      IN VARCHAR2,
                                             p_old_voucher_id in NUMBER,
                                             p_user_id        IN NUMBER) IS
        /***********************************************************************
      NAME :ACC_SCENE_MATCH
      DESCRIPTION :试对冲功能场景匹配
      DATE :2023-03-07
      AUTHOR :CHENJUNFENG

    ***********************************************************************/
        v_sql               VARCHAR(4000);
        V_COUNT             NUMBER(11);
        v_char              varchar(1);
        v_posting_type_code VARCHAR(32);
    BEGIN

        --获取需要匹配入账场景的业务数据
        DELETE FROM acc_temp_entry_data_detail t
        where t.buss_entry_id in
              (SELECT a.buss_entry_id
               FROM acc_buss_entry_data a
               where a.voucher_id = p_old_voucher_id);
        DELETE FROM acc_temp_entry_data t
        where t.buss_entry_id in
              (SELECT a.buss_entry_id
               FROM acc_buss_entry_data a
               where a.voucher_id = p_old_voucher_id);

        SELECT MAX(t.posting_type_code)
        INTO v_posting_type_code
        FROM acc_buss_entry_data t
        where t.voucher_id = p_old_voucher_id;

        insert into acc_temp_entry_data
        (BUSS_ENTRY_ID,
         ENTRY_DATA_ID,
         entity_id,
         BOOK_CODE,
         YEAR_MONTH,
         TASK_CODE,
         PROC_ID,
         SCENARIO_ID,
         account_id_DR,
         account_id_CR,
         posting_type_code,
         SCENARIO_TYPE,
         VOUCHER_ID,
         CHANGE_TIME,
         currency_cu_code,
         currency_code,
         exchange_rate,
         ENTRY_STATE,
         ENTRY_MSG,
         CREATE_TIME,
         CREATOR_ID,
         UPDATE_TIME,
         UPDATOR_ID,
         bu_voucher_no,
         ENTRY_DATE,
         SCEN_SERIAL_NO)
        SELECT BUSS_ENTRY_ID,
               ENTRY_DATA_ID,
               entity_id,
               BOOK_CODE,
               YEAR_MONTH,
               p_task_code,
               PROC_ID,
               null,
               account_id_DR,
               account_id_CR,
               posting_type_code,
               SCENARIO_TYPE,
               null,
               CHANGE_TIME,
               currency_cu_code,
               currency_code,
               acc_pack_common.func_get_exch_rate(entity_id,
                                                  acc_pack_common.func_correct_voucher_date(p_year_month),
                                                  currency_code,
                                                  currency_cu_code,
                                                  '2'),
               '0',
               null,
               sysdate,
               p_user_id,
               sysdate,
               p_user_id,
               bu_voucher_no,
               ENTRY_DATE,
               null
        FROM acc_buss_entry_data t
        where t.voucher_id = p_old_voucher_id;

        BEGIN
            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   NULL,
                                   'proc_entry_temp_scenario_match',
                                   '1',
                                   'start');
            FOR rec_scenario IN (SELECT a.scenario_id,
                                        a.scenario_condition,
                                        a.serial_no,
                                        COUNT(b.model_code_id) model_count,
                                        ' UPDATE acc_temp_entry_data a' ||
                                        ' SET scenario_id = ' || a.scenario_id ||
                                        ',scen_serial_no = ' ||
                                        coalesce(a.serial_no, 1) ||
                                        ' WHERE exists( select 1 from acc_dap_entry_data B' ||
                                        ' WHERE A.entry_data_id = B.entry_data_id' ||
                                        ' AND ' || a.scenario_condition || ')' ||
                                        ' AND A.entry_state = 0' ||
                                        ' AND A.scenario_type = ''AP''' ||
                                        ' AND A.scenario_id IS NULL' ||
                                        ' AND A.entity_id = ' || p_entity_id ||
                                        ' AND A.book_code = ''' || p_book_code || '''' ||
                                        ' AND A.year_month = ''' || p_year_month || '''' ||
                                        ' AND A.task_code = ''' || p_task_code || '''' scenario_sql
                                 FROM acc_conf_scenario a
                                          LEFT JOIN acc_conf_scenario_modelref b
                                                    ON a.scenario_id = b.scenario_id
                                 WHERE a.valid_is = '1'
                                   AND a.audit_state = '1'
                                   AND MODEL_CODE_ID =
                                       (SELECT MODEL_CODE_ID
                                        FROM acc_conf_model_code
                                        WHERE MODEL_CODE_CODE = 'VoucherType')
                                   AND CODE_CODE = v_posting_type_code
                                 HAVING COUNT(b.model_code_id) > 0
                                 GROUP BY a.scenario_id,
                                          a.scenario_condition,
                                          a.serial_no
                                 ORDER BY model_count DESC, scenario_id) LOOP
                    --dbms_output.put_line('proc_entry_scenario_match：' || rec_scenario.scenario_sql);
                    EXECUTE IMMEDIATE rec_scenario.scenario_sql;
                    SELECT count(1)
                    into V_COUNT
                    FROM acc_temp_entry_data t
                    where t.task_code = p_task_code
                      and t.scenario_id is null;
                    if V_COUNT = 0 then
                        SELECT '*' into v_char FROM dual where 1 = 2;
                    end if;

                END LOOP;

            proc_account_entry_log(p_entity_id,
                                   p_year_month,
                                   p_task_code,
                                   p_user_id,
                                   NULL,
                                   'proc_entry_temp_scenario_match',
                                   '2',
                                   'start');
            IF V_COUNT IS NULL OR V_COUNT > 0 THEN
                FOR rec_scenario IN (SELECT a.scenario_id,
                                            a.scenario_condition,
                                            a.serial_no,
                                            COUNT(b.model_code_id) model_count,
                                            ' UPDATE acc_temp_entry_data a' ||
                                            ' SET scenario_id = ' || a.scenario_id ||
                                            ',scen_serial_no = ' ||
                                            coalesce(a.serial_no, 1) ||
                                            ' WHERE exists( select 1 from acc_dap_entry_data B' ||
                                            ' WHERE A.entry_data_id = B.entry_data_id' ||
                                            ' AND ' || a.scenario_condition || ')' ||
                                            ' AND A.entry_state = 0' ||
                                            ' AND A.scenario_type = ''AP''' ||
                                            ' AND A.scenario_id IS NULL' ||
                                            ' AND A.entity_id = ' || p_entity_id ||
                                            ' AND A.book_code = ''' || p_book_code || '''' ||
                                            ' AND A.year_month = ''' || p_year_month || '''' ||
                                            ' AND A.task_code = ''' || p_task_code || '''' scenario_sql
                                     FROM acc_conf_scenario a
                                              LEFT JOIN acc_conf_scenario_modelref b
                                                        ON a.scenario_id = b.scenario_id
                                     WHERE a.valid_is = '1'
                                       AND a.audit_state = '1' HAVING
                                             COUNT(b.model_code_id) > 0
                                     GROUP BY a.scenario_id,
                                              a.scenario_condition,
                                              a.serial_no
                                     ORDER BY model_count DESC, scenario_id) LOOP
                        --dbms_output.put_line('proc_entry_scenario_match：' || rec_scenario.scenario_sql);
                        EXECUTE IMMEDIATE rec_scenario.scenario_sql;
                        SELECT count(1)
                        into V_COUNT
                        FROM acc_temp_entry_data t
                        where t.task_code = p_task_code
                          and t.scenario_id is null;
                        if V_COUNT = 0 then
                            SELECT '*' into v_char FROM dual where 1 = 2;
                        end if;
                    END LOOP;
            END IF;

        EXCEPTION
            WHEN OTHERS THEN
                dbms_output.put_line('匹配结束，' || sqlcode || sqlerrm);
        END;

        --收付方式更新现金流场景：借贷方向标志不为空时，默认更新为现金流入账场景 入账场景编码 CF00001-现金场景
        MERGE INTO acc_temp_entry_data t
        USING (SELECT a.scenario_id, a.scenario_code, a.serial_no
               FROM acc_conf_scenario a
               WHERE a.scenario_code LIKE 'CF%'
                 AND a.valid_is = '1'
                 AND a.audit_state = '1'
                 AND ROWNUM = 1) c
        ON (c.scenario_code LIKE t.scenario_type || '%')
        WHEN MATCHED THEN
            UPDATE
            SET t.scenario_id = c.scenario_id, t.scen_serial_no = c.serial_no
            WHERE t.entity_id = p_entity_id
              AND t.book_code = p_book_code
              --AND t.year_month <= p_year_month --此处不限制下限是为了处理上次匹配失败的数据能再次捞取进行处理
              AND t.year_month = p_year_month
              AND t.task_code = p_task_code
              AND t.entry_state = 0
              AND t.scenario_type = 'CF'
              AND t.scenario_id IS NULL;

        --现金流复制
        INSERT INTO acc_temp_entry_data_detail d
        (buss_entry_dtl_id,
         buss_entry_id,
         display_no,
         account_id_dr,
         account_id_cr)
        SELECT acc_seq_buss_entry_data_dtl.nextval,
               a.buss_entry_id,
               rownum, --一个凭证有多条现金流怎么处理序号
               (CASE dap.account_entry_code
                    WHEN 'D' THEN
                        item.base_account_id
                    ELSE
                        NULL
                   END),
               (CASE dap.account_entry_code
                    WHEN 'C' THEN
                        item.base_account_id
                    ELSE
                        NULL
                   END)
        FROM acc_temp_entry_data a
                 LEFT JOIN acc_dap_entry_data dap
                           ON a.entry_data_id = dap.entry_data_id
                               AND dap.account_id IS NOT NULL
                 LEFT JOIN bpluser.bbs_conf_account_mapping item
                           ON item.other_account_id = dap.account_id
        WHERE a.task_code = p_task_code
          AND a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.entry_state = 0
          AND a.scenario_type = 'CF'
          AND dap.account_id IS NOT NULL;

        --匹配入账科目编码
        INSERT INTO acc_temp_entry_data_detail
        (buss_entry_dtl_id,
         buss_entry_id,
         display_no,
         account_id_dr,
         account_id_cr,
         entry_serial_no)
        SELECT acc_seq_buss_entry_data_dtl.nextval,
               a.buss_entry_id,
               c.display_no,
               c.account_id_dr,
               c.account_id_cr,
               b.serial_no
        FROM acc_temp_entry_data       a,
             acc_conf_entryrule        b,
             acc_conf_entryrule_detail c
        WHERE c.entry_rule_id = b.entry_rule_id
          AND a.scenario_id = b.scenario_id
          AND a.entity_id = b.entity_id
          AND a.book_code = b.book_code
          AND b.valid_is = '1'
          AND b.audit_state = '1'
          AND a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.task_code = p_task_code
          AND a.scenario_type = 'AP'
          AND a.entry_state = 0;

    EXCEPTION
        WHEN OTHERS THEN
            rollback;
            --抛出异常提示信息
            dbms_output.put_line('[EXCEPTION]proc_entry_tmp_scenario_match：' ||
                                 to_char(SQLCODE) || ';' ||
                                 substr(SQLERRM, 1, 200));
            acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                    p_year_month,
                                                    p_task_code,
                                                    p_user_id,
                                                    null,
                                                    'proc_entry_tmp_scenario_match',
                                                    '2',
                                                    SQLERRM);
    END proc_entry_temp_scenario_match;

    --5 对冲功能-重新匹配场景
    PROCEDURE proc_entry_re_scenario_match(p_entity_id      IN NUMBER,
                                           p_book_code      IN VARCHAR2,
                                           p_year_month     IN VARCHAR2,
                                           p_task_code      IN VARCHAR2,
                                           p_old_voucher_id IN NUMBER,
                                           p_user_id        IN NUMBER) IS
        /***********************************************************************
      NAME :ACC_SCENE_MATCH
      DESCRIPTION :试对冲功能场景匹配
      DATE :2023-03-07
      AUTHOR :CHENJUNFENG

    ***********************************************************************/
        v_sql               VARCHAR(4000);
        v_count             number(11);
        v_posting_type_code VARCHAR(32);
    BEGIN
        SELECT MAX(t.posting_type_code)
        INTO v_posting_type_code
        FROM acc_buss_voucher t
        where t.voucher_id = p_old_voucher_id;

        --恢复待入账状态
        update acc_buss_entry_data t
        set t.scenario_id    = null,
            t.scen_serial_no = null,
            t.voucher_id     = null,
            t.entry_state    = '0',
            t.entry_msg      = '',
            t.task_code      = p_task_code,
            t.exchange_rate  = acc_pack_common.func_get_exch_rate(entity_id,
                                                                  acc_pack_common.func_correct_voucher_date(p_year_month),
                                                                  currency_code,
                                                                  currency_cu_code,
                                                                  '2'),
            t.update_time    = sysdate,
            t.updator_id     = p_user_id
        where t.entry_data_id in
              (SELECT b.entry_data_id
               FROM acc_buss_voucher_detail b
               where b.voucher_id = p_old_voucher_id);

        DELETE FROM acc_buss_entry_data_detail a
        where a.buss_entry_id in
              (SELECT c.buss_entry_id
               FROM acc_buss_voucher_detail b, acc_buss_entry_data c
               where b.voucher_id = p_old_voucher_id
                 and c.entry_data_id = b.entry_data_id);
        --收付方式更新现金流场景：借贷方向标志不为空时，默认更新为现金流入账场景 入账场景编码 CF00001-现金场景
        MERGE INTO acc_buss_entry_data t
        USING (SELECT a.scenario_id, a.scenario_code
               FROM acc_conf_scenario a
               WHERE a.scenario_code LIKE 'CF%'
                 AND a.valid_is = '1'
                 AND a.audit_state = '1'
                 AND rownum = 1) c
        ON (c.scenario_code LIKE t.scenario_type || '%')
        WHEN MATCHED THEN
            UPDATE
            SET t.scenario_id = c.scenario_id
            WHERE t.entity_id = p_entity_id
              AND t.book_code = p_book_code
              --AND t.year_month <= p_year_month --此处不限制下限是为了处理上次匹配失败的数据能再次捞取进行处理
              AND t.year_month = p_year_month
              AND t.task_code = p_task_code
              AND t.entry_state = 0
              AND t.scenario_type = 'CF'
              AND t.scenario_id IS NULL;
        --重新匹配场景
        FOR rec_scenario IN (SELECT a.scenario_id,
                                    a.scenario_condition,
                                    a.serial_no,
                                    COUNT(b.model_code_id) model_count,
                                    ' UPDATE acc_buss_entry_data a' ||
                                    ' SET scenario_id = ' || a.scenario_id ||
                                    ',scen_serial_no = ' ||
                                    coalesce(a.serial_no, 1) ||
                                    ' WHERE exists( select 1 from acc_dap_entry_data B' ||
                                    ' WHERE A.entry_data_id = B.entry_data_id' ||
                                    ' AND ' || a.scenario_condition || ')' ||
                                    ' AND A.entry_state = 0' ||
                                    ' AND A.scenario_type = ''AP''' ||
                                    ' AND A.scenario_id IS NULL' ||
                                    ' AND A.entity_id = ' || p_entity_id ||
                                    ' AND A.book_code = ''' || p_book_code || '''' ||
                                    ' AND A.year_month = ''' || p_year_month || '''' ||
                                    ' AND A.task_code = ''' || p_task_code || '''' scenario_sql
                             FROM acc_conf_scenario a
                                      LEFT JOIN acc_conf_scenario_modelref b
                                                ON a.scenario_id = b.scenario_id
                             WHERE a.valid_is = '1'
                               AND a.audit_state = '1'
                               AND MODEL_CODE_ID =
                                   (SELECT MODEL_CODE_ID
                                    FROM acc_conf_model_code
                                    WHERE MODEL_CODE_CODE = 'VoucherType')
                               AND b.CODE_CODE = v_posting_type_code
                               AND a.scenario_condition IS NOT NULL
                             HAVING COUNT(b.model_code_id) > 0
                             GROUP BY a.scenario_id,
                                      a.scenario_condition,
                                      a.serial_no
                             ORDER BY model_count DESC, scenario_id) LOOP
                BEGIN
                    EXECUTE IMMEDIATE rec_scenario.scenario_sql;

                    SELECT count(t.buss_entry_id)
                    into v_count
                    FROM acc_buss_entry_data t
                    where t.task_code = p_task_code
                      and t.scenario_id is null
                      and t.scenario_type = 'AP';
                    if v_count = 0 then
                        return; --当所有数据都匹配到场景主动结束
                    end if;

                EXCEPTION
                    WHEN OTHERS THEN
                        rollback;
                        --抛出异常提示信息
                        dbms_output.put_line('[EXCEPTION]proc_entry_re_scenario_match：' ||
                                             to_char(SQLCODE) || ';' ||
                                             substr(SQLERRM, 1, 200));
                        acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                                p_year_month,
                                                                p_task_code,
                                                                p_user_id,
                                                                rec_scenario.scenario_id,
                                                                'proc_entry_re_scenario_match',
                                                                '2',
                                                                SQLERRM);
                END;
            END LOOP;

        IF V_COUNT IS NULL OR V_COUNT > 0 THEN
            FOR rec_scenario IN (SELECT a.scenario_id,
                                        a.scenario_condition,
                                        a.serial_no,
                                        COUNT(b.model_code_id) model_count,
                                        ' UPDATE acc_buss_entry_data a' ||
                                        ' SET scenario_id = ' || a.scenario_id ||
                                        ',scen_serial_no = ' ||
                                        coalesce(a.serial_no, 1) ||
                                        ' WHERE exists( select 1 from acc_dap_entry_data B' ||
                                        ' WHERE A.entry_data_id = B.entry_data_id' ||
                                        ' AND ' || a.scenario_condition || ')' ||
                                        ' AND A.entry_state = 0' ||
                                        ' AND A.scenario_type = ''AP''' ||
                                        ' AND A.scenario_id IS NULL' ||
                                        ' AND A.entity_id = ' || p_entity_id ||
                                        ' AND A.book_code = ''' || p_book_code || '''' ||
                                        ' AND A.year_month = ''' || p_year_month || '''' ||
                                        ' AND A.task_code = ''' || p_task_code || '''' scenario_sql
                                 FROM acc_conf_scenario a
                                          LEFT JOIN acc_conf_scenario_modelref b
                                                    ON a.scenario_id = b.scenario_id
                                 WHERE a.valid_is = '1'
                                   AND a.audit_state = '1'
                                   AND a.scenario_condition IS NOT NULL
                                 HAVING COUNT(b.model_code_id) > 0
                                 GROUP BY a.scenario_id,
                                          a.scenario_condition,
                                          a.serial_no
                                 ORDER BY model_count DESC, scenario_id) LOOP
                    BEGIN
                        EXECUTE IMMEDIATE rec_scenario.scenario_sql;

                        SELECT count(t.buss_entry_id)
                        into v_count
                        FROM acc_buss_entry_data t
                        where t.task_code = p_task_code
                          and t.scenario_id is null
                          and t.scenario_type = 'AP';
                        if v_count = 0 then
                            return; --当所有数据都匹配到场景主动结束
                        end if;

                    EXCEPTION
                        WHEN OTHERS THEN
                            rollback;
                            --抛出异常提示信息
                            dbms_output.put_line('[EXCEPTION]proc_entry_re_scenario_match：' ||
                                                 to_char(SQLCODE) || ';' ||
                                                 substr(SQLERRM, 1, 200));
                            acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                                    p_year_month,
                                                                    p_task_code,
                                                                    p_user_id,
                                                                    rec_scenario.scenario_id,
                                                                    'proc_entry_re_scenario_match',
                                                                    '2',
                                                                    SQLERRM);
                    END;
                END LOOP;
        END IF;

    EXCEPTION
        WHEN OTHERS THEN
            rollback;
            --抛出异常提示信息
            dbms_output.put_line('[EXCEPTION]proc_entry_re_scenario_match：' ||
                                 to_char(SQLCODE) || ';' ||
                                 substr(SQLERRM, 1, 200));
            acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                    p_year_month,
                                                    p_task_code,
                                                    p_user_id,
                                                    null,
                                                    'proc_entry_re_scenario_match',
                                                    '2',
                                                    SQLERRM);
    END proc_entry_re_scenario_match;

    --6 对冲功能-数据检查
    PROCEDURE proc_entry_re_data_check(p_entity_id  IN NUMBER,
                                       p_book_code  IN VARCHAR2,
                                       p_year_month IN VARCHAR2,
                                       p_proc_id    IN NUMBER,
                                       p_task_code  IN VARCHAR2,
                                       p_user_id    IN NUMBER) IS

        /***********************************************************************
      NAME :acc_pack_voucher_proc_entry_data_check
      DESCRIPTION : 入账处理数据检查
      DATE :2021-04-25
      AUTHOR :YINXH
    ***********************************************************************/
        v_log_id         NUMERIC;
        v_parent_proc_id NUMERIC;
        --REC_BUSINESS record;
        v_error_msg   VARCHAR2(100);
        v_error_count number(6);
        v_char        varchar(1);
    BEGIN

        /*acc_pack_voucher.proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
    p_user_id, p_proc_id, 'proc_entry_data_check',
    '1', 'Start');*/

        SELECT b.parent_proc_id
        INTO v_parent_proc_id
        FROM bpluser.bpl_act_re_procdef b
        WHERE proc_id = p_proc_id;

        SELECT MAX(a.act_log_id)
        INTO v_log_id
        FROM bpluser.bpl_log_action a
        WHERE a.task_code = p_task_code
          AND a.system_code = 'ACC'
          AND a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.parent_proc_id = v_parent_proc_id
          AND a.proc_id = p_proc_id; --入账节点ID

        --获取核算单位、账套、会计期间对应账期的本位币

        UPDATE acc_buss_entry_data a
        SET entry_msg = '006', entry_state = 2
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.task_code = p_task_code
          AND a.entry_state = 0
          --AND a.proc_id = p_proc_id
          AND a.currency_cu_code IS NULL;

        --获取汇率异常
        UPDATE acc_buss_entry_data a
        SET entry_msg = '006', entry_state = 2
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          --AND a.proc_id = p_proc_id
          AND a.task_code = p_task_code
          AND a.entry_state = 0
          AND a.exchange_rate IS NULL;

        --匹配入账场景
        UPDATE acc_buss_entry_data a
        SET entry_msg = '010', entry_state = 2
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.task_code = p_task_code
          AND a.entry_state = 0
          --AND a.proc_id = p_proc_id
          AND scenario_id IS NULL;

        -- 删除历史入账规则,并用最新的入账规则入账
        DELETE FROM acc_buss_entry_data_detail d
        WHERE EXISTS (SELECT a.buss_entry_id
                      FROM acc_buss_entry_data a
                      WHERE a.buss_entry_id = d.buss_entry_id
                        AND a.entity_id = p_entity_id
                        AND a.book_code = p_book_code
                        AND a.year_month = p_year_month
                        AND a.task_code = p_task_code
                        --AND a.proc_id = p_proc_id
                        AND a.voucher_id IS NULL
                        AND a.entry_state <> 1);

        /*acc_pack_voucher.proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
    p_user_id, p_proc_id, 'proc_entry_data_check',
    '1', 'Step2 data check');*/

        INSERT INTO acc_buss_entry_data_detail d
        (buss_entry_dtl_id,
         buss_entry_id,
         display_no,
         account_id_dr,
         account_id_cr)
        SELECT acc_seq_buss_entry_data_dtl.nextval,
               a.buss_entry_id,
               rownum, --一个凭证有多条现金流序号
               (CASE dap.account_entry_code
                    WHEN 'D' THEN
                        item.base_account_id
                    ELSE
                        NULL
                   END),
               (CASE dap.account_entry_code
                    WHEN 'C' THEN
                        item.base_account_id
                    ELSE
                        NULL
                   END)
        FROM acc_buss_entry_data a
                 LEFT JOIN acc_dap_entry_data dap
                           ON a.entry_data_id = dap.entry_data_id
                               AND dap.account_id IS NOT NULL
                 LEFT JOIN bpluser.bbs_conf_account_mapping item
                           ON item.other_account_id = dap.account_id
        WHERE a.task_code = p_task_code
          --AND a.proc_id = p_proc_id
          AND a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.entry_state = 0
          AND a.scenario_type = 'CF'
          AND dap.account_id IS NOT NULL;

        /*acc_pack_voucher.proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
    p_user_id, p_proc_id, 'proc_entry_data_check',
    '1', 'Step3 --insert CF detail');*/
        --匹配入账科目编码
        INSERT INTO acc_buss_entry_data_detail
        (buss_entry_dtl_id,
         buss_entry_id,
         display_no,
         account_id_dr,
         account_id_cr,
         entry_serial_no)
        SELECT acc_seq_buss_entry_data_dtl.nextval,
               a.buss_entry_id,
               c.display_no,
               c.account_id_dr,
               c.account_id_cr,
               b.serial_no
        FROM acc_buss_entry_data a
                 LEFT JOIN acc_conf_entryrule b
                           ON a.scenario_id = b.scenario_id
                               AND a.entity_id = b.entity_id
                               AND a.book_code = b.book_code
                               AND b.audit_state = '1'
                               AND b.valid_is = '1'
                 LEFT JOIN acc_conf_entryrule_detail c
                           ON c.entry_rule_id = b.entry_rule_id
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.task_code = p_task_code
          --AND a.proc_id = p_proc_id
          AND a.scenario_type = 'AP'
          AND a.entry_state = 0;

        /*acc_pack_voucher.proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
    p_user_id, p_proc_id, 'proc_entry_data_check',
    '1', 'Step4 --insert AP detail');*/
        --入账场景匹配入账规则校验
        UPDATE acc_buss_entry_data a
        SET entry_msg = (CASE
                             WHEN a.scenario_type = 'CF' THEN
                                 '008' --现金流
                             ELSE
                                 '002'
            END), --非现金流
            entry_state = 2
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.task_code = p_task_code
          --AND a.proc_id = p_proc_id
          AND a.entry_state = 0
          AND NOT EXISTS
            ( --在明细表没有对应记录
                SELECT 1
                FROM acc_buss_entry_data_detail t
                WHERE t.buss_entry_id = a.buss_entry_id);

        --入账场景匹配入账规则校验
        UPDATE acc_buss_entry_data a
        SET entry_msg = (CASE
                             WHEN a.scenario_type = 'CF' THEN
                                 '008' --现金流
                             ELSE
                                 '002'
            END), --非现金流
            entry_state = 2
        WHERE a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.task_code = p_task_code
          --AND a.proc_id = p_proc_id
          AND a.entry_state = 0
          AND EXISTS ( --在明细表存在对应记录，但借、贷科目都为空
            SELECT 1
            FROM acc_buss_entry_data_detail t
            WHERE t.buss_entry_id = a.buss_entry_id
              AND t.account_id_dr IS NULL
              AND t.account_id_cr IS NULL);

        /*acc_pack_voucher.proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
    p_user_id, p_proc_id, 'proc_entry_data_check',
    '1', 'Step5--item id is null');*/
        --校验是否借贷不平

        MERGE INTO acc_buss_entry_data t
        USING (SELECT ed.entity_id,
                      ed.book_code,
                      ed.year_month,
                      ed.proc_id,
                      ed.task_code,
                      ed.posting_type_code,
                      ed.currency_cu_code,
                      b.bu_voucher_no
               FROM acc_buss_entry_data ed
                        LEFT JOIN acc_dap_entry_data b
                                  ON ed.entry_data_id = b.entry_data_id
                        LEFT JOIN acc_buss_entry_data_detail c
                                  ON c.buss_entry_id = ed.buss_entry_id
               WHERE ed.entity_id = p_entity_id
                 AND ed.book_code = p_book_code
                 AND ed.year_month = p_year_month
                 AND ed.task_code = p_task_code
               --AND ed.proc_id = p_proc_id
               GROUP BY ed.entity_id,
                        ed.book_code,
                        ed.year_month,
                        ed.proc_id,
                        ed.task_code,
                        ed.posting_type_code,
                        ed.currency_cu_code,
                        b.bu_voucher_no
               HAVING SUM((CASE
                               WHEN c.account_id_dr IS NOT NULL AND b.amount >= 0 THEN
                                       b.amount * ed.exchange_rate
                               ELSE
                                   0
                   END) + (CASE
                               WHEN c.account_id_cr IS NOT NULL AND b.amount < 0 THEN
                                   abs(b.amount * ed.exchange_rate)
                               ELSE
                                   0
                   END)) <> SUM((CASE
                                     WHEN c.account_id_cr IS NOT NULL AND
                                          b.amount >= 0 THEN
                                             b.amount * ed.exchange_rate
                                     ELSE
                                         0
                   END) + (CASE
                               WHEN c.account_id_dr IS NOT NULL AND b.amount < 0 THEN
                                   abs(b.amount * ed.exchange_rate)
                               ELSE
                                   0
                   END))) c
        ON (c.entity_id = t.entity_id AND c.book_code = t.book_code AND c.year_month = t.year_month AND c.task_code = t.task_code AND c.posting_type_code = t.posting_type_code /*AND c.proc_id = t.proc_id*/
            AND (c.bu_voucher_no = t.bu_voucher_no OR (c.bu_voucher_no IS NULL AND t.bu_voucher_no IS NULL)))
        WHEN MATCHED THEN
            UPDATE
            SET t.entry_msg = '007', t.entry_state = 2
            WHERE t.entity_id = p_entity_id
              AND t.book_code = p_book_code
              AND t.year_month = p_year_month
              AND t.task_code = p_task_code
              --AND t.proc_id = p_proc_id
              AND t.entry_state = 0;
        commit;

        /*acc_pack_voucher.proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
    p_user_id, p_proc_id, 'proc_entry_data_check',
    '1', 'Step6-drcr');*/
        /*FOR rec_business IN (SELECT a.entry_data_id, a.entry_msg
                             FROM (SELECT a.entry_data_id,
                                          a.entry_msg,
                                          row_number() over(PARTITION BY a.entry_msg ORDER BY a.entry_msg) rn
                                     FROM acc_buss_entry_data a
                                     LEFT JOIN acc_dap_entry_data b
                                       ON a.entry_data_id = b.entry_data_id
                                    WHERE a.entity_id = p_entity_id
                                      AND a.book_code = p_book_code
                                         --AND a.year_month <= p_year_month--此处不限制下限是为了处理上次匹配失败的数据能再次捞取进行处理
                                      AND a.year_month = p_year_month
                                         --AND A.ENTRY_SCENERIO_ID IS NULL
                                         --AND A.DCIND IS NULL
                                      --AND a.proc_id = p_proc_id
                                      AND a.entry_state = 2) a
                            WHERE rn <= 50) LOOP

        --记录业务日志明细表信息
        bpluser.bpl_pack_action_log.proc_add_actionlogdetails(coalesce(v_log_id,1), NULL,
                                                              --校验规则编码
                                                              '2', NULL,
                                                              --校验脚本
                                                              '0',
                                                              --0-失败
                                                              rec_business.entry_msg,
                                                              '' ||
                                                               rec_business.entry_data_id,
                                                              --异常业务主键
                                                              p_user_id
                                                              --创建人员
                                                              );

      END LOOP;
    */
        /*bpluser.bpl_pack_action_log.proc_update_logst(v_log_id, p_user_id);

    acc_pack_voucher.proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
                           p_user_id, p_proc_id, 'proc_entry_data_check',
                           '1', 'End');*/
        --当数据检查异常时主动抛异常
        SELECT count(1)
        into v_error_count
        FROM acc_buss_entry_data t
        where t.task_code = p_task_code
          and t.entry_state = '2';
        if v_error_count > 0 then
            v_error_msg := '数据检查异常';
            SELECT '*' into v_char FROM dual where 1 = 2;
        end if;
    EXCEPTION
        WHEN OTHERS THEN
            rollback;
            --抛出异常提示信息
            --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
            --RAISE EXCEPTION '[EXCEPTION]测试异常：%, %',v_text1,v_text2;
            dbms_output.put_line('[EXCEPTION]proc_entry_data_check：' ||
                                 to_char(SQLCODE) || ';' ||
                                 substr(SQLERRM, 1, 200) || '出错行数：' ||
                                 dbms_utility.format_error_backtrace());
            acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                    p_year_month,
                                                    p_task_code,
                                                    p_user_id,
                                                    p_proc_id,
                                                    'proc_entry_data_check',
                                                    '2',
                                                    v_error_msg || '  ' ||
                                                    SQLERRM);
    END proc_entry_re_data_check;

    --7对冲功能-生成凭证
    PROCEDURE proc_entry_re_voucher(p_entity_id  IN NUMBER,
                                    p_book_code  IN VARCHAR2,
                                    p_year_month IN VARCHAR2,
                                    p_proc_id    IN NUMBER,
                                    p_task_code  IN VARCHAR2,
                                    p_user_id    IN NUMBER) IS
        /***********************************************************************
      NAME :proc_entry_voucher
      DESCRIPTION :生成凭证数据处理(目前仅入账时调用)
      DATE :2022-8-23
      AUTHOR :LEIHUAN
      -------
      MODIFY LOG
      UPDATE DATE : 2023-03-01
      UPDATE BY : LY
      UPDATE DESC :
    ***********************************************************************/
        v_voucher_date DATE;
        v_message      VARCHAR2(200);
        v_log_id       NUMBER(8);
        v_voucher_no   VARCHAR2(32);
        --rec_buss_entry_data record;
    BEGIN

        /*acc_pack_voucher.proc_account_entry_log(p_entity_id, p_year_month, p_task_code,
    p_user_id, p_proc_id, 'proc_entry_voucher', '1',
    'Start');*/
        SELECT MAX(a.act_log_id)
        INTO v_log_id
        FROM bpluser.bpl_log_action a
        WHERE a.task_code = p_task_code
          AND a.system_code = 'ACC'
          AND a.entity_id = p_entity_id
          AND a.book_code = p_book_code
          AND a.year_month = p_year_month
          AND a.proc_id = p_proc_id;

        --获取凭证日期
        v_voucher_date := acc_pack_common.func_correct_voucher_date(p_year_month);
        --按照现行业务系统规则入账
        FOR cur_buss_voucher IN (SELECT a.entity_id,
                                        a.book_code,
                                        a.year_month,
                                        a.posting_type_code,
                                        a.proc_id,
                                        CAST(a.proc_id AS VARCHAR2(32)) AS key_type
                                 FROM acc_buss_entry_data a
                                 WHERE a.entity_id = p_entity_id
                                   AND a.book_code = p_book_code
                                   AND a.year_month = p_year_month
                                   AND a.task_code = p_task_code
                                   AND a.entry_state = 0
                                 --AND a.proc_id = p_proc_id
                                 GROUP BY a.entity_id,
                                          a.book_code,
                                          a.year_month,
                                          a.posting_type_code,
                                          a.proc_id) LOOP
                BEGIN

                    --按凭证类型 生成 凭证信息
                    INSERT INTO acc_buss_voucher
                    (voucher_id,
                     entity_id,
                     book_code,
                     year_month,
                     posting_type_code,
                     proc_id,
                     voucher_no,
                     effective_date,
                     ext_voucher_no,
                     state,
                     remark,
                     valid_is,
                     audit_state,
                     task_code,
                     create_time,
                     creator_id) --存放的是用户表主键非用户账户
                    SELECT acc_seq_buss_voucher.nextval,
                           entity_id,
                           book_code,
                           year_month,
                           posting_type_code,
                           proc_id,
                           acc_pack_voucher.func_generate_voucher_no(entity_id,
                                                                     book_code,
                                                                     year_month,
                                                                     posting_type_code,
                                                                     proc_id || '',
                                                                     rownum),
                           v_voucher_date,
                           bu_voucher_no,
                           '1', --1正常 2 被冲  3 冲销
                           null,
                           '1' AS valid_is, --1 有效
                           '1' AS audit_state,
                           p_task_code AS task_code,
                           localtimestamp,
                           p_user_id
                    FROM (SELECT a.entity_id,
                                 a.book_code,
                                 a.year_month,
                                 a.bu_voucher_no,
                                 a.posting_type_code,
                                 a.proc_id
                          FROM acc_buss_entry_data a
                          WHERE a.entity_id = p_entity_id
                            AND a.book_code = p_book_code
                            AND a.year_month = p_year_month
                            AND a.task_code = p_task_code
                            AND a.posting_type_code =
                                cur_buss_voucher.posting_type_code
                            --AND a.proc_id = p_proc_id
                            AND a.entry_state = 0
                          GROUP BY a.entity_id,
                                   a.book_code,
                                   a.year_month,
                                   a.bu_voucher_no,
                                   a.posting_type_code,
                                   a.proc_id) t;

                    --凭证分录处理
                    --借方科目
                    INSERT INTO acc_buss_voucher_detail
                    (voucher_dtl_id,
                     voucher_id,
                     account_id,
                     currency_code,
                     currency_cu_code,
                     account_entry_code,
                     amount,
                     amount_cu,
                     exchange_rate,
                     remark,
                     article,
                     article1,
                     article2,
                     article3,
                     article4,
                     article5,
                     article6,
                     article7,
                     article8,
                     article9,
                     article10,
                     article11,
                     article12,
                     article13,
                     article14,
                     article15,
                     article16,
                     article17,
                     entry_data_id)
                    SELECT acc_seq_buss_voucher_detail.nextval,
                           voucher_id, --凭证主表ID
                           account_id,
                           currency_code,
                           currency_cu_code,
                           account_entry_code,
                           amount,
                           amount_cu,
                           exchange_rate,
                           NULL remark,
                           nvl2(article1, article1 || '/', '') ||
                           nvl2(article2, article2 || '/', '') ||
                           nvl2(article3, article3 || '/', '') ||
                           nvl2(article4, article4 || '/', '') ||
                           nvl2(article5, article5 || '/', '') ||
                           nvl2(article6, article6 || '/', '') ||
                           nvl2(article7, article7 || '/', '') ||
                           nvl2(article8, article8 || '/', '') ||
                           nvl2(article9, article9 || '/', '') ||
                           nvl2(article10, article10 || '/', '') ||
                           nvl2(article11, article11 || '/', '') ||
                           nvl2(article12, article12 || '/', '') ||
                           nvl2(article13, article13 || '/', '') ||
                           nvl2(article14, article14 || '/', '') ||
                           nvl2(article15, article15 || '/', '') ||
                           nvl2(article16, article16 || '/', '') ||
                           nvl2(article17, article17 || '/', '') AS article,
                           article1,
                           article2,
                           article3,
                           article4,
                           article5,
                           article6,
                           article7,
                           article8,
                           article9,
                           article10,
                           article11,
                           article12,
                           article13,
                           article14,
                           article15,
                           article16,
                           article17,
                           entry_data_id
                    FROM (SELECT v.voucher_id,
                                 c.account_id_dr AS account_id, --借方科目编码
                                 a.currency_code, --原币
                                 a.currency_cu_code, --本位币
                                 (CASE
                                      WHEN SUM(b.amount) >= 0 THEN
                                          'D'
                                      ELSE
                                          'C'
                                     END) AS account_entry_code, --借贷方科目编码(被冲借贷相反)
                                 abs(SUM(b.amount)) AS amount, --原币金额
                                 round(abs(SUM(b.amount)) * a.exchange_rate, 2) AS amount_cu, --本位币金额
                                 a.exchange_rate AS exchange_rate,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE1',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article1,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE2',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article2,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE3',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article3,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE4',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article4,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE5',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article5,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE6',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article6,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE7',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article7,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE8',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article8,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE9',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article9,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE10',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article10,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE11',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article11,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE12',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article12,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE13',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article13,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE14',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article14,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE15',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article15,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE16',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article16,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE17',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article17,
                                 b.entry_data_id
                          FROM acc_buss_entry_data a
                                   LEFT JOIN acc_dap_entry_data b
                                             ON a.entry_data_id = b.entry_data_id
                                   LEFT JOIN acc_buss_entry_data_detail c
                                             ON c.buss_entry_id = a.buss_entry_id
                                   LEFT JOIN acc_buss_voucher v
                                             ON v.entity_id = a.entity_id
                                                 AND v.book_code = a.book_code
                                                 AND v.year_month = a.year_month
                                                 AND v.proc_id = a.proc_id
                                                 AND v.posting_type_code = a.posting_type_code
                                                 AND v.task_code = p_task_code
                                                 AND (v.ext_voucher_no = a.bu_voucher_no OR
                                                      (v.ext_voucher_no IS NULL AND
                                                       a.bu_voucher_no IS NULL))
                          WHERE a.entity_id = p_entity_id
                            AND a.book_code = p_book_code
                            AND a.year_month = p_year_month
                            --AND a.proc_id = p_proc_id
                            AND a.posting_type_code =
                                cur_buss_voucher.posting_type_code
                            AND a.task_code = p_task_code
                            AND a.entry_state = 0
                            AND c.account_id_dr IS NOT NULL
                            AND v.state = '1'
                          GROUP BY v.voucher_id,
                                   c.account_id_dr, --借方科目编码
                                   a.currency_code, --原币
                                   a.currency_cu_code, --本位币
                                   a.exchange_rate,
                                   b.entry_data_id);

                    --贷方科目
                    INSERT INTO acc_buss_voucher_detail
                    (voucher_dtl_id,
                     voucher_id,
                     account_id,
                     currency_code,
                     currency_cu_code,
                     account_entry_code,
                     amount,
                     amount_cu,
                     exchange_rate,
                     remark,
                     article,
                     article1,
                     article2,
                     article3,
                     article4,
                     article5,
                     article6,
                     article7,
                     article8,
                     article9,
                     article10,
                     article11,
                     article12,
                     article13,
                     article14,
                     article15,
                     article16,
                     article17,
                     entry_data_id)
                    SELECT acc_seq_buss_voucher_detail.nextval,
                           voucher_id, --凭证主表ID
                           account_id,
                           currency_code,
                           currency_cu_code,
                           account_entry_code,
                           amount,
                           amount_cu,
                           exchange_rate,
                           NULL remark,
                           nvl2(article1, article1 || '/', '') ||
                           nvl2(article2, article2 || '/', '') ||
                           nvl2(article3, article3 || '/', '') ||
                           nvl2(article4, article4 || '/', '') ||
                           nvl2(article5, article5 || '/', '') ||
                           nvl2(article6, article6 || '/', '') ||
                           nvl2(article7, article7 || '/', '') ||
                           nvl2(article8, article8 || '/', '') ||
                           nvl2(article9, article9 || '/', '') ||
                           nvl2(article10, article10 || '/', '') ||
                           nvl2(article11, article11 || '/', '') ||
                           nvl2(article12, article12 || '/', '') ||
                           nvl2(article13, article13 || '/', '') ||
                           nvl2(article14, article14 || '/', '') ||
                           nvl2(article15, article15 || '/', '') ||
                           nvl2(article16, article16 || '/', '') ||
                           nvl2(article17, article17 || '/', '') AS article,
                           article1,
                           article2,
                           article3,
                           article4,
                           article5,
                           article6,
                           article7,
                           article8,
                           article9,
                           article10,
                           article11,
                           article12,
                           article13,
                           article14,
                           article15,
                           article16,
                           article17,
                           entry_data_id
                    FROM (SELECT v.voucher_id,
                                 c.account_id_cr account_id, --贷方科目编码
                                 b.currency_code, --原币
                                 a.currency_cu_code, --本位币
                                 (CASE
                                      WHEN SUM(b.amount) >= 0 THEN
                                          'C'
                                      ELSE
                                          'D'
                                     END) AS account_entry_code, --借贷方科目编码(被冲借贷相反)
                                 abs(SUM(b.amount)) AS amount, --原币金额
                                 round(abs(SUM(b.amount)) * a.exchange_rate, 2) AS amount_cu, --本位币金额
                                 a.exchange_rate AS exchange_rate,

                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE1',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article1,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE2',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article2,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE3',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article3,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE4',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article4,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE5',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article5,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE6',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article6,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE7',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article7,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE8',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article8,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE9',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article9,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE10',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article10,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE11',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article11,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE12',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article12,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE13',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article13,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE14',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article14,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE15',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article15,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE16',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article16,
                                 acc_pack_common.func_analy_model_mapping('ACC_DAP_ENTRY_DATA',
                                                                          'ACC_BUSS_VOUCHER_DETAIL',
                                                                          'ARTICLE17',
                                                                          'entry_data_id',
                                                                          '=',
                                                                          b.entry_data_id || '',
                                                                          'number') AS article17,
                                 b.entry_data_id
                          FROM acc_buss_entry_data a
                                   LEFT JOIN acc_dap_entry_data b
                                             ON a.entry_data_id = b.entry_data_id
                                   LEFT JOIN acc_buss_entry_data_detail c
                                             ON c.buss_entry_id = a.buss_entry_id
                                   LEFT JOIN acc_buss_voucher v
                                             ON v.entity_id = a.entity_id
                                                 AND v.book_code = a.book_code
                                                 AND v.year_month = a.year_month
                                                 AND v.proc_id = a.proc_id
                                                 AND v.posting_type_code = a.posting_type_code
                                                 AND (v.ext_voucher_no = a.bu_voucher_no OR
                                                      (v.ext_voucher_no IS NULL AND
                                                       a.bu_voucher_no IS NULL))
                                                 AND v.task_code = p_task_code
                          WHERE a.entity_id = p_entity_id
                            AND a.book_code = p_book_code
                            AND a.year_month = p_year_month
                            AND a.posting_type_code =
                                cur_buss_voucher.posting_type_code
                            --AND a.proc_id = p_proc_id
                            AND a.task_code = p_task_code
                            AND a.entry_state = 0
                            AND c.account_id_cr IS NOT NULL
                            and v.state = '1'
                          GROUP BY v.voucher_id,
                                   c.account_id_cr, --贷方科目编码
                                   b.currency_code, --原币
                                   a.currency_cu_code, --本位币
                                   a.exchange_rate,
                                   b.entry_data_id);

                    --更新待入账表信息
                    MERGE INTO acc_buss_entry_data t
                    USING (SELECT v.voucher_id,
                                  v.entity_id,
                                  v.book_code,
                                  v.year_month,
                                  v.posting_type_code,
                                  v.proc_id,
                                  v.ext_voucher_no
                           FROM acc_buss_voucher v
                           WHERE v.entity_id = p_entity_id
                             AND v.book_code = p_book_code
                             AND v.year_month = p_year_month
                             AND v.posting_type_code =
                                 cur_buss_voucher.posting_type_code
                             --AND v.proc_id = p_proc_id
                             AND v.state = '1'
                             AND v.task_code = p_task_code) c
                    ON (c.entity_id = t.entity_id AND c.book_code = t.book_code AND c.year_month = t.year_month AND c.posting_type_code = t.posting_type_code AND c.proc_id = t.proc_id AND (c.ext_voucher_no = t.bu_voucher_no OR (c.ext_voucher_no IS NULL AND t.bu_voucher_no IS NULL)))
                    WHEN MATCHED THEN
                        UPDATE
                        SET t.voucher_id = c.voucher_id, entry_state = 1
                        WHERE t.entity_id = p_entity_id
                          AND t.book_code = p_book_code
                          AND t.year_month = p_year_month
                          AND t.posting_type_code = cur_buss_voucher.posting_type_code
                          --AND t.proc_id = p_proc_id
                          AND t.task_code = p_task_code
                          AND t.entry_state = 0;

                    --更新冲销凭证
                    update acc_buss_voucherhis t
                    set t.voucher_no =
                            (SELECT a.voucher_no
                             FROM acc_buss_voucher a, acc_buss_voucher b
                             where a.ext_voucher_no = b.ext_voucher_no
                               and b.state = '3' --被冲销
                               and a.state = '1' --正常
                               and b.voucher_no = t.re_voucher_no
                               and a.task_code = p_task_code
                               and a.task_code = b.task_code)
                    where exists (SELECT a.voucher_no
                                  FROM acc_buss_voucher a, acc_buss_voucher b
                                  where a.ext_voucher_no = b.ext_voucher_no
                                    and b.state = '3' --被冲销
                                    and a.state = '1' --正常
                                    and b.voucher_no = t.re_voucher_no
                                    and a.task_code = p_task_code
                                    and a.task_code = b.task_code);

                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;

                    --删除借贷异常的凭证信息
                    /*DELETE FROM acc_buss_voucher_detail d
         WHERE EXISTS (SELECT 1
                  FROM acc_buss_voucher v
                 WHERE v.voucher_id = d.voucher_id
                   AND v.entity_id = p_entity_id
                   AND v.book_code = p_book_code
                   AND v.year_month = p_year_month
                   AND v.remark = p_task_code
                   AND v.posting_type_code = cur_buss_voucher.posting_type_code
                   AND v.proc_id = p_proc_id);

        DELETE FROM acc_buss_voucher v
         WHERE v.entity_id = p_entity_id
           AND v.book_code = p_book_code
           AND v.year_month = p_year_month
           AND v.remark = p_task_code
           AND v.posting_type_code = cur_buss_voucher.posting_type_code
           AND v.proc_id = p_proc_id;*/

                    --插入失败记录子信息 按照凭证号码写入
                    /* bpluser.bpl_pack_action_log.proc_add_actionlogdetails(v_log_id,
        --日志主表主键ID
        NULL,
        --校验规则编码
        '2', NULL,
        --校验脚本
        '0',
        --状态 0 失败 1 成功
        SQLERRM,
        --异常原因  入账成功.
        '' ||
         p_proc_id ||
         cur_buss_voucher.posting_type_code,
        --异常业务主键
        p_user_id
        --创建人员
        );*/
                END;

                --校正凭证序列
                acc_pack_voucher.proc_correct_voucher_key(p_entity_id,
                                                          p_book_code,
                                                          p_year_month,
                                                          cur_buss_voucher.posting_type_code,
                                                          cur_buss_voucher.key_type);
            END LOOP;

        update acc_buss_voucher t
        set t.remark = null
        where t.task_code = p_task_code;

        commit; --最后提交
    EXCEPTION
        WHEN OTHERS THEN
            rollback;
            --提示异常信息
            acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                    p_year_month,
                                                    p_task_code,
                                                    p_user_id,
                                                    p_proc_id,
                                                    'proc_entry_re_voucher',
                                                    '2',
                                                    SQLERRM);
    END proc_entry_re_voucher;
    --凭证对冲 end

    PROCEDURE proc_entry_deal_cmunit(p_entity_id  IN NUMBER,
                                     p_book_code  IN VARCHAR2,
                                     p_year_month IN VARCHAR2,
                                     p_user_id    IN NUMBER) AS
        /***********************************************************************
      NAME :proc_entry_deal_cmunit
      DESCRIPTION : 月末处理收付数据的计量单元信息
      DATE :2021-04-25
      AUTHOR :YINXH
    ***********************************************************************/
        v_sql         VARCHAR(4000);
        v_year_month  VARCHAR(6);
        V_COUNT       NUMBER(11);
        v_char        varchar(1);
        v_uw_proc_id  NUMBER(11);
        v_CL_proc_id  NUMBER(11);
        v_RI_proc_id  NUMBER(11);
        v_STL_proc_id NUMBER(11);
    BEGIN
        v_uw_proc_id  := BPLUSER.BPL_PACK_COMMON.FUNC_GET_PROCID('ACC_ACCOUNTENTRY_UW_ACR');
        v_CL_proc_id  := BPLUSER.BPL_PACK_COMMON.FUNC_GET_PROCID('ACC_ACCOUNTENTRY_CL_ACR');
        v_RI_proc_id  := BPLUSER.BPL_PACK_COMMON.FUNC_GET_PROCID('ACC_ACCOUNTENTRY_RI_ACR');
        v_STL_proc_id := BPLUSER.BPL_PACK_COMMON.FUNC_GET_PROCID('ACC_ACCOUNTENTRY_COMP_STL_ACR');

        --获取当前允许操作的会计期间
        SELECT MIN(year_month)
        INTO v_year_month
        FROM acc_conf_accountperiod c
        WHERE c.entity_id = p_entity_id
          AND c.book_code = p_book_code
          AND c.year_month = p_year_month
          AND c.execution_state <> '3';

        IF v_year_month IS NULL THEN
            dbms_output.put_line('会计期间不可执行：v_year_month: ' || v_year_month);
            RETURN;
        END IF;

        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               null,
                               p_user_id,
                               null,
                               'proc_entry_deal_cmunit',
                               '1',
                               'End');

        MERGE INTO ACC_DAP_ENTRY_DATA T
        USING (SELECT BC.POLICY_NO,
                      BC.ICG_NO,
                      BC.PORTFOLIO_NO,
                      BC.CMUNIT_NO,
                      LOA_CODE
               FROM DMUSER.dm_buss_cmunit_direct BC
               WHERE bc.entity_id = p_entity_id
                 AND BC.YEAR_MONTH IS NOT NULL) C
        ON (C.POLICY_NO = T.POLICY_NO)
        WHEN MATCHED THEN
            UPDATE
            SET T.ICG_NO         = C.ICG_NO,
                T.PORTFOLIO_NO   = C.PORTFOLIO_NO,
                T.EXTEND_COLUMN3 = C.CMUNIT_NO,
                t.EXTEND_COLUMN6 = LOA_CODE
            WHERE T.entity_id = p_entity_id
              AND YEAR_MONTH = p_year_month
              AND PROC_ID IN (v_uw_proc_id, v_CL_proc_id)
              AND EXPENSES_TYPE_CODE <> 'cashflow';
        COMMIT;
        --直接业务\临分分入
        MERGE INTO ACC_DAP_ENTRY_DATA T
        USING (SELECT BC.POLICY_NO,
                      BC.ICG_NO,
                      BC.PORTFOLIO_NO,
                      BC.CMUNIT_NO,
                      LOA_CODE
               FROM DMUSER.dm_buss_cmunit_direct BC
               WHERE bc.entity_id = p_entity_id
                 AND BC.YEAR_MONTH IS NOT NULL) C
        ON (C.POLICY_NO = T.POLICY_NO)
        WHEN MATCHED THEN
            UPDATE
            SET T.ICG_NO         = C.ICG_NO,
                T.PORTFOLIO_NO   = C.PORTFOLIO_NO,
                T.EXTEND_COLUMN3 = C.CMUNIT_NO,
                t.EXTEND_COLUMN6 = LOA_CODE
            WHERE T.entity_id = p_entity_id
              AND YEAR_MONTH = P_YEAR_MONTH
              AND PROC_ID IN (V_RI_PROC_ID, V_STL_PROC_ID)
              AND ((RI_ARRANGEMENT_CODE = 'D' AND BUSINESS_SOURCE_CODE = 'DB' AND
                    RI_DIRECTION_CODE = 'D') OR
                   (RI_ARRANGEMENT_CODE = 'D' AND BUSINESS_SOURCE_CODE = 'FB' AND
                    RI_DIRECTION_CODE = 'I'))
              AND EXPENSES_TYPE_CODE NOT IN ('cashflow', '411');
        COMMIT;
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               null,
                               p_user_id,
                               null,
                               'proc_entry_deal_cmunit',
                               '2',
                               'End');
        --临分分出、非比例临分分出（直接业务转临分分出，临分分入转临分分出，直接业务转非比例临分分出，临分分入转非比例临分分出）
        MERGE INTO ACC_DAP_ENTRY_DATA T
        USING (SELECT OD.RI_STATEMENT_NO, OD.EXPENSES_TYPE_CODE, OD.RI_POLICY_NO
               FROM DMUSER.DM_REINS_OUTWARD_DETAIL OD
               WHERE OD.entity_id = p_entity_id
                 AND OD.RI_POLICY_NO IS NOT NULL
               GROUP BY OD.RI_STATEMENT_NO,
                        OD.EXPENSES_TYPE_CODE,
                        OD.RI_POLICY_NO) C
        ON (C.RI_STATEMENT_NO = T.RI_STATEMENT_NO AND C.EXPENSES_TYPE_CODE = T.EXPENSES_TYPE_CODE)
        WHEN MATCHED THEN
            UPDATE
            SET T.RI_POLICY_NO = C.RI_POLICY_NO
            WHERE T.entity_id = p_entity_id
              AND T.YEAR_MONTH = P_YEAR_MONTH
              AND T.PROC_ID IN (V_RI_PROC_ID, V_STL_PROC_ID)
              AND T.RI_ARRANGEMENT_CODE IN ('F', 'NF')
              AND T.RI_DIRECTION_CODE = 'O'
              AND T.EXPENSES_TYPE_CODE NOT IN ('cashflow', '411');
        COMMIT;

        MERGE INTO ACC_DAP_ENTRY_DATA T
        USING (SELECT BB.FAC_NO, ICG_NO, PORTFOLIO_NO, CMUNIT_NO, BB.LOA_CODE
               FROM DMUSER.DM_BUSS_CMUNIT_FAC_OUTWARDS BB
               WHERE BB.entity_id = p_entity_id
                 AND BB.YEAR_MONTH IS NOT NULL) C
        ON (C.FAC_NO = T.RI_POLICY_NO)
        WHEN MATCHED THEN
            UPDATE
            SET T.ICG_NO         = C.ICG_NO,
                T.PORTFOLIO_NO   = C.PORTFOLIO_NO,
                T.EXTEND_COLUMN3 = C.CMUNIT_NO,
                t.EXTEND_COLUMN6 = C.LOA_CODE
            WHERE T.entity_id = p_entity_id
              AND T.YEAR_MONTH = P_YEAR_MONTH
              AND T.PROC_ID IN (V_RI_PROC_ID, V_STL_PROC_ID)
              AND T.RI_ARRANGEMENT_CODE IN ('F', 'NF')
              AND T.RI_DIRECTION_CODE = 'O'
              AND T.EXPENSES_TYPE_CODE NOT IN ('cashflow', '411');
        COMMIT;
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               null,
                               p_user_id,
                               null,
                               'proc_entry_deal_cmunit',
                               '3',
                               'End');
        --合约分入
        MERGE INTO ACC_DAP_ENTRY_DATA T
        USING (SELECT BC.TREATY_NO,
                      BC.ICG_NO,
                      BC.PORTFOLIO_NO,
                      BC.CMUNIT_NO,
                      BC.LOA_CODE
               FROM DMUSER.DM_BUSS_CMUNIT_TREATY BC
               WHERE bc.entity_id = p_entity_id
                 AND BC.YEAR_MONTH IS NOT NULL
                 AND BC.RI_DIRECTION_CODE = 'I') C
        ON (C.TREATY_NO = T.TREATY_CODE || T.TREATY_YEAR)
        WHEN MATCHED THEN
            UPDATE
            SET T.ICG_NO         = C.ICG_NO,
                T.PORTFOLIO_NO   = C.PORTFOLIO_NO,
                T.EXTEND_COLUMN3 = C.CMUNIT_NO,
                T.EXTEND_COLUMN6 = C.LOA_CODE
            WHERE T.entity_id = p_entity_id
              AND YEAR_MONTH = P_YEAR_MONTH
              AND PROC_ID IN (V_RI_PROC_ID, V_STL_PROC_ID)
              AND RI_ARRANGEMENT_CODE = 'D'
              AND BUSINESS_SOURCE_CODE = 'TB'
              AND RI_DIRECTION_CODE = 'I'
              AND EXPENSES_TYPE_CODE NOT IN ('cashflow', '411');
        COMMIT;
        --合约分出
        MERGE INTO ACC_DAP_ENTRY_DATA T
        USING (SELECT BC.TREATY_NO,
                      BC.ICG_NO,
                      BC.PORTFOLIO_NO,
                      BC.CMUNIT_NO,
                      BC.LOA_CODE
               FROM DMUSER.DM_BUSS_CMUNIT_TREATY BC
               WHERE bc.entity_id = p_entity_id
                 AND BC.YEAR_MONTH IS NOT NULL
                 AND BC.RI_DIRECTION_CODE = 'O') C
        ON (C.TREATY_NO = T.TREATY_CODE || T.TREATY_YEAR)
        WHEN MATCHED THEN
            UPDATE
            SET T.ICG_NO         = C.ICG_NO,
                T.PORTFOLIO_NO   = C.PORTFOLIO_NO,
                T.EXTEND_COLUMN3 = C.CMUNIT_NO,
                T.EXTEND_COLUMN6 = C.LOA_CODE
            WHERE T.entity_id = p_entity_id
              AND YEAR_MONTH = P_YEAR_MONTH
              AND PROC_ID IN (V_RI_PROC_ID, V_STL_PROC_ID)
              AND RI_ARRANGEMENT_CODE IN ('X', 'T')
              AND RI_DIRECTION_CODE = 'O'
              AND EXPENSES_TYPE_CODE NOT IN ('cashflow', '411');
        COMMIT;
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               null,
                               p_user_id,
                               null,
                               'proc_entry_deal_cmunit',
                               '4',
                               'End');
        FOR rec_article IN (SELECT t.source_column, t.MAPPING_COLUMN
                            FROM bpluser.bbs_conf_model_mapping t
                            WHERE source_table = 'ACC_DAP_ENTRY_DATA'
                              AND mapping_table = 'ACC_BUSS_VOUCHER_DETAIL'
                              AND SOURCE_COLUMN IN
                                  ('PORTFOLIO_NO',
                                   'ICG_NO',
                                   'EXTEND_COLUMN3',
                                   'EXTEND_COLUMN6')) LOOP
                v_sql := '';
                v_sql := 'UPDATE acc_buss_voucher_detail t2 SET ' ||
                         rec_article.mapping_column || '=' || '(select ' ||
                         rec_article.source_column ||
                         ' from ACC_DAP_ENTRY_DATA ed where ed.entry_data_id= t2.entry_data_id)' ||
                         ' WHERE EXISTS (SELECT 1 FROM acc_buss_voucher t1 WHERE t1.voucher_id=t2.voucher_id

  AND t1.entity_id =' || p_entity_id ||
                         'AND t1.BOOK_CODE =''' || p_book_code ||
                         ''' AND  t1.year_month=''' || p_year_month || ''' and ' ||
                         't1.proc_id in (' || v_uw_proc_id || ',' || v_CL_proc_id || ',' ||
                         v_RI_proc_id || ',' || v_STL_proc_id || ')  and ' ||
                         rec_article.mapping_column || ' is  null )';
                -- dbms_output.put_line(v_sql);
                EXECUTE IMMEDIATE v_sql;
                COMMIT;
            END LOOP;
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               null,
                               p_user_id,
                               null,
                               'proc_entry_deal_cmunit',
                               '5',
                               'End');
        UPDATE acc_buss_voucher_detail t2
        SET t2.article = nvl2(article1, article1 || '/', '') ||
                         nvl2(article2, article2 || '/', '') ||
                         nvl2(article3, article3 || '/', '') ||
                         nvl2(article4, article4 || '/', '') ||
                         nvl2(article5, article5 || '/', '') ||
                         nvl2(article6, article6 || '/', '') ||
                         nvl2(article7, article7 || '/', '') ||
                         nvl2(article8, article8 || '/', '') ||
                         nvl2(article9, article9 || '/', '') ||
                         nvl2(article10, article10 || '/', '') ||
                         nvl2(article11, article11 || '/', '') ||
                         nvl2(article12, article12 || '/', '') ||
                         nvl2(article13, article13 || '/', '') ||
                         nvl2(article14, article14 || '/', '') ||
                         nvl2(article15, article15 || '/', '') ||
                         nvl2(article16, article16 || '/', '') ||
                         nvl2(article17, article17 || '/', '')
        WHERE EXISTS
                  (SELECT 1
                   FROM acc_buss_voucher t1
                   WHERE t1.voucher_id = t2.voucher_id
                     AND t1.entity_id = p_entity_id
                     AND t1.book_code = p_book_code
                     AND t1.year_month = p_year_month
                     AND t1.proc_id IN
                         (v_uw_proc_id, v_CL_proc_id, v_RI_proc_id, v_STL_proc_id));
        COMMIT;
        proc_account_entry_log(p_entity_id,
                               p_year_month,
                               null,
                               p_user_id,
                               null,
                               'proc_entry_deal_cmunit',
                               '6',
                               'End');
    EXCEPTION
        WHEN OTHERS THEN
            --往外层抛出异常信息
            dbms_output.put_line('**出错行数: ' ||
                                 dbms_utility.format_error_backtrace());
            raise_application_error(-20003, '收付数据入账异常:' || SQLERRM);
    END proc_entry_deal_cmunit;

END acc_pack_voucher;
/

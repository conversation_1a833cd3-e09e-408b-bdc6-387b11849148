---------- drop ---------------
do language plpgsql
$$
    declare
        rec record;
    begin
        for rec in select t.proname, t.prokind from pg_proc t where t.proname ~ '^atr_pack_ecf_dap_.*'
            loop
                if rec.prokind = 'f' then
                    call atr_pack_commonutils_proc_drop_func(rec.proname::varchar);
                elsif rec.prokind = 'p' then
                    call atr_pack_commonutils_proc_drop_proc(rec.proname::varchar);
                end if;
            end loop;
    end;
$$;
DROP TYPE IF EXISTS atruser.atr_pack_ecf_dap_record_buss;



---------- create ---------------
CREATE TYPE atruser.atr_pack_ecf_dap_record_buss AS (
	entity_id bigint,
	year_month character varying(6),
	ev_date timestamp without time zone,
	action_no character varying(60),
	buss_period_id bigint,
	is_without_bussperiod character varying(1),
	base_currency character varying(3),
	is_bcai character varying(1)
);

CREATE FUNCTION atruser.atr_pack_ecf_dap_func_check_if_biz_continue(p_buss atruser.atr_pack_ecf_dap_record_buss, p_biz_code character varying) RETURNS bigint
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------------------
    -- 检查模型是否可继续处理； 已处理的模型不应重复处理。
    -- 0-不可继续处理， 1-可继续处理
    ------------------------------------------------------
    v_count integer;

begin

    if p_buss.is_without_bussperiod = '1' then
        return 1;
    end if;

    select count(*)
    into v_count
    from atr_conf_bussperiod_detail t
    where t.buss_period_id = p_buss.buss_period_id
      and t.ready_state = '0'
      and exists (select *
                  from atr_conf_table c
                  where c.biz_code = p_biz_code
                    and c.biz_type_id = t.biz_type_id);

    if v_count = 1 then
        return 1;
    end if;

    if v_count > 1 then
        raise exception '%', '模型' || p_biz_code || '业务期间' ||
                             p_buss.year_month ||
                             '有多条详情，无法处理，请核实业务期间控制详情表' using errcode = '45081';
    end if;

    return 0;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_log_warn(IN p_action_no character varying, IN p_business_info character varying, IN p_mark character varying, IN p_msg character varying)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_ecf_dap_proc_log('WARN', p_action_no,
                                   p_business_info, p_mark, p_msg);
end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_dap_func_get_business_info(p_action_no character varying, p_entity_id bigint, f_business_no character varying, f_get_type character varying, f_business_type character varying, f_reins_direction character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
declare
    v_result varchar(2000);
begin
    if f_get_type = 'ICG' then
        --合同组号码
        if f_business_type in ('DB', 'FB') and f_reins_direction is null then
            select a.icg_no
            into v_result
            from dmuser.dm_buss_cmunit_direct a
            where a.entity_id = p_entity_id
              and a.policy_no = f_business_no
            limit 1;
        elsif f_business_type in ('FB') and f_reins_direction = 'O' then
            select a.icg_no
            into v_result
            from dmuser.dm_buss_cmunit_fac_outwards a
            where a.entity_id = p_entity_id
              and a.fac_no = f_business_no
            limit 1;
        else
            select a.icg_no
            into v_result
            from dmuser.dm_buss_cmunit_treaty a
            where a.entity_id = p_entity_id
              and a.treaty_no = f_business_no
            limit 1;
        end if;

    elsif f_get_type = 'ICP' then
        --合同组合号码
        if f_business_type in ('DB', 'FB') and f_reins_direction is null then
            select a.portfolio_no
            into v_result
            from dmuser.dm_buss_cmunit_direct a
            where a.entity_id = p_entity_id
              and a.policy_no = f_business_no
            limit 1;
        elsif f_business_type in ('FB') and f_reins_direction = 'O' then
            select a.portfolio_no
            into v_result
            from dmuser.dm_buss_cmunit_fac_outwards a
            where a.entity_id = p_entity_id
              and a.fac_no = f_business_no
            limit 1;
        else
            select a.portfolio_no
            into v_result
            from dmuser.dm_buss_cmunit_treaty a
            where a.entity_id = p_entity_id
              and a.treaty_no = f_business_no
            limit 1;
        end if;

    elsif f_get_type = 'UNIT' then
        --计量单元号码
        if f_business_type in ('DB', 'FB') and f_reins_direction is null then
            select a.cmunit_no
            into v_result
            from dmuser.dm_buss_cmunit_direct a
            where a.entity_id = p_entity_id
              and a.policy_no = f_business_no
            limit 1;
        elsif f_business_type in ('FB') and f_reins_direction = 'O' then
            select a.cmunit_no
            into v_result
            from dmuser.dm_buss_cmunit_fac_outwards a
            where a.entity_id = p_entity_id
              and a.fac_no = f_business_no
            limit 1;
        else
            select a.cmunit_no
            into v_result
            from dmuser.dm_buss_cmunit_treaty a
            where a.entity_id = p_entity_id
              and a.treaty_no = f_business_no
            limit 1;
        end if;

    elsif f_get_type = 'PRODUCT' then
        --产品代码
        if f_business_type in ('DB', 'FB') then
            select a.product_code
            into v_result
            from dmuser.dm_policy_premium a
            where a.entity_id = p_entity_id
              and a.policy_no = f_business_no
            limit 1;
        end if;

    elsif f_get_type = 'LOA' then
        --LOA-业务线条编码
        if f_business_type in ('DB', 'FB') and f_reins_direction is null then
            select a.loa_code
            into v_result
            from dmuser.dm_buss_cmunit_direct a
            where a.entity_id = p_entity_id
              and a.policy_no = f_business_no
            limit 1;
        elsif f_business_type in ('FB') and f_reins_direction = 'O' then
            select a.loa_code
            into v_result
            from dmuser.dm_buss_cmunit_fac_outwards a
            where a.entity_id = p_entity_id
              and a.fac_no = f_business_no
            limit 1;
        else
            select a.loa_code
            into v_result
            from dmuser.dm_buss_cmunit_treaty a
            where a.entity_id = p_entity_id
              and a.treaty_no = f_business_no
            limit 1;
        end if;
    elsif f_get_type = 'MODEL' then
        --评估方法
        if f_business_type in ('DB', 'FB') and f_reins_direction is null then
            select a.evaluate_approach
            into v_result
            from dmuser.dm_buss_cmunit_direct a
            where a.entity_id = p_entity_id
              and a.policy_no = f_business_no
            limit 1;
        elsif f_business_type in ('FB') and f_reins_direction = 'O' then
            select a.evaluate_approach
            into v_result
            from dmuser.dm_buss_cmunit_fac_outwards a
            where a.entity_id = p_entity_id
              and a.fac_no = f_business_no
            limit 1;
        else
            select a.evaluate_approach
            into v_result
            from dmuser.dm_buss_cmunit_treaty a
            where a.entity_id = p_entity_id
              and a.treaty_no = f_business_no
            limit 1;
        end if;
    elsif f_get_type = 'RISKCLASS' then
        -- 风险大类
        if f_business_type in ('DB', 'FB') and f_reins_direction is null then
            select a.risk_class_code
            into v_result
            from dmuser.dm_buss_cmunit_direct a
            where a.entity_id = p_entity_id
              and a.policy_no = f_business_no
            limit 1;
        elsif f_business_type in ('FB') and f_reins_direction = 'O' then
            select a.risk_class_code
            into v_result
            from dmuser.dm_buss_cmunit_fac_outwards a
            where a.entity_id = p_entity_id
              and a.fac_no = f_business_no
            limit 1;
        else
            select a.risk_class_code
            into v_result
            from dmuser.dm_buss_cmunit_treaty a
            where a.entity_id = p_entity_id
              and a.treaty_no = f_business_no
            limit 1;
        end if;

    elsif f_get_type = 'DOA' then
        --事故日期
        select to_char(t.accident_date_time, 'YYYY-MM-DD')
        into v_result
        from dmuser.dm_claim_loss t
        where t.entity_id = p_entity_id
          and t.claim_loss_no = f_business_no
        limit 1;
    elsif f_get_type = 'LOSSUW' then
        --核赔日期
        select to_char(a.approval_date, 'YYYY-MM-DD')
        into v_result
        from dmuser.dm_claim_loss a
        where a.entity_id = p_entity_id
          and a.claim_loss_no = f_business_no
          and a.claim_loss_seq_no = f_reins_direction::numeric;
--          AND TO_NUMBER(A.CLAIM_LOSS_SEQ_NO) = F_REINS_DIRECTION;
    elsif f_get_type = 'CLAIM' then
        --立案号码
        select a.claim_no
        into v_result
        from dmuser.dm_claim_loss a
        where a.entity_id = p_entity_id
          and a.claim_loss_no = f_business_no
        limit 1;
    elsif f_get_type = 'STATUS' then
        --保单状态
        select a.policy_status_code
        into v_result
        from dmuser.dm_policy_main a
        where a.entity_id = p_entity_id
          and a.policy_no = f_business_no
          and a.endorse_seq_no = f_reins_direction;
    elsif f_get_type = 'POLICY_TYPE_CODE' then
        --保单类型
        select a.policy_type_code
        into v_result
        from dmuser.dm_policy_main a
        where a.entity_id = p_entity_id
          and a.policy_no = f_business_no
          and a.endorse_seq_no = f_reins_direction;
    elsif f_get_type = 'DEPT_ID' then
        --业务部门
        select a.dept_id
        into v_result
        from dmuser.dm_policy_main a
        where a.entity_id = p_entity_id
          and a.policy_no = f_business_no
        limit 1;
    elsif f_get_type = 'ENDORSE_TYPE_CODE' then
        --批改类型
        select a.endorse_type_code
        into v_result
        from dmuser.dm_policy_main a
        where a.entity_id = p_entity_id
          and a.policy_no = f_business_no
          and a.endorse_seq_no = f_reins_direction;
    elsif f_get_type = 'LOAP' then
        --LOA-业务线条编码
        if f_business_type in ('DB', 'FB') and f_reins_direction is null then
            select a.loa_code
            into v_result
            from dmuser.dm_buss_cmunit_direct a
            where a.entity_id = p_entity_id
              and a.portfolio_no = f_business_no
            limit 1;
        elsif f_business_type in ('FB') and f_reins_direction = 'O' then
            select a.loa_code
            into v_result
            from dmuser.dm_buss_cmunit_fac_outwards a
            where a.entity_id = p_entity_id
              and a.portfolio_no = f_business_no
            limit 1;
        else
            select a.loa_code
            into v_result
            from dmuser.dm_buss_cmunit_treaty a
            where a.entity_id = p_entity_id
              and a.portfolio_no = f_business_no
            limit 1;
        end if;
    elsif f_get_type = 'MODELP' then
        --评估方法
        if f_business_type in ('DB', 'FB') and f_reins_direction is null then
            select a.evaluate_approach
            into v_result
            from dmuser.dm_buss_cmunit_direct a
            where a.entity_id = p_entity_id
              and a.portfolio_no = f_business_no
            limit 1;
        elsif f_business_type in ('FB') and f_reins_direction = 'O' then
            select a.evaluate_approach
            into v_result
            from dmuser.dm_buss_cmunit_fac_outwards a
            where a.entity_id = p_entity_id
              and a.portfolio_no = f_business_no
            limit 1;
        else
            select a.evaluate_approach
            into v_result
            from dmuser.dm_buss_cmunit_treaty a
            where a.entity_id = p_entity_id
              and a.portfolio_no = f_business_no
            limit 1;
        end if;
    elsif f_get_type = 'ACCPERIOD' then
        --合约业务获取事故年月
        select substr(a.account_period, 0, 4) || '-' ||
               substr(a.account_period, -2, 2) || '-01'
        into v_result
        from dmuser.dm_reins_bill a
        where a.entity_id = p_entity_id
          and a.ri_statement_no = f_business_no;
    elsif f_get_type = 'ENDORSEQNO' then
        --批单序号
        select a.endorse_seq_no
        into v_result
        from dmuser.dm_claim_main a
        where a.entity_id = p_entity_id
          and a.claim_no = f_business_no
        limit 1;
    elsif f_get_type = 'ENDORNO' then
        --批改号码
        if f_business_type in ('DB', 'FB') then
            select a.endorse_no
            into v_result
            from dmuser.dm_policy_main a
            where a.entity_id = p_entity_id
              and a.policy_no = f_business_no
              and a.endorse_seq_no = f_reins_direction;
        end if;

    end if;
    return v_result;
exception
    when no_data_found then
        call atr_pack_ecf_dap_proc_log_warn(p_action_no,
                                            'ct=' || p_entity_id || ',bn=' || f_business_no ||
                                            ',gt=' || f_get_type || ',bt=' || f_business_type ||
                                            ',rd=' || f_reins_direction,
                                            'GET_BUSINESS_INFO_NO_DATA',
                                            null);
        return null;
    when others then
        call atr_pack_ecf_dap_proc_log_warn(p_action_no,
                                            'ct=' || p_entity_id || ',bn=' || f_business_no ||
                                            ',gt=' || f_get_type || ',bt=' || f_business_type ||
                                            ',rd=' || f_reins_direction,
                                            'GET_BUSINESS_INFO_ERROR',
                                            sqlerrm || '');
        return null;
end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_dap_func_get_comm(f_entity_id bigint, f_policy_no character varying, f_endorseq_no character varying) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
declare

    v_premium numeric;

begin
    select sum(a.commission_amount)
    into v_premium
    from dmuser.dm_policy_premium a
    where a.policy_no = f_policy_no
      and a.entity_id = f_entity_id
      and a.endorse_seq_no = f_endorseq_no;

    return v_premium;
exception
    when others then
        return 0;
end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_dap_func_get_premium(p_entity_id bigint, f_policy_no character varying, f_endorseq_no character varying) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
declare
    v_premium numeric(19, 2);
begin
    select -- SUM(A.PREMIUM - A.DISCOUNT_AMOUNT) --减去折扣
           sum(a.premium) -- 不需要减去折扣    MODIFY 20230303
    into v_premium
    from dmuser.dm_policy_premium a
    where a.policy_no = f_policy_no
      and a.entity_id = p_entity_id
      and a.endorse_seq_no = f_endorseq_no;

    return v_premium;
exception
    when others then
        return 0;
end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_dap_func_getexchrate(p_entity_id bigint, p_exchdate timestamp without time zone, p_base_currency text, p_tgt_currency text) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
declare

    v_exchrate decimal(16, 8);

begin
    if p_base_currency = p_tgt_currency then
        return 1;
    end if;

    -- 币别倒过来
    v_exchrate := bpluser.bpl_pack_common_func_getexchrate(p_entity_id,
                                                           p_exchdate,
                                                           p_tgt_currency,
                                                           p_base_currency,
                                                           '2');

    if v_exchrate is null then
        raise exception '兑换日期 % 没有配置 % 兑换 % 的兑换率', to_char(p_exchdate, 'yyyy-mm-dd'), p_tgt_currency,
            p_base_currency using errcode = '45092';
    end if;

    return v_exchrate;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_check_and_create_bussperiod_detail(INOUT p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    v_count        bigint;
    v_period_state varchar(1);

begin

    select count(*), max(period_state), max(t.buss_period_id)
    into v_count, v_period_state, p_buss.buss_period_id
    from atr_conf_bussperiod t
    where t.entity_id = p_buss.entity_id
      and t.year_month = p_buss.year_month;

    if v_count = 0 then
        raise exception '%', '业务年月' || p_buss.year_month ||
                             '未开始， 请核实业务期间控制表' using errcode = '45071';
    end if;

    if v_count > 1 then
        raise exception '%', '业务期间控制表有多条业务年月' || p_buss.year_month ||
                             '的数据，无法匹配' using errcode = '45072';
    end if;

    if v_period_state <> '1' then
        raise exception '%', '业务年月' || p_buss.year_month || '的完成标识是' ||
                             v_period_state || '， 不是期待的1-已准备' using errcode = '45073';
    end if;

    select count(*), max(t.period_state)
    into v_count, v_period_state
    from dmuser.dm_conf_bussperiod t
    where t.entity_id = p_buss.entity_id
      and t.year_month = p_buss.year_month;

    if v_count = 0 then
        raise exception '%', '数据平台中业务年月' || p_buss.year_month ||
                             '未开始， 请核实数据平台的业务期间控制表' using errcode = '45071';
    end if;

    if v_count > 1 then
        raise exception '%', '数据平台业务期间控制表有多条业务年月' || p_buss.year_month ||
                             '的数据，无法匹配' using errcode = '45072';
    end if;

    if v_period_state <> '3' then
        raise exception '%', '数据平台中业务年月' || p_buss.year_month ||
                             '未完成， 请核实数据平台的业务期间控制表' using errcode = '45073';
    end if;

    insert into atr_conf_bussperiod_detail(period_detail_id,
                                           buss_period_id,
                                           biz_type_id,
                                           biz_code,
                                           exec_result,
                                           ready_state,
                                           creator_id,
                                           create_time,
                                           updator_id,
                                           update_time)
    select nextval('atr_seq_conf_bussperiod_detail'), x.*
    from (select p_buss.buss_period_id,
                 c.biz_type_id,
                 c.biz_code,
                 null           exec_result,
                 '0'            ready_state,
                 99             creator_id,
                 localtimestamp create_time,
                 99             updator_id,
                 localtimestamp update_time
          from atr_conf_table c
          where c.direction = '1'
            and not exists
              (select *
               from atr_conf_bussperiod_detail t
               where t.biz_type_id = c.biz_type_id
                 and t.buss_period_id = p_buss.buss_period_id)
          order by c.display_no, c.biz_type_id) x;

    commit;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_create_action(IN p_entity_id bigint, IN p_ev_date date, IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
begin
    insert into atr_buss_ecf_dap_action(id,
                                        action_no,
                                        entity_id,
                                        year_month,
                                        ev_date,
                                        status,
                                        creator_id,
                                        create_time,
                                        updator_id,
                                        update_time)
    select nextval('atr_seq_buss_ecf_dap_action'),
           p_action_no,
           p_entity_id,
           to_char(p_ev_date, 'yyyymm'),
           p_ev_date,
           'R' status,
           1,
           clock_timestamp(),
           1,
           clock_timestamp();

    commit;
end;
$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_update_bussperiod_detail(IN p_buss_period_id bigint, IN p_biz_code character varying, IN p_ready_state character varying, IN p_error_msg character varying)
    LANGUAGE plpgsql
    AS $$
begin

    update atr_conf_bussperiod_detail t
    set exec_result = case when p_ready_state = '1' then 'success' else substr(p_error_msg, 1, 500) end,
        ready_state = p_ready_state,
        update_time = localtimestamp
    where t.buss_period_id = p_buss_period_id
      and exists(select *
                 from atr_conf_table c
                 where c.biz_code = p_biz_code
                   and c.biz_type_id = t.biz_type_id);

    commit;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_accident_amount_dd(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare
    ------------------------------------------
    -- 理赔费用 （IBNR、O/S 等） （直保&临分分入）
    ------------------------------------------
    v_biz_code            varchar(30) := 'DAP_DD_CLAIM_ACCIDENT_AMOUNT';
    v_year                varchar(4);
    v_month               varchar(2);
    v_count               bigint;
    v_flag                varchar(1)  := 0;
    v_ibne_id             bigint;
    v_ibnr_import_g_count bigint; --IBNR导入为合同组合 判断标志
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    v_year := to_char(p_buss.ev_date, 'YYYY');
    v_month := to_char(p_buss.ev_date, 'MM');

    delete
    from atr_dap_dd_icg_claim_accident_amount a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    delete
    from atr_dap_dd_icp_claim_accident_amount a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    --判断是否存在导入的确认版本的 IBNR
    select count(*)
    into strict v_count
    from atr_buss_ibnr_import_main a
    where a.confirm_is = '1' --1 确认版本
      and a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    if v_count > 0 then
        --获取版本号码和OS 选择
        select a.use_import_os_is, a.ibnr_main_id
        into strict v_flag, v_ibne_id
        from atr_buss_ibnr_import_main a
        where a.confirm_is = '1' --1 确认版本
          and a.entity_id = p_buss.entity_id
          and a.year_month = p_buss.year_month
        --  LIMIT 1 -- 每个评估期只能确认一个版本，因此不需要
        ;
    end if;

    --判断IBNR是否为合同组合导入
    select count(*)
    into strict v_ibnr_import_g_count
    from atr_conf_code t,
         atr_conf_code t1
    where t.code_id = t1.upper_code_id
      and t.code_code = 'IbnrImportMode'
      and t1.code_code = 'G'
      and t1.valid_is = '1';

    if v_count > 0 then
        if v_ibnr_import_g_count > 0 then

            insert into atr_dap_dd_icp_claim_accident_amount(id, --ID
                                                             entity_id, --  业务单位ID
                                                             currency_code, -- 币种
                                                             year_month, -- 业务年月|评估期的年月
                                                             accident_year_month, --  事故年月
                                                             portfolio_no, -- 合同组合号码
                                                             evaluate_approach, --评估方法
                                                             loa_code, -- LOA编码
                                                             ibnr, -- IBNR
                                                             os, --O/S
                                                             draw_time -- 提数日期
            )
            select nextval('ATR_SEQ_DAP_DD_ICP_CLAIM_ACCIDENT_AMOUNT'),
                   p_buss.entity_id,
                   p_buss.base_currency                                    as currency_code,
                   p_buss.year_month                                       as year_month,
                   b.doa                                                      accident_year_month,
                   b.portfolio_no,
                   (select cm.evaluate_approach
                    from dmuser.dm_buss_cmunit_direct cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                             as evaluate_approach,
                   (select cm.loa_code
                    from dmuser.dm_buss_cmunit_direct cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                             as loa_code,
                   sum(b.ibnr_amount *
                       atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                         p_buss.ev_date,
                                                         p_buss.base_currency,
                                                         a.currency_code)) as ibnr,
                   0::numeric                                                 os,
                   clock_timestamp()
            from atr_buss_ibnr_import_main a,
                 atr_buss_ibnr_import_detail b
            where a.ibnr_main_id = b.ibnr_main_id
              and a.entity_id = p_buss.entity_id
              and b.business_model = 'DD'
              and a.year_month = p_buss.year_month
              and a.ibnr_main_id = v_ibne_id
              and exists (select 1
                          from dmuser.dm_buss_cmunit_direct cm
                          where cm.entity_id = a.entity_id
                            and cm.portfolio_no = b.portfolio_no
                            and cm.year_month <= p_buss.year_month)
            group by a.entity_id, b.doa, b.portfolio_no;

        else
            insert into atr_dap_dd_icg_claim_accident_amount(id, --ID
                                                             entity_id, --  业务单位ID
                                                             currency_code, -- 币种
                                                             year_month, -- 业务年月|评估期的年月
                                                             accident_year_month, --  事故年月
                                                             portfolio_no, -- 合同组合号码
                                                             icg_no,
                                                             evaluate_approach, --评估方法
                                                             loa_code, -- LOA编码
                                                             ibnr, -- IBNR
                                                             os, --O/S
                                                             draw_time -- 提数日期
            )
            select nextval('ATR_SEQ_DAP_DD_ICG_CLAIM_ACCIDENT_AMOUNT'),
                   a.entity_id,
                   p_buss.base_currency                               as currency_code,
                   p_buss.year_month                                  as year_month,
                   b.doa                                                 accident_year_month,
                   b.portfolio_no,
                   b.icg_no,
                   (select cm.evaluate_approach
                    from dmuser.dm_buss_cmunit_direct cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                        as evaluate_approach,
                   (select cm.loa_code
                    from dmuser.dm_buss_cmunit_direct cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                        as loa_code,
                   b.ibnr_amount *
                   atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                     p_buss.ev_date,
                                                     p_buss.base_currency,
                                                     a.currency_code)    ibnr,
                   (case when v_flag = '1' then b.os_amount else 0 end) *
                   atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                     p_buss.ev_date,
                                                     p_buss.base_currency,
                                                     a.currency_code) as os,
                   clock_timestamp()
            from atr_buss_ibnr_import_main a,
                 atr_buss_ibnr_import_detail b
            where a.ibnr_main_id = b.ibnr_main_id
              and a.entity_id = p_buss.entity_id
              and b.business_model = 'DD'
              and a.year_month = p_buss.year_month
              and a.ibnr_main_id = v_ibne_id
              and exists (select 1
                          from dmuser.dm_buss_cmunit_direct cm
                          where cm.entity_id = a.entity_id
                            and cm.portfolio_no = b.portfolio_no
                            and cm.icg_no = b.icg_no
                            and cm.year_month <= p_buss.year_month);
        end if;
    end if;

    commit;

    if v_ibnr_import_g_count > 0 or v_flag = '0' then
        insert into atr_dap_dd_icg_claim_accident_amount(id, --ID
                                                         entity_id, --  业务单位ID
                                                         currency_code, -- 币种
                                                         year_month, -- 业务年月|评估期的年月
                                                         accident_year_month, --  事故年月
                                                         portfolio_no, -- 合同组合号码
                                                         icg_no,
                                                         evaluate_approach, --评估方法
                                                         loa_code, -- LOA编码
                                                         ibnr, -- IBNR
                                                         os, --O/S
                                                         draw_time -- 提数日期
        )
        select nextval('ATR_SEQ_DAP_DD_ICG_CLAIM_ACCIDENT_AMOUNT'),
               entity_id,
               currency_code,
               p_buss.year_month year_month,
               accident_year_month,
               portfolio_no,
               icg_no,
               evaluate_approach,
               loa_code,
               ibnr,
               os,
               clock_timestamp() draw_time
        from (select entity_id,
                     p_buss.base_currency                                          currency_code,
                     accident_year_month,
                     portfolio_no,
                     icg_no,
                     evaluate_approach,
                     loa_code,
                     0                                                          as ibnr,
                     sum(os * atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                                p_buss.ev_date,
                                                                p_buss.base_currency,
                                                                currency_code)) as os
              from (select a.entity_id,
                           a.currency_code,
                           to_char(a.accident_date_time, 'YYYYMM') as accident_year_month,
                           cm.portfolio_no,
                           cm.icg_no,
                           cm.evaluate_approach,
                           cm.loa_code,
                           sum(a.outstanding_amount)               as os
                    from dmuser.dm_claim_outstanding a,
                         dmuser.dm_buss_cmunit_direct cm
                    where a.business_type_code = 'BF'
                      and a.entity_id = p_buss.entity_id
                      and cm.entity_id = a.entity_id
                      and cm.policy_no = a.policy_no
                      and cm.year_month <= p_buss.year_month
                      and a.year = v_year
                      and lpad(a.month, 2, '0') = v_month
                    group by a.entity_id,
                             a.currency_code,
                             to_char(a.accident_date_time, 'YYYYMM'),
                             a.policy_no,
                             cm.portfolio_no,
                             cm.icg_no,
                             cm.evaluate_approach,
                             cm.loa_code) x
              group by x.entity_id,
                       accident_year_month,
                       portfolio_no,
                       icg_no,
                       evaluate_approach,
                       loa_code) a;
    end if;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);


end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_accident_amount_fo(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 理赔费用 （IBNR、O/S 等） （合约分出）
    ------------------------------------------
    v_biz_code            varchar(30) := 'DAP_FO_CLAIM_ACCIDENT_AMOUNT';
    v_year                varchar(4);
    v_month               varchar(2);
    v_count               bigint;
    v_flag                varchar(1)  := 0;
    v_ibne_id             bigint;
    v_ibnr_import_g_count bigint; --IBNR导入为合同组合 判断标志
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    v_year := to_char(p_buss.ev_date, 'yyyy');
    v_month := to_char(p_buss.ev_date, 'mm');

    delete
    from atr_dap_fo_icg_claim_accident_amount a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    delete
    from atr_dap_fo_icp_claim_accident_amount a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    --判断是否存在导入的确认版本的 IBNR
    select count(*)
    into strict v_count
    from atr_buss_ibnr_import_main a
    where a.confirm_is = '1' --1 确认版本
      and a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    if v_count > 0 then
        --获取版本号码和OS 选择
        select a.use_import_os_is, a.ibnr_main_id
        into strict v_flag, v_ibne_id
        from atr_buss_ibnr_import_main a
        where a.confirm_is = '1' --1 确认版本
          and a.entity_id = p_buss.entity_id
          and a.year_month = p_buss.year_month
        --  limit 1 -- 每个评估期只能确认一个版本，因此不需要
        ;
    end if;

    --判断IBNR是否为合同组合导入
    select count(*)
    into strict v_ibnr_import_g_count
    from atr_conf_code t,
         atr_conf_code t1
    where t.code_id = t1.upper_code_id
      and t.code_code = 'IbnrImportMode'
      and t1.code_code = 'G'
      and t1.valid_is = '1';

    if v_count > 0 then
        if v_ibnr_import_g_count > 0 then
            insert into atr_dap_fo_icp_claim_accident_amount(id, --ID
                                                             entity_id, --  业务单位ID
                                                             currency_code, -- 币种
                                                             year_month, -- 业务年月|评估期的年月
                                                             accident_year_month, --  事故年月
                                                             portfolio_no, -- 合同组合号码
                                                             evaluate_approach, --评估方法
                                                             loa_code, -- LOA编码
                                                             ibnr, -- IBNR
                                                             os, --O/S
                                                             draw_time -- 提数日期
            )
            select nextval('ATR_SEQ_DAP_FO_ICP_CLAIM_ACCIDENT_AMOUNT'),
                   p_buss.entity_id,
                   p_buss.base_currency                                    as currency_code,
                   p_buss.year_month                                       as year_month,
                   b.doa                                                      accident_year_month,
                   b.portfolio_no,
                   (select cm.evaluate_approach
                    from dmuser.dm_buss_cmunit_fac_outwards cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                             as evaluate_approach,
                   (select cm.loa_code
                    from dmuser.dm_buss_cmunit_fac_outwards cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                             as loa_code,
                   sum(b.ibnr_amount *
                       atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                         p_buss.ev_date,
                                                         p_buss.base_currency,
                                                         a.currency_code)) as ibnr,
                   0::numeric                                                 os,
                   clock_timestamp()                                          draw_time
            from atr_buss_ibnr_import_main a,
                 atr_buss_ibnr_import_detail b
            where a.ibnr_main_id = b.ibnr_main_id
              and a.entity_id = p_buss.entity_id
              and b.business_model = 'FO'
              and a.year_month = p_buss.year_month
              and a.ibnr_main_id = v_ibne_id
              and exists (select 1
                          from dmuser.dm_buss_cmunit_fac_outwards cm
                          where cm.entity_id = a.entity_id
                            and cm.portfolio_no = b.portfolio_no
                            and cm.year_month <= p_buss.year_month)
            group by a.entity_id, b.doa, b.portfolio_no;
        else
            insert into atr_dap_fo_icg_claim_accident_amount(id, --ID
                                                             entity_id, --  业务单位ID
                                                             currency_code, -- 币种
                                                             year_month, -- 业务年月|评估期的年月
                                                             accident_year_month, --  事故年月
                                                             portfolio_no, -- 合同组合号码
                                                             icg_no,
                                                             evaluate_approach, --评估方法
                                                             loa_code, -- LOA编码
                                                             ibnr, -- IBNR
                                                             os, --O/S
                                                             draw_time -- 提数日期
            )
            select nextval('ATR_SEQ_DAP_FO_ICG_CLAIM_ACCIDENT_AMOUNT'),
                   a.entity_id,
                   p_buss.base_currency                               as currency_code,
                   p_buss.year_month                                  as year_month,
                   b.doa                                                 accident_year_month,
                   b.portfolio_no,
                   b.icg_no,
                   (select cm.evaluate_approach
                    from dmuser.dm_buss_cmunit_fac_outwards cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                        as evaluate_approach,
                   (select cm.loa_code
                    from dmuser.dm_buss_cmunit_fac_outwards cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                        as loa_code,
                   b.ibnr_amount *
                   atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                     p_buss.ev_date,
                                                     p_buss.base_currency,
                                                     a.currency_code)    ibnr,
                   (case when v_flag = '1' then b.os_amount else 0 end) *
                   atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                     p_buss.ev_date,
                                                     p_buss.base_currency,
                                                     a.currency_code) as os,
                   clock_timestamp()                                     draw_time
            from atr_buss_ibnr_import_main a,
                 atr_buss_ibnr_import_detail b
            where a.ibnr_main_id = b.ibnr_main_id
              and a.entity_id = p_buss.entity_id
              and b.business_model = 'FO'
              and a.year_month = p_buss.year_month
              and a.ibnr_main_id = v_ibne_id
              and exists (select 1
                          from dmuser.dm_buss_cmunit_fac_outwards cm
                          where cm.entity_id = a.entity_id
                            and cm.portfolio_no = b.portfolio_no
                            and cm.icg_no = b.icg_no
                            and cm.year_month <= p_buss.year_month);
        end if;
    end if;

    commit;


    if v_ibnr_import_g_count > 0 or v_flag = '0' then
        insert into atr_dap_fo_icg_claim_accident_amount(id, --ID
                                                         entity_id, --  业务单位ID
                                                         currency_code, -- 币种
                                                         year_month, -- 业务年月|评估期的年月
                                                         accident_year_month, --  事故年月
                                                         portfolio_no, -- 合同组合号码
                                                         icg_no,
                                                         evaluate_approach, --评估方法
                                                         loa_code, -- LOA编码
                                                         ibnr, -- IBNR
                                                         os, --O/S
                                                         draw_time -- 提数日期
        )
        select nextval('ATR_SEQ_DAP_FO_ICG_CLAIM_ACCIDENT_AMOUNT'),
               entity_id,
               currency_code,
               p_buss.year_month year_month,
               accident_year_month,
               portfolio_no,
               icg_no,
               evaluate_approach,
               loa_code,
               ibnr,
               os,
               clock_timestamp() draw_time
        from (select entity_id,
                     p_buss.base_currency                                          currency_code,
                     accident_year_month,
                     portfolio_no,
                     icg_no,
                     evaluate_approach,
                     loa_code,
                     0                                                          as ibnr,
                     sum(os * atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                                p_buss.ev_date,
                                                                p_buss.base_currency,
                                                                currency_code)) as os
              from (select a.entity_id,
                           a.currency_code,
                           to_char(a.accident_date_time, 'YYYYMM') as accident_year_month,
                           cm.portfolio_no,
                           cm.icg_no,
                           cm.evaluate_approach,
                           cm.loa_code,
                           sum(a.outstanding_amount)               as os
                    from dmuser.dm_claim_outstanding a,
                         dmuser.dm_buss_cmunit_fac_outwards cm
                    where a.business_type_code = 'AF'
                      and a.entity_id = p_buss.entity_id
                      and a.ri_policy_no is not null
                      and cm.entity_id = a.entity_id
                      and cm.fac_no = a.ri_policy_no
                      and cm.year_month <= p_buss.year_month
                      and a.year = v_year
                      and lpad(a.month, 2, '0') = v_month
                    group by a.entity_id,
                             a.currency_code,
                             to_char(a.accident_date_time, 'YYYYMM'),
                             a.ri_policy_no,
                             cm.portfolio_no,
                             cm.icg_no,
                             cm.loa_code,
                             cm.evaluate_approach) x
              group by x.entity_id,
                       accident_year_month,
                       portfolio_no,
                       icg_no,
                       evaluate_approach,
                       loa_code) a;

    end if;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_accident_amount_ti(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 理赔费用 （IBNR、O/S 等） （合约分入）
    ------------------------------------------
    v_biz_code            varchar(30) := 'DAP_TI_CLAIM_ACCIDENT_AMOUNT';
    v_year                varchar(4);
    v_month               varchar(2);
    v_count               bigint;
    v_flag                varchar(1)  := 0;
    v_ibne_id             bigint;
    v_ibnr_import_g_count bigint; --IBNR导入为合同组合 判断标志
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_accident_amount_to(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 理赔费用 （IBNR、O/S 等） （合约分出）
    ------------------------------------------
    v_biz_code            varchar(30) := 'DAP_TO_CLAIM_ACCIDENT_AMOUNT';
    v_year                varchar(4);
    v_month               varchar(2);
    v_count               bigint;
    v_flag                varchar(1)  := 0;
    v_ibne_id             bigint;
    v_ibnr_import_g_count bigint; --IBNR导入为合同组合 判断标志
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    v_year := to_char(p_buss.ev_date, 'yyyy');
    v_month := to_char(p_buss.ev_date, 'mm');

    delete
    from atr_dap_to_icg_claim_accident_amount a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    delete
    from atr_dap_to_icp_claim_accident_amount a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    --判断是否存在导入的确认版本的 IBNR
    select count(*)
    into strict v_count
    from atr_buss_ibnr_import_main a
    where a.confirm_is = '1' --1 确认版本
      and a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    if v_count > 0 then
        --获取版本号码和OS 选择
        select a.use_import_os_is, a.ibnr_main_id
        into v_flag, v_ibne_id
        from atr_buss_ibnr_import_main a
        where a.confirm_is = '1' --1 确认版本
          and a.entity_id = p_buss.entity_id
          and a.year_month = p_buss.year_month
        --  limit  1 -- 每个评估期只能确认一个版本，因此不需要
        ;
    end if;

    --判断IBNR是否为合同组合导入
    select count(*)
    into strict v_ibnr_import_g_count
    from atr_conf_code t,
         atr_conf_code t1
    where t.code_id = t1.upper_code_id
      and t.code_code = 'IbnrImportMode'
      and t1.code_code = 'G'
      and t1.valid_is = '1';

    if v_count > 0 then
        if v_ibnr_import_g_count > 0 then
            insert into atr_dap_to_icp_claim_accident_amount(id, --ID
                                                             entity_id, --  业务单位ID
                                                             currency_code, -- 币种
                                                             year_month, -- 业务年月|评估期的年月
                                                             accident_year_month, --  事故年月
                                                             portfolio_no, -- 合同组合号码
                                                             evaluate_approach, --评估方法
                                                             loa_code, -- LOA编码
                                                             ibnr, -- IBNR
                                                             os, --O/S
                                                             draw_time -- 提数日期
            )
            select nextval('ATR_SEQ_DAP_TO_ICP_CLAIM_ACCIDENT_AMOUNT'),
                   p_buss.entity_id,
                   p_buss.base_currency                                    as currency_code,
                   p_buss.year_month                                       as year_month,
                   b.doa                                                   as accident_year_month,
                   b.portfolio_no,
                   (select cm.evaluate_approach
                    from dmuser.dm_buss_cmunit_treaty cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                             as evaluate_approach,
                   (select cm.loa_code
                    from dmuser.dm_buss_cmunit_treaty cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                             as loa_code,
                   sum(b.ibnr_amount *
                       atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                         p_buss.ev_date,
                                                         p_buss.base_currency,
                                                         a.currency_code)) as ibnr,

                   0::numeric                                              as os,
                   clock_timestamp()                                          draw_time
            from atr_buss_ibnr_import_main a,
                 atr_buss_ibnr_import_detail b
            where a.ibnr_main_id = b.ibnr_main_id
              and a.entity_id = p_buss.entity_id
              and b.business_model = 'TO'
              and a.year_month = p_buss.year_month
              and a.ibnr_main_id = v_ibne_id
              and exists (select 1
                          from dmuser.dm_buss_cmunit_treaty cm
                          where cm.entity_id = a.entity_id
                            and cm.portfolio_no = b.portfolio_no
                            and cm.year_month <= p_buss.year_month)
            group by a.entity_id, b.doa, b.portfolio_no;
        else
            insert into atr_dap_to_icg_claim_accident_amount(id, --ID
                                                             entity_id, --  业务单位ID
                                                             currency_code, -- 币种
                                                             year_month, -- 业务年月|评估期的年月
                                                             accident_year_month, --  事故年月
                                                             portfolio_no, -- 合同组合号码
                                                             icg_no,
                                                             evaluate_approach, --评估方法
                                                             loa_code, -- LOA编码
                                                             ibnr, -- IBNR
                                                             os, --O/S
                                                             draw_time -- 提数日期
            )
            select nextval('ATR_SEQ_DAP_TO_ICG_CLAIM_ACCIDENT_AMOUNT'),
                   a.entity_id,
                   p_buss.base_currency                               as currency_code,
                   p_buss.year_month                                  as year_month,
                   b.doa                                                 accident_year_month,
                   b.portfolio_no,
                   b.icg_no,
                   (select cm.evaluate_approach
                    from dmuser.dm_buss_cmunit_treaty cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                        as evaluate_approach,
                   (select cm.loa_code
                    from dmuser.dm_buss_cmunit_treaty cm
                    where cm.entity_id = a.entity_id
                      and cm.portfolio_no = b.portfolio_no
                        fetch next 1 row only)                        as loa_code,
                   b.ibnr_amount *
                   atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                     p_buss.ev_date,
                                                     p_buss.base_currency,
                                                     a.currency_code)    ibnr,
                   (case when v_flag = '1' then b.os_amount else 0 end) *
                   atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                     p_buss.ev_date,
                                                     p_buss.base_currency,
                                                     a.currency_code) as os,
                   clock_timestamp()                                     draw_time
            from atr_buss_ibnr_import_main a,
                 atr_buss_ibnr_import_detail b
            where a.ibnr_main_id = b.ibnr_main_id
              and a.entity_id = p_buss.entity_id
              and b.business_model = 'TO'
              and a.year_month = p_buss.year_month
              and a.ibnr_main_id = v_ibne_id
              and exists (select 1
                          from dmuser.dm_buss_cmunit_treaty cm
                          where cm.entity_id = a.entity_id
                            and cm.portfolio_no = b.portfolio_no
                            and cm.icg_no = b.icg_no
                            and cm.year_month <= p_buss.year_month);
        end if;
    end if;
    commit;

    if v_ibnr_import_g_count > 0 or v_flag = '0' then
        --  TREATY OUT( DIRE & FAC)
        insert into atr_dap_to_icg_claim_accident_amount(id, --ID
                                                         entity_id, --  业务单位ID
                                                         currency_code, -- 币种
                                                         year_month, -- 业务年月|评估期的年月
                                                         accident_year_month, --  事故年月
                                                         portfolio_no, -- 合同组合号码
                                                         icg_no,
                                                         evaluate_approach, --评估方法
                                                         loa_code, -- LOA编码
                                                         ibnr, -- IBNR
                                                         os, --O/S
                                                         draw_time -- 提数日期
        )
        select nextval('ATR_SEQ_DAP_TO_ICG_CLAIM_ACCIDENT_AMOUNT'),
               entity_id,
               currency_code,
               p_buss.year_month,
               accident_year_month,
               portfolio_no,
               icg_no,
               evaluate_approach,
               loa_code,
               ibnr,
               os,
               clock_timestamp() draw_time
        from (select entity_id,
                     p_buss.base_currency                                          currency_code,
                     accident_year_month,
                     portfolio_no,
                     icg_no,
                     evaluate_approach,
                     loa_code,
                     0                                                          as ibnr,
                     sum(os * atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                                p_buss.ev_date,
                                                                p_buss.base_currency,
                                                                currency_code)) as os
              from (select a.entity_id,
                           a.currency_code,
                           to_char(a.accident_date_time, 'YYYYMM') as accident_year_month,
                           cm.portfolio_no,
                           cm.icg_no,
                           cm.evaluate_approach,
                           cm.loa_code,
                           sum(a.outstanding_amount)               as os
                    from dmuser.dm_claim_outstanding a,
                         dmuser.dm_buss_cmunit_treaty cm
                    where a.business_type_code = 'AF'
                      and a.entity_id = p_buss.entity_id
                      and a.ri_policy_no is not null
                      and cm.entity_id = a.entity_id
                      and cm.treaty_no = a.reference_no
                      and cm.ri_direction_code = 'O'
                      and cm.year_month <= p_buss.year_month
                      and a.year = v_year
                      and lpad(a.month, 2, '0') = v_month
                    group by a.entity_id,
                             a.currency_code,
                             to_char(a.accident_date_time, 'YYYYMM'),
                             a.reference_no,
                             cm.portfolio_no,
                             cm.icg_no,
                             cm.evaluate_approach,
                             cm.loa_code) x
              group by x.entity_id,
                       accident_year_month,
                       portfolio_no,
                       icg_no,
                       evaluate_approach,
                       loa_code) a;
    end if;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_paid_dd(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 实付赔款 （直保&临分分入）
    ------------------------------------------
begin

    delete
    from atr_dap_dd_claim_paid a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    if p_buss.is_bcai = '1' then

        insert into atr_dap_dd_claim_paid (id, entity_id, claim_loss_no, claim_loss_seq_no, approval_date,
                                           actual_payment_date, year_month, portfolio_no, icg_no, evaluate_approach,
                                           loa_code, product_code, risk_code, cmunit_no, claim_no, policy_no,
                                           endorse_seq_no, accident_date_time, dept_id, currency_code, paid_amount,
                                           draw_time)
        select nextval('atr_seq_dap_dd_claim_paid'),
               t.entity_id,
               t.claim_loss_no,
               t.claim_loss_seq_no,
               t.approval_date,
               t.approval_date as actual_payment_date,
               t.year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               product_code,
               risk_code,
               cmunit_no,
               claim_no,
               policy_no,
               '#'             as endorse_seq_no,
               accident_date_time,
               dept_id,
               currency_code,
               t.recv_amount   as paid_amount,
               clock_timestamp()
        from atr_dap_dd_claim_recv t
        where t.year_month = p_buss.year_month
          and t.entity_id = p_buss.entity_id;

        commit;

        return;
    end if;


    insert into atr_dap_dd_claim_paid (id, -- ID
                                       entity_id, -- 业务单位ID
                                       claim_loss_no, -- 赔案号
                                       claim_loss_seq_no, -- 赔案序号
                                       approval_date, -- 核赔日期
                                       actual_payment_date, -- 实际赔付日期
                                       year_month, -- 业务年月|实际赔付年月
                                       portfolio_no, --   合同组合号码
                                       icg_no, -- 合同组号码
                                       evaluate_approach, -- 评估方法
                                       loa_code, -- LOA编码
                                       product_code, -- 产品代码
                                       risk_code, -- 险种代码
                                       cmunit_no, --   公共项/计量单元编号
                                       claim_no, -- 立案号码
                                       policy_no, --    保单号码
                                       endorse_seq_no, -- 批改序号
                                       accident_date_time, -- 事故日期
                                       dept_id, -- 业务部门
                                       currency_code, -- 币种
                                       paid_amount, -- 实付赔款
                                       draw_time --  提数日期
    )
    select nextval('atr_seq_dap_dd_claim_paid'),
           entity_id,
           claim_loss_no,
           est_payment_seq_no claim_loss_seq_no,
           approval_date,
           actual_payment_date,
           p_buss.year_month  year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           product_code,
           risk_code,
           cmunit_no,
           claim_no,
           policy_no,
           endorse_seq_no,
           accident_date_time,
           dept_id,
           currency_code,
           paid_amount,
           clock_timestamp()  draw_time
    from (select a.entity_id,
                 a.claim_loss_no,
                 a.est_payment_seq_no,
                 a.actual_payment_date,
                 cm.portfolio_no,
                 cm.icg_no,
                 cm.evaluate_approach,
                 cm.loa_code,
                 cm.product_code,
                 cm.risk_code,
                 cm.cmunit_no,
                 atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                         a.entity_id,
                                                         a.claim_loss_no,
                                                         'CLAIM',
                                                         a.business_source_code,
                                                         null)           as claim_no,
                 a.policy_no,
                 a.endorse_seq_no,
                 to_date(atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                                 a.entity_id,
                                                                 a.claim_loss_no,
                                                                 'DOA',
                                                                 a.business_source_code,
                                                                 null),
                         'YYYY-MM-DD')                                   as accident_date_time,
                 to_date(atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                                 a.entity_id,
                                                                 a.claim_loss_no,
                                                                 'LOSSUW',
                                                                 a.business_source_code,
                                                                 a.est_payment_seq_no::varchar),
                         'YYYY-MM-DD')                                   as approval_date,
                 a.dept_id,
                 p_buss.base_currency                                       currency_code,
                 sum(a.amount *
                     atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                       p_buss.ev_date,
                                                       p_buss.base_currency,
                                                       a.currency_code)) as paid_amount
          from dmuser.dm_acc_payment a,
               dmuser.dm_buss_cmunit_direct cm
          where a.entity_id = p_buss.entity_id
            and a.business_source_code in ('DB', 'FB')
            and ((p_buss.is_bcai = '1'
              and a.entry_type_code like '%CLAIM'
              and not a.entry_type_code ~ '^(CN|DN)')
              or (p_buss.is_bcai = '0' and a.entry_type_code like '1%'))
            and a.ri_direction_code in ('D', 'I')
            and a.expenses_type_code in
                (select t.expenses_type_code
                 from bpluser.bbs_conf_fee_type_mapping t
                 where t.fee_class = 'Claim'
                   and t.business_source_code = 'DD')
            and cm.entity_id = a.entity_id
            and cm.policy_no = a.policy_no
            and cm.year_month <= p_buss.year_month
            and to_char(a.bu_voucher_date, 'YYYYMM') =
                p_buss.year_month
            -- mjy 20240623
            and exists (select 1
                        from dmuser.dm_claim_loss lo
                        where lo.entity_id = a.entity_id
                          and lo.claim_loss_no = a.claim_loss_no
                          and lo.claim_loss_seq_no = a.est_payment_seq_no)
          group by a.entity_id,
                   a.claim_loss_no,
                   a.est_payment_seq_no,
                   a.risk_code,
                   a.est_payment_date,
                   a.actual_payment_date,
                   a.policy_no,
                   a.endorse_seq_no,
                   a.est_payment_date,
                   a.dept_id,
                   a.business_source_code,
                   cm.product_code,
                   cm.portfolio_no,
                   cm.loa_code,
                   cm.evaluate_approach,
                   cm.icg_no,
                   cm.product_code,
                   cm.risk_code,
                   cm.risk_class_code,
                   cm.cmunit_no) a;

end ;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_recv_dd(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare
    ------------------------------------------
    -- 应付赔款 （直保&临分分入）
    ------------------------------------------
begin

    delete
    from atr_dap_dd_claim_recv a
    where a.entity_id = p_buss.entity_id
      and year_month = p_buss.year_month;

    drop table if exists jy_claim_loss;

    create temp table jy_claim_loss as
    select *
    from dmuser.dm_claim_loss a
    where a.policy_no is not null
      and to_char(a.approval_date, 'YYYYMM') = p_buss.year_month;

    create index idx_jy_claim_loss on jy_claim_loss (policy_no);

    insert into atr_dap_dd_claim_recv(id, -- ID
                                      entity_id, -- 业务单位ID
                                      claim_loss_no, -- 赔案号
                                      claim_loss_seq_no, -- 赔案序号
                                      approval_date, -- 核赔日期
                                      year_month, -- 业务年月|核赔年月
                                      portfolio_no, -- 合同组合号码
                                      icg_no, -- 合同组号码
                                      evaluate_approach, -- 评估方法
                                      loa_code, -- LOA编码
                                      product_code, -- 产品代码
                                      risk_code, -- 险种代码
                                      cmunit_no, -- 公共项/计量单元编号
                                      claim_no, -- 立案号码
                                      policy_no, -- 保单号码
                                      endorse_seq_no, -- 批改序号
                                      est_payment_date, -- 应付日期
                                      accident_date_time, -- 事故日期
                                      dept_id, -- 业务部门
                                      currency_code, -- 币种
                                      recv_amount, -- 应付赔款
                                      draw_time --  提数日期
    )
    select nextval('atr_seq_dap_dd_claim_recv'),
           a.entity_id,
           a.claim_loss_no,
           a.claim_loss_seq_no,
           a.approval_date,
           p_buss.year_month                                             as year_month,
           cm.portfolio_no,
           cm.icg_no,
           cm.evaluate_approach,
           cm.loa_code,
           cm.product_code,
           cm.risk_code,
           cm.cmunit_no,
           a.claim_no,
           a.policy_no,
           atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                   a.entity_id,
                                                   a.claim_no,
                                                   'ENDORSEQNO',
                                                   'DB',
                                                   null)                 as endorse_seq_no,
           a.approval_date                                                  plan_date, --- ??
           a.accident_date_time::date                                    as accident_date_time,
           0                                                             as dept_id,
           p_buss.base_currency                                          as currency_code,
           a.amount * atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                        p_buss.ev_date,
                                                        p_buss.base_currency,
                                                        a.currency_code) as recv_amount,
           clock_timestamp()                                             as draw_time
    from jy_claim_loss a,
         dmuser.dm_buss_cmunit_direct cm
    where a.entity_id = p_buss.entity_id
      and cm.entity_id = a.entity_id
      and cm.policy_no = a.policy_no
      and cm.year_month <= p_buss.year_month
      and to_char(a.approval_date, 'YYYYMM') = p_buss.year_month;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_log_info(IN p_buss atruser.atr_pack_ecf_dap_record_buss, IN p_mark character varying)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no,
                                        'ym=' || p_buss.year_month,
                                        p_mark,
                                        null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_log_info(IN p_action_no character varying, IN p_business_info character varying, IN p_mark character varying, IN p_msg character varying)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_ecf_dap_proc_log('INFO', p_action_no, p_business_info, p_mark, p_msg);
end;
$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_dd(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 应付、实付赔款汇总 （直保&临分分入）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_DD_CLAIM_AMOUNT';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_recv_dd', '1');
    call atr_pack_ecf_dap_proc_fetch_claim_recv_dd(p_buss);
    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_recv_dd', '2');

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_paid_dd', '1');
    call atr_pack_ecf_dap_proc_fetch_claim_paid_dd(p_buss);
    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_paid_dd', '2');

    delete
    from atr_dap_dd_icg_claim_amount
    where year_month = p_buss.year_month;

    insert into atr_dap_dd_icg_claim_amount(id, --  ID
                                            entity_id, --  业务单位ID
                                            currency_code, --币种
                                            year_month, -- 业务年月|实际赔付年月
                                            accident_year_month, --事故年月
                                            portfolio_no, --合同组合号码
                                            icg_no, --合同组号码
                                            evaluate_approach, --  评估方法
                                            loa_code, -- LOA编码
                                            paid_amount, --实付赔款
                                            recv_amount --应付赔款
    )
    select nextval('atr_seq_dap_dd_icg_claim_amount'),
           entity_id,
           currency_code,
           year_month,
           accident_year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           payment,
           payable
    from (select x.entity_id,
                 p_buss.base_currency                                       currency_code,
                 x.year_month,
                 accident_year_month,
                 x.portfolio_no,
                 x.icg_no,
                 x.evaluate_approach,
                 x.loa_code,
                 sum(payment *
                     atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                       p_buss.ev_date,
                                                       p_buss.base_currency,
                                                       x.currency_code)) as payment,
                 sum(payable *
                     atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                       p_buss.ev_date,
                                                       p_buss.base_currency,
                                                       x.currency_code)) as payable
          from (select a.entity_id,
                       a.currency_code,
                       a.year_month,
                       to_char(a.accident_date_time, 'YYYYMM') as accident_year_month,
                       a.portfolio_no,
                       a.icg_no,
                       a.evaluate_approach,
                       a.loa_code,
                       sum(a.paid_amount)                      as payment,
                       0                                       as payable
                from atr_dap_dd_claim_paid a
                where a.entity_id = p_buss.entity_id
                  and a.year_month = p_buss.year_month
                group by a.entity_id,
                         a.currency_code,
                         a.year_month,
                         to_char(a.accident_date_time, 'YYYYMM'),
                         a.portfolio_no,
                         a.icg_no,
                         a.evaluate_approach,
                         a.loa_code

                union all

                select a.entity_id,
                       a.currency_code,
                       a.year_month,
                       to_char(a.accident_date_time, 'YYYYMM') as accident_year_month,
                       a.portfolio_no,
                       a.icg_no,
                       a.evaluate_approach,
                       a.loa_code,
                       0                                       as payment,
                       sum(a.recv_amount)                      as payable
                from atr_dap_dd_claim_recv a
                where a.entity_id = p_buss.entity_id
                  and a.year_month = p_buss.year_month
                group by a.entity_id,
                         a.currency_code,
                         a.year_month,
                         to_char(a.accident_date_time, 'YYYYMM'),
                         a.portfolio_no,
                         a.icg_no,
                         a.evaluate_approach,
                         a.loa_code) x
          group by x.entity_id,
                   x.year_month,
                   x.accident_year_month,
                   x.portfolio_no,
                   x.icg_no,
                   x.evaluate_approach,
                   x.loa_code) x1;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_paid_fo(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 实付赔款 （临分分出）
    ------------------------------------------
begin

    delete
    from atr_dap_fo_claim_paid a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    if p_buss.is_bcai = '1' then
        insert into atr_dap_fo_claim_paid (id, entity_id, claim_loss_no, claim_loss_seq_no, approval_date,
                                           actual_payment_date, year_month, portfolio_no, icg_no, evaluate_approach,
                                           loa_code, product_code, risk_code, cmunit_no, claim_no, ri_policy_no,
                                           ri_endorse_seq_no, correction_seq_no,
                                           endorse_no, accident_date_time, dept_id, currency_code, paid_amount,
                                           draw_time)
        select nextval('atr_seq_dap_fo_claim_recv'),
               entity_id,
               claim_loss_no,
               claim_loss_seq_no,
               approval_date,
               t.approval_date as actual_payment_date,
               year_month,
               portfolio_no,
               icg_no,
               evaluate_approach,
               loa_code,
               product_code,
               risk_code,
               cmunit_no,
               claim_no,
               ri_policy_no,
               ri_endorse_seq_no,
               correction_seq_no,
               endorse_no,
               accident_date_time,
               dept_id,
               currency_code,
               t.recv_amount   as paid_amount,
               clock_timestamp()
        from atr_dap_fo_claim_recv t
        where t.entity_id = p_buss.entity_id
          and t.year_month = p_buss.year_month;

        commit;
        return;
    end if;

    insert into atr_dap_fo_claim_paid (id, -- ID
                                       entity_id, -- 业务单位ID
                                       claim_loss_no, --赔案号
                                       claim_loss_seq_no, -- 赔案序号
                                       approval_date, -- 核赔日期
                                       actual_payment_date, -- 实际赔付日期
                                       year_month, -- 业务年月|实际赔付年月
                                       portfolio_no, -- 合同组合号码
                                       icg_no, -- 合同组号码
                                       evaluate_approach, -- 评估方法
                                       loa_code, -- LOA编码
                                       product_code, --   产品代码
                                       risk_code, -- 险种代码
                                       cmunit_no, --   公共项/计量单元编号
                                       claim_no, -- 立案号码
                                       ri_policy_no, -- 分保单号
                                       ri_endorse_seq_no, -- 分保批改序号
                                       correction_seq_no, --   冲正次数|保单批改导致的分保调整时固定为1
                                       endorse_no, -- 批单号码
                                       accident_date_time, -- 事故日期
                                       dept_id, -- 业务部门
                                       currency_code, -- 币种
                                       paid_amount, -- 实付赔款
                                       draw_time --  提数日期
    )
    select nextval('atr_seq_dap_fo_claim_paid'),
           entity_id,
           claim_loss_no     claim_loss_seq_no,
           est_payment_seq_no,
           approval_date,
           actual_payment_date,
           p_buss.year_month year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           product_code,
           risk_code,
           cmunit_no,
           claim_no,
           ri_policy_no,
           ri_endorse_seq_no,
           correction_seq_no,
           endorse_no,
           accident_date_time,
           dept_id,
           currency_code,
           paid_amount,
           clock_timestamp() draw_time
    from (select a.entity_id,
                 a.claim_loss_no,
                 a.est_payment_seq_no,
                 to_date(atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                                 a.entity_id,
                                                                 a.claim_loss_no,
                                                                 'LOSSUW',
                                                                 a.business_source_code,
                                                                 a.est_payment_seq_no::varchar),
                         'YYYY-MM-DD')                                               as approval_date,
                 a.actual_payment_date,
                 p_buss.year_month,
                 cm.portfolio_no,
                 cm.icg_no,
                 cm.evaluate_approach,
                 cm.loa_code,
                 cm.product_code,
                 a.risk_code,
                 cm.cmunit_no,
                 atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                         a.entity_id,
                                                         a.claim_loss_no,
                                                         'CLAIM',
                                                         a.business_source_code,
                                                         'O')                        as claim_no,
                 a.ri_policy_no,
                 null                                                                as ri_endorse_seq_no,
                 null::numeric                                                       as correction_seq_no,
                 a.endorse_no,
                 to_date(atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                                 a.entity_id,
                                                                 a.claim_loss_no,
                                                                 'DOA',
                                                                 a.business_source_code,
                                                                 'O'), 'YYYY-MM-DD') as accident_date_time,
                 a.dept_id,
                 p_buss.base_currency                                                   currency_code,
                 sum(a.amount *
                     atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                       p_buss.ev_date,
                                                       p_buss.base_currency,
                                                       a.currency_code))             as paid_amount
          from dmuser.dm_acc_payment a,
               dmuser.dm_buss_cmunit_fac_outwards cm
          where a.entity_id = p_buss.entity_id
            and a.business_source_code = 'FB'
            and ((p_buss.is_bcai = '1'
              and a.entry_type_code like '%CLAIM'
              and not a.entry_type_code ~ '^(CN|DN)')
              or (p_buss.is_bcai = '0' and a.entry_type_code like '1%'))
            and a.ri_direction_code = 'O'
            and a.ri_arrangement_code in ('F', 'NF')
            and a.expenses_type_code in
                (select t.expenses_type_code
                 from bpluser.bbs_conf_fee_type_mapping t
                 where t.fee_class = 'Claim'
                   and t.business_source_code = 'FO')
            and cm.entity_id = a.entity_id
            and cm.fac_no = a.ri_policy_no
            and cm.year_month <= p_buss.year_month
            and to_char(bu_voucher_date, 'YYYYMM') =
                p_buss.year_month
          group by a.entity_id,
                   a.policy_no,
                   a.claim_loss_no,
                   a.est_payment_seq_no,
                   a.ri_policy_no,
                   a.endorse_no,
                   a.est_payment_date,
                   a.dept_id,
                   a.actual_payment_date,
                   a.risk_code,
                   a.business_source_code,
                   cm.portfolio_no,
                   cm.icg_no,
                   cm.loa_code,
                   cm.evaluate_approach,
                   cm.product_code,
                   cm.risk_code,
                   cm.risk_class_code,
                   cm.cmunit_no) a;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_recv_fo(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 应付赔款 （临分分出）
    ------------------------------------------
begin

    delete
    from atr_dap_fo_claim_recv a
    where a.entity_id = p_buss.entity_id
      and year_month = p_buss.year_month;

    insert into atr_dap_fo_claim_recv(id, -- ID
                                      entity_id, -- 业务单位ID
                                      claim_loss_no, -- 赔案号
                                      claim_loss_seq_no, --赔案序号
                                      approval_date, --核赔日期
                                      year_month, -- 业务年月|核赔年月
                                      portfolio_no, -- 合同组合号码
                                      icg_no, -- 合同组号码
                                      evaluate_approach, -- 评估方法
                                      loa_code, -- LOA编码
                                      product_code, --   产品代码
                                      risk_code, -- 险种代码
                                      cmunit_no, -- 公共项/计量单元编号
                                      claim_no, -- 立案号码
                                      ri_policy_no, -- 分保单号
                                      ri_endorse_seq_no, -- 分保批改序号
                                      correction_seq_no, -- 冲正次数|保单批改导致的分保调整时固定为1
                                      endorse_no, -- 批单号码
                                      est_payment_date, -- 应付日期
                                      accident_date_time, -- 事故日期
                                      dept_id, -- 业务部门
                                      currency_code, -- 币种
                                      recv_amount, -- 应付赔款
                                      draw_time --  提数日期
    )
    select nextval('atr_seq_dap_fo_premium'),
           x.entity_id,
           x.ri_policy_no                       claim_loss_no,
           0                                    claim_loss_seq_no,
           x.gl_posting_date                    approval_date,
           p_buss.year_month                    year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           product_code,
           risk_code,
           cmunit_no,
           ri_policy_no                         claim_no,
           ri_policy_no,
           ri_endorse_seq_no,
           correction_seq_no,
           endorse_no,
           gl_posting_date                      plan_date,
           date_trunc('month', gl_posting_date) accident_date_time,
           dept_id::numeric,
           currency_code,
           recv_amount,
           clock_timestamp()                    draw_time
    from (select a.entity_id,
                 b.ri_policy_no,
                 b.ri_endorse_seq_no,
                 c.gl_posting_date,
                 a.portfolio_no,
                 a.icg_no,
                 a.evaluate_approach,
                 a.loa_code,
                 a.product_code,
                 c.risk_code,
                 a.cmunit_no,
                 c.correction_seq_no,
                 atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                         a.entity_id,
                                                         c.policy_no,
                                                         'ENDORNO',
                                                         'DB',
                                                         c.endorse_seq_no) as endorse_no,
                 atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                         a.entity_id,
                                                         c.policy_no,
                                                         'DEPT_ID',
                                                         null,
                                                         null)             as dept_id,
                 p_buss.base_currency                                         currency_code,
                 sum(c.amount *
                     atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                       p_buss.ev_date,
                                                       p_buss.base_currency,
                                                       c.currency_code))   as recv_amount
          from dmuser.dm_buss_cmunit_fac_outwards a,
               dmuser.dm_reins_outward b,
               dmuser.dm_reins_outward_detail c
          where a.entity_id = b.entity_id
            and a.entity_id = c.entity_id
            and a.fac_no = b.ri_policy_no
            and b.ri_policy_no = c.ri_policy_no
            and b.ri_endorse_seq_no = c.ri_endorse_seq_no
            and a.entity_id = p_buss.entity_id
            and c.expenses_type_code in
                (select t.expenses_type_code
                 from bpluser.bbs_conf_fee_type_mapping t
                 where t.fee_class = 'Claim'
                   and t.business_source_code = 'FO')
            and a.year_month <= p_buss.year_month
            and to_char(c.gl_posting_date, 'YYYYMM') = p_buss.year_month
          group by a.entity_id,
                   b.ri_policy_no,
                   b.ri_endorse_seq_no,
                   c.gl_posting_date,
                   a.portfolio_no,
                   a.icg_no,
                   a.evaluate_approach,
                   a.loa_code,
                   a.product_code,
                   a.risk_class_code,
                   c.risk_code,
                   c.policy_no,
                   a.cmunit_no,
                   c.correction_seq_no,
                   c.endorse_seq_no,
                   b.business_source_code) x;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_fo(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 应付、实付赔款汇总 （临分分出）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_FO_CLAIM_AMOUNT';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_recv_fo', '1');
    call atr_pack_ecf_dap_proc_fetch_claim_recv_fo(p_buss);
    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_recv_fo', '2');

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_paid_fo', '1');
    call atr_pack_ecf_dap_proc_fetch_claim_paid_fo(p_buss);
    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_paid_fo', '2');

    delete
    from atr_dap_fo_icg_claim_amount
    where year_month = p_buss.year_month;

    insert into atr_dap_fo_icg_claim_amount(id, --  ID
                                            entity_id, --  业务单位ID
                                            currency_code, --币种
                                            year_month, -- 业务年月|实际赔付年月
                                            accident_year_month, --事故年月
                                            portfolio_no, --合同组合号码
                                            icg_no, --合同组号码
                                            evaluate_approach, --  评估方法
                                            loa_code, -- LOA编码
                                            paid_amount, --实付赔款
                                            recv_amount --应付赔款
    )
    select nextval('atr_seq_dap_fo_icg_claim_amount'),
           entity_id,
           currency_code,
           year_month,
           accident_year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           payment paid_amount,
           payable recv_amount
    from (select entity_id,
                 p_buss.base_currency                                               currency_code,
                 year_month,
                 accident_year_month,
                 portfolio_no,
                 icg_no,
                 evaluate_approach,
                 loa_code,
                 sum(amount * atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                                p_buss.ev_date,
                                                                p_buss.base_currency,
                                                                currency_code))  as payment,
                 sum(payable * atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                                 p_buss.ev_date,
                                                                 p_buss.base_currency,
                                                                 currency_code)) as payable
          from (select a.entity_id,
                       a.currency_code,
                       a.year_month,
                       to_char(a.accident_date_time, 'YYYYMM') as accident_year_month,
                       a.portfolio_no,
                       a.icg_no,
                       a.evaluate_approach,
                       a.loa_code,
                       sum(a.paid_amount)                      as amount,
                       0                                       as payable
                from atr_dap_fo_claim_paid a
                where a.entity_id = p_buss.entity_id
                  and a.year_month = p_buss.year_month
                group by a.entity_id,
                         a.currency_code,
                         a.year_month,
                         to_char(a.accident_date_time, 'YYYYMM'),
                         a.portfolio_no,
                         a.icg_no,
                         a.evaluate_approach,
                         a.loa_code
                union all
                select a.entity_id,
                       a.currency_code,
                       a.year_month,
                       to_char(a.accident_date_time, 'YYYYMM') as accident_year_month,
                       a.portfolio_no,
                       a.icg_no,
                       a.evaluate_approach,
                       a.loa_code,
                       0                                       as payment,
                       sum(a.recv_amount)                      as payable
                from atr_dap_fo_claim_recv a
                where a.entity_id = p_buss.entity_id
                  and a.year_month = p_buss.year_month
                group by a.entity_id,
                         a.currency_code,
                         a.year_month,
                         to_char(a.accident_date_time, 'YYYYMM'),
                         a.portfolio_no,
                         a.icg_no,
                         a.evaluate_approach,
                         a.loa_code) alias4
          group by entity_id,
                   year_month,
                   accident_year_month,
                   portfolio_no,
                   icg_no,
                   evaluate_approach,
                   loa_code) a;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_ti(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 应付、实付赔款汇总 （合约分入）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_TI_CLAIM_AMOUNT';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    
    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_to(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 应付、实付赔款汇总 （合约分出）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_TO_CLAIM_AMOUNT';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_recv_to', '1');
    call atr_pack_ecf_dap_proc_fetch_claim_recv_to(p_buss);
    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_recv_to', '2');

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_paid_to', '1');
    call atr_pack_ecf_dap_proc_fetch_claim_paid_to(p_buss);
    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-claim_paid_to', '2');

    delete
    from atr_dap_to_icg_claim_amount
    where year_month = p_buss.year_month;

    insert into atr_dap_to_icg_claim_amount(id, --  ID
                                            entity_id, --  业务单位ID
                                            currency_code, --币种
                                            year_month, -- 业务年月|实际赔付年月
                                            accident_year_month, --事故年月
                                            portfolio_no, --合同组合号码
                                            icg_no, --合同组号码
                                            evaluate_approach, --  评估方法
                                            loa_code, -- LOA编码
                                            paid_amount, --实付赔款
                                            recv_amount --应付赔款
    )
    select nextval('atr_seq_dap_to_icg_claim_amount'),
           entity_id,
           currency_code,
           year_month,
           accident_year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           payment paid_amount,
           payable recv_amount
    from (select entity_id,
                 p_buss.base_currency                                               currency_code,
                 year_month,
                 accident_year_month,
                 portfolio_no,
                 icg_no,
                 evaluate_approach,
                 loa_code,
                 sum(amount * atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                                p_buss.ev_date,
                                                                p_buss.base_currency,
                                                                currency_code))  as payment,
                 sum(payable * atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                                 p_buss.ev_date,
                                                                 p_buss.base_currency,
                                                                 currency_code)) as payable
          from (select a.entity_id,
                       a.currency_code,
                       a.year_month,
                       to_char(a.accident_date_time, 'YYYYMM') as accident_year_month,
                       a.portfolio_no,
                       a.icg_no,
                       a.evaluate_approach,
                       a.loa_code,
                       sum(a.paid_amount)                      as amount,
                       0                                       as payable
                from atr_dap_to_claim_paid a
                where a.entity_id = p_buss.entity_id
                  and a.year_month = p_buss.year_month
                group by a.entity_id,
                         a.currency_code,
                         a.year_month,
                         to_char(a.accident_date_time, 'YYYYMM'),
                         a.portfolio_no,
                         a.icg_no,
                         a.evaluate_approach,
                         a.loa_code

                union all
                select a.entity_id,
                       a.currency_code,
                       a.year_month,
                       to_char(a.accident_date_time, 'YYYYMM') as accident_year_month,
                       a.portfolio_no,
                       a.icg_no,
                       a.evaluate_approach,
                       a.loa_code,
                       0                                       as payment,
                       sum(a.recv_amount)                      as payable
                from atr_dap_to_claim_recv a
                where a.entity_id = p_buss.entity_id
                  and a.year_month = p_buss.year_month
                group by a.entity_id,
                         a.currency_code,
                         a.year_month,
                         to_char(a.accident_date_time, 'YYYYMM'),
                         a.portfolio_no,
                         a.icg_no,
                         a.evaluate_approach,
                         a.loa_code) alias4
          group by entity_id,
                   year_month,
                   accident_year_month,
                   portfolio_no,
                   icg_no,
                   evaluate_approach,
                   loa_code) a;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_premium_paid_dd(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 实收保费 （直保&临分分入）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_DD_PREMIUM_PAID';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    delete
    from atr_dap_dd_premium_paid a
    where a.entity_id = p_buss.entity_id
      and to_char(paid_date, 'YYYYMM') = p_buss.year_month;

    insert into atr_dap_dd_premium_paid(id, -- ID
                                        entity_id, -- 业务单位ID
                                        business_source_code, -- 业务类型|DB-直保、FB-分入
                                        policy_no, -- 保单号
                                        endorse_seq_no, -- 批单序号
                                        est_payment_seq_no, -- 缴费期次序号
                                        endorse_no, -- 批单号码
                                        year_month, -- 业务年月|实收日期的年月
                                        portfolio_no, -- 合同组合号码
                                        icg_no, -- 合同组号码
                                        evaluate_approach, -- 评估方法
                                        loa_code, -- LOA编码
                                        product_code, --   产品代码
                                        risk_class_code, -- 险类代码
                                        risk_code, -- 险种代码
                                        cm_unit_dim_code, -- 计量单元维度编码|根据计量单元判定维度输入产品代码、业务险类、险种代码之一或合并项
                                        cmunit_no, --   公共项/计量单元编号
                                        dept_id, -- 业务部门
                                        recv_date, -- 应收日期
                                        paid_date, -- 实收日期
                                        currency_code, -- 币种
                                        recv_premium, -- 应收保费
                                        paid_premium, --   实收保费
                                        commission, -- 佣金
                                        draw_time --  提数日期
    )
    select nextval('atr_seq_dap_dd_premium_paid'), x.*
    from (select a.entity_id,
                 a.business_source_code,
                 a.policy_no,
                 a.endorse_seq_no,
                 a.est_payment_seq_no,
                 a.endorse_no,
                 p_buss.year_month                        year_month,
                 '#'      as                              portfolio_no,
                 '#'      as                              icg_no,
                 '#'      as                              evaluate_approach,
                 '#'      as                              loa_code,
                 '#'      as                              product_code,
                 '#'      as                              risk_class_code,
                 a.risk_code,
                 '#'      as                              cm_unit_dim_code,
                 '#'      as                              cmunit_no,
                 a.dept_id,
                 date_trunc('day', a.est_payment_date)    recv_date,
                 date_trunc('day', a.actual_payment_date) paid_date,
                 p_buss.base_currency                     currency_code,
                 sum(case
                         when mp.fee_class = 'Premium' then
                             a.amount *
                             atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                               p_buss.ev_date,
                                                               p_buss.base_currency,
                                                               a.currency_code)
                         else
                             0
                     end) as                              recv_premium,
                 sum(case
                         when mp.fee_class = 'Premium' then
                             a.amount *
                             atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                               p_buss.ev_date,
                                                               p_buss.base_currency,
                                                               a.currency_code)
                         else
                             0
                     end) as                              paid_premium,
                 sum(case
                         when mp.fee_class = 'Comm' then
                             a.amount *
                             atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                               p_buss.ev_date,
                                                               p_buss.base_currency,
                                                               a.currency_code)
                         else
                             0
                     end) as                              commission,
                 clock_timestamp()                        draw_time
          from dmuser.dm_acc_payment a,
               bpluser.bbs_conf_fee_type_mapping mp
          where a.entity_id = p_buss.entity_id
            and (case
                     when mp.fee_class = 'Premium' then
                         substr(a.extend_column1, 1, 2) in ('DI', 'IC', 'IR')
                     when mp.fee_class = 'Comm' then
                         substr(a.extend_column1, 1, 2) in ('BA', 'BB', 'BO', 'BP', 'IC', 'IR')
              end)
            and substr(a.entry_type_code, 1, 2) not in ('CN', 'DN')
            -- and a.ri_direction_code in ('D', 'I')
            and mp.business_source_code = 'DD'
            and mp.expenses_type_code = a.expenses_type_code
            and mp.fee_class in ('Premium', 'Comm')
            and to_char(a.bu_voucher_date, 'YYYYMM') = p_buss.year_month
          group by a.entity_id,
                   a.business_source_code,
                   a.policy_no,
                   a.endorse_seq_no,
                   a.est_payment_seq_no,
                   a.endorse_no,
                   a.risk_code,
                   a.dept_id,
                   date_trunc('day', a.est_payment_date),
                   date_trunc('day', a.actual_payment_date)) x;

    commit;

    --     call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
--                                                         v_biz_code,
--                                                         '1',
--                                                         null);

end ;


$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_premium_paid_fo(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 实收保费 （临分分出）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_FO_PREMIUM_PAID';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    delete
    from atr_dap_fo_premium_paid a
    where a.entity_id = p_buss.entity_id
      and to_char(paid_date, 'YYYYMM') = p_buss.year_month;

    insert into atr_dap_fo_premium_paid(id, -- ID
                                        entity_id, -- 业务单位ID
                                        ri_policy_no, -- 分保单号
                                        ri_endorse_seq_no, --    分保批改序号
                                        ri_statement_no, --   账单号
                                        policy_no, -- 保单号
                                        endorse_seq_no, -- 批改序号
                                        year_month, -- 业务年月|实收日期的年月
                                        portfolio_no, --   合同组合号码
                                        icg_no, -- 合同组号码
                                        evaluate_approach, --    评估方法
                                        loa_code, -- LOA编码
                                        product_code, -- 产品代码
                                        risk_class_code, -- 险类代码
                                        risk_code, -- 险种代码
                                        cm_unit_dim_code, -- 计量单元维度编码|根据计量单元判定维度输入产品代码、业务险类、险种代码之一或合并项
                                        cmunit_no, --   公共项/计量单元编号
                                        dept_id, -- 业务部门
                                        recv_date, -- 应收日期
                                        paid_date, -- 实收日期
                                        currency_code, -- 币种
                                        recv_premium, --   应收保费
                                        paid_premium, -- 实收保费
                                        commission, -- 佣金
                                        draw_time --   提数日期
    )
    select nextval('atr_seq_dap_fo_premium_paid'), x.*
    from (select a.entity_id,
                 a.ri_policy_no as                        ri_policy_no,
                 '#'                                      ri_endorse_seq_no,
                 a.ri_statement_no,
                 a.policy_no,
                 a.endorse_seq_no,
                 p_buss.year_month                        year_month,
                 '#'            as                        portfolio_no,
                 '#'            as                        icg_no,
                 '#'            as                        evaluate_approach,
                 '#'            as                        loa_code,
                 '#'            as                        product_code,
                 '#'            as                        risk_class_code,
                 a.risk_code,
                 a.risk_code                              cm_unit_dim_code,
                 '#'            as                        cmunit_no,
                 a.dept_id,
                 date_trunc('day', a.est_payment_date)    recv_date,
                 date_trunc('day', a.actual_payment_date) paid_date,
                 p_buss.base_currency                     currency_code,
                 sum(case
                         when mp.fee_class = 'Premium' then
                             a.amount *
                             atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                               p_buss.ev_date,
                                                               p_buss.base_currency,
                                                               a.currency_code)
                         else
                             0
                     end)       as                        recv_premium,
                 sum(case
                         when mp.fee_class = 'Premium' then
                             a.amount *
                             atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                               p_buss.ev_date,
                                                               p_buss.base_currency,
                                                               a.currency_code)
                         else
                             0
                     end)       as                        paid_premium,
                 sum(case
                         when mp.fee_class = 'Comm' then
                             a.amount *
                             atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                               p_buss.ev_date,
                                                               p_buss.base_currency,
                                                               a.currency_code)
                         else
                             0
                     end)       as                        commission,
                 clock_timestamp()                        draw_time
          from dmuser.dm_acc_payment a,
               bpluser.bbs_conf_fee_type_mapping mp
          where a.entity_id = p_buss.entity_id
            and substr(a.extend_column1, 1, 2) in ('CF', 'JP', 'AF', 'FO')
            and substr(a.entry_type_code, 1, 2) not in ('CN', 'DN')
            --   and a.ri_direction_code = 'O'
            --   and a.ri_arrangement_code in ('F', 'NF')
            --   and a.claim_loss_no is null
            and mp.business_source_code = 'FO'
            and mp.expenses_type_code = a.expenses_type_code
            and mp.fee_class in ('Premium', 'Comm')
            and to_char(a.bu_voucher_date, 'YYYYMM') =
                p_buss.year_month
          group by a.entity_id,
                   a.ri_policy_no,
                   a.endorse_seq_no,
                   a.ri_statement_no,
                   a.policy_no,
                   a.endorse_seq_no,
                   a.business_source_code,
                   a.risk_code,
                   a.dept_id,
                   date_trunc('day', a.est_payment_date),
                   date_trunc('day', a.actual_payment_date)) x;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end ;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_premium_paid_ti(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 实收保费 （合约分入）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_TI_PREMIUM_PAID';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_premium_paid_to(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 实收保费 （合约分出）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_TO_PREMIUM_PAID';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    delete
    from atr_dap_to_premium_paid a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    commit;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-premium_paid_to', '1');

    drop table if exists jy_acc_payment;

    create temp table jy_acc_payment as
    select *
    from dmuser.dm_acc_payment a
    where a.treaty_no is not null
      and substr(a.entry_type_code, 1, 2) in ('MR', 'PV', 'RV')
      and to_char(a.bu_voucher_date, 'YYYYMM') = p_buss.year_month;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-premium_paid_to', '2');

    create index idx_jy_acc_payment on jy_acc_payment (treaty_no);

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-premium_paid_to', '3');

    insert into atr_dap_to_premium_paid(id, -- ID
                                        entity_id, -- 业务单位ID
                                        ri_statement_no, -- 帐单号
                                        ri_master_statement_no, -- 总账单号码
                                        treaty_no, -- 合约号|合约分入转分出特有
                                        ri_policy_no, -- 保单号码|“直保&临分分入”转分出特有
                                        year_month, -- 业务年月|实收日期的年月
                                        portfolio_no, -- 合同组合号码
                                        icg_no, -- 合同组号码
                                        evaluate_approach, -- 评估方法
                                        loa_code, -- LOA编码
                                        product_code, -- 产品代码
                                        risk_class_code, --   险类代码
                                        risk_code, -- 险种代码
                                        cm_unit_dim_code, -- 计量单元维度编码|根据计量单元判定维度输入产品代码、业务险类、险种代码之一或合并项
                                        cmunit_no, --    公共项/计量单元编号
                                        dept_id, -- 业务部门
                                        recv_date, -- 应收日期
                                        paid_date, -- 实收日期
                                        currency_code, -- 币种
                                        recv_premium, -- 应收保费
                                        paid_premium, -- 实收保费
                                        commission, -- 佣金
                                        draw_time --   提数日期
    )
    select nextval('atr_seq_dap_to_premium_paid') as id,
           a.entity_id,
           a.ri_statement_no,
           a.ri_statement_no                         ri_master_statement_no,
           a.treaty_no,
           a.ri_policy_no,
           p_buss.year_month                      as year_month,
           c.portfolio_no,
           c.icg_no,
           c.evaluate_approach,
           c.loa_code,
           a.risk_code                               product_code,
           c.risk_class_code,
           a.risk_code,
           a.treaty_no                               cm_unit_dim_code,
           '#'                                    as cmunit_no,
           0                                      as dept_id,
           date_trunc('day', a.est_payment_date)     recv_date,
           date_trunc('day', a.actual_payment_date)  paid_date,
           p_buss.base_currency                   as currency_code,
           sum(case
                   when mp.fee_class = 'Premium' then
                       a.amount *
                       (case
                            when p_buss.base_currency = a.currency_code then 1
                            else (select ex.exchange_rate
                                  from bpluser.bbs_conf_currencyrate ex
                                  where ex.entity_id = p_buss.entity_id
                                    and ex.currency_code = p_buss.base_currency
                                    and ex.exch_currency_code = a.currency_code
                                    and ex.frequency_code = '2'
                                    and ex.effective_date <= p_buss.ev_date
                                    and ex.valid_is = '1'
                                    and ex.audit_state = '1'
                                  order by ex.effective_date desc
                                  limit 1) end)
                   else
                       0
               end)                               as recv_premium,
           sum(case
                   when mp.fee_class = 'Premium' then
                       a.amount *
                       (case
                            when p_buss.base_currency = a.currency_code then 1
                            else (select ex.exchange_rate
                                  from bpluser.bbs_conf_currencyrate ex
                                  where ex.entity_id = p_buss.entity_id
                                    and ex.currency_code = p_buss.base_currency
                                    and ex.exch_currency_code = a.currency_code
                                    and ex.frequency_code = '2'
                                    and ex.effective_date <= p_buss.ev_date
                                    and ex.valid_is = '1'
                                    and ex.audit_state = '1'
                                  order by ex.effective_date desc
                                  limit 1) end)
                   else
                       0
               end)                               as paid_premium,
           sum(case
                   when mp.fee_class = 'Comm' then
                       a.amount *
                       (case
                            when p_buss.base_currency = a.currency_code then 1
                            else (select ex.exchange_rate
                                  from bpluser.bbs_conf_currencyrate ex
                                  where ex.entity_id = p_buss.entity_id
                                    and ex.currency_code = p_buss.base_currency
                                    and ex.exch_currency_code = a.currency_code
                                    and ex.frequency_code = '2'
                                    and ex.effective_date <= p_buss.ev_date
                                    and ex.valid_is = '1'
                                    and ex.audit_state = '1'
                                  order by ex.effective_date desc
                                  limit 1) end)
                   else
                       0
               end)                               as commission,
           clock_timestamp()                      as draw_time
    from jy_acc_payment a,
         bpluser.bbs_conf_fee_type_mapping mp,
         dmuser.dm_buss_cmunit_treaty c
    where a.entity_id = p_buss.entity_id
      and a.entity_id = c.entity_id
      and a.treaty_no = c.treaty_no
      and mp.business_source_code = 'TO'
      and mp.expenses_type_code = a.expenses_type_code
      and mp.fee_class in ('Premium', 'Comm')
      and (case
               when mp.fee_class = 'Premium' then
                   substr(a.extend_column1, 1, 2) in ('CP', 'DC', 'QS', 'SP', 'XP', 'AP', 'RP', 'XX')
               when mp.fee_class = 'Comm' then
                   substr(a.extend_column1, 1, 2) in ('CP', 'DC', 'QS', 'SP', 'XP')
        end)
      and c.year_month <= p_buss.year_month
    group by a.entity_id,
             a.ri_statement_no,
             a.treaty_no,
             a.ri_policy_no,
             a.business_source_code,
             a.risk_code,
             a.dept_id,
             date_trunc('day', a.est_payment_date),
             date_trunc('day', a.actual_payment_date),
             c.portfolio_no,
             c.icg_no,
             c.evaluate_approach,
             c.loa_code,
             c.risk_class_code;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_premium_recovered_to(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 摊回保费 （合约分出）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_TO_PREMIUM_RECOVERED';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    delete
    from atr_dap_to_premium_recovered a
    where a.entity_id = p_buss.entity_id
      and year_month = p_buss.year_month;

    commit;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-to_premium_recovered', '1');

    drop table if exists tmp_reins_outward_1;
    drop table if exists tmp_reins_outward_2;

    create temp table tmp_reins_outward_1 as
    select d.entity_id,
           d.treaty_no,
           d.ri_policy_no,
           d.ri_endorse_seq_no,
           d.policy_no,
           d.endorse_seq_no,
           e.fee_class,
           d.currency_code,
           d.amount,
           m.effective_date::date  as effective_date,
           m.expiry_date::date     as expiry_date,
           m.approval_date::date   as approval_date,
           d.gl_posting_date::date as gl_posting_date,
           m.payment_frequency_no,
           m.payment_frequency_code
    from dmuser.dm_reins_outward m,
         dmuser.dm_reins_outward_detail d,
         bpluser.bbs_conf_fee_type_mapping e
    where m.entity_id = d.entity_id
      and m.ri_policy_no = d.ri_policy_no
      and m.ri_endorse_seq_no = d.ri_endorse_seq_no
      and e.business_source_code = 'TO'
      and e.expenses_type_code = d.expenses_type_code
      and e.fee_class in ('Premium', 'Comm')
      and d.entity_id = p_buss.entity_id
      and case
              when d.ri_endorse_seq_no like '000%' then true
              else
                  to_char(d.gl_posting_date, 'yyyymm') = p_buss.year_month
        end;


    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-to_premium_recovered', '2');

    create index idx_tmp_reins_outward_1 on tmp_reins_outward_1 (treaty_no);

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-to_premium_recovered', '3');

    create temp table tmp_reins_outward_2 as
    select t.entity_id,
           t.treaty_no,
           t.ri_policy_no,
           t.ri_endorse_seq_no,
           t.policy_no,
           t.endorse_seq_no,
           c.portfolio_no,
           c.icg_no,
           c.evaluate_approach,
           c.loa_code,
           c.cmunit_no,
           t.effective_date,
           t.expiry_date,
           t.approval_date,
           c.border_date,
           t.gl_posting_date,
           t.payment_frequency_code,
           t.payment_frequency_no,
           t.fee_class,
           t.amount,
           t.currency_code
    from tmp_reins_outward_1 t,
         dmuser.dm_buss_cmunit_treaty c
    where t.entity_id = c.entity_id
      and t.treaty_no = c.treaty_no
      and c.ri_direction_code = 'O'
      and c.year_month <= p_buss.year_month
      and case when t.ri_endorse_seq_no like '000%' then c.year_month = p_buss.year_month else true end;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-to_premium_recovered', '4');

    insert into atr_dap_to_premium_recovered(id,
                                             entity_id,
                                             ri_statement_no,
                                             ri_master_statement_no,
                                             treaty_no,
                                             ri_policy_no,
                                             ri_endorse_seq_no,
                                             correction_seq_no,
                                             policy_no,
                                             endorse_seq_no,
                                             year_month,
                                             portfolio_no,
                                             icg_no,
                                             evaluate_approach,
                                             loa_code,
                                             product_code,
                                             risk_class_code,
                                             risk_code,
                                             cm_unit_dim_code,
                                             cmunit_no,
                                             effective_date,
                                             expiry_date,
                                             approval_date,
                                             contract_date,
                                             dept_id,
                                             payment_frequency_code,
                                             payment_frequency_no,
                                             currency_code,
                                             premium,
                                             commission,
                                             commission_rate,
                                             coverage_amount,
                                             draw_time,
                                             main_treaty_type)
    select nextval('atr_seq_dap_to_premium_recovered'),
           entity_id,
           '--'                                               as ri_statement_no,
           '--'                                               as ri_master_statement_no,
           treaty_no,
           ri_policy_no,
           ri_endorse_seq_no,
           -1                                                 as correction_seq_no,
           policy_no,
           endorse_seq_no,
           p_buss.year_month                                     year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           null                                                  product_code,
           null                                                  risk_class_code,
           null                                                  risk_code,
           treaty_no                                             cm_unit_dim_code,
           cmunit_no,
           effective_date,
           expiry_date,
           approval_date,
           border_date                                        as contract_date,
           atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                   entity_id,
                                                   policy_no,
                                                   'DEPT_ID',
                                                   null,
                                                   null)::int as dept_id,
           payment_frequency_code,
           payment_frequency_no,
           p_buss.base_currency                                  currency_code,
           premium,
           comm                                                  commission,
           case
               when premium = 0 then
                   0
               else
                   round(comm / premium, 6)
               end                                            as commission_rate,
           a.coverage_amount,
           clock_timestamp()                                  as draw_time,
           'T'                                                as main_treaty_type
    from (select t.entity_id,
                 t.treaty_no,
                 t.ri_policy_no,
                 t.ri_endorse_seq_no,
                 t.policy_no,
                 t.endorse_seq_no,
                 t.portfolio_no,
                 t.icg_no,
                 t.evaluate_approach,
                 t.loa_code,
                 t.cmunit_no,
                 t.effective_date,
                 t.expiry_date,
                 t.approval_date,
                 t.border_date,
                 t.gl_posting_date,
                 t.payment_frequency_code,
                 t.payment_frequency_no,
                 sum(case t.fee_class
                         when 'Premium' then
                             t.amount *
                             atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                               p_buss.ev_date,
                                                               p_buss.base_currency,
                                                               t.currency_code)
                         else 0 end) as premium,
                 sum(case t.fee_class
                         when 'Comm' then
                             t.amount *
                             atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                               p_buss.ev_date,
                                                               p_buss.base_currency,
                                                               t.currency_code)
                         else 0 end) as comm,
                 0                   as coverage_amount
          from tmp_reins_outward_2 t,
               bpluser.bbs_conf_loa d
          where t.entity_id = d.entity_id
            and t.loa_code = d.loa_code
            and d.business_model = 'T'
            and d.business_direction = 'O'
          group by t.entity_id,
                   t.treaty_no,
                   t.ri_policy_no,
                   t.ri_endorse_seq_no,
                   t.policy_no,
                   t.endorse_seq_no,
                   t.portfolio_no,
                   t.icg_no,
                   t.evaluate_approach,
                   t.loa_code,
                   t.cmunit_no,
                   t.effective_date,
                   t.expiry_date,
                   t.approval_date,
                   t.border_date,
                   t.gl_posting_date,
                   t.payment_frequency_code,
                   t.payment_frequency_no) a;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_split_paid_premium_dd(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare
    v_biz_code varchar(30) := 'DAP_DD_PREMIUM_PAID';
begin

    --     if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
--         return;
--     end if;

    drop table if exists tmp_atr_premium_paid_ods;
    drop table if exists tmp_atr_paid_premium_spli;
    drop table if exists tmp_atr_paid_premium_spli2;

    create temp table tmp_atr_paid_premium_spli
    (
        main_unit_no         varchar(100),
        policy_no            varchar(100),
        endorse_seq_no       varchar(10),
        portfolio_no         varchar(100),
        icg_no               varchar(100),
        loa_code             varchar(100),
        evaluate_approach    varchar(100),
        cmunit_no            varchar(100),
        business_source_code varchar(100),
        prem                 numeric(32, 8),
        comm                 numeric(32, 8),
        sum_prem             numeric(32, 8),
        sum_comm             numeric(32, 8),
        row_count            int,
        prem_ratio           numeric(32, 16),
        comm_ratio           numeric(32, 16)
    );

    create temp table tmp_atr_paid_premium_spli2
    (
        id                int,
        src_id            int,
        main_unit_no      varchar(100),
        policy_no         varchar(100),
        endorse_seq_no    varchar(10),
        portfolio_no      varchar(100),
        icg_no            varchar(100),
        loa_code          varchar(100),
        evaluate_approach varchar(100),
        cmunit_no         varchar(100),
        prem_ratio        numeric(32, 16),
        comm_ratio        numeric(32, 16),
        paid_prem         numeric(32, 8),
        paid_comm         numeric(32, 8)
    );
    
    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_dd', '1');

    drop table if exists jy_acc_payment;

    create temp table jy_acc_payment as
    select *
    from dmuser.dm_acc_payment a
    where a.policy_no is not null
      and substr(a.entry_type_code, 1, 2) in ('MR', 'PV', 'RV')
      and to_char(a.bu_voucher_date, 'YYYYMM') = p_buss.year_month;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_dd', '2');

    create index idx_jy_acc_payment on jy_acc_payment (policy_no);

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_dd', '3');

    create temp table tmp_atr_premium_paid_ods as
    select row_number() over () as id,
           a.policy_no,
           sum(case
                   when mp.fee_class = 'Premium' then
                       a.amount *
                       atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                         p_buss.ev_date,
                                                         p_buss.base_currency,
                                                         a.currency_code)
                   else
                       0
               end)             as paid_premium,
           sum(case
                   when mp.fee_class = 'Comm' then
                       a.amount *
                       atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                         p_buss.ev_date,
                                                         p_buss.base_currency,
                                                         a.currency_code)
                   else
                       0
               end)             as commission
    from jy_acc_payment a,
         bpluser.bbs_conf_fee_type_mapping mp
    where a.entity_id = p_buss.entity_id
      and (case
               when mp.fee_class = 'Premium' then
                   substr(a.extend_column1, 1, 2) in ('DI', 'IR', 'IC', 'CF', 'JP')
               when mp.fee_class = 'Comm' then
                   substr(a.extend_column1, 1, 2) in ('BA', 'BB', 'BO', 'BP', 'IR', 'IC', 'CF', 'JP')
        end)
      and mp.business_source_code = 'DD'
      and mp.expenses_type_code = a.expenses_type_code
      and mp.fee_class in ('Premium', 'Comm')
    group by a.policy_no;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_dd', '4');

    insert into tmp_atr_paid_premium_spli (main_unit_no, policy_no, endorse_seq_no, portfolio_no,
                                           icg_no, loa_code, evaluate_approach, cmunit_no,
                                           prem, comm, sum_prem, sum_comm, row_count)
    select main_unit_no,
           policy_no,
           endorse_seq_no,
           portfolio_no,
           icg_no,
           loa_code,
           evaluate_approach,
           cmunit_no,
           premium,
           commission,
           sum(x.premium) over (partition by x.main_unit_no)    as sum_premium,
           sum(x.commission) over (partition by x.main_unit_no) as sum_commission,
           count(*) over (partition by x.main_unit_no)          as row_count
    from (select t.policy_no,
                 t.endorse_seq_no,
                 t.portfolio_no,
                 t.icg_no,
                 t.loa_code,
                 t.evaluate_approach,
                 t.cmunit_no,
                 case
                     when strpos(t.policy_no, '-') > 0 then
                         substr(t.policy_no, 1, strpos(t.policy_no, '-') - 1)
                     else
                         t.policy_no
                     end                     main_unit_no,
                 round(sum(t.premium))    as premium,
                 round(sum(t.commission)) as commission
          from atr_dap_dd_premium t
          where t.year_month <= p_buss.year_month
          group by t.policy_no, t.endorse_seq_no, t.portfolio_no, t.icg_no, t.loa_code,
                   t.evaluate_approach, t.cmunit_no) x;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_dd', '5');

    update tmp_atr_paid_premium_spli t
    set prem_ratio = case
                         when t.row_count = 1 then 1
                         when t.sum_prem = 0 then 0
                         else t.prem / t.sum_prem end,
        comm_ratio = case
                         when t.row_count = 1 then 1
                         when t.sum_comm = 0 then 0
                         else t.comm / t.sum_comm end
    where true;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_dd', '6');

    insert into tmp_atr_paid_premium_spli2 (id, src_id, main_unit_no, policy_no, endorse_seq_no, portfolio_no,
                                            icg_no, loa_code,
                                            evaluate_approach, cmunit_no,
                                            prem_ratio, comm_ratio, paid_prem, paid_comm)
    select row_number() over (),
           t2.id as src_id,
           t1.main_unit_no,
           t1.policy_no,
           t1.endorse_seq_no,
           t1.portfolio_no,
           t1.icg_no,
           t1.loa_code,
           t1.evaluate_approach,
           t1.cmunit_no,
           t1.prem_ratio,
           t1.comm_ratio,
           coalesce(t2.paid_premium * t1.prem_ratio, 0),
           coalesce(t2.commission * t1.comm_ratio, 0)
    from tmp_atr_paid_premium_spli t1,
         tmp_atr_premium_paid_ods t2
    where t1.main_unit_no = t2.policy_no;


    -- 尾差处理
    merge into tmp_atr_paid_premium_spli2 t
    using (select t.src_id,
                  sum(t.paid_prem)        sum_,
                  (select t2.id
                   from tmp_atr_paid_premium_spli2 t2
                   where t2.src_id = t.src_id
                   order by abs(t2.paid_prem) desc
                   limit 1) as            id,
                  (select s.paid_premium
                   from tmp_atr_premium_paid_ods s
                   where s.id = t.src_id) src_
           from tmp_atr_paid_premium_spli2 t
           where t.prem_ratio <> 1
             and t.paid_prem <> 0
           group by t.src_id) x
    on (t.id = x.id and x.sum_ <> src_)
    when matched then
        update set paid_prem = t.paid_prem + x.sum_ - x.src_;

    merge into tmp_atr_paid_premium_spli2 t
    using (select t.src_id,
                  sum(t.paid_comm)        sum_,
                  (select t2.id
                   from tmp_atr_paid_premium_spli2 t2
                   where t2.src_id = t.src_id
                   order by abs(t2.paid_comm) desc
                   limit 1) as            id,
                  (select s.commission
                   from tmp_atr_premium_paid_ods s
                   where s.id = t.src_id) src_
           from tmp_atr_paid_premium_spli2 t
           where t.comm_ratio <> 1
             and t.paid_comm <> 0
           group by t.src_id) x
    on (t.id = x.id and x.sum_ <> src_)
    when matched then
        update set paid_comm = t.paid_comm + x.sum_ - x.src_;


    -- 刷新实收实付表
    delete from atr_dap_dd_premium_paid t where t.entity_id = p_buss.entity_id and t.year_month = p_buss.year_month;

    insert into atr_dap_dd_premium_paid (id, entity_id, business_source_code, policy_no, endorse_seq_no,
                                         est_payment_seq_no, endorse_no, year_month, portfolio_no, icg_no,
                                         evaluate_approach, loa_code, product_code, risk_class_code, risk_code,
                                         cm_unit_dim_code, cmunit_no,
                                         dept_id, recv_date, paid_date, currency_code, recv_premium, paid_premium,
                                         commission, draw_time)
    select nextval('atr_seq_dap_dd_premium_paid') as id,
           p_buss.entity_id,
           '#'                                    as business_source_code,
           t.policy_no,
           t.endorse_seq_no,
           0                                      as est_payment_seq_no,
           '#'                                    as endorse_no,
           p_buss.year_month,
           t.portfolio_no,
           t.icg_no,
           t.evaluate_approach,
           t.loa_code,
           '#'                                    as product_code,
           '#'                                    as risk_class_code,
           '#'                                    as risk_code,
           '#'                                    as cm_unit_dim_code,
           t.cmunit_no,
           0                                      as dept_id,
           p_buss.ev_date                         as recv_date,
           p_buss.ev_date                         as paid_date,
           p_buss.base_currency,
           t.paid_prem                            as recv_prem,
           t.paid_prem,
           t.paid_comm,
           clock_timestamp()
    from tmp_atr_paid_premium_spli2 t;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;
$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_premium_recv_dd(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 保费 （直保&临分分入）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_DD_PREMIUM';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    delete
    from atr_dap_dd_premium a
    where a.entity_id = p_buss.entity_id
      and year_month = p_buss.year_month;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-premium_recv_dd', '1');

    insert into atr_dap_dd_premium(id, --ID
                                   entity_id, --业务单位ID
                                   business_source_code, -- 业务类型|DB-直保、FB-分入
                                   policy_no, -- 保单号
                                   endorse_seq_no, -- 批单序号
                                   endorse_no, -- 批单号码
                                   main_policy_no,
                                   year_month, --  业务年月|核保通过日期的年月
                                   portfolio_no, --  合同组合号码
                                   icg_no, --  合同组号码
                                   evaluate_approach, --  评估方法
                                   loa_code, --  LOA编码
                                   product_code, --  产品代码
                                   risk_class_code, --  险类代码
                                   risk_code, -- 险种代码
                                   cm_unit_dim_code, --  计量单元维度编码|根据计量单元判定维度输入产品代码、业务险类、险种代码之一或合并项
                                   cmunit_no, --    公共项/计量单元编号
                                   policy_status_code, --  保单状态|0-退保，1-正常
                                   policy_type_code, --保单类型|N-新保、E-批改、R-续保
                                   effective_date, --  起保日期
                                   expiry_date, --  终保日期
                                   valid_date, --  生效日期|批改时为批改生效日期，其他为起保日期
                                   endorse_end_date, --  批改终止日期
                                   approval_date, --  核保通过日期
                                   contract_date, --  合同确认日期
                                   dept_id, --  业务部门
                                   payment_frequency_code, --  缴费方式|S-趸缴、M-月缴、Q-季缴、H-半年缴、Y-年缴
                                   payment_frequency_no, -- 约定缴费次数
                                   currency_code, --  币种
                                   premium, -- 保费
                                   commission, --佣金
                                   commission_rate, --佣金率
                                   coverage_amount, --  保额/限额
                                   draw_time, --  提数日期
                                   endorse_type_code, --批改类型
                                   pl_judge_rslt)
    select nextval('atr_seq_dap_dd_premium'),
           entity_id,
           business_source_code,
           policy_no,
           endorse_seq_no,
           endorse_no,
           case
               when strpos(policy_no, '-') > 0 then
                   substr(policy_no, 1, strpos(policy_no, '-') - 1)
               else
                   policy_no
               end as main_policy_no,
           year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           product_code,
           risk_class_code,
           risk_code,
           cm_unit_dim_code,
           cmunit_no,
           policy_status_code,
           policy_type_code,
           effective_date,
           expiry_date,
           valid_date,
           endorse_end_date,
           approval_date,
           contract_date,
           dept_id,
           payment_frequency_code,
           payment_frequency_no,
           currency_code,
           premium,
           commission,
           coalesce(case when premium <> 0 then round(commission / premium, 6) end, 0)
                   as commission_rate,
           case
               when coalesce(coverage_amount, 0) = 0 then
                   coalesce((select q.quota_value::numeric
                             from atr_conf_quota q,
                                  atr_conf_quota_def qd
                             where qd.quota_def_id = q.quota_def_id
                               and q.entity_id = x.entity_id
                               and q.year_month = p_buss.year_month
                               and q.dimension_value = x.loa_code
                               and q.business_source_code = 'DD'
                               and qd.quota_code = 'BE023'),
                            0)
               else
                   coverage_amount
               end as coverage_amount,
           draw_time,
           endorse_type_code,
           pl_judge_rslt
    from (select a.entity_id,
                 a.business_source_code,
                 a.policy_no,
                 a.endorse_seq_no,
                 a.endorse_no,
                 p_buss.year_month                                          as year_month,
                 b.portfolio_no,
                 b.icg_no,
                 b.evaluate_approach,
                 b.loa_code,
                 b.product_code,
                 b.risk_class_code,
                 b.risk_code,
                 b.product_code                                                cm_unit_dim_code,
                 b.cmunit_no,
                 a.policy_status_code,
                 a.policy_type_code,
                 date_trunc('day', case
                                       when a.endorse_seq_no not like '000%' and
                                            a.endorse_type_code like '%99%' then
                                           (select a2.endorse_effective_date
                                            from dmuser.dm_policy_main a2
                                            where a2.entity_id = a.entity_id
                                              and a2.policy_no = a.policy_no
                                              and a2.endorse_seq_no like '000%'
                                            limit 1)
                                       when a.endorse_seq_no not like '000%' then
                                           a.endorse_effective_date
                                       else
                                           a.effective_date
                     end)                                                   as effective_date,
                 date_trunc('day', case
                                       when a.endorse_seq_no not like '000%' and
                                            a.endorse_type_code in ('15', '16') then
                                           -- 退保单取批改前的终保日期
                                           (select a2.expiry_date
                                            from dmuser.dm_policy_main a2
                                            where a2.entity_id = a.entity_id
                                              and a2.policy_no = a.policy_no
                                              and a2.endorse_seq_no like '000%'
                                            limit 1)
                                       else
                                           a.expiry_date
                     end)                                                   as expiry_date,
                 greatest(a.endorse_effective_date, a.effective_date,
                          a.approval_date)                                  as valid_date,
                 a.expiry_date                                                 endorse_end_date,
                 b.border_date                                              as approval_date,   --EY 的需求方案，是需要提供对应保单合同的初始确认日期
                 case
                     when a.endorse_seq_no like '000%' then b.border_date
                     else
                         greatest(a.endorse_effective_date, a.approval_date, a.effective_date,
                                  b.border_date) end                        as contract_date,
                 a.dept_id,
                 a.payment_frequency_code,
                 a.payment_frequency_no,
                 p_buss.base_currency                                          currency_code,
                 a.premium *
                 atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                   p_buss.ev_date,
                                                   p_buss.base_currency,
                                                   a.insured_currency_code) as premium,
                 a.commission_amount *
                 atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                   p_buss.ev_date,
                                                   p_buss.base_currency,
                                                   a.insured_currency_code) as commission,

                 (case
                      when c.coverage_unit::numeric = 0 then 0
                      when c.coverage_unit::numeric = 1 then a.insured_amount
                      when c.coverage_unit::numeric = 2 then a.limit_amount
                      when c.coverage_unit::numeric = 3 then a.insured_amount + a.limit_amount
                      else 0 end) *
                 atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                   p_buss.ev_date,
                                                   p_buss.base_currency,
                                                   a.insured_currency_code) as coverage_amount, --风险范围: 0-暂无 1-保额 2-限额 3-限额+保额
                 clock_timestamp()                                          as draw_time,
                 a.endorse_type_code,
                 b.pl_judge_rslt
          from dmuser.dm_policy_main a,
               dmuser.dm_buss_cmunit_direct b,
               bpluser.bbs_conf_loa c
          where a.policy_no = b.policy_no
            and a.entity_id = b.entity_id
            and a.entity_id = c.entity_id
            and a.entity_id = p_buss.entity_id
            and c.loa_code = b.loa_code
            and b.evaluate_approach is not null
            and c.business_model = 'D'
            and c.business_direction = 'D'
            and b.year_month <= p_buss.year_month
            and ((a.endorse_seq_no like '000%' and
                  b.year_month = p_buss.year_month) or
                 (a.endorse_seq_no not like '000%' and
                  to_char(greatest(a.endorse_effective_date, a.approval_date, a.effective_date, b.border_date),
                          'yyyymm') = p_buss.year_month))) x;


    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-premium_recv_dd', '2');


    -- 缴费计划
    delete
    from atr_dap_dd_payment_plan t
    where t.year_month = p_buss.year_month
      and t.entity_id = p_buss.entity_id;

    insert into atr_dap_dd_payment_plan(id,
                                        entity_id,
                                        policy_no,
                                        endorse_seq_no,
                                        year_month,
                                        est_payment_date,
                                        currency_code,
                                        est_payment_amount,
                                        draw_time)
    select nextval('atr_seq_dap_dd_payment_plan'), x.*
    from (select t.entity_id,
                 t.policy_no,
                 t.endorse_seq_no,
                 p_buss.year_month,
                 date_trunc('day', t.est_payment_date) est_payment_date,
                 p_buss.base_currency,
                 sum(t.est_payment_amount *
                     (case
                          when p_buss.base_currency = t.currency_code then 1
                          else (select ex.exchange_rate
                                from bpluser.bbs_conf_currencyrate ex
                                where ex.entity_id = p_buss.entity_id
                                  and ex.currency_code = p_buss.base_currency
                                  and ex.exch_currency_code = t.currency_code
                                  and ex.frequency_code = '2'
                                  and ex.effective_date <= p_buss.ev_date
                                  and ex.valid_is = '1'
                                  and ex.audit_state = '1'
                                order by ex.effective_date desc
                                limit 1) end))         est_payment_amount,
                 clock_timestamp()
          from dmuser.dm_policy_payment_plan t
          where exists (select *
                        from atr_dap_dd_premium p
                        where p.entity_id = t.entity_id
                          and p.policy_no = t.policy_no
                          and p.endorse_seq_no = t.endorse_seq_no
                          and p.year_month = p_buss.year_month)
          group by t.entity_id,
                   t.policy_no,
                   t.endorse_seq_no,
                   date_trunc('day', t.est_payment_date)
          order by t.policy_no,
                   t.endorse_seq_no,
                   date_trunc('day', t.est_payment_date)) x;


    commit;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-paid_premium_dd', '1');
    call atr_pack_ecf_dap_proc_split_paid_premium_dd(p_buss);
    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-paid_premium_dd', '2');

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;
$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_split_paid_premium_fo(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare
    v_biz_code varchar(30) := 'DAP_FO_PREMIUM_PAID';
begin

    --     if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
--         return;
--     end if;

    drop table if exists tmp_atr_premium_paid_ods;
    drop table if exists tmp_atr_paid_premium_spli;
    drop table if exists tmp_atr_paid_premium_spli2;

    create temp table tmp_atr_paid_premium_spli
    (
        main_unit_no         varchar(100),
        ri_policy_no         varchar(100) not null,
        ri_endorse_seq_no    varchar(100) not null,
        policy_no            varchar(100),
        endorse_seq_no       varchar(10),
        portfolio_no         varchar(100),
        icg_no               varchar(100),
        loa_code             varchar(100),
        evaluate_approach    varchar(100),
        cmunit_no            varchar(100),
        business_source_code varchar(100),
        prem                 numeric(32, 8),
        comm                 numeric(32, 8),
        sum_prem             numeric(32, 8),
        sum_comm             numeric(32, 8),
        row_count            int,
        prem_ratio           numeric(32, 16),
        comm_ratio           numeric(32, 16)
    );

    create temp table tmp_atr_paid_premium_spli2
    (
        id                int,
        src_id            int,
        main_unit_no      varchar(100),
        ri_policy_no      varchar(100) not null,
        ri_endorse_seq_no varchar(100) not null,
        policy_no         varchar(100),
        endorse_seq_no    varchar(10),
        portfolio_no      varchar(100),
        icg_no            varchar(100),
        loa_code          varchar(100),
        evaluate_approach varchar(100),
        cmunit_no         varchar(100),
        prem_ratio        numeric(32, 16),
        comm_ratio        numeric(32, 16),
        paid_prem         numeric(32, 8),
        paid_comm         numeric(32, 8)
    );

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_fo', '1');

    drop table if exists jy_acc_payment;

    create temp table jy_acc_payment as
    select *
    from dmuser.dm_acc_payment a
    where a.policy_no is not null
      and a.treaty_no is not null
      and substr(a.entry_type_code, 1, 2) in ('MR', 'PV', 'RV')
      and to_char(a.bu_voucher_date, 'YYYYMM') = p_buss.year_month;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_fo', '2');

    create index idx_jy_acc_payment on jy_acc_payment (policy_no);

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_fo', '3');

    create temp table tmp_atr_premium_paid_ods as
    select row_number() over ()               as id,
           a.policy_no || '//' || a.treaty_no as main_unit_no,
           sum(case
                   when mp.fee_class = 'Premium' then
                       a.amount *
                       atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                         p_buss.ev_date,
                                                         p_buss.base_currency,
                                                         a.currency_code)
                   else
                       0
               end)                           as paid_premium,
           sum(case
                   when mp.fee_class = 'Comm' then
                       a.amount *
                       atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                         p_buss.ev_date,
                                                         p_buss.base_currency,
                                                         a.currency_code)
                   else
                       0
               end)                           as commission
    from jy_acc_payment a,
         bpluser.bbs_conf_fee_type_mapping mp
    where a.entity_id = p_buss.entity_id
      and substr(a.extend_column1, 1, 2) in ('AF', 'FO')
      and substr(a.entry_type_code, 1, 2) in ('MR', 'PV', 'RV')
      and mp.business_source_code = 'FO'
      and mp.expenses_type_code = a.expenses_type_code
      and mp.fee_class in ('Premium', 'Comm')
      and to_char(bu_voucher_date, 'YYYYMM') = p_buss.year_month
    group by a.policy_no, a.treaty_no;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_fo', '4');


    -- 搜集应收信息
    insert into tmp_atr_paid_premium_spli (main_unit_no, ri_policy_no, ri_endorse_seq_no, policy_no, endorse_seq_no,
                                           portfolio_no,
                                           icg_no, loa_code, evaluate_approach, cmunit_no,
                                           prem, comm, sum_prem, sum_comm, row_count)
    select main_unit_no,
           ri_policy_no,
           ri_endorse_seq_no,
           policy_no,
           endorse_seq_no,
           portfolio_no,
           icg_no,
           loa_code,
           evaluate_approach,
           cmunit_no,
           premium,
           commission,
           sum(x.premium) over (partition by x.main_unit_no)    as sum_premium,
           sum(x.commission) over (partition by x.main_unit_no) as sum_commission,
           count(*) over (partition by x.main_unit_no)          as row_count
    from (select t.ri_policy_no,
                 t.ri_endorse_seq_no,
                 t.policy_no,
                 t.endorse_seq_no,
                 t.portfolio_no,
                 t.icg_no,
                 t.loa_code,
                 t.evaluate_approach,
                 t.cmunit_no,
                 case
                     when strpos(t.policy_no, '-') > 0 then
                         substr(t.policy_no, 1, strpos(t.policy_no, '-') - 1)
                     else
                         t.policy_no
                     end || '//' || t.treaty_no as main_unit_no,
                 round(sum(t.premium))          as premium,
                 round(sum(t.commission))       as commission
          from atr_dap_fo_premium t
          where t.year_month <= p_buss.year_month
          group by t.ri_policy_no, t.ri_endorse_seq_no, t.treaty_no, t.policy_no, t.endorse_seq_no, t.portfolio_no,
                   t.icg_no, t.loa_code,
                   t.evaluate_approach, t.cmunit_no) x;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_fo', '5');

    -- 根据应收计算分摊占比
    update tmp_atr_paid_premium_spli t
    set prem_ratio = case
                         when t.row_count = 1 then 1
                         when t.sum_prem = 0 then 0
                         else t.prem / t.sum_prem end,
        comm_ratio = case
                         when t.row_count = 1 then 1
                         when t.sum_comm = 0 then 0
                         else t.comm / t.sum_comm end
    where true;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'split-premium_paid_fo', '6');

    -- 分摊金额
    insert into tmp_atr_paid_premium_spli2 (id, src_id, main_unit_no,
                                            ri_policy_no, ri_endorse_seq_no, policy_no, endorse_seq_no, portfolio_no,
                                            icg_no, loa_code,
                                            evaluate_approach, cmunit_no,
                                            prem_ratio, comm_ratio, paid_prem, paid_comm)
    select row_number() over (),
           t2.id as src_id,
           t1.main_unit_no,
           t1.ri_policy_no,
           t1.ri_endorse_seq_no,
           t1.policy_no,
           t1.endorse_seq_no,
           t1.portfolio_no,
           t1.icg_no,
           t1.loa_code,
           t1.evaluate_approach,
           t1.cmunit_no,
           t1.prem_ratio,
           t1.comm_ratio,
           coalesce(t2.paid_premium * t1.prem_ratio, 0),
           coalesce(t2.commission * t1.comm_ratio, 0)
    from tmp_atr_paid_premium_spli t1,
         tmp_atr_premium_paid_ods t2
    where t1.main_unit_no = t2.main_unit_no;


    -- 尾差处理
    merge into tmp_atr_paid_premium_spli2 t
    using (select t.src_id,
                  sum(t.paid_prem)        sum_,
                  (select t2.id
                   from tmp_atr_paid_premium_spli2 t2
                   where t2.src_id = t.src_id
                   order by abs(t2.paid_prem) desc
                   limit 1) as            id,
                  (select s.paid_premium
                   from tmp_atr_premium_paid_ods s
                   where s.id = t.src_id) src_
           from tmp_atr_paid_premium_spli2 t
           where t.prem_ratio <> 1
             and t.paid_prem <> 0
           group by t.src_id) x
    on (t.id = x.id and x.sum_ <> src_)
    when matched then
        update set paid_prem = t.paid_prem + x.sum_ - x.src_;

    merge into tmp_atr_paid_premium_spli2 t
    using (select t.src_id,
                  sum(t.paid_comm)        sum_,
                  (select t2.id
                   from tmp_atr_paid_premium_spli2 t2
                   where t2.src_id = t.src_id
                   order by abs(t2.paid_comm) desc
                   limit 1) as            id,
                  (select s.commission
                   from tmp_atr_premium_paid_ods s
                   where s.id = t.src_id) src_
           from tmp_atr_paid_premium_spli2 t
           where t.comm_ratio <> 1
             and t.paid_comm <> 0
           group by t.src_id) x
    on (t.id = x.id and x.sum_ <> src_)
    when matched then
        update set paid_comm = t.paid_comm + x.sum_ - x.src_;


    -- 刷新实收实付表
    delete from atr_dap_fo_premium_paid t where t.entity_id = p_buss.entity_id and t.year_month = p_buss.year_month;

    insert into atr_dap_fo_premium_paid (id, entity_id, ri_policy_no, ri_endorse_seq_no, ri_statement_no,
                                         policy_no, endorse_seq_no, year_month, portfolio_no, icg_no,
                                         evaluate_approach, loa_code, product_code, risk_class_code,
                                         risk_code, cm_unit_dim_code, cmunit_no, dept_id, recv_date,
                                         paid_date, currency_code, recv_premium, paid_premium, commission, draw_time)
    select nextval('atr_seq_dap_fo_premium_paid') as id,
           p_buss.entity_id,
           ri_policy_no,
           ri_endorse_seq_no,
           '#'                                    as ri_statement_no,
           t.policy_no,
           t.endorse_seq_no,
           p_buss.year_month,
           t.portfolio_no,
           t.icg_no,
           t.evaluate_approach,
           t.loa_code,
           '#'                                    as product_code,
           '#'                                    as risk_class_code,
           '#'                                    as risk_code,
           '#'                                    as cm_unit_dim_code,
           t.cmunit_no,
           0                                      as dept_id,
           to_date(p_buss.year_month, 'yyyymm')   as recv_date,
           to_date(p_buss.year_month, 'yyyymm')   as paid_date,
           p_buss.base_currency,
           t.paid_prem                            as recv_premium,
           t.paid_prem,
           t.paid_comm,
           clock_timestamp()
    from tmp_atr_paid_premium_spli2 t;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end ;
$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_premium_recv_fo(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 保费 （临分分出）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_FO_PREMIUM';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    delete
    from atr_dap_fo_premium a
    where a.entity_id = p_buss.entity_id
      and year_month = p_buss.year_month;

    commit;

    insert into atr_dap_fo_premium(id, -- ID
                                   entity_id, -- 业务单位ID
                                   ri_policy_no, -- 分保单号
                                   ri_endorse_seq_no, -- 分保批改序号
                                   correction_seq_no, --   冲正次数|保单批改导致的分保调整时固定为1
                                   ri_statement_no,
                                   treaty_no,
                                   policy_no, -- 保单号
                                   endorse_seq_no, -- 批改序号
                                   main_policy_no,
                                   year_month, -- 业务年月|核保通过日期的年月
                                   portfolio_no, -- 合同组合号码
                                   icg_no, -- 合同组号码
                                   evaluate_approach, -- 评估方法
                                   loa_code, -- LOA编码
                                   product_code, -- 产品代码
                                   risk_class_code, -- 险类代码
                                   risk_code, -- 险种代码
                                   cm_unit_dim_code, -- 计量单元维度编码|根据计量单元判定维度输入产品代码、业务险类、险种代码之一或合并项
                                   cmunit_no, --    公共项/计量单元编号
                                   policy_status_code, -- 保单状态|0-退保，1-正常
                                   policy_type_code, --保单类型|N-新保、E-批改、R-续保
                                   effective_date, -- 起保日期
                                   expiry_date, --终保日期
                                   valid_date, -- 生效日期|批改时为批改生效日期，其他为起保日期
                                   endorse_end_date, -- 批改终止日期
                                   approval_date, -- 核保通过日期
                                   contract_date, -- 合同确认日期
                                   dept_id, -- 业务部门
                                   payment_frequency_code, -- 缴费方式|S-趸缴、M-月缴、Q-季缴、H-半年缴、Y-年缴
                                   payment_frequency_no, -- 约定缴费次数
                                   currency_code, --   币种
                                   premium, -- 保费
                                   commission, --佣金
                                   commission_rate,
                                   coverage_amount, --    保额/限额
                                   draw_time, --  提数日期
                                   endorse_type_code --批改类型
    )
    select nextval('atr_seq_dap_fo_premium'),
           entity_id,
           ri_policy_no,
           ri_endorse_seq_no,
           -1                           as correction_seq_no,
           '-'                          as ri_statement_no,
           treaty_no,
           policy_no,
           endorse_seq_no,
           case
               when strpos(policy_no, '-') > 0 then
                   substr(policy_no, 1, strpos(policy_no, '-') - 1)
               else
                   policy_no
               end                      as main_policy_no,
           p_buss.year_month               year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           product_code,
           risk_class_code,
           risk_code,
           risk_code                       cm_unit_dim_code,
           cmunit_no,
           '#'                          as policy_status_code,
           '#'                          as policy_type_code,
           effective_date,
           expiry_date,
           greatest(effective_date, gl_posting_date,
                    gl_posting_date)    as valid_date,
           x.expiry_date                as endorse_end_date,
           case
               when ri_endorse_seq_no like '000%' then border_date
               else gl_posting_date end as approval_date, --EY 给出方案，核保通过日期使用IFRS 17 下的合同确认日期
           case
               when ri_endorse_seq_no like '000%' then border_date
               else gl_posting_date end as contract_date,
           0                            as dept_id,
           payment_frequency_code,
           payment_frequency_no,
           p_buss.base_currency            currency_code,
           premium,
           comm                            commission,
           case
               when premium = 0 then
                   0
               else
                   round(comm / premium, 6)
               end                      as commission_rate,
           (case
                when coalesce(coverage_amount, 0) = 0 then
                    coalesce((select q.quota_value::numeric
                              from atr_conf_quota q,
                                   atr_conf_quota_def qd
                              where qd.quota_def_id = q.quota_def_id
                                and q.entity_id = x.entity_id
                                and q.year_month = p_buss.year_month
                                and q.dimension_value = x.loa_code
                                and q.business_source_code = 'FO'
                                and qd.quota_code = 'BE023'),
                             0)
                else
                    coverage_amount
               end)                     as coverage_amount,
           clock_timestamp()               draw_time,
           endorse_type_code
    from (select x1.entity_id,
                 x1.ri_policy_no,
                 x1.ri_endorse_seq_no,
                 x1.treaty_no,
                 x1.policy_no,
                 x1.endorse_seq_no,
                 x1.portfolio_no,
                 x1.icg_no,
                 x1.evaluate_approach,
                 x1.loa_code,
                 x1.product_code,
                 x1.risk_class_code,
                 x1.risk_code,
                 x1.cmunit_no,
                 x1.effective_date,
                 x1.expiry_date,
                 x1.gl_posting_date,
                 x1.border_date,
                 x1.payment_frequency_code,
                 x1.payment_frequency_no,
                 sum(x1.premium) as premium,
                 sum(x1.comm)    as comm,
                 sum(case
                         when x1.rn = 1 then -- 再保安排表此数据重复， 在此去重
                             x1.coverage_amount
                     end)        as coverage_amount,
                 '#'             as endorse_type_code --再保前保单批改类型 
          from (select a.entity_id,
                       b.ri_policy_no,
                       b.ri_endorse_seq_no,
                       c.treaty_no,
                       c.policy_no,
                       c.endorse_seq_no,
                       a.portfolio_no,
                       a.icg_no,
                       a.evaluate_approach,
                       a.loa_code,
                       a.product_code,
                       a.risk_class_code,
                       c.risk_code,
                       a.cmunit_no,
                       date_trunc('day', b.effective_date) as          effective_date,
                       date_trunc('day', b.expiry_date)    as          expiry_date,
                       c.gl_posting_date,
                       a.border_date,
                       b.payment_frequency_code,
                       b.payment_frequency_no,
                       (case e.fee_class
                            when 'Premium' then
                                c.amount *
                                (case
                                     when p_buss.base_currency = c.currency_code then 1
                                     else (select ex.exchange_rate
                                           from bpluser.bbs_conf_currencyrate ex
                                           where ex.entity_id = p_buss.entity_id
                                             and ex.currency_code = p_buss.base_currency
                                             and ex.exch_currency_code = c.currency_code
                                             and ex.frequency_code = '2'
                                             and ex.effective_date <= p_buss.ev_date
                                             and ex.valid_is = '1'
                                             and ex.audit_state = '1'
                                           order by ex.effective_date desc
                                           limit 1) end)
                            else 0 end)                    as          premium,
                       (case e.fee_class
                            when 'Comm' then
                                c.amount *
                                (case
                                     when p_buss.base_currency = c.currency_code then 1
                                     else (select ex.exchange_rate
                                           from bpluser.bbs_conf_currencyrate ex
                                           where ex.entity_id = p_buss.entity_id
                                             and ex.currency_code = p_buss.base_currency
                                             and ex.exch_currency_code = c.currency_code
                                             and ex.frequency_code = '2'
                                             and ex.effective_date <= p_buss.ev_date
                                             and ex.valid_is = '1'
                                             and ex.audit_state = '1'
                                           order by ex.effective_date desc
                                           limit 1) end)
                            else 0 end)                    as          comm,
                       (case d.coverage_unit
                            when
                                '0' then
                                0
                            when
                                '1' then
                                c.insured_amount
                            when
                                '2' then
                                c.limit_amount
                            when
                                '3' then
                                c.insured_amount + c.limit_amount
                            else
                                0 end) *
                       (case
                            when p_buss.base_currency = c.currency_code then 1
                            else (select ex.exchange_rate
                                  from bpluser.bbs_conf_currencyrate ex
                                  where ex.entity_id = p_buss.entity_id
                                    and ex.currency_code = p_buss.base_currency
                                    and ex.exch_currency_code = c.currency_code
                                    and ex.frequency_code = '2'
                                    and ex.effective_date <= p_buss.ev_date
                                    and ex.valid_is = '1'
                                    and ex.audit_state = '1'
                                  order by ex.effective_date desc
                                  limit 1) end)            as          coverage_amount,
                       row_number()
                       over (partition by c.entity_id, c.ri_policy_no, c.ri_endorse_seq_no, c.policy_no, c.endorse_seq_no,
                           c.risk_code, c.currency_code order by c.id) rn
                from dmuser.dm_buss_cmunit_fac_outwards a,
                     dmuser.dm_reins_outward b,
                     dmuser.dm_reins_outward_detail c,
                     bpluser.bbs_conf_loa d,
                     bpluser.bbs_conf_fee_type_mapping e
                where a.entity_id = b.entity_id
                  and a.entity_id = c.entity_id
                  and a.entity_id = d.entity_id
                  and a.fac_no = b.ri_policy_no
                  and b.ri_policy_no = c.ri_policy_no
                  and b.ri_endorse_seq_no = c.ri_endorse_seq_no
                  and a.entity_id = p_buss.entity_id
                  and a.loa_code = d.loa_code
                  and e.business_source_code = 'FO'
                  and e.expenses_type_code = c.expenses_type_code
                  and e.fee_class in ('Premium', 'Comm')
                  and d.business_model = 'D'
                  and d.business_direction = 'D'
                  and a.year_month <= p_buss.year_month
                  and case
                          when b.ri_endorse_seq_no like '000%' then a.year_month = p_buss.year_month
                          else to_char(c.gl_posting_date, 'yyyymm') = p_buss.year_month end) x1
          group by x1.entity_id,
                   x1.ri_policy_no,
                   x1.ri_endorse_seq_no,
                   x1.treaty_no,
                   x1.policy_no,
                   x1.endorse_seq_no,
                   x1.portfolio_no,
                   x1.icg_no,
                   x1.evaluate_approach,
                   x1.loa_code,
                   x1.product_code,
                   x1.risk_class_code,
                   x1.risk_code,
                   x1.cmunit_no,
                   x1.effective_date,
                   x1.expiry_date,
                   x1.gl_posting_date,
                   x1.border_date,
                   x1.payment_frequency_code,
                   x1.payment_frequency_no) x;

    commit;

    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-paid_premium_fo', '1');
    call atr_pack_ecf_dap_proc_split_paid_premium_fo(p_buss);
    call atr_pack_ecf_dap_proc_log_info(p_buss.action_no, null, 'fetch-paid_premium_fo', '2');

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_premium_recv_ti(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 保费 （合约分入）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_TI_PREMIUM';
begin

    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;


    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_premium_recv_to(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 保费 （合约分出）
    ------------------------------------------
    v_biz_code varchar(30) := 'DAP_TO_PREMIUM';
begin
    if atr_pack_ecf_dap_func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
        return;
    end if;

    delete
    from atr_dap_to_premium a
    where a.entity_id = p_buss.entity_id
      and year_month = p_buss.year_month;

    --合约分入转合约分出
    insert into atr_dap_to_premium(id, -- id
                                   entity_id, -- 业务单位id
                                   ri_statement_no, -- 帐单号
                                   ri_master_statement_no, -- 总账单号码
                                   treaty_no, --    合约号|合约分入转分出特有
                                   ri_policy_no, -- 分保单号码|“直保&临分分入”转分出特有
                                   year_month, -- 业务年月|核保通过日期（“直保&临分分入”）或账单确认日期（合约分入转分）
                                   portfolio_no, -- 合同组合号码
                                   icg_no, --   合同组号码
                                   evaluate_approach, --    评估方法
                                   loa_code, -- loa编码
                                   product_code, --   产品代码
                                   risk_class_code, -- 险类代码
                                   risk_code, -- 险种代码
                                   cm_unit_dim_code, --   计量单元维度编码|根据计量单元判定维度输入产品代码、业务险类、险种代码之一或合并项
                                   cmunit_no, --    公共项/计量单元编号
                                   effective_date, -- 分保安排起期
                                   expiry_date, --分保安排止期
                                   contract_date, -- 合同确认日期
                                   dept_id, -- 业务部门
                                   payment_frequency_code, -- 缴费方式|s-趸缴、m-月缴、q-季缴、h-半年缴、y-年缴
                                   payment_frequency_no, -- 约定缴费次数
                                   currency_code, --   原币币种
                                   premium, -- 保费
                                   commission, --佣金
                                   commission_rate,
                                   coverage_amount, -- 保额/限额
                                   draw_time, --  提数日期
                                   main_treaty_type -- 合约大类  T-比例合约  X-超赔合约  R--合约分入转合约分出
    )
    select nextval('atr_seq_dap_to_premium'),
           entity_id,
           ri_statement_no,
           ri_master_statement_no,
           treaty_no,
           null                 ri_policy_no, --合约分入转分出不存在分保单号码
           p_buss.year_month    year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           null                 product_code,
           null                 risk_class_code,
           null                 risk_code,
           treaty_no            cm_unit_dim_code,
           cmunit_no,
           effective_date,
           expiry_date,
           border_date          contract_date,
           null                 dept_id,
           payment_frequency_code,
           payment_frequency_no,
           p_buss.base_currency currency_code,
           premium,
           comm                 commission,
           case
               when premium = 0 then
                   0
               else
                   round(comm / premium, 6)
               end  as          commission_rate,
           (case
                when coalesce(coverage_amount, 0) = 0 then
                    coalesce((select q.quota_value::numeric
                              from atr_conf_quota q,
                                   atr_conf_quota_def qd
                              where qd.quota_def_id = q.quota_def_id
                                and q.entity_id = a.entity_id
                                and q.year_month = p_buss.year_month
                                and q.dimension_value = a.loa_code
                                and q.business_source_code = 'TO'
                                and qd.quota_code = 'BE023'),
                             0)
                else
                    coverage_amount
               end) as          coverage_amount,
           clock_timestamp()    draw_time,
           main_treaty_type
    from (select entity_id,
                 ri_statement_no,
                 ri_master_statement_no,
                 treaty_no,
                 portfolio_no,
                 icg_no,
                 evaluate_approach,
                 loa_code,
                 cmunit_no,
                 effective_date,
                 expiry_date,
                 border_date,
                 payment_frequency_code,
                 payment_frequency_no,
                 main_treaty_type,
                 sum(premium) as premium,
                 sum(comm)    as comm,
                 sum(case
                         when rn = 1 then
                             coverage_amount
                     end)     as coverage_amount
          from (select a.entity_id,
                       b.ri_statement_no,
                       b.ri_master_statement_no,
                       a.treaty_no,
                       a.portfolio_no,
                       a.icg_no,
                       a.evaluate_approach,
                       a.loa_code,
                       a.cmunit_no,
                       a.effective_date,
                       a.expiry_date,
                       a.border_date,
                       b.payment_frequency_code,
                       b.payment_frequency_no,
                       (case e.fee_class
                            when 'Premium' then
                                c.amount *
                                atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                                  p_buss.ev_date,
                                                                  p_buss.base_currency,
                                                                  c.currency_code)
                            else 0 end)                  as                                                premium,
                       (case e.fee_class
                            when 'Comm' then
                                c.amount *
                                atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                                  p_buss.ev_date,
                                                                  p_buss.base_currency,
                                                                  c.currency_code)
                            else 0 end)                  as                                                comm,
                       (select sum(t.limit_amount *
                                   atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                                     p_buss.ev_date,
                                                                     p_buss.base_currency,
                                                                     t.currency_code))
                        from bpluser.bbs_conf_treaty t
                        where t.treaty_no = a.treaty_no) as                                                coverage_amount,
                       (case a.treaty_type_code
                            when '71' then
                                'X'
                            when '72' then
                                'X'
                            else 'R' end)                as                                                main_treaty_type, --X超赔 R合约分入转合约分出
                       row_number()
                       over (partition by b.entity_id, b.treaty_no, c.ri_statement_no order by b.id, c.id) rn
                from dmuser.dm_buss_cmunit_treaty a,
                     dmuser.dm_reins_bill b,
                     dmuser.dm_reins_bill_detail c,
                     bpluser.bbs_conf_fee_type_mapping e
                where a.entity_id = b.entity_id
                  and a.entity_id = c.entity_id
                  and a.treaty_no = b.treaty_no
                  and a.entity_id = p_buss.entity_id
                  and b.ri_statement_no = c.ri_statement_no
                  and e.fee_class in ('Premium', 'Comm')
                  and e.business_source_code = 'TO'
                  and c.expenses_type_code = e.expenses_type_code
                  and a.ri_direction_code = 'O'
                  and not exists
                    (select 1
                     from dmuser.dm_reins_outward_detail o
                     where o.treaty_no = a.treaty_no
                       and o.entity_id = a.entity_id)
                  and to_char(b.statement_approval_date, 'YYYYMM') =
                      p_buss.year_month
                  and a.year_month <= p_buss.year_month) x1
          group by entity_id,
                   ri_statement_no,
                   ri_master_statement_no,
                   treaty_no,
                   portfolio_no,
                   icg_no,
                   evaluate_approach,
                   loa_code,
                   cmunit_no,
                   effective_date,
                   expiry_date,
                   border_date,
                   payment_frequency_code,
                   payment_frequency_no,
                   main_treaty_type) a;

    commit;

    call atr_pack_ecf_dap_proc_update_bussperiod_detail(p_buss.buss_period_id,
                                                        v_biz_code,
                                                        '1',
                                                        null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_all_buss_data(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
begin

    call atr_pack_ecf_dap_proc_fetch_premium_recv_dd(p_buss);
    call atr_pack_ecf_dap_proc_fetch_premium_recv_fo(p_buss);
    call atr_pack_ecf_dap_proc_fetch_premium_recv_ti(p_buss);
    call atr_pack_ecf_dap_proc_fetch_premium_recv_to(p_buss);

    call atr_pack_ecf_dap_proc_fetch_premium_recovered_to(p_buss);

    call atr_pack_ecf_dap_proc_fetch_premium_paid_dd(p_buss);
    call atr_pack_ecf_dap_proc_fetch_premium_paid_fo(p_buss);
    call atr_pack_ecf_dap_proc_fetch_premium_paid_ti(p_buss);
    call atr_pack_ecf_dap_proc_fetch_premium_paid_to(p_buss);

    call atr_pack_ecf_dap_proc_fetch_claim_dd(p_buss);
    call atr_pack_ecf_dap_proc_fetch_claim_fo(p_buss);
    call atr_pack_ecf_dap_proc_fetch_claim_ti(p_buss);
    call atr_pack_ecf_dap_proc_fetch_claim_to(p_buss);

    call atr_pack_ecf_dap_proc_fetch_claim_accident_amount_dd(p_buss);
    call atr_pack_ecf_dap_proc_fetch_claim_accident_amount_fo(p_buss);
    call atr_pack_ecf_dap_proc_fetch_claim_accident_amount_ti(p_buss);
    call atr_pack_ecf_dap_proc_fetch_claim_accident_amount_to(p_buss);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_update_action(IN p_action_no character varying, IN p_status character varying)
    LANGUAGE plpgsql
    AS $$
begin
    update atr_buss_ecf_dap_action t
    set status      = p_status,
        update_time = clock_timestamp()
    where t.action_no = p_action_no;

    commit;
end;
$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_all(IN p_entity_id bigint, IN p_draw_date date, INOUT p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare

    --------------------------------
    -- 提取所有接口表数据
    --------------------------------
    v_year_month    varchar(6);
    v_ev_date       timestamp;
    v_buss          atr_pack_ecf_dap_record_buss;
    v_error_modules varchar(4000);
    v_base_currency varchar(3);
    v_error_msg     text;

begin
    p_action_no := atr_pack_ecf_util_func_create_action_no();

    call atr_pack_ecf_dap_proc_create_action(p_entity_id, p_draw_date, p_action_no);

    select t.currency_code
    into v_base_currency
    from bpluser.bbs_conf_account_set t
    where t.entity_id = p_entity_id;

    if v_base_currency is null then
        call atr_pack_ecf_dap_proc_update_action(p_action_no, 'E');
        raise exception '%', '没有配置本位币' using errcode = '45091';
    end if;

    v_ev_date := last_day(p_draw_date);
--     v_ev_date := date_trunc('day', ((date_trunc('month', (P_DRAW_DATE)::timestamp + interval '1 month'))::date - 1));
    v_year_month := to_char(v_ev_date, 'yyyymm');

    call atr_pack_ecf_dap_proc_log_info(p_action_no, 'ym=' || v_year_month, 'fetch-start', null);

    v_buss.base_currency := v_base_currency;
    v_buss.entity_id := p_entity_id;
    v_buss.year_month := v_year_month;
    v_buss.ev_date := v_ev_date;
    v_buss.action_no := p_action_no;

    call atr_pack_ecf_dap_proc_check_and_create_bussperiod_detail(v_buss);

    call atr_pack_ecf_dap_proc_fetch_all_buss_data(v_buss);

    --提交事务
    commit;

    -- 错误信息
    begin
        select string_agg(x.mark, ',' order by x.id)
        into strict v_error_modules
        from (select t.mark, max(t.id) id
              from atr_log_ecf_dap t
              where t.action_no = p_action_no
                and t.log_type = 'ERROR'
              group by t.mark) x
        --group by 1
        ;
    exception
        when no_data_found then
            v_error_modules := null;
    end;

    if v_error_modules is not null then
        call atr_pack_ecf_dap_proc_update_action(p_action_no, 'E');

        select string_agg(t.mark || '-' || t.msg, chr(10) order by t.id)
        into v_error_msg
        from atr_log_ecf_dap t
        where t.action_no = p_action_no
          and t.log_type = 'ERROR'
        group by t.action_no;
        raise notice '%', v_error_msg;

        raise exception '%', 'The following modules [' || v_error_modules ||
                             '] have encountered errors. Action No. is ' ||
                             p_action_no using errcode = '45099';
    end if;

    --同步业务期间执行状态
    call atr_pack_buss_period_proc_period_execution(p_entity_id, '1');

    commit;

    call atr_pack_ecf_dap_proc_update_action(p_action_no, 'S');

    call atr_pack_ecf_dap_proc_log_info(p_action_no, 'ym=' || v_year_month, 'fetch-end', null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_all_step1(IN p_entity_id bigint, IN p_year_month character varying, IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare

    --------------------------------
    -- 提取所有接口表数据
    --------------------------------
    v_ev_date       date;
    v_buss          atr_pack_ecf_dap_record_buss;
    v_base_currency varchar(3);
begin
    v_ev_date := last_day(to_date(p_year_month, 'yyyymm'));

    call atr_pack_ecf_dap_proc_create_action(p_entity_id, v_ev_date, p_action_no);

    select t.currency_code
    into v_base_currency
    from bpluser.bbs_conf_account_set t
    where t.entity_id = p_entity_id;

    if v_base_currency is null then
        call atr_pack_ecf_dap_proc_update_action(p_action_no, 'E');
        raise exception '%', '没有配置本位币' using errcode = '45091';
    end if;

    call atr_pack_ecf_dap_proc_log_info(p_action_no, 'ym=' || p_year_month, 'fetch-start', null);

    v_buss.base_currency := v_base_currency;
    v_buss.entity_id := p_entity_id;
    v_buss.year_month := p_year_month;
    v_buss.ev_date := v_ev_date;
    v_buss.action_no := p_action_no;

    call atr_pack_ecf_dap_proc_check_and_create_bussperiod_detail(v_buss);

    --提交事务
    commit;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_all_step2(IN p_action_no character varying, IN p_biz_code character varying)
    LANGUAGE plpgsql
    AS $$
declare
    v_buss atr_pack_ecf_dap_record_buss;
begin
    select t.action_no,
           a.currency_code,
           t.entity_id,
           t.year_month,
           last_day(to_date(t.year_month, 'yyyymm')),
           p.buss_period_id,
           case when a.currency_code = 'IDR' then '1' else '0' end
    into v_buss.action_no,
        v_buss.base_currency,
        v_buss.entity_id,
        v_buss.year_month,
        v_buss.ev_date,
        v_buss.buss_period_id,
        v_buss.is_bcai
    from atr_buss_ecf_dap_action t,
         bpluser.bbs_conf_account_set a,
         atr_conf_bussperiod p
    where t.action_no = p_action_no
      and a.entity_id = t.entity_id
      and p.entity_id = t.entity_id
      and p.year_month = t.year_month;

    call atr_pack_ecf_dap_proc_log_info(p_action_no, null, 'fetch-' || p_biz_code, '1');

    if p_biz_code = 'DAP_DD_PREMIUM' then
        call atr_pack_ecf_dap_proc_fetch_premium_recv_dd(v_buss);
    elsif p_biz_code = 'DAP_FO_PREMIUM' then
        call atr_pack_ecf_dap_proc_fetch_premium_recv_fo(v_buss);
    elsif p_biz_code = 'DAP_TI_PREMIUM' then
        call atr_pack_ecf_dap_proc_fetch_premium_recv_ti(v_buss);
    elsif p_biz_code = 'DAP_TO_PREMIUM' then
        call atr_pack_ecf_dap_proc_fetch_premium_recv_to(v_buss);
    elsif p_biz_code = 'DAP_TO_PREMIUM_RECOVERED' then
        call atr_pack_ecf_dap_proc_fetch_premium_recovered_to(v_buss);
    elsif p_biz_code = 'DAP_DD_PREMIUM_PAID' then
        null; --  call atr_pack_ecf_dap_proc_fetch_premium_paid_dd(v_buss);
    elsif p_biz_code = 'DAP_FO_PREMIUM_PAID' then
        null; --  call atr_pack_ecf_dap_proc_fetch_premium_paid_fo(v_buss);
    elsif p_biz_code = 'DAP_TI_PREMIUM_PAID' then
        call atr_pack_ecf_dap_proc_fetch_premium_paid_ti(v_buss);
    elsif p_biz_code = 'DAP_TO_PREMIUM_PAID' then
        call atr_pack_ecf_dap_proc_fetch_premium_paid_to(v_buss);
    elsif p_biz_code = 'DAP_DD_CLAIM_AMOUNT' then
        call atr_pack_ecf_dap_proc_fetch_claim_dd(v_buss);
    elsif p_biz_code = 'DAP_FO_CLAIM_AMOUNT' then
        call atr_pack_ecf_dap_proc_fetch_claim_fo(v_buss);
    elsif p_biz_code = 'DAP_TI_CLAIM_AMOUNT' then
        call atr_pack_ecf_dap_proc_fetch_claim_ti(v_buss);
    elsif p_biz_code = 'DAP_TO_CLAIM_AMOUNT' then
        call atr_pack_ecf_dap_proc_fetch_claim_to(v_buss);
    elsif p_biz_code = 'DAP_DD_CLAIM_ACCIDENT_AMOUNT' then
        call atr_pack_ecf_dap_proc_fetch_claim_accident_amount_dd(v_buss);
    elsif p_biz_code = 'DAP_FO_CLAIM_ACCIDENT_AMOUNT' then
        call atr_pack_ecf_dap_proc_fetch_claim_accident_amount_fo(v_buss);
    elsif p_biz_code = 'DAP_TI_CLAIM_ACCIDENT_AMOUNT' then
        call atr_pack_ecf_dap_proc_fetch_claim_accident_amount_ti(v_buss);
    elsif p_biz_code = 'DAP_TO_CLAIM_ACCIDENT_AMOUNT' then
        call atr_pack_ecf_dap_proc_fetch_claim_accident_amount_to(v_buss);
    else
        raise exception 'Unsupport biz_code = %, action_no = %', p_biz_code, p_action_no;
    end if;

    call atr_pack_ecf_dap_proc_log_info(p_action_no, null, 'fetch-' || p_biz_code, '2');

end;
$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_all_step3(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    v_entity_id   bigint;
    v_year_month  varchar(6);
    v_error_count int;
begin

    select count(*)
    into v_error_count
    from atr_log_ecf_dap t
    where t.action_no = p_action_no
      and t.log_type = 'ERROR';

    if v_error_count > 0 then
        call atr_pack_ecf_dap_proc_update_action(p_action_no, 'E');
        raise 'An error has occurred. Action No. is %', p_action_no;
    end if;

    select t.entity_id, t.year_month
    into v_entity_id, v_year_month
    from atr_buss_ecf_dap_action t
    where t.action_no = p_action_no;

    --同步业务期间执行状态
    call atr_pack_buss_period_proc_period_execution(v_entity_id, '2');
    commit;

    call atr_pack_ecf_dap_proc_update_action(p_action_no, 'S');

    call atr_pack_ecf_dap_proc_log_info(p_action_no, 'ym=' || v_year_month, 'fetch-end', null);
end;
$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_all_without_bussperiod(IN p_entity_id bigint, IN p_draw_date date, INOUT p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare

    v_year_month    varchar(6);
    v_ev_date       timestamp;
    v_buss          atr_pack_ecf_dap_record_buss;
    v_base_currency varchar(3);
    v_error_modules varchar(4000);

begin
    p_action_no := atr_pack_ecf_util_func_create_action_no();

    call atr_pack_ecf_dap_proc_create_action(p_entity_id, p_draw_date, p_action_no);

    select t.currency_code
    into v_base_currency
    from bpluser.bbs_conf_account_set t
    where t.entity_id = p_entity_id;

    if v_base_currency is null then
        call atr_pack_ecf_dap_proc_update_action(p_action_no, 'E');
        raise exception '%', '没有配置本位币' using errcode = '45091';
    end if;

    v_ev_date := last_day(p_draw_date);
    v_year_month := to_char(v_ev_date, 'yyyymm');

    call atr_pack_ecf_dap_proc_log_info(p_action_no,
                                        'ym=' || v_year_month,
                                        'fetch-w-start',
                                        null);

    v_buss.base_currency := v_base_currency;
    v_buss.entity_id := p_entity_id;
    v_buss.year_month := v_year_month;
    v_buss.ev_date := v_ev_date;
    v_buss.action_no := p_action_no;
    v_buss.is_without_bussperiod := '1';

    -- atr_pack_ecf_dap_proc_check_and_create_bussperiod_detail(v_buss);

    call atr_pack_ecf_dap_proc_fetch_all_buss_data(v_buss);

    --提交事务
    commit;

    -- 错误信息
    begin
        select t.mark
        from (select string_agg(x.mark, ',' order by x.id) as mark
              --listagg(x.mark, ',') within group (order by x.id)
              into strict v_error_modules
              from (select t.mark, max(t.id) id
                    from atr_log_ecf_dap t
                    where t.action_no = p_action_no
                      and t.log_type = 'ERROR'
                    group by t.mark) x) t
        group by t.mark;
    exception
        when no_data_found then
            v_error_modules := null;
    end;

    if v_error_modules is not null then
        call atr_pack_ecf_dap_proc_update_action(p_action_no, 'E');
        raise exception 'The following modules [%]  have encountered errors. Action No. is %', v_error_modules,p_action_no using errcode = '-20099';
    end if;

    call atr_pack_ecf_dap_proc_update_action(p_action_no, 'S');

    call atr_pack_ecf_dap_proc_log_info(p_action_no, 'ym=' || v_year_month, 'fetch-w-end', null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_paid_ti(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    --- 实付赔款 （合约分入）
    ------------------------------------------
begin

    delete
    from atr_dap_ti_claim_paid a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    insert into atr_dap_ti_claim_paid(id, -- ID
                                      entity_id, --    业务单位ID
                                      claim_loss_no, -- 赔案号
                                      claim_loss_seq_no, -- 赔案序号
                                      approval_date, -- 核赔日期
                                      actual_payment_date, -- 实际赔付日期
                                      year_month, -- 业务年月|实际赔付年月
                                      portfolio_no, -- 合同组合号码
                                      icg_no, -- 合同组号码
                                      evaluate_approach, -- 评估方法
                                      loa_code, -- LOA编码
                                      product_code, -- 产品代码
                                      risk_code, --    险种代码
                                      cmunit_no, --   公共项/计量单元编号
                                      claim_no, --   立案号码
                                      ri_statement_no, -- 帐单号
                                      ri_master_statement_no, -- 总账单号码
                                      treaty_no, -- 合约号
                                      accident_date_time, -- 事故日期
                                      dept_id, -- 业务部门
                                      currency_code, --   币种
                                      paid_amount, -- 实付赔款
                                      draw_time --  提数日期
    )
    select nextval('atr_seq_dap_ti_claim_paid'),
           entity_id,
           ri_statement_no        claim_loss_no,
           est_payment_seq_no     claim_loss_seq_no,
           est_payment_date       approval_date,
           actual_payment_date as actual_payment_date,
           p_buss.year_month      year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           risk_code              product_code,
           risk_code,
           cmunit_no,
           claim_no,
           ri_statement_no,
           ri_statement_no        ri_master_statement_no,
           treaty_no,
           accident_date_time,
           dept_id,
           currency_code,
           paid_amount,
           clock_timestamp()      draw_time
    from (select a.entity_id,
                 a.ri_statement_no,                                                             --合约分入不存在对应案子，因此存放账单号码
                 a.est_payment_seq_no,
                 a.est_payment_date,
                 a.actual_payment_date,
                 cm.portfolio_no,
                 cm.icg_no,
                 cm.evaluate_approach,
                 cm.loa_code,
                 a.risk_code,
                 cm.cmunit_no,
                 null                                                    as claim_no,           --合约分入不存在立案号码
                 a.treaty_no,
                 coalesce(to_date(atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                                          a.entity_id,
                                                                          a.ri_statement_no,
                                                                          'ACCPERIOD',
                                                                          a.business_source_code,
                                                                          null),
                                  'YYYY-MM-DD'),
                          date_trunc('month', a.actual_payment_date))    as accident_date_time, --合约业务事故日期需要待 EY 给出解决方案
                 a.dept_id,
                 p_buss.base_currency                                       currency_code,
                 sum(a.amount *
                     atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                       p_buss.ev_date,
                                                       p_buss.base_currency,
                                                       a.currency_code)) as paid_amount
          from dmuser.dm_acc_payment a,
               dmuser.dm_buss_cmunit_treaty cm
          where a.entity_id = p_buss.entity_id
            and a.business_source_code = 'TB'
            and a.entry_type_code like '1%'
            and a.ri_direction_code = 'I'
            and a.expenses_type_code in
                (select t.expenses_type_code
                 from bpluser.bbs_conf_fee_type_mapping t
                 where t.fee_class = 'Claim'
                   and t.business_source_code = 'TI') --匹配保费费用类型
            and cm.entity_id = a.entity_id
            and cm.treaty_no = a.treaty_no
            and cm.year_month <= p_buss.year_month
            and to_char(a.bu_voucher_date, 'YYYYMM') = p_buss.year_month
          group by a.entity_id,
                   a.ri_statement_no,
                   a.est_payment_seq_no,
                   a.est_payment_date,
                   a.actual_payment_date,
                   a.risk_code,
                   a.treaty_no,
                   a.est_payment_date,
                   a.dept_id,
                   a.business_source_code,
                   cm.portfolio_no,
                   cm.icg_no,
                   cm.evaluate_approach,
                   cm.loa_code,
                   cm.cmunit_no) a;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_paid_to(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 实付赔款 （合约分出）
    ------------------------------------------
begin

    delete
    from atr_dap_to_claim_paid a
    where a.entity_id = p_buss.entity_id
      and a.year_month = p_buss.year_month;

    if p_buss.is_bcai = '1' then
        insert into atr_dap_to_claim_paid (id, entity_id, claim_loss_no, claim_loss_seq_no, approval_date,
                                           actual_payment_date, year_month, portfolio_no, icg_no, evaluate_approach,
                                           loa_code, product_code, risk_code, cmunit_no, claim_no, ri_statement_no,
                                           ri_master_statement_no, treaty_no,
                                           ri_policy_no, accident_date_time, dept_id,
                                           currency_code, paid_amount, draw_time)
        select nextval('atr_seq_dap_to_claim_recv'),
               entity_id,
               claim_loss_no,
               claim_loss_seq_no,
               approval_date,
               t.approval_date as actual_payment_date,
               year_month,
               portfolio_no,
               icg_no,
               evaluate_approach,
               loa_code,
               product_code,
               risk_code,
               cmunit_no,
               claim_no,
               ri_statement_no,
               ri_master_statement_no,
               treaty_no,
               ri_policy_no,
               accident_date_time,
               dept_id,
               currency_code,
               t.recv_amount   as paid_amount,
               clock_timestamp()
        from atr_dap_to_claim_recv t
        where t.entity_id = p_buss.entity_id
          and t.year_month = p_buss.year_month;

        commit;

        return;
    end if;


    insert into atr_dap_to_claim_paid(id, -- ID
                                      entity_id, -- 业务单位ID
                                      claim_loss_no, -- 赔案号
                                      claim_loss_seq_no, -- 赔案序号
                                      approval_date, --   核赔日期
                                      actual_payment_date, -- 实际赔付日期
                                      year_month, -- 业务年月|实际赔付年月
                                      portfolio_no, --   合同组合号码
                                      icg_no, -- 合同组号码
                                      evaluate_approach, --    评估方法
                                      loa_code, -- LOA编码
                                      product_code, --   产品代码
                                      risk_code, -- 险种代码
                                      cmunit_no, --   公共项/计量单元编号
                                      claim_no, -- 立案号码
                                      ri_statement_no, -- 帐单号
                                      ri_master_statement_no, -- 总账单号码
                                      treaty_no, -- 合约号|合约分入转分出特有
                                      ri_policy_no, -- 分保单号码|“直保&临分分入”转分出特有
                                      accident_date_time, -- 事故日期
                                      dept_id, -- 业务部门
                                      currency_code, -- 币种
                                      paid_amount, -- 实付赔款
                                      draw_time --  提数日期  EVALUATE_APPROACH, -- 评ATR_SEQ_BUSS_TO_CLAIM_PAID  计量模型
    )
    select nextval('atr_seq_dap_to_claim_paid'),
           entity_id,
           claim_loss_no,
           est_payment_seq_no claim_loss_seq_no,
           lossuw             approval_date,
           actual_payment_date,
           p_buss.year_month  year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           product_code,
           risk_code,
           cmunit_no,
           claim_no,
           ri_statement_no,
           ri_statement_no    ri_master_statement_no,
           treaty_no,
           ri_policy_no,
           accident_date_time,
           dept_id,
           currency_code,
           paid_amount,
           clock_timestamp()  draw_time
    from (select a.entity_id,
                 coalesce(a.claim_loss_no, a.ri_statement_no)            as claim_loss_no,
                 a.est_payment_seq_no,
                 (case
                      when a.ri_policy_no is null then a.est_payment_date
                      else to_date(atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                                           a.entity_id,
                                                                           a.claim_loss_no,
                                                                           'DOA',
                                                                           a.business_source_code,
                                                                           'O'),
                                   'YYYY-MM-DD') end)                    as lossuw,
                 a.actual_payment_date,
                 cm.portfolio_no,
                 cm.icg_no,
                 cm.evaluate_approach,
                 cm.loa_code,
                 atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                         a.entity_id,
                                                         a.policy_no,
                                                         'PRODUCT',
                                                         a.business_source_code,
                                                         'O')            as product_code,
                 a.risk_code,
                 cm.cmunit_no,
                 coalesce(atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                                  a.entity_id,
                                                                  a.claim_loss_no,
                                                                  'CLAIM',
                                                                  a.business_source_code,
                                                                  'O'),
                          a.ri_statement_no)                             as claim_no,
                 a.ri_statement_no,
                 a.treaty_no,
                 a.ri_policy_no,
                 a.est_payment_date,
                 (case
                      when a.ri_policy_no is null then date_trunc('month', a.est_payment_date)
                      else to_date(atr_pack_ecf_dap_func_get_business_info(p_buss.action_no,
                                                                           a.entity_id,
                                                                           a.claim_loss_no,
                                                                           'DOA',
                                                                           a.business_source_code,
                                                                           'O'),
                                   'YYYY-MM-DD') end)                    as accident_date_time, --待提供合约分入转分出的出险日期
                 a.dept_id,
                 p_buss.base_currency                                       currency_code,
                 sum(a.amount *
                     atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                       p_buss.ev_date,
                                                       p_buss.base_currency,
                                                       a.currency_code)) as paid_amount
          from dmuser.dm_acc_payment a,
               dmuser.dm_buss_cmunit_treaty cm
          where a.entity_id = p_buss.entity_id
            and a.business_source_code = 'TB'
            and ((p_buss.is_bcai = '1' and a.entry_type_code like '%CLAIM'
              and not a.entry_type_code ~ '^(CN|DN)')
              or (p_buss.is_bcai = '0' and a.entry_type_code like '1%'))
            and a.ri_direction_code = 'O'
            and a.ri_arrangement_code in ('T', 'X')
            and a.expenses_type_code in
                (select t.expenses_type_code
                 from bpluser.bbs_conf_fee_type_mapping t
                 where t.fee_class = 'Claim'
                   and t.business_source_code = 'TO')
            and cm.entity_id = a.entity_id
            and cm.treaty_no = a.treaty_no
            and cm.year_month <= p_buss.year_month
            and to_char(a.bu_voucher_date, 'YYYYMM') = p_buss.year_month
          group by a.entity_id,
                   a.policy_no,
                   a.claim_loss_no,
                   a.est_payment_seq_no,
                   a.ri_statement_no,
                   a.ri_statement_no,
                   a.treaty_no,
                   a.ri_policy_no,
                   a.dept_id,
                   a.est_payment_date,
                   a.est_payment_date,
                   a.risk_code,
                   a.actual_payment_date,
                   a.business_source_code,
                   cm.portfolio_no,
                   cm.icg_no,
                   cm.evaluate_approach,
                   cm.loa_code,
                   cm.cmunit_no,
                   cm.risk_class_code) a;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_recv_ti(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 应付赔款 （合约分入）
    ------------------------------------------
begin

    delete
    from atr_dap_ti_claim_recv a
    where a.entity_id = p_buss.entity_id
      and year_month = p_buss.year_month;

    insert into atr_dap_ti_claim_recv(id, -- ID
                                      entity_id, -- 业务单位ID
                                      claim_loss_no, -- 赔案号
                                      claim_loss_seq_no, -- 赔案序号
                                      approval_date, -- 核赔日期
                                      year_month, -- 业务年月|核赔年月
                                      portfolio_no, -- 合同组合号码
                                      icg_no, -- 合同组号码
                                      evaluate_approach, -- 评估方法
                                      loa_code, -- LOA编码
                                      product_code, -- 产品代码
                                      risk_code, -- 险种代码
                                      cmunit_no, -- 公共项/计量单元编号
                                      claim_no, -- 立案号码
                                      ri_statement_no, -- 帐单号
                                      ri_master_statement_no, -- 总账单号码
                                      treaty_no, -- 合约号
                                      est_payment_date, -- 应付日期
                                      accident_date_time, -- 事故日期
                                      dept_id, -- 业务部门
                                      currency_code, -- 币种
                                      recv_amount, -- 应付赔款
                                      draw_time --  提数日期
    )
    select nextval('atr_seq_dap_ti_claim_recv'),
           entity_id,
           ri_statement_no                             claim_loss_no,
           '001'                                       claim_loss_seq_no,
           statement_approval_date                     approval_date,
           p_buss.year_month                           year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           risk_code                                   product_code,
           risk_code,
           cmunit_no,
           ri_statement_no                             claim_no,
           ri_statement_no,
           ri_master_statement_no,
           treaty_no,
           statement_payment_date,
           date_trunc('month', statement_payment_date) accident_date_time,
           dept_id,
           currency_code,
           recv_amount,
           clock_timestamp()                           draw_time
    from (select a.entity_id,
                 b.ri_statement_no,
                 b.ri_master_statement_no,
                 b.statement_approval_date,
                 a.treaty_no,
                 a.portfolio_no,
                 a.icg_no,
                 a.evaluate_approach,
                 a.loa_code,
                 c.risk_code,
                 a.cmunit_no,
                 c.dept_id,
                 p_buss.base_currency                                       currency_code,
                 b.statement_payment_date,
                 sum(c.amount *
                     atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                       p_buss.ev_date,
                                                       p_buss.base_currency,
                                                       c.currency_code)) as recv_amount
          from dmuser.dm_buss_cmunit_treaty a,
               dmuser.dm_reins_bill b,
               dmuser.dm_reins_bill_detail c,
               bpluser.bbs_conf_fee_type_mapping e
          where a.entity_id = b.entity_id
            and a.entity_id = c.entity_id
            and b.ri_statement_no = c.ri_statement_no
            and a.treaty_no = b.treaty_no
            and a.entity_id = p_buss.entity_id
            and e.fee_class = 'Claim'
            and e.business_source_code = 'TI'
            and c.expenses_type_code = e.expenses_type_code
            and a.ri_direction_code = 'I'
            and to_char(b.statement_approval_date, 'YYYYMM') = p_buss.year_month
            and a.year_month <= p_buss.year_month
          group by a.entity_id,
                   b.ri_statement_no,
                   b.ri_master_statement_no,
                   b.statement_payment_date,
                   a.treaty_no,
                   a.portfolio_no,
                   a.icg_no,
                   b.statement_approval_date,
                   a.evaluate_approach,
                   a.loa_code,
                   c.risk_code,
                   a.cmunit_no,
                   c.dept_id) a;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_fetch_claim_recv_to(IN p_buss atruser.atr_pack_ecf_dap_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 应付赔款 （合约分出）
    ------------------------------------------
begin

    delete
    from atr_dap_to_claim_recv a
    where a.entity_id = p_buss.entity_id
      and year_month = p_buss.year_month;

    --直接业务&临分分入业务转合约分出
    insert into atr_dap_to_claim_recv(id, -- ID
                                      entity_id, -- 业务单位ID
                                      claim_loss_no, -- 赔案号
                                      claim_loss_seq_no, -- 赔案序号
                                      approval_date, -- 核赔日期
                                      year_month, -- 业务年月|核赔年月
                                      portfolio_no, -- 合同组合号码
                                      icg_no, --合同组号码
                                      evaluate_approach, --    评估方法
                                      loa_code, --LOA编码
                                      product_code, -- 产品代码
                                      risk_code, -- 险种代码
                                      cmunit_no, -- 公共项/计量单元编号
                                      claim_no, -- 立案号码
                                      ri_statement_no, -- 帐单号
                                      ri_master_statement_no, -- 总账单号码
                                      treaty_no, --合约号|合约分入转分出特有
                                      ri_policy_no, -- 分保单号码|“直保&临分分入”转分出特有
                                      est_payment_date, --应付日期
                                      accident_date_time, --事故日期
                                      dept_id, --业务部门
                                      currency_code, -- 币种
                                      recv_amount, -- 应付赔款
                                      draw_time --  提数日期
    )
    select nextval('atr_seq_dap_to_claim_recv'),
           entity_id,
           ri_policy_no                         claim_loss_no,
           ri_endorse_seq_no                    claim_loss_seq_no,
           gl_posting_date                      approval_date,
           p_buss.year_month                    year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           null                                 product_code,
           risk_code,
           cmunit_no,
           ri_statement_no                      claim_no,
           ri_statement_no,
           ri_statement_no                      ri_master_statement_no,
           treaty_no,
           ri_policy_no,
           gl_posting_date                      plan_date,
           date_trunc('month', gl_posting_date) accident_date_time,
           dept_id,
           currency_code,
           recv_amount,
           clock_timestamp()                    draw_time
    from (select a.entity_id,
                 b.ri_policy_no,
                 0                                                       as ri_endorse_seq_no,
                 c.gl_posting_date,
                 a.portfolio_no,
                 a.icg_no,
                 a.evaluate_approach,
                 a.loa_code,
                 c.risk_code,
                 a.cmunit_no,
                 c.ri_statement_no,
                 c.treaty_no,
                 null::numeric                                           as dept_id,
                 p_buss.base_currency                                       currency_code,
                 sum(c.amount *
                     atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                       p_buss.ev_date,
                                                       p_buss.base_currency,
                                                       c.currency_code)) as recv_amount
          from dmuser.dm_buss_cmunit_treaty a,
               dmuser.dm_reins_outward b,
               dmuser.dm_reins_outward_detail c
          where a.entity_id = b.entity_id
            and a.entity_id = c.entity_id
            and a.treaty_no = c.treaty_no
            and b.ri_policy_no = c.ri_policy_no
            and b.ri_endorse_seq_no = c.ri_endorse_seq_no
            and a.entity_id = p_buss.entity_id
            and c.expenses_type_code in
                (select t.expenses_type_code
                 from bpluser.bbs_conf_fee_type_mapping t
                 where t.fee_class = 'Claim'
                   and t.business_source_code = 'TO')
            and a.ri_direction_code = 'O'
            and a.year_month <= p_buss.year_month
            and to_char(c.gl_posting_date, 'YYYYMM') = p_buss.year_month
          group by a.entity_id,
                   b.ri_endorse_seq_no,
                   c.gl_posting_date,
                   a.portfolio_no,
                   a.icg_no,
                   a.evaluate_approach,
                   b.business_source_code,
                   a.loa_code,
                   c.risk_code,
                   a.cmunit_no,
                   c.ri_statement_no,
                   c.treaty_no,
                   b.ri_policy_no,
                   c.gl_posting_date,
                   c.policy_no) x;

    --合约分入转合约分出
    insert into atr_dap_to_claim_recv (id, -- ID
                                       entity_id, -- 业务单位ID
                                       claim_loss_no, -- 赔案号
                                       claim_loss_seq_no, -- 赔案序号
                                       approval_date, -- 核赔日期
                                       year_month, -- 业务年月|核赔年月
                                       portfolio_no, -- 合同组合号码
                                       icg_no, --合同组号码
                                       evaluate_approach, --    评估方法
                                       loa_code, --LOA编码
                                       product_code, -- 产品代码
                                       risk_code, -- 险种代码
                                       cmunit_no, -- 公共项/计量单元编号
                                       claim_no, -- 立案号码
                                       ri_statement_no, -- 帐单号
                                       ri_master_statement_no, -- 总账单号码
                                       treaty_no, --合约号|合约分入转分出特有
                                       ri_policy_no, -- 分保单号码|“直保&临分分入”转分出特有
                                       est_payment_date, --应付日期
                                       accident_date_time, --事故日期
                                       dept_id, --业务部门
                                       currency_code, -- 币种
                                       recv_amount, -- 应付赔款
                                       draw_time --  提数日期
    )
    select nextval('atr_seq_dap_to_claim_recv'),
           x.entity_id,
           ri_statement_no                             claim_loss_no,
           claim_loss_seq_no,
           statement_approval_date                     approval_date,
           p_buss.year_month                           year_month,
           portfolio_no,
           icg_no,
           evaluate_approach,
           loa_code,
           risk_code                                   product_code,
           risk_code,
           cmunit_no,
           ri_statement_no                             claim_no,
           ri_statement_no,
           ri_master_statement_no,
           treaty_no,
           null                                        ri_policy_no,
           statement_payment_date                      plan_date,
           date_trunc('month', statement_payment_date) accident_date_time,
           dept_id,
           currency_code,
           recv_amount,
           clock_timestamp()                           draw_time
    from (select a.entity_id,
                 b.ri_statement_no,
                 '001'::numeric                                          as claim_loss_seq_no,
                 b.statement_approval_date,
                 a.portfolio_no,
                 a.icg_no,
                 a.evaluate_approach,
                 a.loa_code,
                 c.risk_code,
                 a.cmunit_no,
                 b.ri_master_statement_no,
                 b.treaty_no,
                 b.statement_payment_date,
                 c.dept_id,
                 p_buss.base_currency                                       currency_code,
                 sum(c.amount *
                     atr_pack_ecf_dap_func_getexchrate(p_buss.entity_id,
                                                       p_buss.ev_date,
                                                       p_buss.base_currency,
                                                       c.currency_code)) as recv_amount
          from dmuser.dm_buss_cmunit_treaty a,
               dmuser.dm_reins_bill b,
               dmuser.dm_reins_bill_detail c,
               bpluser.bbs_conf_fee_type_mapping e
          where a.entity_id = b.entity_id
            and a.entity_id = c.entity_id
            and a.treaty_no = b.treaty_no
            and a.entity_id = p_buss.entity_id
            and b.ri_statement_no = c.ri_statement_no
            and e.fee_class = 'Claim'
            and e.business_source_code = 'TO'
            and c.expenses_type_code = e.expenses_type_code
            and a.ri_direction_code = 'O'
            and to_char(b.statement_approval_date, 'YYYYMM') = p_buss.year_month
            and a.year_month <= p_buss.year_month
          group by a.entity_id,
                   b.ri_statement_no,
                   b.statement_approval_date,
                   a.portfolio_no,
                   a.icg_no,
                   a.evaluate_approach,
                   a.loa_code,
                   c.risk_code,
                   a.cmunit_no,
                   b.ri_master_statement_no,
                   b.treaty_no,
                   b.statement_payment_date,
                   c.dept_id) x;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_log(IN p_log_type character varying, IN p_action_no character varying, IN p_business_info character varying, IN p_mark character varying, IN p_msg character varying)
    LANGUAGE plpgsql
    AS $$
begin
    insert into atr_log_ecf_dap
    (id, log_type, action_no, business_info, mark, msg, create_time)
    values (nextval('atr_seq_log_ecf_dap'),
            p_log_type,
            p_action_no,
            p_business_info,
            p_mark,
            substr(p_msg, 1, 4000),
            clock_timestamp());

    commit;
end
$$;

CREATE PROCEDURE atruser.atr_pack_ecf_dap_proc_log_error(IN p_action_no character varying, IN p_business_info character varying, IN p_mark character varying, IN p_msg character varying)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_ecf_dap_proc_log('ERROR', p_action_no,
                                   p_business_info, p_mark, p_msg);
end;

$$;


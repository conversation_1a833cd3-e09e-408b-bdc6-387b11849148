create or replace view atr_v_conf_code as
select v.code_id,
       v.upper_code_id,
       v.code_code,
       SUBSTR(code_code_idx,2,length(code_code_idx)-2) code_code_idx,
       v.code_c_name,
       v.code_e_name,
       v.code_l_name,
       v.valid_is,
       v.display_no,
       v.creator_id,
       v.create_time,
       v.updator_id,
       v.update_time from (
select item.code_id,
       item.upper_code_id,
       item.code_code,
       SYS_CONNECT_BY_PATH(item.code_code,'/') ||'/'  as code_code_idx,
       item.code_c_name,
       item.code_e_name,
       item.code_l_name,
       item.valid_is,
       item.display_no,
       item.creator_id,
       item.create_time,
       item.updator_id,
       item.update_time
  from atruser.atr_conf_code item
 START WITH item.upper_code_id = 0
CONNECT BY PRIOR item.code_id = item.upper_code_id)v
       order by v.code_code_idx;
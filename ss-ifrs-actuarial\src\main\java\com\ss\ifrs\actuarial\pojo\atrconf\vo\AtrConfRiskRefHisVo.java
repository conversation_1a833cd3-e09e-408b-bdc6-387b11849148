/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-07-08 15:17:45
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-07-08 15:17:45<br/>
 * Description: LRC计量方式配置轨迹表<br/>
 * Table Name: atr_conf_risk_refhis<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC计量方式配置轨迹表")
public class AtrConfRiskRefHisVo implements Serializable {
    /**
     * Database column: atr_conf_risk_refhis.risk_ref_his_id
     * Database remarks: null
     */
    private Long riskRefHisId;

    /**
     * Database column: atr_conf_risk_refhis.risk_ref_id
     * Database remarks: risk_ref_id|键
     */
    @ApiModelProperty(value = "risk_ref_id|键", required = false)
    private Long riskRefId;

    /**
     * Database column: atr_conf_risk_refhis.center_id
     * Database remarks: center_id|核算单位ID
     */
    @ApiModelProperty(value = "center_id|核算单位ID", required = false)
    private Long entityId;

    /**
     * Database column: atr_conf_risk_refhis.atr_type
     * Database remarks: atr_type|风险分布模式
     */
    @ApiModelProperty(value = "atr_type|风险分布模式", required = false)
    private String atrType;

    /**
     * Database column: atr_conf_risk_refhis.business_model
     * Database remarks: Business_Model|业务模型：D-直保 T-合约 F-临分
     */
    @ApiModelProperty(value = "Business_Model|业务模型：D-直保 T-合约 F-临分", required = true)
    private String businessModel;

    /**
     * Database column: atr_conf_risk_refhis.business_direction
     * Database remarks: Business_Direction|业务方向：D-不区分 I-分入 O-分出
     */
    @ApiModelProperty(value = "Business_Direction|业务方向：D-不区分 I-分入 O-分出", required = true)
    private String businessDirection;

    /**
     * Database column: atr_conf_risk_refhis.loa_code
     * Database remarks: Loa_Code|loa代码
     */
    @ApiModelProperty(value = "Loa_Code|loa代码", required = true)
    private String loaCode;

    /**
     * Database column: atr_conf_risk_refhis.checked_id
     * Database remarks: checked_id|审核人
     */
    @ApiModelProperty(value = "checked_id|审核人", required = false)
    private Long checkedId;

    /**
     * Database column: atr_conf_risk_refhis.checked_time
     * Database remarks: checked_time|审核时间
     */
    @ApiModelProperty(value = "checked_time|审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: atr_conf_risk_refhis.checked_state
     * Database remarks: checked_state|审核状态
     */
    @ApiModelProperty(value = "checked_state|审核状态", required = false)
    private String auditState;

    /**
     * Database column: atr_conf_risk_refhis.checked_msg
     * Database remarks: checked_msg|审核意见
     */
    @ApiModelProperty(value = "checked_msg|审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: atr_conf_risk_refhis.valid_is
     * Database remarks: valid_is|是否有效
     */
    @ApiModelProperty(value = "valid_is|是否有效", required = false)
    private String validIs;

    /**
     * Database column: atr_conf_risk_refhis.remark
     * Database remarks: remark|备注
     */
    @ApiModelProperty(value = "remark|备注", required = false)
    private String remark;

    /**
     * Database column: atr_conf_risk_refhis.oper_type
     * Database remarks: oper_type|操作类型(1-add；2-Modiffy；3-delete；4-audit;)
     */
    @ApiModelProperty(value = "oper_type|操作类型(1-add；2-Modiffy；3-delete；4-audit;)", required = false)
    private String operType;

    /**
     * Database column: atr_conf_risk_refhis.oper_id
     * Database remarks: oper_id|操作人
     */
    @ApiModelProperty(value = "oper_id|操作人", required = false)
    private Long operId;

    /**
     * Database column: atr_conf_risk_refhis.oper_time
     * Database remarks: oper_time|操作时间，格式：yyyy-MM-dd hh:mm:ss
     */
    @ApiModelProperty(value = "oper_time|操作时间，格式：yyyy-MM-dd hh:mm:ss", required = false)
    private Date operTime;

    /**
     * Database column: atr_conf_risk_refhis.creator_id
     * Database remarks: creator_id|创建人
     */
    @ApiModelProperty(value = "creator_id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_risk_refhis.create_time
     * Database remarks: create_time|创建时间
     */
    @ApiModelProperty(value = "create_time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_risk_refhis.updator_id
     * Database remarks: updator_id|最后修改人
     */
    @ApiModelProperty(value = "updator_id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_conf_risk_refhis.update_time
     * Database remarks: update_time|最后修改时间
     */
    @ApiModelProperty(value = "update_time|最后修改时间", required = false)
    private Date updateTime;

    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Integer serialNo;

    private String creatorName;

    private String updatorName;

    private static final long serialVersionUID = 1L;

    public Long getRiskRefHisId() {
        return riskRefHisId;
    }

    public void setRiskRefHisId(Long riskRefHisId) {
        this.riskRefHisId = riskRefHisId;
    }

    public Long getRiskRefId() {
        return riskRefId;
    }

    public void setRiskRefId(Long riskRefId) {
        this.riskRefId = riskRefId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getAtrType() {
        return atrType;
    }

    public void setAtrType(String atrType) {
        this.atrType = atrType;
    }

    public String getBusinessModel() {
        return businessModel;
    }

    public void setBusinessModel(String businessModel) {
        this.businessModel = businessModel;
    }

    public String getBusinessDirection() {
        return businessDirection;
    }

    public void setBusinessDirection(String businessDirection) {
        this.businessDirection = businessDirection;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperType() {
        return operType;
    }

    public void setOperType(String operType) {
        this.operType = operType;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }
}
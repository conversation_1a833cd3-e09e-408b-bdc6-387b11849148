call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_article_balance');
create table accuser.acc_buss_article_balance (
  article_id bigint primary key not null,
  entity_id bigint not null,
  book_code character varying(8) not null,
  year_month character varying(6) not null,
  root_account_id bigint,
  account_id bigint,
  article1 character varying(60),
  article2 character varying(60),
  article3 character varying(60),
  article4 character varying(60),
  article5 character varying(60),
  article6 character varying(60),
  article7 character varying(60),
  article8 character varying(60),
  article9 character varying(60),
  article10 character varying(60),
  article11 character varying(60),
  article12 character varying(60),
  article13 character varying(60),
  article14 character varying(60),
  article15 character varying(60),
  article16 character varying(60),
  article17 character varying(60),
  currency_code character varying(3),
  currency_cu_code character varying(3),
  debit_amount numeric(32,8) not null,
  debit_amount_cu numeric(32,8) not null,
  credit_amount numeric(32,8) not null,
  credit_amount_cu numeric(32,8) not null,
  debit_amount_quarter numeric(32,8) not null,
  debit_amount_quarter_cu numeric(32,8) not null,
  credit_amount_quarter numeric(32,8) not null,
  credit_amount_quarter_cu numeric(32,8) not null,
  debit_amount_year numeric(32,8) not null,
  debit_amount_year_cu numeric(32,8) not null,
  credit_amount_year numeric(32,8) not null,
  credit_amount_year_cu numeric(32,8) not null,
  opening_balance numeric(32,8) not null,
  closing_balance numeric(32,8) not null,
  opening_balance_cu numeric(32,8) not null,
  closing_balance_cu numeric(32,8) not null,
  create_time timestamp without time zone,
  creator_id bigint,
  update_time timestamp without time zone,
  updator_id bigint,
  serial_no integer,
  article character varying(400),
  center_code character varying(32), -- 核算单位
  article18 character varying(60),
  article19 character varying(60),
  article20 character varying(60),
  article21 character varying(60),
  article22 character varying(60),
  article23 character varying(60)
);
create index idx_acc_buss_article_balance on acc_buss_article_balance using btree (entity_id, book_code, year_month, account_id, currency_code, currency_cu_code, article);
comment on column accuser.acc_buss_article_balance.center_code is '核算单位';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_article_balancehis');
create table accuser.acc_buss_article_balancehis (
  article_his_id bigint primary key not null,
  article_id bigint not null,
  serial_no integer not null,
  entity_id bigint not null,
  book_code character varying(8) not null,
  year_month character varying(6) not null,
  root_account_id bigint,
  account_id bigint,
  article1 character varying(60),
  article2 character varying(60),
  article3 character varying(60),
  article4 character varying(60),
  article5 character varying(60),
  article6 character varying(60),
  article7 character varying(60),
  article8 character varying(60),
  article9 character varying(60),
  article10 character varying(60),
  article11 character varying(60),
  article12 character varying(60),
  article13 character varying(60),
  article14 character varying(60),
  article15 character varying(60),
  article16 character varying(60),
  article17 character varying(60),
  currency_code character varying(3),
  currency_cu_code character varying(3),
  debit_amount numeric(32,8) not null,
  debit_amount_cu numeric(32,8) not null,
  credit_amount numeric(32,8) not null,
  credit_amount_cu numeric(32,8) not null,
  debit_amount_quarter numeric(32,8) not null,
  debit_amount_quarter_cu numeric(32,8) not null,
  credit_amount_quarter numeric(32,8) not null,
  credit_amount_quarter_cu numeric(32,8) not null,
  debit_amount_year numeric(32,8) not null,
  debit_amount_year_cu numeric(32,8) not null,
  credit_amount_year numeric(32,8) not null,
  credit_amount_year_cu numeric(32,8) not null,
  opening_balance numeric(32,8) not null,
  closing_balance numeric(32,8) not null,
  opening_balance_cu numeric(32,8) not null,
  closing_balance_cu numeric(32,8) not null,
  create_time timestamp without time zone,
  creator_id bigint,
  update_time timestamp without time zone,
  updator_id bigint,
  center_code character varying(32), -- 核算单位
  article18 character varying(60),
  article19 character varying(60),
  article20 character varying(60),
  article21 character varying(60),
  article22 character varying(60),
  article23 character varying(60)
);
comment on column accuser.acc_buss_article_balancehis.center_code is '核算单位';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_entry_data');
create table accuser.acc_buss_entry_data (
  buss_entry_id bigint not null,
  entry_data_id bigint,
  entity_id bigint,
  book_code character varying(8),
  year_month character varying(6) not null,
  task_code character varying(32),
  proc_id bigint not null,
  scenario_id bigint,
  account_id_dr bigint,
  account_id_cr bigint,
  posting_type_code character varying(10),
  scenario_type character varying(2),
  voucher_id bigint,
  change_time smallint,
  currency_cu_code character varying(3),
  currency_code character varying(3),
  exchange_rate numeric(32,8),
  entry_state smallint,
  entry_msg character varying(100),
  create_time timestamp(6) without time zone,
  creator_id bigint,
  update_time timestamp(6) without time zone,
  updator_id bigint,
  bu_voucher_no character varying(60),
  entry_date timestamp(6) without time zone,
  scen_serial_no bigint,
  center_code character varying(32), -- 核算单位
  article_msg character varying(500) -- 辅助核算项校验
)
partition by LIST (entity_id);

create index idx_acc_buss_entry_data_id on acc_buss_entry_data using btree (entry_data_id);
comment on column accuser.acc_buss_entry_data.center_code is '核算单位';
comment on column accuser.acc_buss_entry_data.article_msg is '辅助核算项校验';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_entry_data_detail');
create table accuser.acc_buss_entry_data_detail (
  buss_entry_dtl_id bigint not null,
  buss_entry_id bigint,
  entity_id bigint,
  year_month character varying(6) not null,
  book_code character varying(8),
  proc_id bigint not null,
  display_no bigint,
  account_id_dr bigint,
  account_id_cr bigint,
  create_time timestamp(6) without time zone,
  creator_id bigint,
  update_time timestamp(6) without time zone,
  updator_id bigint,
  entry_serial_no bigint,
  center_code character varying(32) -- 核算单位
)
partition by LIST (entity_id);

create index idx_acc_buss_entry_data_detail on acc_buss_entry_data_detail using btree (buss_entry_id);
comment on column accuser.acc_buss_entry_data_detail.center_code is '核算单位';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_entry_data_detail_entry_temp');
/*create table accuser.acc_buss_entry_data_detail_entry_temp (
  buss_entry_dtl_id bigint primary key not null,
  buss_entry_id bigint,
  display_no bigint,
  account_id_dr bigint,
  account_id_cr bigint,
  entry_data_id integer,
  currency_code character varying(32),
  currency_cu_code character varying(32),
  amount numeric(32,8),
  exchange_rate numeric(32,8),
  bu_voucher_no character varying(64),
  proc_id integer,
  posting_type_code character varying(24),
  task_code character varying(64)
)
partition by LIST (entity_id);

create index idx_acc_buss_entry_data_detail_entry_temp_bu_voucher_no on acc_buss_entry_data_detail_entry_temp using btree (bu_voucher_no);
create index idx_acc_buss_entry_data_detail_entry_temp_buss_entry_id on acc_buss_entry_data_detail_entry_temp using btree (buss_entry_id);*/

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_entry_data_err_copy');
/*create table accuser.acc_buss_entry_data_err_copy (
  buss_entry_id bigint,
  entry_data_id bigint,
  entity_id bigint,
  book_code character varying,
  year_month character varying,
  task_code character varying,
  proc_id bigint,
  scenario_id bigint,
  account_id_dr bigint,
  account_id_cr bigint,
  posting_type_code character varying,
  scenario_type character varying,
  voucher_id bigint,
  change_time bigint,
  currency_cu_code character varying,
  currency_code character varying,
  exchange_rate numeric(32,8),
  entry_state bigint,
  entry_msg character varying,
  create_time timestamp(6) without time zone,
  creator_id bigint,
  update_time timestamp(6) without time zone,
  updator_id bigint,
  bu_voucher_no character varying,
  entry_date timestamp(6) without time zone,
  scen_serial_no bigint
)
partition by LIST (entity_id);*/

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_entry_data_success_part');
/*create table accuser.acc_buss_entry_data_success_part (
  buss_entry_id bigint primary key not null, -- BUSS_ENTRY_ID|入账处理数据id
  entry_data_id bigint not null, -- ENTRY_DATA_ID|入账业务数据id
  entity_id bigint, -- center_id|核算单位ID
  book_code character varying, -- book_code|账套
  year_month character varying, -- year_month|会计期间
  task_code character varying, -- task_code|任务ID
  proc_id bigint, -- proc_id|数据归属节点BPL_ACT_RE_PROCDEF
  scenario_id bigint, -- scenario_id|业务场景ID
  account_id_dr bigint, -- item_id_dr|借方科目ID
  account_id_cr bigint, -- item_id_cr|贷方科目ID
  posting_type_code character varying, -- post_type|入账类型
  scenario_type character varying, -- scenario_type|场景类型AP-非现金流,CF-现金流
  voucher_id bigint, -- voucher_id|凭证号码
  change_time bigint, -- change_time|冲正次数
  currency_cu_code character varying, -- currency_cu|币种_本位币 
  currency_code character varying, -- currency|币种
  exchange_rate numeric(32,8), -- exch_rate|兑换率
  entry_state bigint, -- entry_state|入账状态
  entry_msg character varying, -- entry_msg|入账结果信息
  create_time timestamp(6) without time zone, -- create_time|创建时间
  creator_id bigint, -- creator_id|创建人ID
  update_time timestamp(6) without time zone, -- update_time|最后更新时间
  updator_id bigint, -- updator_id|最后更新人ID
  bu_voucher_no character varying, -- B_VOUCHER_NO|业务凭证号
  entry_date timestamp(6) without time zone, -- 入账时点
  scen_serial_no bigint, -- 场景版本序号
  account_entry_code character varying, -- 借贷方向
  account_id bigint, -- 科目id
  amount numeric(32,8) -- 待入账金额
)
partition by LIST (entity_id);

comment on table accuser.acc_buss_entry_data_success_part is 'TRIAL';
comment on column accuser.acc_buss_entry_data_success_part.buss_entry_id is 'BUSS_ENTRY_ID|入账处理数据id';
comment on column accuser.acc_buss_entry_data_success_part.entry_data_id is 'ENTRY_DATA_ID|入账业务数据id';
comment on column accuser.acc_buss_entry_data_success_part.entity_id is 'center_id|核算单位ID';
comment on column accuser.acc_buss_entry_data_success_part.book_code is 'book_code|账套';
comment on column accuser.acc_buss_entry_data_success_part.year_month is 'year_month|会计期间';
comment on column accuser.acc_buss_entry_data_success_part.task_code is 'task_code|任务ID';
comment on column accuser.acc_buss_entry_data_success_part.proc_id is 'proc_id|数据归属节点BPL_ACT_RE_PROCDEF';
comment on column accuser.acc_buss_entry_data_success_part.scenario_id is 'scenario_id|业务场景ID';
comment on column accuser.acc_buss_entry_data_success_part.account_id_dr is 'item_id_dr|借方科目ID';
comment on column accuser.acc_buss_entry_data_success_part.account_id_cr is 'item_id_cr|贷方科目ID';
comment on column accuser.acc_buss_entry_data_success_part.posting_type_code is 'post_type|入账类型';
comment on column accuser.acc_buss_entry_data_success_part.scenario_type is 'scenario_type|场景类型AP-非现金流,CF-现金流';
comment on column accuser.acc_buss_entry_data_success_part.voucher_id is 'voucher_id|凭证号码';
comment on column accuser.acc_buss_entry_data_success_part.change_time is 'change_time|冲正次数';
comment on column accuser.acc_buss_entry_data_success_part.currency_cu_code is 'currency_cu|币种_本位币 ';
comment on column accuser.acc_buss_entry_data_success_part.currency_code is 'currency|币种';
comment on column accuser.acc_buss_entry_data_success_part.exchange_rate is 'exch_rate|兑换率';
comment on column accuser.acc_buss_entry_data_success_part.entry_state is 'entry_state|入账状态';
comment on column accuser.acc_buss_entry_data_success_part.entry_msg is 'entry_msg|入账结果信息';
comment on column accuser.acc_buss_entry_data_success_part.create_time is 'create_time|创建时间';
comment on column accuser.acc_buss_entry_data_success_part.creator_id is 'creator_id|创建人ID';
comment on column accuser.acc_buss_entry_data_success_part.update_time is 'update_time|最后更新时间';
comment on column accuser.acc_buss_entry_data_success_part.updator_id is 'updator_id|最后更新人ID';
comment on column accuser.acc_buss_entry_data_success_part.bu_voucher_no is 'B_VOUCHER_NO|业务凭证号';
comment on column accuser.acc_buss_entry_data_success_part.entry_date is '入账时点';
comment on column accuser.acc_buss_entry_data_success_part.scen_serial_no is '场景版本序号';
comment on column accuser.acc_buss_entry_data_success_part.account_entry_code is '借贷方向';
comment on column accuser.acc_buss_entry_data_success_part.account_id is '科目id';
comment on column accuser.acc_buss_entry_data_success_part.amount is '待入账金额';*/

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_ledger_balance');
create table accuser.acc_buss_ledger_balance (
  ledger_balance_id bigint primary key not null,
  entity_id bigint not null,
  book_code character varying(8) not null,
  year_month character varying(6) not null,
  root_account_id bigint,
  account_id bigint,
  currency_code character varying(3),
  currency_cu_code character varying(3),
  debit_amount numeric(32,8) not null,
  debit_amount_cu numeric(32,8) not null,
  credit_amount numeric(32,8) not null,
  credit_amount_cu numeric(32,8) not null,
  debit_amount_quarter numeric(32,8) not null,
  debit_amount_quarter_cu numeric(32,8) not null,
  credit_amount_quarter numeric(32,8) not null,
  credit_amount_quarter_cu numeric(32,8) not null,
  debit_amount_year numeric(32,8) not null,
  debit_amount_year_cu numeric(32,8) not null,
  credit_amount_year numeric(32,8) not null,
  credit_amount_year_cu numeric(32,8) not null,
  opening_balance numeric(32,8) not null,
  closing_balance numeric(32,8) not null,
  opening_balance_cu numeric(32,8) not null,
  closing_balance_cu numeric(32,8) not null,
  create_time timestamp without time zone,
  creator_id bigint,
  update_time timestamp without time zone,
  updator_id bigint,
  serial_no integer,
  center_code character varying(32) -- 核算单位
);
comment on column accuser.acc_buss_ledger_balance.center_code is '核算单位';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_ledger_balancehis');
create table accuser.acc_buss_ledger_balancehis (
  ledger_balance_his_id bigint primary key not null,
  ledger_balance_id bigint not null,
  serial_no integer not null,
  entity_id bigint not null,
  book_code character varying(8) not null,
  year_month character varying(6) not null,
  root_account_id bigint,
  account_id bigint,
  currency_code character varying(3),
  currency_cu_code character varying(3),
  debit_amount numeric(32,8) not null,
  debit_amount_cu numeric(32,8) not null,
  credit_amount numeric(32,8) not null,
  credit_amount_cu numeric(32,8) not null,
  debit_amount_quarter numeric(32,8) not null,
  debit_amount_quarter_cu numeric(32,8) not null,
  credit_amount_quarter numeric(32,8) not null,
  credit_amount_quarter_cu numeric(32,8) not null,
  debit_amount_year numeric(32,8) not null,
  debit_amount_year_cu numeric(32,8) not null,
  credit_amount_year numeric(32,8) not null,
  credit_amount_year_cu numeric(32,8) not null,
  opening_balance numeric(32,8) not null,
  closing_balance numeric(32,8) not null,
  opening_balance_cu numeric(32,8) not null,
  closing_balance_cu numeric(32,8) not null,
  create_time timestamp without time zone,
  creator_id bigint,
  update_time timestamp without time zone,
  updator_id bigint,
  center_code character varying(32) -- 核算单位
);
comment on column accuser.acc_buss_ledger_balancehis.center_code is '核算单位';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_manual_voucher');
create table accuser.acc_buss_manual_voucher (
  voucher_id bigint primary key not null, -- VOUCHER_id|主键
  entity_id bigint not null, -- ENTITY_ID|核算单位Id
  book_code character varying(8) not null, -- Book_Code|账套编号
  posting_type_code character varying(3) not null, -- posting_type_code|入账类型(bpl_code .codetype= 'Entrytype' 03预收/预付 02实收/实付 01应收/应付/07手工凭证
  proc_id bigint, -- PROC_ID|数据归属节点  BPL_ACT_RE_PROCDEF
  voucher_no character varying(32) not null, -- Voucher_code|凭证编号
  year_month character varying(6) not null, -- Year_Month|会计期间
  effective_date date not null, -- Effective_Date|凭证生效日期-记账日期
  state character varying(1) not null, -- state|凭证状态 1 正常  2 被冲   3 冲销  
  remark character varying(2000), -- remark|备注
  valid_is character varying(1) not null, -- Valind_is|有效标志
  audit_state character(1), -- auditState|审核状态
  checked_id bigint, -- checkedId|审核人ID
  checked_time timestamp(6) without time zone, -- checkedTime|审核时间
  checked_msg character varying(500), -- checkedMsg|审核信息
  create_time timestamp(6) without time zone, -- Create_Time|创建时间
  creator_id bigint, -- creator_id|创建人
  update_time timestamp(6) without time zone, -- Update_time|最后变更时间
  updator_id bigint, -- updator_id|最后变更经手人
  ext_voucher_no character varying(32), -- ext_voucher_no|现行准则凭证号
  center_code character varying(32) -- 核算单位
);
comment on table accuser.acc_buss_manual_voucher is '手工录入凭证主表';
comment on column accuser.acc_buss_manual_voucher.voucher_id is 'VOUCHER_id|主键';
comment on column accuser.acc_buss_manual_voucher.entity_id is 'ENTITY_ID|核算单位Id';
comment on column accuser.acc_buss_manual_voucher.book_code is 'Book_Code|账套编号';
comment on column accuser.acc_buss_manual_voucher.posting_type_code is 'posting_type_code|入账类型(bpl_code .codetype= ''Entrytype'' 03预收/预付 02实收/实付 01应收/应付/07手工凭证';
comment on column accuser.acc_buss_manual_voucher.proc_id is 'PROC_ID|数据归属节点  BPL_ACT_RE_PROCDEF';
comment on column accuser.acc_buss_manual_voucher.voucher_no is 'Voucher_code|凭证编号';
comment on column accuser.acc_buss_manual_voucher.year_month is 'Year_Month|会计期间';
comment on column accuser.acc_buss_manual_voucher.effective_date is 'Effective_Date|凭证生效日期-记账日期';
comment on column accuser.acc_buss_manual_voucher.state is 'state|凭证状态 1 正常  2 被冲   3 冲销  ';
comment on column accuser.acc_buss_manual_voucher.remark is 'remark|备注';
comment on column accuser.acc_buss_manual_voucher.valid_is is 'Valind_is|有效标志';
comment on column accuser.acc_buss_manual_voucher.audit_state is 'auditState|审核状态';
comment on column accuser.acc_buss_manual_voucher.checked_id is 'checkedId|审核人ID';
comment on column accuser.acc_buss_manual_voucher.checked_time is 'checkedTime|审核时间';
comment on column accuser.acc_buss_manual_voucher.checked_msg is 'checkedMsg|审核信息';
comment on column accuser.acc_buss_manual_voucher.create_time is 'Create_Time|创建时间';
comment on column accuser.acc_buss_manual_voucher.creator_id is 'creator_id|创建人';
comment on column accuser.acc_buss_manual_voucher.update_time is 'Update_time|最后变更时间';
comment on column accuser.acc_buss_manual_voucher.updator_id is 'updator_id|最后变更经手人';
comment on column accuser.acc_buss_manual_voucher.ext_voucher_no is 'ext_voucher_no|现行准则凭证号';
comment on column accuser.acc_buss_manual_voucher.center_code is '核算单位';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_manual_voucher_detail');
create table accuser.acc_buss_manual_voucher_detail (
  voucher_dtl_id bigint primary key not null, -- VOUCHER_DTL_ID|主键
  voucher_id bigint, -- voucher_id|凭证编号
  account_id bigint, -- account_id|科目编码
  currency_code character varying(3) not null, -- currency|币种
  currency_cu_code character varying(3) not null, -- CURRENCY_CU|币种_本位币 
  account_entry_code character(1) not null, -- account_entry_code|借贷标识 D-借 C-贷
  amount numeric(32,8) not null, -- amount|原币发生额
  amount_cu numeric(32,8) not null, -- amount_CU|本位币发生额
  exchange_rate numeric(32,8) not null, -- exchange_rate|兑换率
  remark character varying(400), -- remark|备注
  article1 character varying(60), -- Article1_Code|专项编码值1
  article2 character varying(60), -- Article2_Code|专项编码值2
  article3 character varying(60), -- Article3_Code|专项编码值3
  article4 character varying(60), -- Article4_Code|专项编码值4
  article5 character varying(60), -- Article5_Code|专项编码值5
  article6 character varying(60), -- Article6_Code|专项编码值6
  article7 character varying(60), -- Article7_Code|专项编码值7
  article8 character varying(60), -- Article8_Code|专项编码值8
  article9 character varying(60), -- Article9_Code|专项编码值9
  article10 character varying(60), -- Article10_Code|专项编码值10
  article11 character varying(60), -- Article11_Code|专项编码值11
  article12 character varying(60), -- Article12_Code|专项编码值12
  article13 character varying(60), -- Article13_Code|专项编码值13
  article14 character varying(60), -- Article14_Code|专项编码值14
  article15 character varying(60), -- Article15_Code|专项编码值15
  article16 character varying(60), -- Article16_Code|专项编码值16
  article17 character varying(60), -- Article17_Code|专项编码值17
  create_time timestamp(6) without time zone, -- 创建时间
  creator_id bigint, -- 创建人ID
  update_time timestamp(6) without time zone, -- 修改时间
  updator_id bigint, -- 修改人ID
  entry_data_id bigint, -- BUSS_ENTRY_ID|入账处理数据id
  article character varying(1000), -- article_code|专项汇总编码值
  center_code character varying(32) -- 核算单位
);
comment on table accuser.acc_buss_manual_voucher_detail is '手工录入凭证明细表';
comment on column accuser.acc_buss_manual_voucher_detail.voucher_dtl_id is 'VOUCHER_DTL_ID|主键';
comment on column accuser.acc_buss_manual_voucher_detail.voucher_id is 'voucher_id|凭证编号';
comment on column accuser.acc_buss_manual_voucher_detail.account_id is 'account_id|科目编码';
comment on column accuser.acc_buss_manual_voucher_detail.currency_code is 'currency|币种';
comment on column accuser.acc_buss_manual_voucher_detail.currency_cu_code is 'CURRENCY_CU|币种_本位币 ';
comment on column accuser.acc_buss_manual_voucher_detail.account_entry_code is 'account_entry_code|借贷标识 D-借 C-贷';
comment on column accuser.acc_buss_manual_voucher_detail.amount is 'amount|原币发生额';
comment on column accuser.acc_buss_manual_voucher_detail.amount_cu is 'amount_CU|本位币发生额';
comment on column accuser.acc_buss_manual_voucher_detail.exchange_rate is 'exchange_rate|兑换率';
comment on column accuser.acc_buss_manual_voucher_detail.remark is 'remark|备注';
comment on column accuser.acc_buss_manual_voucher_detail.article1 is 'Article1_Code|专项编码值1';
comment on column accuser.acc_buss_manual_voucher_detail.article2 is 'Article2_Code|专项编码值2';
comment on column accuser.acc_buss_manual_voucher_detail.article3 is 'Article3_Code|专项编码值3';
comment on column accuser.acc_buss_manual_voucher_detail.article4 is 'Article4_Code|专项编码值4';
comment on column accuser.acc_buss_manual_voucher_detail.article5 is 'Article5_Code|专项编码值5';
comment on column accuser.acc_buss_manual_voucher_detail.article6 is 'Article6_Code|专项编码值6';
comment on column accuser.acc_buss_manual_voucher_detail.article7 is 'Article7_Code|专项编码值7';
comment on column accuser.acc_buss_manual_voucher_detail.article8 is 'Article8_Code|专项编码值8';
comment on column accuser.acc_buss_manual_voucher_detail.article9 is 'Article9_Code|专项编码值9';
comment on column accuser.acc_buss_manual_voucher_detail.article10 is 'Article10_Code|专项编码值10';
comment on column accuser.acc_buss_manual_voucher_detail.article11 is 'Article11_Code|专项编码值11';
comment on column accuser.acc_buss_manual_voucher_detail.article12 is 'Article12_Code|专项编码值12';
comment on column accuser.acc_buss_manual_voucher_detail.article13 is 'Article13_Code|专项编码值13';
comment on column accuser.acc_buss_manual_voucher_detail.article14 is 'Article14_Code|专项编码值14';
comment on column accuser.acc_buss_manual_voucher_detail.article15 is 'Article15_Code|专项编码值15';
comment on column accuser.acc_buss_manual_voucher_detail.article16 is 'Article16_Code|专项编码值16';
comment on column accuser.acc_buss_manual_voucher_detail.article17 is 'Article17_Code|专项编码值17';
comment on column accuser.acc_buss_manual_voucher_detail.create_time is '创建时间';
comment on column accuser.acc_buss_manual_voucher_detail.creator_id is '创建人ID';
comment on column accuser.acc_buss_manual_voucher_detail.update_time is '修改时间';
comment on column accuser.acc_buss_manual_voucher_detail.updator_id is '修改人ID';
comment on column accuser.acc_buss_manual_voucher_detail.entry_data_id is 'BUSS_ENTRY_ID|入账处理数据id';
comment on column accuser.acc_buss_manual_voucher_detail.article is 'article_code|专项汇总编码值';
comment on column accuser.acc_buss_manual_voucher_detail.center_code is '核算单位';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_multi_reconrst');
create table accuser.acc_buss_multi_reconrst (
  recon_rst_id bigint primary key not null,
  serial_no integer,
  multi_scenario_id bigint,
  recon_id bigint,
  entity_id bigint,
  book_code character varying(8),
  year_month character varying(6),
  account_code_base character varying(1000),
  book_code_other character varying(8),
  account_code_other character varying(1000),
  account_balance_base character varying(1000),
  account_balance_other character varying(1000),
  reconcil_result character varying(1),
  creator_id bigint,
  create_time timestamp without time zone,
  updator_id bigint,
  update_time timestamp without time zone,
  center_code character varying(60) -- 核算单位
);

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_multi_reconrstdtl');
create table accuser.acc_buss_multi_reconrstdtl (
  recon_rst_dtl_id bigint primary key not null,
  recon_rst_id bigint not null,
  source_type smallint,
  recon_type character varying(32),
  business_id bigint,
  business_no character varying(32),
  last_accounting_date character varying(32),
  risk_code character varying(32),
  dept_id bigint,
  account_id bigint,
  account_entry_code character varying(1),
  currency_code character varying(3),
  currency_cu_code character varying(3),
  exchange_rate numeric(32,8),
  amount numeric(32,8),
  amount_cu numeric(32,8),
  creator_id bigint,
  create_time timestamp without time zone,
  updator_id bigint,
  update_time timestamp without time zone,
  center_code character varying(60) -- 核算单位
);

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_multi_reconrstdtlhis');
create table accuser.acc_buss_multi_reconrstdtlhis (
  recon_rst_dtl_his_id bigint primary key not null,
  recon_rst_dtl_id bigint not null,
  recon_rst_id bigint not null,
  source_type smallint,
  recon_type character varying(32),
  business_id bigint,
  business_no character varying(32),
  last_accounting_date character varying(32),
  risk_code character varying(32),
  dept_id bigint,
  account_id bigint,
  account_entry_code character varying(1),
  currency_code character varying(3),
  currency_cu_code character varying(3),
  exchange_rate numeric(32,8),
  amount numeric(32,8),
  amount_cu numeric(32,8),
  creator_id bigint,
  create_time timestamp without time zone,
  updator_id bigint,
  update_time timestamp without time zone,
  center_code character varying(60) -- 核算单位
);

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_multi_reconrsthis');
create table accuser.acc_buss_multi_reconrsthis (
  recon_rst_his_id bigint primary key not null,
  recon_rst_id bigint not null,
  serial_no integer,
  multi_scenario_id bigint,
  recon_id bigint,
  entity_id bigint,
  book_code character varying(8),
  year_month character varying(6),
  account_code_base character varying(1000),
  book_code_other character varying(8),
  account_code_other character varying(1000),
  account_balance_base character varying(1000),
  account_balance_other character varying(1000),
  reconcil_result character varying(1),
  creator_id bigint,
  create_time timestamp without time zone,
  updator_id bigint,
  update_time timestamp without time zone
);

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_push_detail');
create table accuser.acc_buss_push_detail (
  id numeric(11,0) primary key not null, -- 主键
  record_id numeric(11,0), -- record表id
  task_code character varying(64), -- 任务编码
  status character varying(2), -- 状态（1-成功，2-失败，3-未知：接口未返回，无法确切的知道是否推送成功，如果重推，此部分数据应该重新推送）
  account_code character varying(128), -- 科目编码（暂定）
  journal_line_number character varying(124), -- 凭证行号（暂定）
  center_code character varying(60) -- 核算单位
);
comment on table accuser.acc_buss_push_detail is '总账推送记录详情表';
comment on column accuser.acc_buss_push_detail.id is '主键';
comment on column accuser.acc_buss_push_detail.record_id is 'record表id';
comment on column accuser.acc_buss_push_detail.task_code is '任务编码';
comment on column accuser.acc_buss_push_detail.status is '状态（1-成功，2-失败，3-未知：接口未返回，无法确切的知道是否推送成功，如果重推，此部分数据应该重新推送）';
comment on column accuser.acc_buss_push_detail.account_code is '科目编码（暂定）';
comment on column accuser.acc_buss_push_detail.journal_line_number is '凭证行号（暂定）';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_push_ledger_record');
create table accuser.acc_buss_push_ledger_record (
  id numeric(11,0) primary key not null,
  task_code character varying(64), -- 任务号
  status character varying(4), -- 状态（1-推送中，2-成功，3-失败，4部分成功）
  push_type character varying(4), -- 推送类型（1-全量，2-指定task_code，3-指定会计年月，4-单凭证数据推送，5-反向冲销）
  error_msg character varying(3500), -- 失败原因（sun系统对接不会在此处，对方会返回具体条数对应的err_msg）
  task_count numeric(11,0), -- 本次任务号对应的推送数量
  year_month character varying(6), -- 本次任务推送的对应的会计年月（全量推送会根据会计年月生成多个任务号）
  success_count numeric(11,0), -- 成功条数
  fail_count numeric(11,0), -- 失败条数
  create_time timestamp without time zone, -- 创建时间
  update_time timestamp without time zone, -- 更新时间
  entity_id numeric(11,0), -- 业务单位id
  book_code character varying(12), -- 账套编码
  center_code character varying(60) -- 核算单位
);
comment on table accuser.acc_buss_push_ledger_record is '总账推送记录表';
comment on column accuser.acc_buss_push_ledger_record.task_code is '任务号';
comment on column accuser.acc_buss_push_ledger_record.status is '状态（1-推送中，2-成功，3-失败，4部分成功）';
comment on column accuser.acc_buss_push_ledger_record.push_type is '推送类型（1-全量，2-指定task_code，3-指定会计年月，4-单凭证数据推送，5-反向冲销）';
comment on column accuser.acc_buss_push_ledger_record.error_msg is '失败原因（sun系统对接不会在此处，对方会返回具体条数对应的err_msg）';
comment on column accuser.acc_buss_push_ledger_record.task_count is '本次任务号对应的推送数量';
comment on column accuser.acc_buss_push_ledger_record.year_month is '本次任务推送的对应的会计年月（全量推送会根据会计年月生成多个任务号）';
comment on column accuser.acc_buss_push_ledger_record.success_count is '成功条数';
comment on column accuser.acc_buss_push_ledger_record.fail_count is '失败条数';
comment on column accuser.acc_buss_push_ledger_record.create_time is '创建时间';
comment on column accuser.acc_buss_push_ledger_record.update_time is '更新时间';
comment on column accuser.acc_buss_push_ledger_record.entity_id is '业务单位id';
comment on column accuser.acc_buss_push_ledger_record.book_code is '账套编码';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_push_voucher');
create table accuser.acc_buss_push_voucher (
  voucher_id numeric(11,0) primary key not null, -- VOUCHER_id|主键
  entity_id numeric(11,0) not null, -- Center_Id|核算单位Id
  book_code character varying(8) not null, -- Book_Code|账套编号
  posting_type_code character varying(3) not null, -- Post_Type|入账类型(bpl_code .codetype= 'Entrytype' 03预收;预付 02实收;实付 01应收;应付
  proc_id numeric(11,0), -- PROC_ID|数据归属节点  BPL_ACT_RE_PROCDEF
  voucher_no character varying(32) not null, -- Voucher_code|凭证编号
  year_month character varying(6) not null, -- Year_Month|会计期间
  effective_date timestamp without time zone not null, -- Effective_Date|凭证生效日期-记账日期
  state character varying(1) not null, -- state|凭证状态 1 正常  2 被冲   3 冲销  
  remark character varying(2000), -- remark|备注
  valid_is character varying(1) not null, -- Valind_is|有效标志
  create_time timestamp without time zone, -- Create_Time|创建时间
  creator_id numeric(11,0), -- creator_id|创建人
  update_time timestamp without time zone, -- Update_time|最后变更时间
  updator_id numeric(11,0), -- updator_id|最后变更经手人
  ext_voucher_no character varying(32), -- ext_voucher_no|现行准则凭证号
  audit_state character(1), -- auditState|审核状态
  task_code character varying(32), -- task_code|任务号
  center_code character varying(60) -- 核算单位
);
create index idx_acc_buss_push_voucher on acc_buss_push_voucher using btree (entity_id, book_code, year_month);
create index idx_acc_buss_push_voucher_task_code on acc_buss_push_voucher using btree (task_code);
create index idx_acc_buss_push_voucher_main on acc_buss_push_voucher using btree (entity_id, year_month);
comment on table accuser.acc_buss_push_voucher is '过渡期手工调整凭证';
comment on column accuser.acc_buss_push_voucher.voucher_id is 'VOUCHER_id|主键';
comment on column accuser.acc_buss_push_voucher.entity_id is 'Center_Id|核算单位Id';
comment on column accuser.acc_buss_push_voucher.book_code is 'Book_Code|账套编号';
comment on column accuser.acc_buss_push_voucher.posting_type_code is 'Post_Type|入账类型(bpl_code .codetype= ''Entrytype'' 03预收;预付 02实收;实付 01应收;应付';
comment on column accuser.acc_buss_push_voucher.proc_id is 'PROC_ID|数据归属节点  BPL_ACT_RE_PROCDEF';
comment on column accuser.acc_buss_push_voucher.voucher_no is 'Voucher_code|凭证编号';
comment on column accuser.acc_buss_push_voucher.year_month is 'Year_Month|会计期间';
comment on column accuser.acc_buss_push_voucher.effective_date is 'Effective_Date|凭证生效日期-记账日期';
comment on column accuser.acc_buss_push_voucher.state is 'state|凭证状态 1 正常  2 被冲   3 冲销  ';
comment on column accuser.acc_buss_push_voucher.remark is 'remark|备注';
comment on column accuser.acc_buss_push_voucher.valid_is is 'Valind_is|有效标志';
comment on column accuser.acc_buss_push_voucher.create_time is 'Create_Time|创建时间';
comment on column accuser.acc_buss_push_voucher.creator_id is 'creator_id|创建人';
comment on column accuser.acc_buss_push_voucher.update_time is 'Update_time|最后变更时间';
comment on column accuser.acc_buss_push_voucher.updator_id is 'updator_id|最后变更经手人';
comment on column accuser.acc_buss_push_voucher.ext_voucher_no is 'ext_voucher_no|现行准则凭证号';
comment on column accuser.acc_buss_push_voucher.audit_state is 'auditState|审核状态';
comment on column accuser.acc_buss_push_voucher.task_code is 'task_code|任务号';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_push_voucher_detail');
create table accuser.acc_buss_push_voucher_detail (
  voucher_dtl_id numeric(11,0) primary key not null, -- VOUCHER_DTL_ID|主键
  voucher_id numeric(11,0), -- voucher_id|凭证编号
  account_id numeric(11,0), -- item_id|科目编码
  currency_code character varying(3) not null, -- currency|币种
  currency_cu_code character varying(3) not null, -- CURRENCY_CU|币种_本位币 
  account_entry_code character(1) not null, -- drcr_type|借贷标识 D-借 C-贷
  amount numeric(32,8) not null, -- aCCOUNT|原币发生额
  amount_cu numeric(32,8) not null, -- ACCOUNT_CU|本位币发生额
  exchange_rate numeric(32,8) not null, -- Exch_Rate|兑换率
  remark character varying(400), -- remark|备注
  article1 character varying(60), -- Article1_Code|专项编码值1
  article2 character varying(60), -- Article2_Code|专项编码值2
  article3 character varying(60), -- Article3_Code|专项编码值3
  article4 character varying(60), -- Article4_Code|专项编码值4
  article5 character varying(60), -- Article5_Code|专项编码值5
  article6 character varying(60), -- Article6_Code|专项编码值6
  article7 character varying(60), -- Article7_Code|专项编码值7
  article8 character varying(60), -- Article8_Code|专项编码值8
  article9 character varying(60), -- Article9_Code|专项编码值9
  article10 character varying(60), -- Article10_Code|专项编码值10
  article11 character varying(60), -- Article11_Code|专项编码值11
  article12 character varying(60), -- Article12_Code|专项编码值12
  article13 character varying(60), -- Article13_Code|专项编码值13
  article14 character varying(60), -- Article14_Code|专项编码值14
  article15 character varying(60), -- Article15_Code|专项编码值15
  article16 character varying(60), -- Article16_Code|专项编码值16
  article17 character varying(60), -- Article17_Code|专项编码值17
  create_time timestamp without time zone, -- TRIAL
  creator_id numeric(11,0), -- TRIAL
  update_time timestamp without time zone, -- TRIAL
  updator_id numeric(11,0), -- TRIAL
  entry_data_id numeric(11,0), -- BUSS_ENTRY_ID|入账处理数据id
  article character varying(1000), -- article_code|专项汇总编码值
  entity_id numeric(11,0), -- 业务单位id
  year_month character varying(6), -- 会计年月
  book_code character varying(8), -- 账套编码
  proc_id numeric(11,0), -- 流程节点id
  center_code character varying(60) -- 核算单位
);
create index idx_acc_buss_push_voucher_detail_id on acc_buss_push_voucher_detail using btree (voucher_id);
comment on table accuser.acc_buss_push_voucher_detail is '凭证明细字表';
comment on column accuser.acc_buss_push_voucher_detail.voucher_dtl_id is 'VOUCHER_DTL_ID|主键';
comment on column accuser.acc_buss_push_voucher_detail.voucher_id is 'voucher_id|凭证编号';
comment on column accuser.acc_buss_push_voucher_detail.account_id is 'item_id|科目编码';
comment on column accuser.acc_buss_push_voucher_detail.currency_code is 'currency|币种';
comment on column accuser.acc_buss_push_voucher_detail.currency_cu_code is 'CURRENCY_CU|币种_本位币 ';
comment on column accuser.acc_buss_push_voucher_detail.account_entry_code is 'drcr_type|借贷标识 D-借 C-贷';
comment on column accuser.acc_buss_push_voucher_detail.amount is 'aCCOUNT|原币发生额';
comment on column accuser.acc_buss_push_voucher_detail.amount_cu is 'ACCOUNT_CU|本位币发生额';
comment on column accuser.acc_buss_push_voucher_detail.exchange_rate is 'Exch_Rate|兑换率';
comment on column accuser.acc_buss_push_voucher_detail.remark is 'remark|备注';
comment on column accuser.acc_buss_push_voucher_detail.article1 is 'Article1_Code|专项编码值1';
comment on column accuser.acc_buss_push_voucher_detail.article2 is 'Article2_Code|专项编码值2';
comment on column accuser.acc_buss_push_voucher_detail.article3 is 'Article3_Code|专项编码值3';
comment on column accuser.acc_buss_push_voucher_detail.article4 is 'Article4_Code|专项编码值4';
comment on column accuser.acc_buss_push_voucher_detail.article5 is 'Article5_Code|专项编码值5';
comment on column accuser.acc_buss_push_voucher_detail.article6 is 'Article6_Code|专项编码值6';
comment on column accuser.acc_buss_push_voucher_detail.article7 is 'Article7_Code|专项编码值7';
comment on column accuser.acc_buss_push_voucher_detail.article8 is 'Article8_Code|专项编码值8';
comment on column accuser.acc_buss_push_voucher_detail.article9 is 'Article9_Code|专项编码值9';
comment on column accuser.acc_buss_push_voucher_detail.article10 is 'Article10_Code|专项编码值10';
comment on column accuser.acc_buss_push_voucher_detail.article11 is 'Article11_Code|专项编码值11';
comment on column accuser.acc_buss_push_voucher_detail.article12 is 'Article12_Code|专项编码值12';
comment on column accuser.acc_buss_push_voucher_detail.article13 is 'Article13_Code|专项编码值13';
comment on column accuser.acc_buss_push_voucher_detail.article14 is 'Article14_Code|专项编码值14';
comment on column accuser.acc_buss_push_voucher_detail.article15 is 'Article15_Code|专项编码值15';
comment on column accuser.acc_buss_push_voucher_detail.article16 is 'Article16_Code|专项编码值16';
comment on column accuser.acc_buss_push_voucher_detail.article17 is 'Article17_Code|专项编码值17';
comment on column accuser.acc_buss_push_voucher_detail.create_time is 'TRIAL';
comment on column accuser.acc_buss_push_voucher_detail.creator_id is 'TRIAL';
comment on column accuser.acc_buss_push_voucher_detail.update_time is 'TRIAL';
comment on column accuser.acc_buss_push_voucher_detail.updator_id is 'TRIAL';
comment on column accuser.acc_buss_push_voucher_detail.entry_data_id is 'BUSS_ENTRY_ID|入账处理数据id';
comment on column accuser.acc_buss_push_voucher_detail.article is 'article_code|专项汇总编码值';
comment on column accuser.acc_buss_push_voucher_detail.entity_id is '业务单位id';
comment on column accuser.acc_buss_push_voucher_detail.year_month is '会计年月';
comment on column accuser.acc_buss_push_voucher_detail.book_code is '账套编码';
comment on column accuser.acc_buss_push_voucher_detail.proc_id is '流程节点id';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_voucher');
create table accuser.acc_buss_voucher (
  voucher_id bigint not null,
  entity_id bigint not null,
  book_code character varying(8) not null,
  posting_type_code character varying(3) not null,
  proc_id bigint not null,
  voucher_no character varying(32) not null,
  year_month character varying(6) not null,
  effective_date timestamp(6) without time zone not null,
  state character varying(1) not null,
  remark character varying(2000),
  valid_is character varying(1) not null,
  create_time timestamp(6) without time zone,
  creator_id bigint,
  update_time timestamp(6) without time zone,
  updator_id bigint,
  ext_voucher_no character varying(32),
  audit_state character(1), -- auditState|审核状态
  task_code character varying(32), -- task_code|任务号
  center_code character varying(60) -- 核算单位
)
partition by LIST (entity_id);

create index idx_acc_buss_voucher_ext_voucher_no on acc_buss_voucher using btree (ext_voucher_no);
create index idx_acc_buss_voucher_task_code_proc_id on acc_buss_voucher using btree (task_code, proc_id);
comment on column accuser.acc_buss_voucher.audit_state is 'auditState|审核状态';
comment on column accuser.acc_buss_voucher.task_code is 'task_code|任务号';
comment on column accuser.acc_buss_voucher.center_code is '核算单位';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_voucher_detail');
create table accuser.acc_buss_voucher_detail (
  voucher_dtl_id bigint not null,
  voucher_id bigint,
  account_id bigint,
  entity_id bigint,
  year_month character varying(6) not null,
  book_code character varying(8),
  proc_id bigint not null,
  currency_code character varying(3) not null,
  currency_cu_code character varying(3) not null,
  account_entry_code character(1) not null,
  amount numeric(32,8) not null,
  amount_cu numeric(32,8) not null,
  exchange_rate numeric(32,8) not null,
  remark character varying(400),
  article1 character varying(60),
  article2 character varying(60),
  article3 character varying(60),
  article4 character varying(60),
  article5 character varying(60),
  article6 character varying(60),
  article7 character varying(60),
  article8 character varying(60),
  article9 character varying(60),
  article10 character varying(60),
  article11 character varying(60),
  article12 character varying(60),
  article13 character varying(60),
  article14 character varying(60),
  article15 character varying(60),
  article16 character varying(60),
  article17 character varying(60),
  create_time timestamp(6) without time zone,
  creator_id bigint,
  update_time timestamp(6) without time zone,
  updator_id bigint,
  entry_data_id bigint,
  article character varying(1000),
  center_code character varying(60), -- 核算单位
  article18 character varying(60),
  article19 character varying(60),
  article20 character varying(60),
  article21 character varying(60),
  article22 character varying(60),
  article23 character varying(60)
)
partition by LIST (entity_id);

create index idx_acc_buss_voucher_detail_id on acc_buss_voucher_detail using btree (voucher_id);
comment on column accuser.acc_buss_voucher_detail.center_code is '核算单位';

call accuser.acc_pack_commonutils_proc_drop_table('acc_buss_voucherhis');
create table accuser.acc_buss_voucherhis (
  his_id bigint primary key not null,
  entity_id bigint,
  book_code character varying(8),
  year_month character varying(6),
  old_voucher_no character varying(32),
  re_voucher_no character varying(32),
  voucher_no character varying(32),
  oper_message character varying(400),
  create_time timestamp without time zone,
  creator_id bigint,
  center_code character varying(60) -- 核算单位
);

call accuser.acc_pack_commonutils_proc_drop_table('acc_duct_contra_data');
create table accuser.acc_duct_contra_data (
  duct_entry_id bigint not null,
  entity_id bigint,
  book_code character varying(8),
  year_month character varying(6),
  task_code character varying(32),
  proc_id bigint,
  scenario_id bigint,
  account_id_dr bigint,
  account_id_cr bigint,
  posting_type_code character varying(10),
  scenario_type character varying(2),
  voucher_id bigint,
  corrected_times smallint,
  currency_cu_code character varying(3),
  currency_code character varying(3),
  exchange_rate numeric(32,8),
  entry_state smallint,
  entry_msg character varying(100),
  create_time timestamp without time zone,
  creator_id bigint,
  update_time timestamp without time zone,
  updator_id bigint
);

call accuser.acc_pack_commonutils_proc_drop_table('acc_duct_entry_data_all');
create table accuser.acc_duct_entry_data_all (
  buss_entry_id bigint not null, -- BUSS_ENTRY_ID|入账处理数据id
  entry_data_id bigint not null, -- ENTRY_DATA_ID|入账业务数据id
  entity_id bigint, -- center_id|核算单位ID
  book_code character varying, -- book_code|账套
  year_month character varying, -- year_month|会计期间
  task_code character varying, -- task_code|任务ID
  proc_id bigint, -- proc_id|数据归属节点BPL_ACT_RE_PROCDEF
  posting_type_code character varying, -- post_type|入账类型
  scenario_type character varying, -- scenario_type|场景类型AP-非现金流,CF-现金流
  change_time bigint, -- change_time|冲正次数
  currency_cu_code character varying, -- currency_cu|币种_本位币 
  currency_code character varying, -- currency|币种
  exchange_rate numeric(32,8), -- exch_rate|兑换率
  entry_state bigint, -- entry_state|入账状态
  entry_msg character varying, -- entry_msg|入账结果信息
  create_time timestamp(6) without time zone, -- create_time|创建时间
  creator_id bigint, -- creator_id|创建人ID
  bu_voucher_no character varying, -- B_VOUCHER_NO|业务凭证号
  entry_date timestamp(6) without time zone, -- 入账时点
  ri_arrangement_code character varying, -- 业务来源 I分入业务 S自营业务 D不区分
  business_source_code character varying, -- 业务类型|business_Type'DB直接业务 FB临汾业务 TB合约业务 PB收付业务
  ri_direction_code character varying, -- reins_direction|再保方向 I分入，O分出
  treaty_type_code character varying, -- tty_Type|合约类型
  extend_column1 character varying, -- remark1|预收标致 ADVANCE_IND
  extend_column2 character varying, -- remark2|业务字段备注2
  extend_column3 character varying, -- remark3|业务字段备注3
  extend_column4 character varying, -- remark4|业务字段备注4
  extend_column5 character varying, -- remark5|业务字段备注5
  extend_column6 character varying, -- remark6|业务字段备注6
  extend_column7 character varying, -- remark7|业务字段备注7
  extend_column8 character varying, -- remark8|业务字段备注8
  extend_column9 character varying, -- remark9|业务字段备注9
  extend_column10 character varying, -- remark10|业务字段备注10
  extend_column11 character varying, -- remark11|业务字段备注11
  extend_column12 character varying, -- remark12|业务字段备注12
  extend_column13 character varying, -- remark13|业务字段备注13
  extend_column14 character varying, -- remark14|业务字段备注14
  evaluate_approach character varying, -- Evaluate Approach|评估方法
  expenses_type_code character varying, -- EXPENSES_TYPE_CODE|费用类型
  accident_date_time timestamp(6) without time zone, -- 出险时间
  current_previous_is character varying, -- 当年/往年标识：1-当年，2-往年，0或空-不区分
  account_entry_code character varying, -- 借贷方向
  account_id bigint, -- 科目id
  amount numeric(32,8), -- 待入账金额
  cg_dept_code character varying(10), -- 往来机构，适用于BCA
  cg_is character varying(1), -- 往来标识
  bu_voucher_date date, -- 收付日期
  risk_code character varying(32), -- risk_Code|险种编码
  policy_no character varying(60), -- policy_No|保单号
  endorse_no character varying(60), -- endorse_No|批单号
  endorse_seq_no character varying(60), -- endorse_Seq_No|批单序号
  ri_policy_no character varying(60), -- re_Policy_No|分保单号 分保单号_批单次数_再保冲销次数
  claim_no character varying(60), -- claim_no|立案号
  claim_loss_no character varying(60), -- loss_no|理赔号
  ri_statement_no character varying(200), -- bill_no|账单号码[再保的业务号码]
  est_payment_seq_no integer, -- pay_no|缴费次数
  policy_type_code character varying(2), -- policy_type|保单类型-POLICY_TYPE  N新单，E批单，C退保，R续保
  treaty_code character varying(60), -- treaty_code|合约编码
  treaty_year character varying(4), -- treaty_year|合约年份
  portfolio_no character varying(60), -- portfolio_no|合同组合编号
  icg_no character varying(60), -- icg_no|合同组编号
  input_date timestamp(6) without time zone, -- input_date|入机时间，默认sysdate
  cashflow_article character varying(32), -- cashflow_article|现金流专项
  business_id bigint, -- dm_tgt_fin_payment 的ID
  offshore_is character varying(1), -- Offshore Is|是否海外业务
  account_code character varying(32), -- 现金流科目编码
  ri_broker_code character varying(100), -- 交易对手
  reins_flag character varying(12), -- 共分保类型
  shareholder_flag character varying(24), -- 客户类型
  center_code character varying(32), -- 核算机构
  risk_class_code character varying(32), -- 险类
  ri_master_statement_no character varying(60), -- 总账单号
  clause_code character varying(32), -- 险别/条款代码
  if_documentation character varying(1), -- 是否跟单：0:不跟跟单,1:跟单
  product_id character varying(60), -- 财务产品段
  detail_id character varying(60), -- 财务明细段
  dept_ment_id character varying(60), -- 财务部门段
  channel_code character varying(60), -- 财务渠道段
  sup_product_id character varying(60), -- 财务补充产品段
  scheme_status character varying(1), -- 管理方案（0：内部管理，1：监管报送）
  vouitem character varying(60), -- I4vouitem
  budget_code character varying(60), -- 预算项目段
  scenario_id bigint -- scenario_id|业务场景ID
)
partition by LIST (entity_id);

comment on table accuser.acc_duct_entry_data_all is '入账数据过程表(全量数据)';
comment on column accuser.acc_duct_entry_data_all.buss_entry_id is 'BUSS_ENTRY_ID|入账处理数据id';
comment on column accuser.acc_duct_entry_data_all.entry_data_id is 'ENTRY_DATA_ID|入账业务数据id';
comment on column accuser.acc_duct_entry_data_all.entity_id is 'center_id|核算单位ID';
comment on column accuser.acc_duct_entry_data_all.book_code is 'book_code|账套';
comment on column accuser.acc_duct_entry_data_all.year_month is 'year_month|会计期间';
comment on column accuser.acc_duct_entry_data_all.task_code is 'task_code|任务ID';
comment on column accuser.acc_duct_entry_data_all.proc_id is 'proc_id|数据归属节点BPL_ACT_RE_PROCDEF';
comment on column accuser.acc_duct_entry_data_all.posting_type_code is 'post_type|入账类型';
comment on column accuser.acc_duct_entry_data_all.scenario_type is 'scenario_type|场景类型AP-非现金流,CF-现金流';
comment on column accuser.acc_duct_entry_data_all.change_time is 'change_time|冲正次数';
comment on column accuser.acc_duct_entry_data_all.currency_cu_code is 'currency_cu|币种_本位币 ';
comment on column accuser.acc_duct_entry_data_all.currency_code is 'currency|币种';
comment on column accuser.acc_duct_entry_data_all.exchange_rate is 'exch_rate|兑换率';
comment on column accuser.acc_duct_entry_data_all.entry_state is 'entry_state|入账状态';
comment on column accuser.acc_duct_entry_data_all.entry_msg is 'entry_msg|入账结果信息';
comment on column accuser.acc_duct_entry_data_all.create_time is 'create_time|创建时间';
comment on column accuser.acc_duct_entry_data_all.creator_id is 'creator_id|创建人ID';
comment on column accuser.acc_duct_entry_data_all.bu_voucher_no is 'B_VOUCHER_NO|业务凭证号';
comment on column accuser.acc_duct_entry_data_all.entry_date is '入账时点';
comment on column accuser.acc_duct_entry_data_all.ri_arrangement_code is '业务来源 I分入业务 S自营业务 D不区分';
comment on column accuser.acc_duct_entry_data_all.business_source_code is '业务类型|business_Type''DB直接业务 FB临汾业务 TB合约业务 PB收付业务';
comment on column accuser.acc_duct_entry_data_all.ri_direction_code is 'reins_direction|再保方向 I分入，O分出';
comment on column accuser.acc_duct_entry_data_all.treaty_type_code is 'tty_Type|合约类型';
comment on column accuser.acc_duct_entry_data_all.extend_column1 is 'remark1|预收标致 ADVANCE_IND';
comment on column accuser.acc_duct_entry_data_all.extend_column2 is 'remark2|业务字段备注2';
comment on column accuser.acc_duct_entry_data_all.extend_column3 is 'remark3|业务字段备注3';
comment on column accuser.acc_duct_entry_data_all.extend_column4 is 'remark4|业务字段备注4';
comment on column accuser.acc_duct_entry_data_all.extend_column5 is 'remark5|业务字段备注5';
comment on column accuser.acc_duct_entry_data_all.extend_column6 is 'remark6|业务字段备注6';
comment on column accuser.acc_duct_entry_data_all.extend_column7 is 'remark7|业务字段备注7';
comment on column accuser.acc_duct_entry_data_all.extend_column8 is 'remark8|业务字段备注8';
comment on column accuser.acc_duct_entry_data_all.extend_column9 is 'remark9|业务字段备注9';
comment on column accuser.acc_duct_entry_data_all.extend_column10 is 'remark10|业务字段备注10';
comment on column accuser.acc_duct_entry_data_all.extend_column11 is 'remark11|业务字段备注11';
comment on column accuser.acc_duct_entry_data_all.extend_column12 is 'remark12|业务字段备注12';
comment on column accuser.acc_duct_entry_data_all.extend_column13 is 'remark13|业务字段备注13';
comment on column accuser.acc_duct_entry_data_all.extend_column14 is 'remark14|业务字段备注14';
comment on column accuser.acc_duct_entry_data_all.evaluate_approach is 'Evaluate Approach|评估方法';
comment on column accuser.acc_duct_entry_data_all.expenses_type_code is 'EXPENSES_TYPE_CODE|费用类型';
comment on column accuser.acc_duct_entry_data_all.accident_date_time is '出险时间';
comment on column accuser.acc_duct_entry_data_all.current_previous_is is '当年/往年标识：1-当年，2-往年，0或空-不区分';
comment on column accuser.acc_duct_entry_data_all.account_entry_code is '借贷方向';
comment on column accuser.acc_duct_entry_data_all.account_id is '科目id';
comment on column accuser.acc_duct_entry_data_all.amount is '待入账金额';
comment on column accuser.acc_duct_entry_data_all.cg_dept_code is '往来机构，适用于BCA';
comment on column accuser.acc_duct_entry_data_all.cg_is is '往来标识';
comment on column accuser.acc_duct_entry_data_all.bu_voucher_date is '收付日期';
comment on column accuser.acc_duct_entry_data_all.risk_code is 'risk_Code|险种编码';
comment on column accuser.acc_duct_entry_data_all.policy_no is 'policy_No|保单号';
comment on column accuser.acc_duct_entry_data_all.endorse_no is 'endorse_No|批单号';
comment on column accuser.acc_duct_entry_data_all.endorse_seq_no is 'endorse_Seq_No|批单序号';
comment on column accuser.acc_duct_entry_data_all.ri_policy_no is 're_Policy_No|分保单号 分保单号_批单次数_再保冲销次数';
comment on column accuser.acc_duct_entry_data_all.claim_no is 'claim_no|立案号';
comment on column accuser.acc_duct_entry_data_all.claim_loss_no is 'loss_no|理赔号';
comment on column accuser.acc_duct_entry_data_all.ri_statement_no is 'bill_no|账单号码[再保的业务号码]';
comment on column accuser.acc_duct_entry_data_all.est_payment_seq_no is 'pay_no|缴费次数';
comment on column accuser.acc_duct_entry_data_all.policy_type_code is 'policy_type|保单类型-POLICY_TYPE  N新单，E批单，C退保，R续保';
comment on column accuser.acc_duct_entry_data_all.treaty_code is 'treaty_code|合约编码';
comment on column accuser.acc_duct_entry_data_all.treaty_year is 'treaty_year|合约年份';
comment on column accuser.acc_duct_entry_data_all.portfolio_no is 'portfolio_no|合同组合编号';
comment on column accuser.acc_duct_entry_data_all.icg_no is 'icg_no|合同组编号';
comment on column accuser.acc_duct_entry_data_all.input_date is 'input_date|入机时间，默认sysdate';
comment on column accuser.acc_duct_entry_data_all.cashflow_article is 'cashflow_article|现金流专项';
comment on column accuser.acc_duct_entry_data_all.business_id is 'dm_tgt_fin_payment 的ID';
comment on column accuser.acc_duct_entry_data_all.offshore_is is 'Offshore Is|是否海外业务';
comment on column accuser.acc_duct_entry_data_all.account_code is '现金流科目编码';
comment on column accuser.acc_duct_entry_data_all.ri_broker_code is '交易对手';
comment on column accuser.acc_duct_entry_data_all.reins_flag is '共分保类型';
comment on column accuser.acc_duct_entry_data_all.shareholder_flag is '客户类型';
comment on column accuser.acc_duct_entry_data_all.center_code is '核算机构';
comment on column accuser.acc_duct_entry_data_all.risk_class_code is '险类';
comment on column accuser.acc_duct_entry_data_all.ri_master_statement_no is '总账单号';
comment on column accuser.acc_duct_entry_data_all.clause_code is '险别/条款代码';
comment on column accuser.acc_duct_entry_data_all.if_documentation is '是否跟单：0:不跟跟单,1:跟单';
comment on column accuser.acc_duct_entry_data_all.product_id is '财务产品段';
comment on column accuser.acc_duct_entry_data_all.detail_id is '财务明细段';
comment on column accuser.acc_duct_entry_data_all.dept_ment_id is '财务部门段';
comment on column accuser.acc_duct_entry_data_all.channel_code is '财务渠道段';
comment on column accuser.acc_duct_entry_data_all.sup_product_id is '财务补充产品段';
comment on column accuser.acc_duct_entry_data_all.scheme_status is '管理方案（0：内部管理，1：监管报送）';
comment on column accuser.acc_duct_entry_data_all.vouitem is 'I4vouitem';
comment on column accuser.acc_duct_entry_data_all.budget_code is '预算项目段';
comment on column accuser.acc_duct_entry_data_all.scenario_id is 'scenario_id|业务场景ID';

call accuser.acc_pack_commonutils_proc_drop_table('acc_duct_entry_data_fail');
create table accuser.acc_duct_entry_data_fail (
  buss_entry_id bigint not null, -- BUSS_ENTRY_ID|入账处理数据id
  entry_data_id bigint not null, -- ENTRY_DATA_ID|入账业务数据id
  entity_id bigint, -- center_id|核算单位ID
  book_code character varying, -- book_code|账套
  year_month character varying, -- year_month|会计期间
  task_code character varying, -- task_code|任务ID
  proc_id bigint, -- proc_id|数据归属节点BPL_ACT_RE_PROCDEF
  scenario_id bigint, -- scenario_id|业务场景ID
  account_id_dr bigint, -- item_id_dr|借方科目ID
  account_id_cr bigint, -- item_id_cr|贷方科目ID
  posting_type_code character varying, -- post_type|入账类型
  scenario_type character varying, -- scenario_type|场景类型AP-非现金流,CF-现金流
  voucher_id bigint, -- voucher_id|凭证号码
  change_time bigint, -- change_time|冲正次数
  currency_cu_code character varying, -- currency_cu|币种_本位币 
  currency_code character varying, -- currency|币种
  exchange_rate numeric(32,8), -- exch_rate|兑换率
  entry_state bigint, -- entry_state|入账状态
  entry_msg character varying, -- entry_msg|入账结果信息
  create_time timestamp(6) without time zone, -- create_time|创建时间
  creator_id bigint, -- creator_id|创建人ID
  update_time timestamp(6) without time zone, -- update_time|最后更新时间
  updator_id bigint, -- updator_id|最后更新人ID
  bu_voucher_no character varying, -- B_VOUCHER_NO|业务凭证号
  entry_date timestamp(6) without time zone, -- 入账时点
  scen_serial_no bigint, -- 场景版本序号
  other_scn_type character varying(10), -- I4场景码，适用于BCA
  other_scn_vou_type character varying(2), -- 实收实付时，ext3里面的DN/CN，使用于BCA
  cg_dept_code character varying(10), -- 往来机构，适用于BCA
  cg_is character varying(1), -- 往来标识
  product_is character varying(1), -- 产品标识，适用匹配场景
  center_code character varying(32), -- 核算机构
  article_msg character varying(500) -- 辅助核算项校验
)
partition by LIST (entity_id);

comment on table accuser.acc_duct_entry_data_fail is '入账数据过程表(错误数据)';
comment on column accuser.acc_duct_entry_data_fail.buss_entry_id is 'BUSS_ENTRY_ID|入账处理数据id';
comment on column accuser.acc_duct_entry_data_fail.entry_data_id is 'ENTRY_DATA_ID|入账业务数据id';
comment on column accuser.acc_duct_entry_data_fail.entity_id is 'center_id|核算单位ID';
comment on column accuser.acc_duct_entry_data_fail.book_code is 'book_code|账套';
comment on column accuser.acc_duct_entry_data_fail.year_month is 'year_month|会计期间';
comment on column accuser.acc_duct_entry_data_fail.task_code is 'task_code|任务ID';
comment on column accuser.acc_duct_entry_data_fail.proc_id is 'proc_id|数据归属节点BPL_ACT_RE_PROCDEF';
comment on column accuser.acc_duct_entry_data_fail.scenario_id is 'scenario_id|业务场景ID';
comment on column accuser.acc_duct_entry_data_fail.account_id_dr is 'item_id_dr|借方科目ID';
comment on column accuser.acc_duct_entry_data_fail.account_id_cr is 'item_id_cr|贷方科目ID';
comment on column accuser.acc_duct_entry_data_fail.posting_type_code is 'post_type|入账类型';
comment on column accuser.acc_duct_entry_data_fail.scenario_type is 'scenario_type|场景类型AP-非现金流,CF-现金流';
comment on column accuser.acc_duct_entry_data_fail.voucher_id is 'voucher_id|凭证号码';
comment on column accuser.acc_duct_entry_data_fail.change_time is 'change_time|冲正次数';
comment on column accuser.acc_duct_entry_data_fail.currency_cu_code is 'currency_cu|币种_本位币 ';
comment on column accuser.acc_duct_entry_data_fail.currency_code is 'currency|币种';
comment on column accuser.acc_duct_entry_data_fail.exchange_rate is 'exch_rate|兑换率';
comment on column accuser.acc_duct_entry_data_fail.entry_state is 'entry_state|入账状态';
comment on column accuser.acc_duct_entry_data_fail.entry_msg is 'entry_msg|入账结果信息';
comment on column accuser.acc_duct_entry_data_fail.create_time is 'create_time|创建时间';
comment on column accuser.acc_duct_entry_data_fail.creator_id is 'creator_id|创建人ID';
comment on column accuser.acc_duct_entry_data_fail.update_time is 'update_time|最后更新时间';
comment on column accuser.acc_duct_entry_data_fail.updator_id is 'updator_id|最后更新人ID';
comment on column accuser.acc_duct_entry_data_fail.bu_voucher_no is 'B_VOUCHER_NO|业务凭证号';
comment on column accuser.acc_duct_entry_data_fail.entry_date is '入账时点';
comment on column accuser.acc_duct_entry_data_fail.scen_serial_no is '场景版本序号';
comment on column accuser.acc_duct_entry_data_fail.other_scn_type is 'I4场景码，适用于BCA';
comment on column accuser.acc_duct_entry_data_fail.other_scn_vou_type is '实收实付时，ext3里面的DN/CN，使用于BCA';
comment on column accuser.acc_duct_entry_data_fail.cg_dept_code is '往来机构，适用于BCA';
comment on column accuser.acc_duct_entry_data_fail.cg_is is '往来标识';
comment on column accuser.acc_duct_entry_data_fail.product_is is '产品标识，适用匹配场景';
comment on column accuser.acc_duct_entry_data_fail.center_code is '核算机构';
comment on column accuser.acc_duct_entry_data_fail.article_msg is '辅助核算项校验';

call accuser.acc_pack_commonutils_proc_drop_table('acc_duct_entry_data_success');
create table accuser.acc_duct_entry_data_success (
  buss_entry_id bigint not null, -- BUSS_ENTRY_ID|入账处理数据id
  entry_data_id bigint not null, -- ENTRY_DATA_ID|入账业务数据id
  entity_id bigint, -- center_id|核算单位ID
  book_code character varying, -- book_code|账套
  year_month character varying, -- year_month|会计期间
  task_code character varying, -- task_code|任务ID
  proc_id bigint, -- proc_id|数据归属节点BPL_ACT_RE_PROCDEF
  scenario_id bigint, -- scenario_id|业务场景ID
  account_id_dr bigint, -- item_id_dr|借方科目ID
  account_id_cr bigint, -- item_id_cr|贷方科目ID
  posting_type_code character varying, -- post_type|入账类型
  scenario_type character varying, -- scenario_type|场景类型AP-非现金流,CF-现金流
  voucher_id bigint, -- voucher_id|凭证号码
  change_time bigint, -- change_time|冲正次数
  currency_cu_code character varying, -- currency_cu|币种_本位币 
  currency_code character varying, -- currency|币种
  exchange_rate numeric(32,8), -- exch_rate|兑换率
  entry_state bigint, -- entry_state|入账状态
  entry_msg character varying, -- entry_msg|入账结果信息
  create_time timestamp(6) without time zone, -- create_time|创建时间
  creator_id bigint, -- creator_id|创建人ID
  update_time timestamp(6) without time zone, -- update_time|最后更新时间
  updator_id bigint, -- updator_id|最后更新人ID
  bu_voucher_no character varying, -- B_VOUCHER_NO|业务凭证号
  entry_date timestamp(6) without time zone, -- 入账时点
  scen_serial_no bigint, -- 场景版本序号
  account_entry_code character varying, -- drcr_type|现金流借贷标识 D-借 C-贷
  account_id bigint, -- 科目id
  amount numeric(32,8), -- 待入账金额
  cg_dept_code character varying(10), -- 往来机构，适用于BCA
  cg_is character varying(1), -- 往来标识
  extend_column1 character varying(100), -- remark1|预收标致 ADVANCE_IND
  extend_column2 character varying(100), -- remark2|业务字段备注2
  extend_column3 character varying(100), -- remark3|业务字段备注3
  extend_column4 character varying(100), -- remark4|业务字段备注4
  extend_column5 character varying(100), -- remark5|业务字段备注5
  extend_column6 character varying(100), -- remark6|业务字段备注6
  extend_column7 character varying(100), -- remark7|业务字段备注7
  extend_column8 character varying(100), -- remark8|业务字段备注8
  extend_column9 character varying(100), -- remark9|业务字段备注9
  extend_column10 character varying(100), -- remark10|业务字段备注10
  extend_column11 character varying(100), -- remark11|业务字段备注11
  extend_column12 character varying(100), -- remark12|业务字段备注12
  extend_column13 character varying(100), -- remark13|业务字段备注13
  extend_column14 character varying(100), -- remark14|业务字段备注14
  treaty_year character varying(4), -- 合约年份
  portfolio_no character varying(60), -- 合同组合号
  icg_no character varying(60), -- 合同组号
  cashflow_article character varying(32), -- cashflow_article|现金流专项
  business_id numeric(22,0), -- dm_tgt_fin_payment 的ID
  offshore_is character varying(1), -- Offshore Is|是否海外业务
  ri_arrangement_code character varying(2), -- 业务来源 I分入业务 S自营业务 D不区分
  business_source_code character varying(2), -- 业务类型|business_Type|DB直接业务 FB临汾业务 TB合约业务 PB收付业务
  ri_direction_code character varying(1),
  treaty_type_code character varying(8), -- 合约类型
  evaluate_approach character varying(60), -- Evaluate Approach|评估方法
  expenses_type_code character varying(10), -- fee_Type|费用类型(bpl_code .codetype= FeeType 费用类型编码)
  current_previous_is character varying(1), -- 当年/往年标识：1-当年，2-往年，0或空-不区分
  risk_code character varying(32), -- 险种代码
  policy_no character varying(60), -- 保单号
  endorse_no character varying(60), -- 批单号
  endorse_seq_no character varying(60), -- 批单序号
  ri_policy_no character varying(60), -- 分保单号
  claim_no character varying(60), -- 立案号
  claim_loss_no character varying(60), -- 理赔号
  ri_statement_no character varying(60), -- 账单号
  est_payment_seq_no numeric(22,0), -- 预期缴费期次
  policy_type_code character varying(2), -- policy_type|保单类型-POLICY_TYPE  N新单，E批单，C退保，R续保
  treaty_code character varying(10), -- 合约编码
  account_code character varying(32), -- 现金流科目编码
  ri_broker_code character varying(100), -- 交易对手
  reins_flag character varying(12), -- 共分保类型
  shareholder_flag character varying(24), -- 客户类型
  center_code character varying(32), -- 核算机构
  risk_class_code character varying(32), -- 险类
  ri_master_statement_no character varying(60), -- 总账单号
  clause_code character varying(32), -- 险别/条款代码
  if_documentation character varying(1), -- 是否跟单：0:不跟跟单,1:跟单
  product_id character varying(60), -- 财务产品段
  detail_id character varying(60), -- 财务明细段
  dept_ment_id character varying(60), -- 财务部门段
  channel_code character varying(60), -- 财务渠道段
  sup_product_id character varying(60), -- 财务补充产品段
  scheme_status character varying(1), -- 管理方案（0：内部管理，1：监管报送）
  vouitem character varying(60), -- I4vouitem
  budget_code character varying(60) -- 预算项目段
)
partition by LIST (entity_id);

drop index if exists idx_acc_duct_entry_data_success_bu_voucher_no;
drop index if exists idx_acc_duct_entry_data_success_proc_id;
drop index if exists idx_acc_duct_entry_data_success_proc_id_bu_voucher_no;
create index idx_acc_duct_entry_data_success_bu_voucher_no on acc_duct_entry_data_success using btree (bu_voucher_no);
create index idx_acc_duct_entry_data_success_proc_id on acc_duct_entry_data_success using btree (proc_id, posting_type_code);
create index idx_acc_duct_entry_data_success_proc_id_bu_voucher_no on acc_duct_entry_data_success using btree (proc_id, bu_voucher_no);
comment on table accuser.acc_duct_entry_data_success is '入账数据过程表(成功数据)';
comment on column accuser.acc_duct_entry_data_success.buss_entry_id is 'BUSS_ENTRY_ID|入账处理数据id';
comment on column accuser.acc_duct_entry_data_success.entry_data_id is 'ENTRY_DATA_ID|入账业务数据id';
comment on column accuser.acc_duct_entry_data_success.entity_id is 'center_id|核算单位ID';
comment on column accuser.acc_duct_entry_data_success.book_code is 'book_code|账套';
comment on column accuser.acc_duct_entry_data_success.year_month is 'year_month|会计期间';
comment on column accuser.acc_duct_entry_data_success.task_code is 'task_code|任务ID';
comment on column accuser.acc_duct_entry_data_success.proc_id is 'proc_id|数据归属节点BPL_ACT_RE_PROCDEF';
comment on column accuser.acc_duct_entry_data_success.scenario_id is 'scenario_id|业务场景ID';
comment on column accuser.acc_duct_entry_data_success.account_id_dr is 'item_id_dr|借方科目ID';
comment on column accuser.acc_duct_entry_data_success.account_id_cr is 'item_id_cr|贷方科目ID';
comment on column accuser.acc_duct_entry_data_success.posting_type_code is 'post_type|入账类型';
comment on column accuser.acc_duct_entry_data_success.scenario_type is 'scenario_type|场景类型AP-非现金流,CF-现金流';
comment on column accuser.acc_duct_entry_data_success.voucher_id is 'voucher_id|凭证号码';
comment on column accuser.acc_duct_entry_data_success.change_time is 'change_time|冲正次数';
comment on column accuser.acc_duct_entry_data_success.currency_cu_code is 'currency_cu|币种_本位币 ';
comment on column accuser.acc_duct_entry_data_success.currency_code is 'currency|币种';
comment on column accuser.acc_duct_entry_data_success.exchange_rate is 'exch_rate|兑换率';
comment on column accuser.acc_duct_entry_data_success.entry_state is 'entry_state|入账状态';
comment on column accuser.acc_duct_entry_data_success.entry_msg is 'entry_msg|入账结果信息';
comment on column accuser.acc_duct_entry_data_success.create_time is 'create_time|创建时间';
comment on column accuser.acc_duct_entry_data_success.creator_id is 'creator_id|创建人ID';
comment on column accuser.acc_duct_entry_data_success.update_time is 'update_time|最后更新时间';
comment on column accuser.acc_duct_entry_data_success.updator_id is 'updator_id|最后更新人ID';
comment on column accuser.acc_duct_entry_data_success.bu_voucher_no is 'B_VOUCHER_NO|业务凭证号';
comment on column accuser.acc_duct_entry_data_success.entry_date is '入账时点';
comment on column accuser.acc_duct_entry_data_success.scen_serial_no is '场景版本序号';
comment on column accuser.acc_duct_entry_data_success.account_entry_code is 'drcr_type|现金流借贷标识 D-借 C-贷';
comment on column accuser.acc_duct_entry_data_success.account_id is '科目id';
comment on column accuser.acc_duct_entry_data_success.amount is '待入账金额';
comment on column accuser.acc_duct_entry_data_success.cg_dept_code is '往来机构，适用于BCA';
comment on column accuser.acc_duct_entry_data_success.cg_is is '往来标识';
comment on column accuser.acc_duct_entry_data_success.extend_column1 is 'remark1|预收标致 ADVANCE_IND';
comment on column accuser.acc_duct_entry_data_success.extend_column2 is 'remark2|业务字段备注2';
comment on column accuser.acc_duct_entry_data_success.extend_column3 is 'remark3|业务字段备注3';
comment on column accuser.acc_duct_entry_data_success.extend_column4 is 'remark4|业务字段备注4';
comment on column accuser.acc_duct_entry_data_success.extend_column5 is 'remark5|业务字段备注5';
comment on column accuser.acc_duct_entry_data_success.extend_column6 is 'remark6|业务字段备注6';
comment on column accuser.acc_duct_entry_data_success.extend_column7 is 'remark7|业务字段备注7';
comment on column accuser.acc_duct_entry_data_success.extend_column8 is 'remark8|业务字段备注8';
comment on column accuser.acc_duct_entry_data_success.extend_column9 is 'remark9|业务字段备注9';
comment on column accuser.acc_duct_entry_data_success.extend_column10 is 'remark10|业务字段备注10';
comment on column accuser.acc_duct_entry_data_success.extend_column11 is 'remark11|业务字段备注11';
comment on column accuser.acc_duct_entry_data_success.extend_column12 is 'remark12|业务字段备注12';
comment on column accuser.acc_duct_entry_data_success.extend_column13 is 'remark13|业务字段备注13';
comment on column accuser.acc_duct_entry_data_success.extend_column14 is 'remark14|业务字段备注14';
comment on column accuser.acc_duct_entry_data_success.treaty_year is '合约年份';
comment on column accuser.acc_duct_entry_data_success.portfolio_no is '合同组合号';
comment on column accuser.acc_duct_entry_data_success.icg_no is '合同组号';
comment on column accuser.acc_duct_entry_data_success.cashflow_article is 'cashflow_article|现金流专项';
comment on column accuser.acc_duct_entry_data_success.business_id is 'dm_tgt_fin_payment 的ID';
comment on column accuser.acc_duct_entry_data_success.offshore_is is 'Offshore Is|是否海外业务';
comment on column accuser.acc_duct_entry_data_success.ri_arrangement_code is '业务来源 I分入业务 S自营业务 D不区分';
comment on column accuser.acc_duct_entry_data_success.business_source_code is '业务类型|business_Type|DB直接业务 FB临汾业务 TB合约业务 PB收付业务';
comment on column accuser.acc_duct_entry_data_success.treaty_type_code is '合约类型';
comment on column accuser.acc_duct_entry_data_success.evaluate_approach is 'Evaluate Approach|评估方法';
comment on column accuser.acc_duct_entry_data_success.expenses_type_code is 'fee_Type|费用类型(bpl_code .codetype= FeeType 费用类型编码)';
comment on column accuser.acc_duct_entry_data_success.current_previous_is is '当年/往年标识：1-当年，2-往年，0或空-不区分';
comment on column accuser.acc_duct_entry_data_success.risk_code is '险种代码';
comment on column accuser.acc_duct_entry_data_success.policy_no is '保单号';
comment on column accuser.acc_duct_entry_data_success.endorse_no is '批单号';
comment on column accuser.acc_duct_entry_data_success.endorse_seq_no is '批单序号';
comment on column accuser.acc_duct_entry_data_success.ri_policy_no is '分保单号';
comment on column accuser.acc_duct_entry_data_success.claim_no is '立案号';
comment on column accuser.acc_duct_entry_data_success.claim_loss_no is '理赔号';
comment on column accuser.acc_duct_entry_data_success.ri_statement_no is '账单号';
comment on column accuser.acc_duct_entry_data_success.est_payment_seq_no is '预期缴费期次';
comment on column accuser.acc_duct_entry_data_success.policy_type_code is 'policy_type|保单类型-POLICY_TYPE  N新单，E批单，C退保，R续保';
comment on column accuser.acc_duct_entry_data_success.treaty_code is '合约编码';
comment on column accuser.acc_duct_entry_data_success.account_code is '现金流科目编码';
comment on column accuser.acc_duct_entry_data_success.ri_broker_code is '交易对手';
comment on column accuser.acc_duct_entry_data_success.reins_flag is '共分保类型';
comment on column accuser.acc_duct_entry_data_success.shareholder_flag is '客户类型';
comment on column accuser.acc_duct_entry_data_success.center_code is '核算机构';
comment on column accuser.acc_duct_entry_data_success.risk_class_code is '险类';
comment on column accuser.acc_duct_entry_data_success.ri_master_statement_no is '总账单号';
comment on column accuser.acc_duct_entry_data_success.clause_code is '险别/条款代码';
comment on column accuser.acc_duct_entry_data_success.if_documentation is '是否跟单：0:不跟跟单,1:跟单';
comment on column accuser.acc_duct_entry_data_success.product_id is '财务产品段';
comment on column accuser.acc_duct_entry_data_success.detail_id is '财务明细段';
comment on column accuser.acc_duct_entry_data_success.dept_ment_id is '财务部门段';
comment on column accuser.acc_duct_entry_data_success.channel_code is '财务渠道段';
comment on column accuser.acc_duct_entry_data_success.sup_product_id is '财务补充产品段';
comment on column accuser.acc_duct_entry_data_success.scheme_status is '管理方案（0：内部管理，1：监管报送）';
comment on column accuser.acc_duct_entry_data_success.vouitem is 'I4vouitem';
comment on column accuser.acc_duct_entry_data_success.budget_code is '预算项目段';

call accuser.acc_pack_commonutils_proc_drop_table('acc_duct_reconciliation_base');
create table accuser.acc_duct_reconciliation_base (
  recon_cr_id bigint primary key not null, -- 主键ID
  entity_id bigint not null, -- 业务单位
  year_month character varying(8) not null, -- 业务年月
  voucher_no character varying(32) not null, -- 凭证号码
  posting_type_code character varying(2) not null, -- 凭证类型
  rule_code character varying(32) not null, -- 规则编码
  account_code character varying(32) not null, -- 科目编码
  amount numeric(32,8), -- 金额(原币)
  amount_cu numeric(32,8), -- 金额（本位币）
  amount_collate numeric(32,8), -- 核对金额
  account_entry_code character varying(1) not null -- 借贷标识
);
comment on table accuser.acc_duct_reconciliation_base is '对账过程记录-I4';
comment on column accuser.acc_duct_reconciliation_base.recon_cr_id is '主键ID';
comment on column accuser.acc_duct_reconciliation_base.entity_id is '业务单位';
comment on column accuser.acc_duct_reconciliation_base.year_month is '业务年月';
comment on column accuser.acc_duct_reconciliation_base.voucher_no is '凭证号码';
comment on column accuser.acc_duct_reconciliation_base.posting_type_code is '凭证类型';
comment on column accuser.acc_duct_reconciliation_base.rule_code is '规则编码';
comment on column accuser.acc_duct_reconciliation_base.account_code is '科目编码';
comment on column accuser.acc_duct_reconciliation_base.amount is '金额(原币)';
comment on column accuser.acc_duct_reconciliation_base.amount_cu is '金额（本位币）';
comment on column accuser.acc_duct_reconciliation_base.amount_collate is '核对金额';
comment on column accuser.acc_duct_reconciliation_base.account_entry_code is '借贷标识';

call accuser.acc_pack_commonutils_proc_drop_table('acc_duct_reconciliation_other');
create table accuser.acc_duct_reconciliation_other (
  recon_cr_id bigint primary key not null, -- 主键ID
  entity_id bigint not null, -- 业务单位
  year_month character varying(8) not null, -- 业务年月
  voucher_no character varying(32) not null, -- 凭证号码
  posting_type_code character varying(2) not null, -- 凭证类型
  rule_code character varying(32) not null, -- 规则编码
  account_code character varying(32) not null, -- 科目编码
  amount numeric(32,8), -- 金额(原币)
  amount_cu numeric(32,8), -- 金额（本位币）
  amount_collate numeric(32,8), -- 核对金额
  account_entry_code character varying(1) not null -- 借贷标识
);
comment on table accuser.acc_duct_reconciliation_other is '对账过程记录-II7';
comment on column accuser.acc_duct_reconciliation_other.recon_cr_id is '主键ID';
comment on column accuser.acc_duct_reconciliation_other.entity_id is '业务单位';
comment on column accuser.acc_duct_reconciliation_other.year_month is '业务年月';
comment on column accuser.acc_duct_reconciliation_other.voucher_no is '凭证号码';
comment on column accuser.acc_duct_reconciliation_other.posting_type_code is '凭证类型';
comment on column accuser.acc_duct_reconciliation_other.rule_code is '规则编码';
comment on column accuser.acc_duct_reconciliation_other.account_code is '科目编码';
comment on column accuser.acc_duct_reconciliation_other.amount is '金额(原币)';
comment on column accuser.acc_duct_reconciliation_other.amount_cu is '金额（本位币）';
comment on column accuser.acc_duct_reconciliation_other.amount_collate is '核对金额';
comment on column accuser.acc_duct_reconciliation_other.account_entry_code is '借贷标识';

call accuser.acc_pack_commonutils_proc_drop_table('acc_duct_voucher_amount');
create table accuser.acc_duct_voucher_amount (
  bu_voucher_no character varying(32) not null, -- 凭证号
  entity_id bigint,
  year_month character varying(6) not null,
  book_code character varying(8),
  proc_id bigint not null,
  dr_amount numeric(32,8), -- 借方金额
  cr_amount numeric(32,8), -- 贷方金额
  center_code character varying(32) -- 核算单位
)
partition by LIST (entity_id);

comment on column accuser.acc_duct_voucher_amount.bu_voucher_no is '凭证号';
comment on column accuser.acc_duct_voucher_amount.dr_amount is '借方金额';
comment on column accuser.acc_duct_voucher_amount.cr_amount is '贷方金额';
comment on column accuser.acc_duct_voucher_amount.center_code is '核算单位';

call accuser.acc_pack_commonutils_proc_drop_table('acc_duct_voucher_article');
create table accuser.acc_duct_voucher_article (
  article1 character varying(32),
  article12 character varying(1),
  article13 character varying(3),
  article14 character varying(100),
  article2 character varying(32),
  article3 character varying(60),
  article4 character varying(100),
  article6 character varying(100),
  article7 character varying(32),
  article8 character varying(60),
  article9 character varying(60),
  article17 character varying(60),
  article16 character varying(60),
  article15 character varying(60),
  article11 character varying(60),
  article10 character varying(60),
  article5 character varying(60),
  entry_data_id numeric(11,0),
  voucher_id numeric(11,0) not null,
  voucher_dtl_id numeric(11,0),
  entity_id numeric(11,0),
  book_code character varying(8),
  year_month character varying(6),
  proc_id numeric(11,0)
)
partition by LIST (entity_id);

drop index if exists idx_acc_duct_voucher_article_voucher_id;
create index idx_acc_duct_voucher_article_voucher_dtl_id on acc_duct_voucher_article using btree (voucher_dtl_id);
comment on table accuser.acc_duct_voucher_article is '凭证详情的专项数据过程表';

call accuser.acc_pack_commonutils_proc_drop_table('acc_duct_voucher_detail');
create table accuser.acc_duct_voucher_detail (
  voucher_dtl_id numeric(20,0) not null,
  voucher_id numeric(20,0),
  account_id numeric(11,0),
  currency_code character varying(3) not null,
  currency_cu_code character varying(3) not null,
  account_entry_code character(1) not null,
  amount numeric(32,8) not null,
  amount_cu numeric(32,8) not null,
  exchange_rate numeric(32,8) not null,
  remark character varying(400),
  entity_id numeric(11,0),
  book_code character varying(8),
  year_month character varying(6),
  proc_id numeric(11,0),
  task_code character varying(32),
  posting_type_code character varying(10),
  ext_voucher_no character varying(32),
  article1 character varying(60),
  article2 character varying(60),
  article3 character varying(60),
  article4 character varying(60),
  article5 character varying(60),
  article6 character varying(60),
  article7 character varying(60),
  article8 character varying(60),
  article9 character varying(60),
  article10 character varying(60),
  article11 character varying(60),
  article12 character varying(60),
  article13 character varying(60),
  article14 character varying(60),
  article15 character varying(60),
  article16 character varying(60),
  article17 character varying(60),
  create_time timestamp(6) without time zone,
  creator_id numeric(11,0),
  update_time timestamp(6) without time zone,
  updator_id numeric(11,0),
  entry_data_id numeric(11,0),
  article character varying(1000)
)
partition by LIST (entity_id);

drop index if exists idx_acc_duct_voucher_detail_entry_data_id;
create index idx_acc_duct_voucher_detail_entry_data_id on acc_duct_voucher_detail using btree (entry_data_id);
comment on table accuser.acc_duct_voucher_detail is '凭证详情数据过程表（数据只在处理有效）';

call accuser.acc_pack_commonutils_proc_drop_table('acc_log_check_rule');
create table accuser.acc_log_check_rule (
  log_id bigint primary key not null, -- log_id|主键
  entity_id bigint, -- Entity Id|业务单位ID
  book_code character varying(32),
  year_month character varying(6),
  task_code character varying(64), -- Task_Code|任务号
  parent_proc_id bigint,
  proc_id bigint,
  quantity integer,
  data_model character varying(60),
  data_key bigint, -- Data_Key|数据主键 关联表查询具体数据
  business_no character varying(500), -- Business_No|业务单号
  rule_id bigint, -- rule_Id|规则主键
  check_state character varying(1), -- Check_State|校验状态 1-通过,2-不通过,3-告警，
  rule_version numeric(6,2), -- Rule_Version|规则版本号
  creator_id bigint, -- creator_id|创建人代码
  create_time timestamp(6) without time zone, -- createTime|时间
  rule_code character varying(100), -- rule_code|规则编码
  rule_type character varying(1), -- rule_type|规则类型 1-告警，2-异常
  cumulative_is character varying(1), -- 是否累计数据 N-否，Y-是
  center_code character varying(32) -- 核算单位
);
drop index if exists idx_acc_log_check_rule_task_code;
create index idx_acc_log_check_rule_task_code on acc_log_check_rule using btree (task_code);
comment on column accuser.acc_log_check_rule.log_id is 'log_id|主键';
comment on column accuser.acc_log_check_rule.entity_id is 'Entity Id|业务单位ID';
comment on column accuser.acc_log_check_rule.task_code is 'Task_Code|任务号';
comment on column accuser.acc_log_check_rule.data_key is 'Data_Key|数据主键 关联表查询具体数据';
comment on column accuser.acc_log_check_rule.business_no is 'Business_No|业务单号';
comment on column accuser.acc_log_check_rule.rule_id is 'rule_Id|规则主键';
comment on column accuser.acc_log_check_rule.check_state is 'Check_State|校验状态 1-通过,2-不通过,3-告警，';
comment on column accuser.acc_log_check_rule.rule_version is 'Rule_Version|规则版本号';
comment on column accuser.acc_log_check_rule.creator_id is 'creator_id|创建人代码';
comment on column accuser.acc_log_check_rule.create_time is 'createTime|时间';
comment on column accuser.acc_log_check_rule.rule_code is 'rule_code|规则编码';
comment on column accuser.acc_log_check_rule.rule_type is 'rule_type|规则类型 1-告警，2-异常';
comment on column accuser.acc_log_check_rule.cumulative_is is '是否累计数据 N-否，Y-是';

drop table acc_log_trail_final_settle;
create table accuser.acc_log_trail_final_settle (
  log_settle_id bigint primary key not null, -- log_id|主键
  entity_id bigint, -- Entity Id|业务单位ID
  book_code character varying(32),
  year_month character varying(6),
  task_code character varying(64), -- Task_Code|任务号
  rule_code character varying(100),
  debit_amount_year numeric(32,8),
  credit_amount_year numeric(32,8),
  closing_balance numeric(32,8),
  check_state character varying(1),
  center_code character varying(32) -- 核算单位
);
comment on column accuser.acc_log_trail_final_settle.log_settle_id is 'log_id|主键';
comment on column accuser.acc_log_trail_final_settle.entity_id is 'Entity Id|业务单位ID';
comment on column accuser.acc_log_trail_final_settle.task_code is 'Task_Code|任务号';

drop table acc_stat_check_result;
create table accuser.acc_stat_check_result (
  stat_id bigint primary key not null,
  entity_id bigint, -- Entity Code|业务单位
  book_code character varying(32), -- 账套
  year_month character varying(6), -- 业务期间
  parent_proc_id bigint, -- 父级流程id
  proc_id bigint, -- 流程id
  task_code character varying(64), -- 任务号
  check_state character varying(1), -- 状态
  quantity integer, -- 数量统计
  update_time timestamp(6) without time zone, -- 更新时间
  center_code character varying(32) -- 核算单位
);
comment on column accuser.acc_stat_check_result.entity_id is 'Entity Code|业务单位';
comment on column accuser.acc_stat_check_result.book_code is '账套';
comment on column accuser.acc_stat_check_result.year_month is '业务期间';
comment on column accuser.acc_stat_check_result.parent_proc_id is '父级流程id';
comment on column accuser.acc_stat_check_result.proc_id is '流程id';
comment on column accuser.acc_stat_check_result.task_code is '任务号';
comment on column accuser.acc_stat_check_result.check_state is '状态';
comment on column accuser.acc_stat_check_result.quantity is '数量统计';
comment on column accuser.acc_stat_check_result.update_time is '更新时间';


drop view   ACC_v_LOG_CHECK_RULE  ;
create view accuser.acc_v_log_check_rule
            (entity_id, book_code, year_month, task_code, proc_id, rule_id, rule_code, check_state, rule_type,
             create_time) as
SELECT entity_id,
       book_code,
       year_month,
       task_code,
       proc_id,
       rule_id,
       rule_code,
       check_state,
       rule_type,
       max(create_time) AS create_time
FROM acc_log_check_rule t
WHERE (EXISTS (SELECT 1
               FROM acc_stat_check_result scr
               WHERE scr.entity_id = t.entity_id
                 AND scr.book_code::text = t.book_code::text
                 AND scr.year_month::text = t.year_month::text
                 AND scr.proc_id = t.proc_id
                 AND scr.task_code::text = t.task_code::text
                 AND scr.check_state::text = t.check_state::text))
GROUP BY entity_id, book_code, year_month, task_code, proc_id, rule_id, rule_code, check_state, rule_type;

alter table accuser.acc_v_log_check_rule
    owner to accuser;



call accuser.acc_pack_commonutils_proc_drop_table('acc_conf_posting_summary');
create table accuser.acc_conf_posting_summary
(
    id              bigint       not null
        constraint pk_acc_conf_posting_summary
            primary key,
    data_type       char         not null,
    entity_id       bigint       not null,
    book_code       varchar(12)  not null,
    posting_summary varchar(512),
    audit_state     char         not null,
    checked_id      bigint,
    checked_time    timestamp(6),
    checked_msg     varchar(512),
    creator_id      bigint       not null,
    create_time     timestamp(6) not null,
    updator_id      bigint,
    update_time     timestamp(6)
);

comment on table accuser.acc_conf_posting_summary is '凭证摘要配置表';

comment on column accuser.acc_conf_posting_summary.id is '主键';

comment on column accuser.acc_conf_posting_summary.data_type is '数据类型：1-实际现金流，2-费用分摊，3-计量';

comment on column accuser.acc_conf_posting_summary.entity_id is '业务单位id';

comment on column accuser.acc_conf_posting_summary.book_code is '账套编码';

comment on column accuser.acc_conf_posting_summary.posting_summary is '凭证摘要';

comment on column accuser.acc_conf_posting_summary.audit_state is '审核状态';

comment on column accuser.acc_conf_posting_summary.checked_time is '审核时间';

comment on column accuser.acc_conf_posting_summary.checked_msg is '审核信息';

comment on column accuser.acc_conf_posting_summary.creator_id is '创建人';

comment on column accuser.acc_conf_posting_summary.create_time is '创建时间';

comment on column accuser.acc_conf_posting_summary.updator_id is '最后变更经手人';

comment on column accuser.acc_conf_posting_summary.update_time is '最后变更时间';

alter table accuser.acc_conf_posting_summary
    owner to accuser;

call accuser.acc_pack_commonutils_proc_drop_table('acc_conf_revaluation');
create table accuser.acc_conf_revaluation
(
    id               bigint not null
        constraint acc_conf_revaluation_pk
            primary key,
    ora_account_code varchar(64),
    ora_account_id   bigint,
    ora_entry_code   char,
    to_account_code  varchar(64),
    to_account_id    bigint,
    to_entry_code    char,
    cal_sign         bigint,
    revaluation_type varchar(3),
    revaluation_desc varchar(100)
);

comment on table accuser.acc_conf_revaluation is '损益重估配置';

comment on column accuser.acc_conf_revaluation.id is '主键';

comment on column accuser.acc_conf_revaluation.ora_account_code is '原始科目编码';

comment on column accuser.acc_conf_revaluation.ora_account_id is '原始科目id';

comment on column accuser.acc_conf_revaluation.ora_entry_code is '原始科目方向';

comment on column accuser.acc_conf_revaluation.to_account_code is '转换的科目编码';

comment on column accuser.acc_conf_revaluation.to_account_id is '转换的科目id';

comment on column accuser.acc_conf_revaluation.to_entry_code is '转换科目方向';

comment on column accuser.acc_conf_revaluation.cal_sign is '计算符号';

comment on column accuser.acc_conf_revaluation.revaluation_type is '分类类型';

comment on column accuser.acc_conf_revaluation.revaluation_desc is '分类描述';

alter table accuser.acc_conf_revaluation
    owner to accuser;

call accuser.acc_pack_commonutils_proc_drop_table('acc_conf_reclassify_mapping');
create table accuser.acc_conf_reclassify_mapping
(
    id                      bigint not null
        constraint acc_conf_reclassify_mapping_pk
            primary key,
    transfer_account_code   varchar(64),
    transfer_account_id     bigint,
    transfer_entry_code     char,
    reclassify_account_code varchar(64),
    reclassify_account_id   bigint,
    reclassify_entry_code   char
);

comment on table accuser.acc_conf_reclassify_mapping is '重分类-科目映射表';

comment on column accuser.acc_conf_reclassify_mapping.id is '主键id';

comment on column accuser.acc_conf_reclassify_mapping.transfer_account_code is '中转科目代码';

comment on column accuser.acc_conf_reclassify_mapping.transfer_account_id is '中转科目id';

comment on column accuser.acc_conf_reclassify_mapping.transfer_entry_code is '中转科目方向';

comment on column accuser.acc_conf_reclassify_mapping.reclassify_account_code is '重分类科目代码';

comment on column accuser.acc_conf_reclassify_mapping.reclassify_account_id is '重分类科目id';

comment on column accuser.acc_conf_reclassify_mapping.reclassify_entry_code is '重分类科目方向';

alter table accuser.acc_conf_reclassify_mapping
    owner to accuser;

call accuser.acc_pack_commonutils_proc_drop_table('acc_conf_transfer_mapping');
create table accuser.acc_conf_transfer_mapping
(
    id                       bigint not null
        primary key,
    ora_account_code         varchar(64),
    ora_account_id           bigint,
    ora_entry_code           char,
    dr_transfer_account_code varchar(64),
    dr_transfer_account_id   bigint,
    cr_transfer_account_code varchar(64),
    cr_transfer_account_id   bigint,
    transfer_entry_code      char,
    cal_sign                 bigint,
    transfer_group           varchar(3)
);

comment on table accuser.acc_conf_transfer_mapping is '重分类-科目映射表';

comment on column accuser.acc_conf_transfer_mapping.id is '主键id';

comment on column accuser.acc_conf_transfer_mapping.ora_account_code is '原始科目代码';

comment on column accuser.acc_conf_transfer_mapping.ora_account_id is '原始科目id';

comment on column accuser.acc_conf_transfer_mapping.ora_entry_code is '原始科目方向';

comment on column accuser.acc_conf_transfer_mapping.dr_transfer_account_code is '中转科目代码';

comment on column accuser.acc_conf_transfer_mapping.dr_transfer_account_id is '中转科目id';

comment on column accuser.acc_conf_transfer_mapping.cr_transfer_account_code is '贷方中转科目代码';

comment on column accuser.acc_conf_transfer_mapping.cr_transfer_account_id is '贷方中转科目id';

comment on column accuser.acc_conf_transfer_mapping.transfer_entry_code is '中转科目方向';

comment on column accuser.acc_conf_transfer_mapping.cal_sign is '计算符号';

comment on column accuser.acc_conf_transfer_mapping.transfer_group is '分组';

alter table accuser.acc_conf_transfer_mapping
    owner to accuser;

call accuser.acc_pack_commonutils_proc_drop_table('acc_conf_keyno');
create table accuser.acc_conf_keyno
(
    entity_id         bigint            not null,
    book_code         varchar(8)        not null,
    year_month        varchar(6)        not null,
    center_code       varchar(32)       not null,
    source_code       varchar(6)        not null,
    posting_type_code varchar(2)        not null,
    key_type          varchar(4)        not null,
    digit             bigint  default 6 not null,
    key_no            integer default 0 not null,
    flag              varchar(200),
    remark            varchar(4000),
    primary key (entity_id, book_code, year_month, center_code, source_code, posting_type_code, key_type, digit)
);

alter table accuser.acc_conf_keyno
    owner to accuser;
	
call accuser.acc_pack_commonutils_proc_drop_table('acc_temp_entry_data');
create table accuser.acc_temp_entry_data
(
    buss_entry_id     bigint not null,
    entry_data_id     bigint,
    entity_id         bigint,
    book_code         varchar(8),
    year_month        varchar(6),
    task_code         varchar(32),
    proc_id           bigint,
    scenario_id       bigint,
    account_id_dr     bigint,
    account_id_cr     bigint,
    posting_type_code varchar(10),
    scenario_type     varchar(2),
    voucher_id        bigint,
    change_time       smallint,
    currency_cu_code  varchar(3),
    currency_code     varchar(3),
    exchange_rate     numeric(32, 8),
    entry_state       smallint,
    entry_msg         varchar(100),
    create_time       timestamp,
    creator_id        bigint,
    update_time       timestamp,
    updator_id        bigint,
    bu_voucher_no     varchar(60),
    entry_date        timestamp,
    scen_serial_no    bigint,
	center_code       varchar(60)
);

alter table accuser.acc_temp_entry_data
    owner to accuser;


call accuser.acc_pack_commonutils_proc_drop_table('acc_temp_voucher');
create table accuser.acc_temp_voucher
(
    voucher_id        numeric(20) not null,
    entity_id         bigint      not null,
    book_code         varchar(8)  not null,
    posting_type_code varchar(3)  not null,
    proc_id           bigint,
    voucher_no        varchar(32),
    year_month        varchar(6)  not null,
    effective_date    timestamp   not null,
    state             varchar(1)  not null,
    remark            varchar(2000),
    valid_is          varchar(1)  not null,
    create_time       timestamp,
    creator_id        bigint,
    update_time       timestamp,
    updator_id        bigint,
    ext_voucher_no    varchar(32),
	center_code       varchar(60)
);

alter table accuser.acc_temp_voucher
    owner to accuser;
	
	
call accuser.acc_pack_commonutils_proc_drop_table('acc_temp_voucher_detail');
create table accuser.acc_temp_voucher_detail (
  voucher_dtl_id bigint not null,
  voucher_id bigint,
  account_id bigint,
  currency_code character varying(3) not null,
  currency_cu_code character varying(3) not null,
  account_entry_code character(1) not null,
  amount numeric(32,8) not null,
  amount_cu numeric(32,8) not null,
  exchange_rate numeric(32,8) not null,
  remark character varying(400),
  article1 character varying(60),
  article2 character varying(60),
  article3 character varying(60),
  article4 character varying(60),
  article5 character varying(60),
  article6 character varying(60),
  article7 character varying(60),
  article8 character varying(60),
  article9 character varying(60),
  article10 character varying(60),
  article11 character varying(60),
  article12 character varying(60),
  article13 character varying(60),
  article14 character varying(60),
  article15 character varying(60),
  article16 character varying(60),
  article17 character varying(60),
  create_time timestamp(6) without time zone,
  creator_id bigint,
  update_time timestamp(6) without time zone,
  updator_id bigint,
  entry_data_id bigint,
  article character varying(1000),
  center_code character varying(60), -- 核算单位
  article18 character varying(60),
  article19 character varying(60),
  article20 character varying(60),
  article21 character varying(60),
  article22 character varying(60),
  article23 character varying(60)
);

CALL acc_pack_commonutils_proc_drop_table('acc_push_voucher');
CREATE TABLE acc_push_voucher (
    INTERFACE_ID BIGINT NOT NULL,
    STATUS VARCHAR(25) NOT NULL DEFAULT 'NEW',
    LEDGER_ID VARCHAR(25) NOT NULL,
    ACCOUNTING_DATE DATE NOT NULL,
    CURRENCY_CODE VARCHAR(15) NOT NULL DEFAULT 'CNY',
    DATE_CREATED DATE NOT NULL,
    ACTUAL_FLAG VARCHAR(1) NOT NULL DEFAULT 'A',
    SOURCE_BATCH_ID VARCHAR(50) NOT NULL,
    USER_JE_CATEGORY_NAME VARCHAR(25) NOT NULL,
    USER_JE_SOURCE_NAME VARCHAR(25) NOT NULL DEFAULT '会计引擎',
    CURRENCY_CONVERSION_DATE DATE NOT NULL,
    USER_CURRENCY_CONVERSION_TYPE VARCHAR(30) NOT NULL DEFAULT 'User',
    CURRENCY_CONVERSION_RATE NUMERIC NOT NULL DEFAULT 1,
    IMPORT_FLAG VARCHAR(1) NOT NULL DEFAULT 'N',
    IMPORT_DATE DATE,
    ERROR_MESSAGE VARCHAR(2000),
    LAST_UPDATE_DATE DATE,
    LAST_UPDATE_BY VARCHAR(25),
    SEGMENT1 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT2 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT3 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT4 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT5 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT6 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT7 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT8 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT9 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT10 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT11 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT12 VARCHAR(25) NOT NULL DEFAULT '0',
    SEGMENT13 VARCHAR(25) NOT NULL DEFAULT '0',
    ENTERED_DR NUMERIC(32,2),
    ENTERED_CR NUMERIC(32,2),
    ACCOUNTED_DR NUMERIC(32,2),
    ACCOUNTED_CR NUMERIC(32,2),
    DOC_SEQUENCE_VALUE VARCHAR(100),
    REFERENCE1 VARCHAR(100) NOT NULL,
    REFERENCE2 VARCHAR(240),
    REFERENCE3 VARCHAR(100),
    REFERENCE4 VARCHAR(100),
    REFERENCE5 VARCHAR(240),
    REFERENCE10 VARCHAR(240),
    JE_BATCH_ID BIGINT,
    PERIOD_NAME VARCHAR(15),
    JE_HEADER_ID BIGINT,
    JE_LINE_NUM BIGINT,
    CHART_OF_ACCOUNTS_ID BIGINT,
    FUNCTIONAL_CURRENCY_CODE VARCHAR(15),
    WARNING_CODE VARCHAR(4),
    STAT_AMOUNT NUMERIC(32,2),
    GROUP_ID BIGINT,
    REQUEST_ID BIGINT,
    SET_OF_BOOKS_ID VARCHAR(10) ,
    ATTRIBUTE_CATEGORY VARCHAR(30),
    ATTRIBUTE1 VARCHAR(150),
    ATTRIBUTE2 VARCHAR(150),
    ATTRIBUTE3 VARCHAR(150),
    ATTRIBUTE4 VARCHAR(150),
    ATTRIBUTE5 VARCHAR(150),
    ATTRIBUTE6 VARCHAR(150),
    ATTRIBUTE7 VARCHAR(150),
    ATTRIBUTE8 VARCHAR(150),
    ATTRIBUTE9 VARCHAR(150),
    ATTRIBUTE10 VARCHAR(150),
    ATTRIBUTE11 VARCHAR(150),
    ATTRIBUTE12 VARCHAR(150),
    ATTRIBUTE13 VARCHAR(150),
    ATTRIBUTE14 VARCHAR(150),
    ATTRIBUTE15 VARCHAR(150),
    voucher_no BIGINT,
    voucher_dtl_id BIGINT,
    push_type VARCHAR(10),
    
    -- 添加主键约束
    CONSTRAINT pk_acc_push_voucher PRIMARY KEY (INTERFACE_ID),
    
    -- 添加检查约束
    CONSTRAINT chk_status CHECK (STATUS IN ('NEW', 'PROCESSED', 'POSTED')),
    CONSTRAINT chk_actual_flag CHECK (ACTUAL_FLAG IN ('A', 'B', 'E')),
    CONSTRAINT chk_import_flag CHECK (IMPORT_FLAG IN ('N', 'P', 'E', 'Y')),
    CONSTRAINT chk_amount CHECK (
        (ENTERED_DR IS NULL AND ENTERED_CR IS NOT NULL) OR
        (ENTERED_DR IS NOT NULL AND ENTERED_CR IS NULL)
    ),
    CONSTRAINT chk_accounted_amount CHECK (
        (ACCOUNTED_DR IS NULL AND ACCOUNTED_CR IS NOT NULL) OR
        (ACCOUNTED_DR IS NOT NULL AND ACCOUNTED_CR IS NULL)
    )
);

-- 添加注释
COMMENT ON TABLE acc_push_voucher IS 'Oracle总账接口表，用于凭证导入EBS系统';

-- 列注释
COMMENT ON COLUMN acc_push_voucher.STATUS IS '凭证导入状态，NEW新建；PROCESSED已导入；POSTED已过账';
COMMENT ON COLUMN acc_push_voucher.LEDGER_ID IS '账套名称,必须与EBS系统账套ID一致。测试环境账套ID为2081，生产环境需要总账投产后生成。';
COMMENT ON COLUMN acc_push_voucher.ACTUAL_FLAG IS '余额类型 A-实际;B-预算;E-保留款';
COMMENT ON COLUMN acc_push_voucher.SOURCE_BATCH_ID IS '会计引擎凭证批次号,建议规则：系统代码(例如IFRS17)+系统时间(YYYYMMDDHHMMSS)';
COMMENT ON COLUMN acc_push_voucher.USER_JE_SOURCE_NAME IS '凭证来源名称（固定值"会计引擎"）';
COMMENT ON COLUMN acc_push_voucher.IMPORT_FLAG IS '导入标识，N未导入；P处理中；E数据有误；Y导入成功';
COMMENT ON COLUMN acc_push_voucher.IMPORT_DATE IS 'Oracle系统处理日期，由Oracle系统回写';
COMMENT ON COLUMN acc_push_voucher.ERROR_MESSAGE IS 'Oracle回写的详细错误信息';
COMMENT ON COLUMN acc_push_voucher.SEGMENT1 IS 'HGIC_COA_COM_IFRS 公司段';
COMMENT ON COLUMN acc_push_voucher.SEGMENT2 IS 'HGIC_COA_DEPT_IFRS 部门段';
COMMENT ON COLUMN acc_push_voucher.SEGMENT3 IS 'HGIC_COA_ACC_IFRS 科目段IFRS';
COMMENT ON COLUMN acc_push_voucher.SEGMENT4 IS 'HGIC_COA_DETAIL 明细段';
COMMENT ON COLUMN acc_push_voucher.SEGMENT5 IS 'HGIC_COA_PROD 产品段';
COMMENT ON COLUMN acc_push_voucher.SEGMENT6 IS 'HGIC_COA_SUBPROD 补充产品段';
COMMENT ON COLUMN acc_push_voucher.SEGMENT7 IS 'HGIC_COA_CHAN 渠道段';
COMMENT ON COLUMN acc_push_voucher.SEGMENT8 IS 'HGIC_COA_CF 现金流量段';
COMMENT ON COLUMN acc_push_voucher.SEGMENT9 IS 'HGIC_COA_GOC 合同组段';
COMMENT ON COLUMN acc_push_voucher.SEGMENT10 IS 'HGIC_COA_POC 合同组合段';
COMMENT ON COLUMN acc_push_voucher.SEGMENT11 IS 'HGIC_COA_NOTE 附注段';
COMMENT ON COLUMN acc_push_voucher.SEGMENT12 IS 'HGIC_COA_FUT1 备用段1';
COMMENT ON COLUMN acc_push_voucher.SEGMENT13 IS 'HGIC_COA_FUT2 备用段2';
COMMENT ON COLUMN acc_push_voucher.REFERENCE3 IS '必须为空';
COMMENT ON COLUMN acc_push_voucher.REFERENCE5 IS '凭证说明说明。需要包含会计引擎的生成的总账凭证编号。';
COMMENT ON COLUMN acc_push_voucher.voucher_no IS 'I17凭证号';
COMMENT ON COLUMN acc_push_voucher.voucher_dtl_id IS 'I17凭证明细ID';
COMMENT ON COLUMN acc_push_voucher.push_type IS '推送类型';

-- 创建序列用于INTERFACE_ID自增
CREATE SEQUENCE seq_acc_push_voucher_id
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- 设置序列归属
ALTER SEQUENCE seq_acc_push_voucher_id OWNED BY acc_push_voucher.INTERFACE_ID;

-- 设置默认值
ALTER TABLE acc_push_voucher ALTER COLUMN INTERFACE_ID SET DEFAULT nextval('seq_acc_push_voucher_id');	

drop table acc_generalledger_push_log;
CREATE TABLE acc_generalledger_push_log (
    push_log_id bigint PRIMARY KEY,
    entity_id BIGINT,
    book_code varchar(8),
    year_month VARCHAR(6),
    action_no varchar(25),
    execution_status VARCHAR(1),
    total_num INT,
    total_success INT,
    total_fail INT,
    pusher_id BIGINT,
    push_start_data TIMESTAMP,
    push_end_data TIMESTAMP,
    fail_message varchar(1000)
);

COMMENT ON TABLE acc_generalledger_push_log IS '总账推送日志表，用于记录每次将会计数据推送至总账系统的操作记录';

COMMENT ON COLUMN acc_generalledger_push_log.push_log_id IS '日志主键，自增或唯一标识一次推送记录';
COMMENT ON COLUMN acc_generalledger_push_log.entity_id IS '账套ID或组织ID，用于标识所属实体';
COMMENT ON COLUMN acc_generalledger_push_log.book_code IS '账簿代码，标识推送目标的总账账簿';
COMMENT ON COLUMN acc_generalledger_push_log.year_month IS '期间（年月），格式如202405，标识数据所属期间';
COMMENT ON COLUMN acc_generalledger_push_log.action_no IS '推送动作编号，用于标识一次具体的推送任务';
COMMENT ON COLUMN acc_generalledger_push_log.execution_status IS '执行状态 0：待执行| 1：执行中 | 2：执行成功 | 3： 执行失败';
COMMENT ON COLUMN acc_generalledger_push_log.total_num IS '本次推送的总数据量';
COMMENT ON COLUMN acc_generalledger_push_log.total_success IS '推送成功的数据条数';
COMMENT ON COLUMN acc_generalledger_push_log.total_fail IS '推送失败的数据条数';
COMMENT ON COLUMN acc_generalledger_push_log.pusher_id IS '推送人ID，记录是谁执行了推送动作';
COMMENT ON COLUMN acc_generalledger_push_log.push_start_data IS '推送时间，即推送操作发生的时间(推送開始時間)';
COMMENT ON COLUMN acc_generalledger_push_log.push_end_data IS '推送时间，即推送操作发生的时间(推送結束時間)';
COMMENT ON COLUMN acc_generalledger_push_log.fail_message IS '失败异常信息';


drop sequence if exists acc_seq_generalledger_push_log;
CREATE SEQUENCE acc_seq_generalledger_push_log
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

drop table acc_conf_reclassify_mapping;
CREATE TABLE acc_conf_reclassify_mapping (
                                             id int8 NOT NULL,
                                             dr_transfer_account_code varchar(64) NULL,
                                             dr_transfer_account_id int8 NULL,
                                             cr_transfer_account_code varchar(64) NULL,
                                             cr_transfer_account_id int8 NULL,
                                             transfer_entry_code bpchar(1) NULL,
                                             reclassify_account_code varchar(64) NULL,
                                             reclassify_account_id int8 NULL,
                                             reclassify_entry_code bpchar(1) NULL,
                                             CONSTRAINT acc_conf_reclassify_mapping_pk PRIMARY KEY (id)
);
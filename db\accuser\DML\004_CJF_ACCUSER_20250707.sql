TRUNCATE TABLE acc_conf_checkrule;
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (386, 1, 'BookI17', 41, 'RD1741008', 'Non blank inspection of domestic and foreign identifiers for reinsurance business account data', '再保业务账数据海内外标识非空检验', '再保待入賬數據海內外標識非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and OFFSHORE_IS IS NULL AND upper(expenses_type_code) NOT IN (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''JXS'')', '101', 'The overseas and domestic identification information of the reinsurance business account data is empty, and the posting dimension is missing. Please check!', '再保业务账数据海内外标识信息为空，缺少入账维度，请检查！', '再保业务账数据海内外标识信息为空，缺少入账维度，请检查！', '0', '数据检查规则', '2023-05-22 16:16:36', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (342, 1, 'BookI17', 45, 'RD1745001', 'Non empty verification of voucher type for expense allocation business ledger data', '费用分摊业务账数据凭证类型非空检验', '費用分攤待入賬數據憑證類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_DATACHECK_NONCOUNTER_CS_BCR'') and posting_type_code IS NULL', '101', 'The voucher type information of the expense allocation business account data is empty and the posting dimension is missing. Please check!', '费用分摊业务账数据凭证类型信息为空，缺少入账维度，请检查！', '费用分摊业务账数据凭证类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (343, 1, 'BookI17', 45, 'RD1745002', 'Non null verification of data source for expense allocation business ledger', '费用分摊业务账数据数据来源非空检验', '費用分攤待入賬數據數據來源非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_DATACHECK_NONCOUNTER_CS_BCR'') and PROC_ID IS NULL', '101', 'The data source information of the expense allocation business account is empty, and the posting dimension is missing. Please check!', '费用分摊业务账数据数据来源信息为空，缺少入账维度，请检查！', '费用分摊业务账数据数据来源信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (369, 1, 'BookI17', 39, 'RD1739004', 'Non empty inspection of expense type in underwriting business account data', '承保业务账数据费用类型非空检验', '承保待入賬數據費用類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and expenses_type_code IS NULL', '101', 'The expense type information in the underwriting business account data is empty and the posting dimension is missing. Please check!', '承保业务账数据费用类型信息为空，缺少入账维度，请检查！', '承保业务账数据费用类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (383, 1, 'BookI17', 41, 'RD1741005', 'Verification of non empty voucher type for reinsurance business account data', '再保业务账数据凭证类型非空检验', '再保待入賬數據憑證類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code IS NULL', '101', 'The voucher type information of the reinsurance business account data is empty and the posting dimension is missing. Please check!', '再保业务账数据凭证类型信息为空，缺少入账维度，请检查！', '再保业务账数据凭证类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (273, 1, 'BookI17', 23, 'RC172301', 'Entry Rule Check', '入账规则检查', '入场规则检查', '2', '2021-04-21 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT DISTINCT entry_rule_id FROM ACCUSER.ACC_conf_entryrule ', '110', 'Entry rules are not configured!', '未配置入账规则！', '未配置入帳規則！', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (264, 1, 'BookI17', 55, 'RULE55', 'Business/Finance Reconciliation[PY]', '业财对账[收付]', '業財對賬[收付]', '2', '2021-02-02 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT d.entry_data_id FROM accuser.acc_buss_entry_data_{entity_id}_{year_month}_{book_code} D left join accuser.acc_dap_entry_data_{entity_id}_{year_month} A on A.entry_data_id = D.entry_data_id left join accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} B on B.VOUCHER_ID = D.VOUCHER_ID and A.PROC_ID = B.PROC_ID AND B.book_code = D.book_code WHERE A.PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code = ''ACC_ACCOUNTENTRY_PY_ACR'') AND B.voucher_no is null', '111', 'Business/Finance Reconciliation[PY]', '业财对账[收付]', '業財對賬[收付]', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (320, 1, 'BookI17', 66, 'RL175501', 'Account closing data check', '关账数据检查', '關賬數據檢查', '2', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '0', 'select count(act_buss_id) from bpluser.bms_buss_action_state  where proc_id in (52,53,54,55,56,57) and state<>''1''', '111', 'Result data with financial reconciliation error', '存在业财对账错误的结果数据', '存在業財對帳錯誤的結果數據', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (324, 1, 'BookI17', 67, 'RL175503', 'Closing data check - multi criteria reconciliation result check', '关账数据检查-多准则对账结果检查', '關帳數據檢查-多準則對帳結果檢查', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '1', 'select count(recon_rst_dtl_id)from accuser.acc_buss_multi_reconrst ajoin accuser.acc_buss_multi_reconrstdtl bon a.recon_rst_id = b.recon_rst_idwhere a.entity_id = {entity_id}and a.book_code = {book_code}and a.year_month = {year_month}and b.source_type = ''1'' ', '111', 'There is no multi criteria reconciliation result', '不存在多准则对账结果', '不存在多準則對賬結果', '0', NULL, '2022-08-10 11:27:12', 1, '0', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (304, 1, 'BookI17', 55, 'RULE5501', 'Business/Finance Reconciliation Numbers[PY]', '业财对账条数检查（收付）', '業財對賬條數檢查（收付）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', ' select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID WHERE  B.PROC_ID = (SELECT PROC_ID FROM BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_PY_ACR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.account_id_dr IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_CR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''D'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_PY_ACR'')) t', '111', 'Check the number of data: the total number of debit (PY) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据借方（收付）总条数与生成凭证明细的数据总条数是否一致', '檢查數據條數:待入賬數據借方（收付）總條數與生成憑證明細的數據總條數是否一致', '1', NULL, '2023-08-17 16:18:46.374', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:18:46.375');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (353, 1, 'BookI17', 244, 'RD17244001', 'Data platform data to accounting integrity verification', '数据平台数据到会计完整性校验', '数据平台数据到会计完整性校验', '1', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select count(t.bu_voucher_date) from dmuser.dm_acc_payment t where entity_id = {entity_id} and to_char(bu_voucher_date , ''yyyymm'') = {year_month} and not exists (select 1 from accuser.acc_dap_entry_data_{entity_id}_{year_month} ed where t.id = ed.business_id)', '101', 'There is currently data on the data platform that has not been obtained yet!', '数据平台收付数据截至目前存在还未获取到的数据！', '数据平台收付数据截至目前存在还未获取到的数据！', '1', '数据检查规则', '2023-05-22 14:53:33', 1, '1', NULL, 3, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (334, 1, 'BookI17', 45, 'RD174501', 'Check whether the data of expense allocation to be posted is blank', '费用分摊待入账数据是否为空检验', '費用分攤待入帳數據是否為空檢驗', '1', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '0', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACCUSER.ACC_DATACHECK_NONCOUNTER_CS_BCR'')', '101', 'There is no expense allocation data to be posted!', '不存在费用分摊待入账数据！', '不存在費用分攤待入帳數據！', '1', NULL, '2023-02-22 17:43:51', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (321, 1, 'BookI17', 66, 'RL175502', 'Account closing data check', '关账数据检查', '關賬數據檢查', '2', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '1', 'select count(act_buss_id) from bpluser.bms_buss_action_state  where proc_id in (52,53,54,55,56,57)', '111', 'error！', '错误！', '錯誤！', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (298, 1, 'BookI17', 244, 'RC171406', 'Check whether the amount of data to be posted is empty', '待入账数据金额是否为空校验', '待入帳數據金額是否為空校驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(entry_data_id) from accuser.acc_dap_entry_data_{entity_id}_{year_month} where amount is null', '101', 'The field center of data table to be amount is empty', '待入账数据表字段amount为空', '待入賬數據表字段amount為空', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (378, 1, 'BookI17', 40, 'RD1740007', 'Non null testing of the measurement model for claims business ledger data', '理赔业务账数据计量模型非空检验', '理賠待入賬數據計量模型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and EVALUATE_APPROACH IS NULL AND upper(expenses_type_code) NOT IN (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'')', '101', 'The measurement model information of the claims business account data is empty and the posting dimension is missing. Please check!', '理赔业务账数据计量模型信息为空，缺少入账维度，请检查！', '理赔业务账数据计量模型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2023-05-22 14:50:33', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (362, 1, 'BookI17', 24, 'RC172401', 'Whether the rule is configured', '检查校验规则是否有配置', '檢查校驗規則是否有配置', '2', '2021-04-21 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT DISTINCT check_rule_id FROM acc_conf_checkrule where VALID_IS = ''1'' and AUDIT_STATE=''1''', '110', 'Check rules not configured!', '未配置检查规则！', '未配置檢查規則！', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (292, 1, 'BookI17', 244, 'RC171411', 'Check whether the data Department to be posted is empty', '待入账数据部门是否为空校验', '待入帳數據部門是否為空校驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(entry_data_id) from accuser.acc_dap_entry_data_{entity_id}_{year_month} where dept_id is null', '101', 'The field center of data table to be dept_id is empty', '待入账数据表字段dept_id为空', '待入賬數據表字段dept_id為空', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (322, 1, 'BookI17', 67, 'RL175601', 'Check whether the multi criteria data reconciliation is passed', '检查多准则数据对账是否通过', '檢查多準則數據對帳是否通過', '2', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '1', 'select  count(recon_rst_id)  from accuser.acc_buss_multi_reconrst where reconcil_result = ''1''', '111', 'There is no multi criteria data reconciliation data that has passed the check!', '不存在检查通过的多准则数据对账数据！', '不存在檢查通過的多準則數據對帳數據！', '0', NULL, '2022-08-10 11:27:12', 1, '0', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (294, 1, 'BookI17', 244, 'RC171409', 'Check whether the expense type of data to be posted is blank', '待入账数据费用类型是否为空校验', '待入帳數據費用類型是否為空校驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(entry_data_id) from accuser.acc_dap_entry_data_{entity_id}_{year_month} where expenses_type_code is null', '101', 'The field center of data table to be fee_type is empty', '待入账数据表字段fee_type为空', '待入賬數據表字段fee_type為空', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (339, 1, 'BookI17', 53, 'RULE5304', 'Business/Finance Reconciliation Numbers[CL]', '业财对账条数检查（理赔）', '業財對賬條數檢查（理賠）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID WHERE  B.PROC_ID = (SELECT PROC_ID FROM BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_CL_ACR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.account_id_dr IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_CR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''D'' and voucher_id in (select voucher_id from accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} where entity_id = {entity_id} and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR''))) t', '111', 'Check the number of data: the total number of debit (CL) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据借方（理赔）总条数与生成凭证明细的数据总条数不一致', '檢查數據條數:待入賬數據借方（理賠）總條數與生成憑證明細的數據總條數不一致', '1', NULL, '2023-08-17 16:03:59.161', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:03:59.162');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (307, 1, 'BookI17', 56, 'RULE56', 'Business/Finance Reconciliation[QTP]', '业财对账[计量]', '業財對賬[計量]', '2', '2021-02-02 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT d.entry_data_id FROM accuser.acc_buss_entry_data_{entity_id}_{year_month}_{book_code} D left join accuser.acc_dap_entry_data_{entity_id}_{year_month} A on A.entry_data_id = D.entry_data_id left join accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} B on B.VOUCHER_ID = D.VOUCHER_ID and A.PROC_ID = B.PROC_ID AND B.book_code = D.book_code WHERE A.PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code = ''ACC_ACCOUNTENTRY_QTP_BCR'') AND B.voucher_no is null', '111', 'Business/Finance Reconciliation[QTP]', '业财对账[计量]', '業財對賬[計量]', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (261, 1, 'BookI17', 54, 'RULE5402', 'Business/Finance Reconciliation Numbers[RI]', '业财对账条数检查（再保）', '業財對賬條數檢查（再保）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', ' select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID WHERE  B.PROC_ID = (SELECT PROC_ID FROM BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_RI_ACR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.account_id_cr IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_DR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''C'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'')) t', '111', 'Check the number of data: the total number of credit (RI) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据贷方（再保）总条数与生成凭证明细的数据总条数不一致', '檢查數據條數:待入賬數據貸方（再保）總條數與生成憑證明細的數據總條數不一致', '1', NULL, '2023-08-17 16:15:36.591', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:15:36.593');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (301, 1, 'BookI17', 364, 'RYC0201', 'Current month balance consistency check - account balance', '当月科目余额一致性检查', '當月科目餘額一致性檢查', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(account_id) from accuser.acc_buss_ledger_balance t where entity_id = {entity_id}     and book_code = {book_code}     and year_month = {year_month}     and (opening_balance_cu + (debit_amount_cu - credit_amount_cu)  <>closing_balance_cu)', '111', 'Account balance of the current month closing balance is not equal to opening balance plus voucher de', '当月科目余额期末余额不等于期初余额加凭证借贷发生额', '當月科目餘額期末餘額不等於期初餘額加憑證借貸發生額', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (355, 1, 'BookI17', 244, 'RD17244003', 'Verification of Accounting Integrity from Measurement Platform Data', '计量平台数据到会计完整性校验', '计量平台数据到会计完整性校验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT COUNT(EM.EVALUATE_MAIN_ID) FROM ATRUSER.ATR_BUSS_EVALUATE_MAIN EM WHERE ENTITY_ID = {entity_id} AND YEAR_MONTH = {year_month} AND EM.CONFIRM_IS = ''1'' AND NOT EXISTS (SELECT 1 FROM ACCUSER.ACC_DAP_ENTRY_DATA_{entity_id}_{year_month} ED , ATRUSER.ATR_BUSS_EVALUATE_RESULT ER WHERE ER.EVALUATE_RESULT_ID = ED.BUSINESS_ID AND EM.EVALUATE_MAIN_ID = ER.EVALUATE_RESULT_ID)', '101', 'There is currently data on the measurement platform that has not been obtained yet!', '计量平台结果数据截至目前存在还未获取到的数据！', '计量平台结果数据截至目前存在还未获取到的数据！', '0', '数据检查规则', '2023-10-19 22:26:19.923', 1, '1', NULL, 3, 1, 1, '2022-08-10 11:27:12', '2023-10-19 22:26:19.926');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (381, 1, 'BookI17', 41, 'RD1741003', 'Reinsurance business account data business type non null verification', '再保业务账数据业务类型非空检验', '再保待入賬數據業務類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_ri_acr'') and business_source_code is null', '101', 'The reinsurance business account data and business type information are empty, and the posting dimension is missing. Please check!', '再保业务账数据业务类型信息为空，缺少入账维度，请检查！', '再保业务账数据业务类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (349, 1, 'BookI17', 44, 'RD1744004', 'Verification of non null data source for measurement business ledger data', '计量业务账数据数据来源非空检验', '計量待入賬數據數據來源非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and PROC_ID IS NULL', '101', 'The source information of the measurement business account data is empty and the posting dimension is missing. Please check!', '计量业务账数据数据来源信息为空，缺少入账维度，请检查！', '计量业务账数据数据来源信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (277, 1, 'BookI17', 42, 'RD174201', 'Check whether the data of receipt / payment to be posted is empty', '收付待入账数据是否为空检验', '收付待入帳數據是否為空檢驗', '1', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '1', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_PY_ACR'')', '101', 'Counter data check [receipt and Payment] no data', ' 柜面数据检查[收付]无数据', '櫃面數據檢查[收付]無數據', '1', NULL, '2023-05-22 16:18:57', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (351, 1, 'BookI17', 44, 'RD1744006', 'Non null test for measurement factor type in measurement business ledger data', '计量业务账数据计量因子类型非空检验', '計量待入賬數據計量因數類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and EXTEND_COLUMN7 IS NULL', '101', 'The measurement factor type information in the measurement business ledger data is empty and the posting dimension is missing. Please check!', '计量业务账数据计量因子类型信息为空，缺少入账维度，请检查！', '计量业务账数据计量因子类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (280, 1, 'BookI17', 44, 'RD17207', 'Check whether the measurement data to be posted is empty', '计量待入账数据是否为空检验', '計量待入帳數據是否為空檢驗', '1', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '1', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'')', '101', 'Non-Counter Data Check[QTP] no data!', '非柜面数据检查 [计量]无数据!', '非櫃面數據檢查 [計量]無數據!', '1', NULL, '2023-04-10 19:45:24', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (296, 1, 'BookI17', 244, 'RC171413', 'Check whether the accounting unit of the data to be posted is empty', '待入账数据核算单位是否为空校验', '待入帳數據核算單位是否為空校驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(entry_data_id) from accuser.acc_dap_entry_data where entity_id is null ', '000', 'The field center of data table to be posted_id is empty', '待入账数据表字段center_id为空', '待入賬數據表字段center_id為空', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (385, 1, 'BookI17', 41, 'RD1741007', 'Reinsurance business account data contract type non null test', '再保业务账数据合约类型非空检验', '再保待入賬數據合約類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and TREATY_TYPE_CODE IS NULL AND upper(expenses_type_code) NOT IN (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'')', '101', 'The contract type information of the reinsurance business account data is empty and the posting dimension is missing. Please check!', '再保业务账数据合约类型信息为空，缺少入账维度，请检查！', '再保业务账数据合约类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2023-05-22 14:51:45', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (288, 1, 'BookI17', 244, 'RC171401', 'Verification of posting node of data to be posted', '待入账数据的入账节点校验', '待入帳數據的入帳節點校驗', '2', '2021-10-25 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT entry_data_id FROM accuser.acc_dap_entry_data_{entity_id}_{year_month} T WHERE NOT EXISTS (SELECT 1 FROM bpluser.bms_conf_action_procdef b WHERE system_code = ''ACC'' AND T.proc_id = b.proc_id AND parent_proc_id = (SELECT proc_id FROM bpluser.bms_conf_action_procdef b WHERE proc_code = ''ACC_ACCOUNTENTRY''))', '101', 'Data not within the posting range', '不在入账范围内的数据', '不在入帳範圍內的數據', '1', NULL, '2023-08-17 17:24:38.702', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-17 17:24:38.703');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (338, 1, 'BookI17', 53, 'RULE5302', 'Business/Finance Reconciliation[CL]', '业财对账[理赔]', '業財對賬[理賠]', '2', '2021-02-02 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT d.entry_data_id FROM accuser.acc_buss_entry_data_{entity_id}_{year_month}_{book_code} D left join accuser.acc_dap_entry_data_{entity_id}_{year_month} A on A.entry_data_id = D.entry_data_id left join accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} B on B.VOUCHER_ID = D.VOUCHER_ID and A.PROC_ID = B.PROC_ID AND B.book_code = D.book_code WHERE A.PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code = ''ACC_ACCOUNTENTRY_CL_ACR'') AND B.voucher_no is null', '111', 'Business/Finance Reconciliation[CL]', '业财对账[理赔]', '業財對賬[理賠]', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (380, 1, 'BookI17', 41, 'RD1741002', 'Reinsurance business account data business source non null verification', '再保业务账数据业务来源非空检验', '再保待入賬數據業務來源非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_cl_acr'') and ri_arrangement_code is null', '101', 'The source information of the reinsurance business account data is empty and the posting dimension is missing. Please check!', '再保业务账数据业务来源信息为空，缺少入账维度，请检查！', '再保业务账数据业务来源信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (305, 1, 'BookI17', 55, 'RULE5502', 'Business/Finance Reconciliation Numbers[PY]', '业财对账条数检查（收付）', '業財對賬條數檢查（收付）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', ' select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID WHERE B.PROC_ID = (SELECT PROC_ID FROM BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_PY_ACR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.account_id_cr IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_DR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''C'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_PY_ACR'')) t', '111', 'Check the number of data: the total number of credit (PY) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据贷方（收付）总条数与生成凭证明细的数据总条数是否一致', '檢查數據條數:待入賬數據貸方（收付）總條數與生成憑證明細的數據總條數是否一致', '1', NULL, '2023-08-17 16:17:11.634', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:17:11.636');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (299, 1, 'BookI17', 1, 'RYC0101', 'Whether the voucher debit amount is equal to the account balance', '凭证借方发生额与科目余额是否相等', '凭证借方发生额与科目余额是否相等', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT a.account_id FROM (SELECT account_id, SUM(b.amount_cu) as amount_cu FROM accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} A LEFT JOIN accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b ON A.voucher_id = b.voucher_id WHERE  b.account_entry_code = ''C'' GROUP BY account_id) A LEFT JOIN (SELECT account_id, SUM(credit_amount_cu) as amount_cu FROM accuser.acc_buss_ledger_balance b WHERE entity_id = {entity_id} AND b.year_month ={year_month} AND b.book_code = {book_code} GROUP BY account_id) b ON A.account_id = b.account_id where (a.amount_cu - b.amount_cu) <> 0', '111', 'The field center of data table to be risk_code is empty', '凭证借方发生额与科目余额不等', '憑證借方發生額與科目餘額不等', '1', NULL, '2022-12-20 19:15:38', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (328, 1, 'BookI17', 68, 'RL175506', 'Account closing data check-Total amount of voucher data and account amount data', '关账数据检查-凭证数据与科目发生额数据总金额', '關帳數據檢查-憑證數據與科目發生額數據總金額', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(credit_amount_cu) from (SELECT sum(credit_amount_cu) credit_amount_cu FROM accuser.acc_buss_ledger_balance t LEFT JOIN bpluser.bbs_account ai ON t.account_id = ai.account_id where t.entity_id = {entity_id} and t.book_code= {book_code} and t.year_month = {year_month} and ai.account_level=1 union all select sum(case when b.account_entry_code = ''C'' then b.amount_cu else 0 end ) * (-1) credit_amount_cu from accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} a join accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b on a.voucher_id = b.voucher_id where a.valid_is = ''1'') g HAVING sum(credit_amount_cu) <> 0', '111', 'The voucher data is inconsistent with the total amount of the account amount data', '检查凭证数据与科目发生额数据总金额不一致', '檢查憑證數據與科目發生額數據總金額不一致', '0', NULL, '2023-10-21 19:37:53.582', 1, '1', NULL, 5, 1, 1, '2022-08-10 11:27:12', '2023-10-21 19:37:53.584');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (344, 1, 'BookI17', 45, 'RD1745003', 'Non empty inspection of expense type in expense allocation business ledger data', '费用分摊业务账数据费用类型非空检验', '費用分攤待入賬數據費用類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_DATACHECK_NONCOUNTER_CS_BCR'') and expenses_type_code IS NULL', '101', 'The expense type information in the expense allocation business account data is empty and the posting dimension is missing. Please check!', '费用分摊业务账数据费用类型信息为空，缺少入账维度，请检查！', '费用分摊业务账数据费用类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (345, 1, 'BookI17', 45, 'RD1745004', 'Expense Allocation Business Ledger Data 17 Expense Type Non Empty Verification', '费用分摊业务账数据17费用类型非空检验', '費用分攤待入賬數據17費用類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_DATACHECK_NONCOUNTER_CS_BCR'') and EXTEND_COLUMN5 IS NULL', '101', 'The expense allocation business account data 17 has empty expense type information and is missing the posting dimension. Please check!', '费用分摊业务账数据17费用类型信息为空，缺少入账维度，请检查！', '费用分摊业务账数据17费用类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (346, 1, 'BookI17', 44, 'RD1744001', 'Non null verification of the direction of data reinsurance for measurement business accounts', '计量业务账数据再保方向非空检验', '計量待入賬數據再保方向非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and RI_DIRECTION_CODE IS NULL', '101', 'The reinsurance direction information of the measurement business account data is empty and the posting dimension is missing. Please check!', '计量业务账数据再保方向信息为空，缺少入账维度，请检查！', '计量业务账数据再保方向信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (274, 1, 'BookI17', 39, 'RD173901', 'Check whether the underwriting data to be posted is empty', '承保待入账数据是否为空检验', '承保待入帳數據是否為空檢驗', '1', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'')', '101', 'There is no underwriting data to be recorded!', '不存在承保待入账数据！', '不存在承保待入帳數據！', '0', NULL, '2023-06-19 14:51:58', 1, '1', NULL, 9, 1, 1, '2022-08-10 11:27:12', '2025-06-17 14:23:32.956');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (348, 1, 'BookI17', 44, 'RD1744003', 'Non empty verification of voucher type for measurement business ledger data', '计量业务账数据凭证类型非空检验', '計量待入賬數據憑證類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and posting_type_code IS NULL', '101', 'The voucher type information of the measurement business account data is empty and the posting dimension is missing. Please check!', '计量业务账数据凭证类型信息为空，缺少入账维度，请检查！', '计量业务账数据凭证类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (350, 1, 'BookI17', 44, 'RD1744005', 'Non null test of measurement model for measurement business ledger data', '计量业务账数据计量模型非空检验', '計量待入賬數據計量模型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and EVALUATE_APPROACH IS NULL', '101', 'The measurement business account data measurement model information is empty and the posting dimension is missing. Please check!', '计量业务账数据计量模型信息为空，缺少入账维度，请检查！', '计量业务账数据计量模型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (352, 1, 'BookI17', 44, 'RD1744007', 'Non null testing of measurement factors in measurement business ledger data', '计量业务账数据计量因子非空检验', '計量待入賬數據計量因數非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and EXTEND_COLUMN4 IS NULL', '101', 'The measurement factor information in the measurement business account data is empty and the posting dimension is missing. Please check!', '计量业务账数据计量因子信息为空，缺少入账维度，请检查！', '计量业务账数据计量因子信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (370, 1, 'BookI17', 39, 'RD1739005', 'Non empty verification of underwriting business account data voucher type', '承保业务账数据凭证类型非空检验', '承保待入賬數據憑證類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code IS NULL', '101', 'The voucher type information of the underwriting business account data is empty and the posting dimension is missing. Please check!', '承保业务账数据凭证类型信息为空，缺少入账维度，请检查！', '承保业务账数据凭证类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (289, 1, 'BookI17', 244, 'RC171403', 'Check whether the insurance type of data to be posted is empty', '待入账数据险种是否为空校验', '待入帳數據險種是否為空校驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(entry_data_id) from accuser.acc_dap_entry_data_{entity_id}_{year_month} where risk_code is null', '101', 'The field center of data table to be risk_code is empty', '待入账数据表字段risk_code为空', '待入賬數據表字段risk_code為空', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (371, 1, 'BookI17', 39, 'RD1739006', 'Verification of non null data source for underwriting business ledger data', '承保业务账数据数据来源非空检验', '承保待入賬數據數據來源非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and PROC_ID IS NULL', '101', 'The source information of the underwriting business account data is empty and the posting dimension is missing. Please check!', '承保业务账数据数据来源信息为空，缺少入账维度，请检查！', '承保业务账数据数据来源信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (270, 1, 'BookI17', 22, 'RC172202', 'Accounting period allocation rules', '会计期间配置规则状态检查', '會計期間配置規則狀態檢查', '2', '2021-01-31 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT period_id  FROM ACCUSER.acc_conf_accountperiod where valid_is<>''1'' and entity_id = {entity_id} and book_code =  {book_code}  and year_month ={year_month} ', '111', 'Accounting period invalid status!', '会计期间无效状态！', '會計期間無效狀態！', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (295, 1, 'BookI17', 244, 'RC171414', 'Check whether the accounting period of data to be posted is empty', '数据检查待入账数据会计期间是否为空', '數據檢查待入帳數據會計期間是否為空', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(entry_data_id) from accuser.acc_dap_entry_data where year_month is null ', '000', 'The field center of data table to be year_month is empty', '待入账数据表字段year_month为空', '待入賬數據表字段year_month為空', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (376, 1, 'BookI17', 40, 'RD1740005', 'Non empty verification of claim business account data voucher type', '理赔业务账数据凭证类型非空检验', '理賠待入賬數據憑證類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and posting_type_code IS NULL', '101', 'The voucher type information of the claim business account data is empty and the posting dimension is missing. Please check!', '理赔业务账数据凭证类型信息为空，缺少入账维度，请检查！', '理赔业务账数据凭证类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (379, 1, 'BookI17', 41, 'RD1741001', 'Reinsurance Business Account Data Reinsurance Direction Non Empty Inspection', '再保业务账数据再保方向非空检验', '再保待入賬數據再保方向非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and RI_DIRECTION_CODE IS NULL', '101', 'The reinsurance direction information of the reinsurance business account data is empty, and the posting dimension is missing. Please check!', '再保业务账数据再保方向信息为空，缺少入账维度，请检查！', '再保业务账数据再保方向信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (382, 1, 'BookI17', 41, 'RD1741004', 'Reinsurance business account data expense type non null verification', '再保业务账数据费用类型非空检验', '再保待入賬數據費用類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and expenses_type_code IS NULL', '101', 'The expense type information in the reinsurance business account data is empty and the posting dimension is missing. Please check!', '再保业务账数据费用类型信息为空，缺少入账维度，请检查！', '再保业务账数据费用类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (384, 1, 'BookI17', 41, 'RD1741006', 'Verification of non null data source for reinsurance business account data', '再保业务账数据数据来源非空检验', '再保待入賬數據數據來源非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and PROC_ID IS NULL', '101', 'The source information of the reinsurance business account data is empty and the posting dimension is missing. Please check!', '再保业务账数据数据来源信息为空，缺少入账维度，请检查！', '再保业务账数据数据来源信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (337, 1, 'BookI17', 52, 'RULE5201', 'Business/Finance Reconciliation Numbers[UW]', '业财对账条数检查（承保）', '業財對賬條數檢查（承保）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID  WHERE  B.PROC_ID = (SELECT PROC_ID FROM  BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_UW_ACR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.account_id_dr IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_CR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''D'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'')) t', '111', 'Check the number of data: the total number of debit (UW) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据借方（承保）总条数与生成凭证明细的数据总条数不一致', '檢查數據條數:待入賬數據借方（承保）總條數與生成憑證明細的數據總條數不一致', '1', NULL, '2023-08-17 15:53:35.918', 1, '1', NULL, 6, 1, 1, '2022-08-10 11:27:12', '2023-08-17 15:53:35.919');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (287, 1, 'BookI17', 244, 'RC171417', 'Data check - synchronous quantification platform data task status', '数据检查-同步计量平台数据任务状态', '數據檢查-同步計量平臺數據任務狀態', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT count(task_log_id)FROM bpluser.bpl_log_pub_task tl LEFT JOIN bpluser.bpl_qrtz_conf_task_detail dtl ON tl.task_detail_id = dtl.task_detail_id LEFT JOIN bpluser.bpl_qrtz_conf_task ct ON ct.conf_task_id = dtl.conf_task_idWHERE tl.entity_id = {entity_id}and tl.year_month = {year_month}and tl.task_status <> ''2''and ct.task_group = ''ATR'' ', '101', 'The scheduled task (Quantification Platform) contains input data that is not normally executed', '定时任务（计量平台）含有未正常执行的输入数据', '定時任務（計量平臺）含有未正常執行的輸入數據', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (262, 1, 'BookI17', 54, 'RULE5401', 'Business/Finance Reconciliation Numbers[RI]', '业财对账条数检查（再保）', '業財對賬條數檢查（再保）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID WHERE B.PROC_ID = (SELECT PROC_ID FROM BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_RI_ACR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.account_id_dr IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_CR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''D'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'')) t', '111', 'Check the number of data: the total number of debit (RI) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据借方（再保）总条数与生成凭证明细的数据总条数不一致', '檢查數據條數:待入賬數據借方（再保）總條數與生成憑證明細的數據總條數不一致', '1', NULL, '2023-08-17 16:14:07.576', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:14:07.586');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (311, 1, 'BookI17', 57, 'RULE5701', 'Business/Finance Reconciliation Numbers[EXP]', '业财对账条数检查（费用分摊）', '業財對賬條數檢查（費用分攤）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID WHERE  B.PROC_ID = (SELECT PROC_ID FROM BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_EXP_BCR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.account_id_dr IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_CR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''D'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_EXP_BCR'')) t', '111', 'Check the number of data: the total number of debit (CS) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据借方（费用分摊）总条数与生成凭证明细的数据总条数不一致', '檢查數據條數:待入賬數據借方（費用分攤）總條數與生成憑證明細的數據總條數不一致', '1', NULL, '2023-08-17 16:23:34.422', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:23:34.424');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (375, 1, 'BookI17', 40, 'RD1740004', 'Claim business ledger data expense type non null verification', '理赔业务账数据费用类型非空检验', '理賠待入賬數據費用類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and expenses_type_code IS NULL', '101', 'The expense type information in the claims business account data is empty and the posting dimension is missing. Please check!', '理赔业务账数据费用类型信息为空，缺少入账维度，请检查！', '理赔业务账数据费用类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (310, 1, 'BookI17', 57, 'RULE5702', 'Business/Finance Reconciliation Numbers[EXP]', '业财对账条数检查（费用分摊）', '業財對賬條數檢查（費用分攤）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID WHERE  B.PROC_ID = (SELECT PROC_ID FROM BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_EXP_BCR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.account_id_cr IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_DR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''C'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_EXP_BCR'')) t', '111', 'Check the number of data: the total number of credit (CS) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据贷方（费用分摊）总条数与生成凭证明细的数据总条数不一致', '檢查數據條數:待入賬數據貸方（費用分攤）總條數與生成憑證明細的數據總條數不一致', '1', NULL, '2023-08-17 16:25:25.611', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:25:25.612');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (325, 1, 'BookI17', 67, 'RL175602', 'Account closing data check', '关账数据检查', '關賬數據檢查', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(recon_rst_dtl_id) from accuser.acc_buss_multi_reconrst a join accuser.acc_buss_multi_reconrstdtl b on a.recon_rst_id = b.recon_rst_id where a.entity_id = {entity_id} and  book_code = {book_code} and a.year_month = {year_month} and a.reconcil_result= ''3'' ', '111', 'Incomplete multi criteria reconciliation results', '多准则对账结果不全通过', '多準則對賬結果不全通過', '1', NULL, '2023-08-17 16:50:17.753', 1, '1', NULL, 6, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:50:17.754');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (293, 1, 'BookI17', 244, 'RC171410', 'Check whether the currency of data to be posted is empty', '待入账数据币别是否为空校验', '待入帳數據幣別是否為空校驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(entry_data_id) from accuser.acc_dap_entry_data_{entity_id}_{year_month} where currency_code is null', '101', 'The field center of data table to be currency is empty', '待入账数据表字段currency为空', '待入賬數據表字段currency為空', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (315, 1, 'BookI17', 63, 'RL175201', 'Account closing data check[Non-Counter Data]', '关账数据检查（非柜面数据检查）', '關賬數據檢查（非櫃面數據檢查）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT count(act_buss_id) FROM bpluser.bms_buss_action_state WHERE STATE <> ''1''AND entity_id = {entity_id} AND year_month = {year_month} AND proc_id IN ( SELECT proc_id FROM bpluser.bms_conf_action_procdef WHERE system_code = ''ACC'' AND proc_code IN ( ''ACC_DATACHECK_NONCOUNTER_QTP_BCR'', ''ACC_DATACHECK_NONCOUNTER_EXP_BCR'' ) )', '101', 'The node (Non-Counter Data Check) configuration rule failed', '节点（非柜面数据检查）配置规则不通过', '節點（非櫃面數據檢查）配置規則不通過', '1', NULL, '2023-08-17 16:36:42.468', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:36:42.469');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (297, 1, 'BookI17', 244, 'RC171412', 'Check whether the business type of data to be posted is empty', '待入账数据业务类型是否为空校验', '待入帳數據業務類型是否為空校驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(entry_data_id) from accuser.acc_dap_entry_data_{entity_id}_{year_month} where business_source_code is null and posting_type_code != ''05'' and extend_column5!= ''1''', '101', 'The field center of data table to be business_type is empty', '待入账数据表字段business_type为空', '待入賬數據表字段business_type為空', '1', NULL, '2023-03-21 18:22:22', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (275, 1, 'BookI17', 40, 'RD174001', 'Check whether the claim data to be posted is empty', '理赔待入账数据是否为空检验', '理賠待入帳數據是否為空檢驗', '1', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '1', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'')', '101', 'Counter data check [claim settlement] no data', '不存在理赔待入账数据！', '不存在理賠待入帳數據！', '1', NULL, '2023-06-19 14:52:12', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (303, 1, 'BookI17', 364, 'RYC0203', 'Whether the account system summary amount of account balance is equal', '科目余额的科目体系汇总金额是否相等', '科目餘額的科目體系匯總金額是否相等', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select account_id from accuser.acc_buss_ledger_balance t where entity_id = {entity_id}     and book_code = {book_code}     and year_month = {year_month}     and EXISTS (select 1 from accuser.acc_buss_ledger_balance t1 left join  bpluser.bbs_account item on t1.account_id = item.account_id        where t.entity_id = t1.entity_id          and t.book_code= t1.book_code          and t.year_month= t1.year_month          and t.currency_code= t1.currency_code          and t.account_id = item.upper_account_id          group  by item.upper_account_id,t1.currency_code          HAVING sum(t1.closing_balance_cu) <> t.closing_balance_cu     ) ', '111', 'The current month''s account balance exists. The parent account balance is not equal to the total child account balance', '当月科目余额存在父级科目余额不等于子级科目余额总数', '當月科目餘額存在父級科目餘額不等於子級科目餘額總數', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (314, 1, 'BookI17', 62, 'RL175101', 'Check whether all the counter data of account closing data pass ', '关账数据检查柜面数据是否都检查通过', '關賬數據檢查櫃面數據是否都檢查通過', '2', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '1', 'select count(act_buss_id) from bpluser.bms_buss_action_state  where proc_id in (39,40,41,42,43)', '111', 'Check of closing data: check whether the data to be recorded on the counter is checked to ensure the', '关账数据检查:柜面待入账数据是否有检查，保证入账正确性', '關賬數據檢查:櫃面待入賬數據是否有檢查，保證入账正確性', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2024-11-28 10:33:33.019');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (327, 1, 'BookI17', 68, 'RL175504', 'Account closing data check', '关账数据检查-凭证数据与科目发生额数据总金额', '關賬數據檢查', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(debit_amount_cu) from (SELECT sum(debit_amount_cu) debit_amount_cu FROM accuser.acc_buss_ledger_balance t LEFT JOIN bpluser.bbs_account ai ON t.account_id = ai.account_id where t.entity_id = {entity_id} and t.book_code= {book_code} and t.year_month = {year_month} and ai.final_level_is = ''1'' union all select sum(case when b.account_entry_code = ''D'' then b.amount_cu else 0 end ) * (-1) debit_amount_cu from accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} a join accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b on a.voucher_id = b.voucher_id where a.valid_is = ''1'') g HAVING sum(debit_amount_cu) <> 0', '111', 'The voucher data is inconsistent with the total amount of the account amount data', '检查凭证数据与科目发生额数据总金额不一致', '檢查憑證數據與科目發生額數據總金額不一致', '0', NULL, '2023-10-19 23:21:33.17', 1, '1', NULL, 7, 1, 1, '2022-08-10 11:27:12', '2023-10-19 23:21:33.172');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (335, 1, 'BookI17', 52, 'RULE52', 'Business/Finance Reconciliation[UW]', '业财对账[承保]', '業財對賬[承保]', '2', '2021-01-20 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT d.entry_data_id FROM accuser.acc_buss_entry_data_{entity_id}_{year_month}_{book_code} D left join accuser.acc_dap_entry_data_{entity_id}_{year_month} A on A.entry_data_id = D.entry_data_id left join accuser.ACC_BUSS_VOUCHER_{entity_id}_{year_month}_{book_code} B on B.VOUCHER_ID = D.VOUCHER_ID and A.PROC_ID = B.PROC_ID AND B.book_code = D.book_code WHERE A.PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code = ''ACC_ACCOUNTENTRY_UW_ACR'') AND B.voucher_no is null', '111', 'Business/Finance Reconciliation[UW]', '业财对账[承保]', '業財對賬[承保]', '1', NULL, '2022-12-20 18:50:27', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (364, 1, 'BookI17', 365, 'RYC0301', 'Account balance trial balance (Terminal Balance)', '科目余额试算是否平衡（期末余额）', '科目余额试算是否平衡（期末餘額）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(closing_balance_cu) from accuser.acc_buss_ledger_balance b left join bpluser.bbs_account v on v.book_code={book_code} and b.account_id=v.account_id where b.entity_id = {entity_id}  and b.year_month ={year_month} and b.book_code =  {book_code} and account_category_code in (''1'',''2'',''3'',''4'') and v.final_level_is=''1''  having sum(closing_balance_cu)<>0', '111', 'The trial balance of account balance is unbalanced. The sum of closing balance of asset liability ac', '科目余额试算不平衡，资产负债科目+权益科目+损益科目的期末余额和不等于0', '科目餘額試算不平衡，資產負債科目+權益科目+損益科目的期末餘額和不等於0', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (290, 1, 'BookI17', 244, 'RC171416', 'Data check - synchronize expense allocation data task status', '数据检查-同步费用分摊数据任务状态', '數據檢查-同步費用分攤數據任務狀態', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT count(task_log_id) FROM bpluser.bpl_log_pub_task tl LEFT JOIN bpluser.bpl_qrtz_conf_task_detail dtl ON tl.task_detail_id = dtl.task_detail_id LEFT JOIN bpluser.bpl_qrtz_conf_task ct ON ct.conf_task_id = dtl.conf_task_id WHERE tl.entity_id = {entity_id} and tl.year_month = {year_month} and tl.task_status <> ''2'' and ct.task_group = ''CSH'' ', '101', 'The scheduled task (Cost Sharing) contains input data that is not normally executed', '定时任务（费用分摊）含有未正常执行的输入数据', '定時任務（費用分攤）含有未正常執行的輸入數據', '1', NULL, '2023-10-19 16:20:56.496', 1, '1', NULL, 3, 1, 1, '2022-08-10 11:27:12', '2023-10-19 16:20:56.499');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (365, 1, 'BookI17', 365, 'RYC0302', 'Account balance trial balance (Initial Balance)', '科目余额试算平衡验证(期初)', '科目餘額試算平衡驗證(期初)', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(opening_balance_cu) from accuser.acc_buss_ledger_balance b left join bpluser.bbs_account v on v.book_code={book_code} and b.account_id=v.account_id where b.entity_id = {entity_id}  and b.year_month ={year_month} and b.book_code =  {book_code} and account_category_code in (''1'',''2'',''3'',''4'') and v.account_level=''1'' having sum(opening_balance_cu)<>0', '111', 'Trial balance of currency conversion amount of current month account balance is unbalanced', '当月科目余额折币发生额试算不平衡', '當月科目餘額折幣發生額試算不平衡', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (263, 1, 'BookI17', 54, 'RULE54', 'Business/Finance Reconciliation[RI]', '业财对账[再保]', '業財對賬[再保]', '2', '2021-02-02 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT d.entry_data_id FROM accuser.acc_buss_entry_data_{entity_id}_{year_month}_{book_code} D left join accuser.acc_dap_entry_data_{entity_id}_{year_month} A on A.entry_data_id = D.entry_data_id left join accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} B on B.VOUCHER_ID = D.VOUCHER_ID and A.PROC_ID = B.PROC_ID AND B.book_code = D.book_code WHERE A.PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code = ''ACC_ACCOUNTENTRY_RI_ACR'') AND B.voucher_no is null', '111', 'Business/Finance Reconciliation[RI]', '业财对账[再保]', '業財對賬[再保]', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (374, 1, 'BookI17', 40, 'RD1740003', 'Claim business ledger data business type non null verification', '理赔业务账数据业务类型非空检验', '理賠待入賬數據業務類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_cl_acr'') and business_source_code is null', '101', 'The claim business account data and business type information are empty, and the posting dimension is missing. Please check!', '理赔业务账数据业务类型信息为空，缺少入账维度，请检查！', '理赔业务账数据业务类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (340, 1, 'BookI17', 53, 'RULE5305', 'Business/Finance Reconciliation Numbers[CL]', '业财对账条数检查（理赔）', '業財對賬條數檢查（理賠）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(num) diff from (select count(b.entry_data_id) num from accuser.acc_buss_entry_data_{entity_id}_{year_month}_{book_code} b left join accuser.acc_buss_entry_data_detail_{entity_id}_{year_month}_{book_code} c on b.buss_entry_id = c.buss_entry_id where  b.proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_cl_acr'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id and ((c.account_id_cr is not null and a.amount >= 0) or (c.account_id_dr is not null and a.amount < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''c'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_cl_acr'')) t', '111', 'Check the number of data: the total number of credit (CL) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据贷方（理赔）总条数与生成凭证明细的数据总条数不一致', '檢查數據條數:待入賬數據貸方（理賠）總條數與生成憑證明細的數據總條數不一致', '1', NULL, '2023-08-17 16:05:41.751', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:05:41.753');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (302, 1, 'BookI17', 364, 'RYC0202', 'Consistency check of special balance in the current month', '当月专项余额一致性检查', '當月專項餘額一致性檢查', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(account_id) from accuser.acc_buss_article_balance t   where entity_id =  {entity_id}     and book_code = {book_code}     and year_month = {year_month}     and (opening_balance_cu + (debit_amount_cu - credit_amount_cu)  <>closing_balance_cu)', '111', 'Special balance of the current month closing balance is not equal to opening balance plus voucher de', '当月专项余额期末余额不等于期初余额加凭证借贷发生额', '當月专项餘額期末餘額不等於期初餘額加憑證借貸發生額', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (306, 1, 'BookI17', 56, 'RULE5602', 'Business/Finance Reconciliation Numbers[QTP]', '业财对账条数检查（计量平台）', '業財對賬條數檢查（計量平臺）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', ' select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID WHERE  B.PROC_ID = (SELECT PROC_ID FROM BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_QTP_BCR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.account_id_cr IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_DR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''C'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'')) t', '111', 'Check the number of data: the total number of credit (AC) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据贷方（计量平台）总条数与生成凭证明细的数据总条数不一致', '檢查數據條數:待入賬數據貸方（計量平臺）總條數與生成憑證明細的數據總條數不一致', '1', NULL, '2023-08-17 16:22:11.331', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:22:11.332');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (347, 1, 'BookI17', 44, 'RD1744002', 'Measurement business ledger data business type non null verification', '计量业务账数据业务类型非空检验', '計量待入賬數據業務類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_qtp_bcr'') and business_source_code is null', '101', 'The measurement business account data and business type information are empty, and the posting dimension is missing. Please check!', '计量业务账数据业务类型信息为空，缺少入账维度，请检查！', '计量业务账数据业务类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (286, 1, 'BookI17', 244, 'RC171404', 'Check whether the policy type of data to be posted is empty', '待入账数据保单类型是否为空校验', '待入帳數據保單類型是否為空校驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(entry_data_id) from accuser.acc_dap_entry_data_{entity_id}_{year_month} where posting_type_code is null', '101', 'The field center of data table to be post_type is empty', '待入账数据表字段post_type为空', '待入賬數據表字段post_type為空', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (341, 1, 'BookI17', 53, 'RULE53', 'Business/Finance Reconciliation Amount[CL]', '业财对账金额检查（理赔）', '業財對賬金額檢查（理賠）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(amount) from (select sum(amount) amount from accuser.acc_dap_entry_data_{entity_id}_{year_month} a left join accuser.acc_buss_entry_data_{entity_id}_{year_month}_{book_code} b on a.entry_data_id = b.entry_data_id left join accuser.acc_buss_entry_data_detail_{entity_id}_{year_month}_{book_code} c on b.buss_entry_id = c.buss_entry_id where  c.account_id_dr is not null and a.amount >= 0 and a.proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') union all select sum(abs(amount)) amount from accuser.acc_dap_entry_data_{entity_id}_{year_month} a left join accuser.acc_buss_entry_data_{entity_id}_{year_month}_{book_code} b on a.entry_data_id = b.entry_data_id left join accuser.acc_buss_entry_data_detail_{entity_id}_{year_month}_{book_code} c on b.buss_entry_id = c.buss_entry_id where  c.account_id_cr is not null and a.amount < 0 and a.proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') union all select sum(b.amount_cu) * -1 amount from accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} a join accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b on a.voucher_id = b.voucher_id where a.state = ''1''and b.account_entry_code=''D''and a.proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'')) t having sum(amount) <> 0', '111', 'Check data amount: the total amount of data to be posted (CL) is inconsistent with the total amount of data generated from vouchers', '检查数据金额:待入账数据（理赔）总金额与生成凭证的数据总金额不一致', '檢查數據金額:待入賬數據（理賠）總金額與生成憑證的數據總金額不一致', '1', NULL, '2023-08-17 16:11:28.204', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:11:28.205');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (309, 1, 'BookI17', 57, 'RULE57', 'Business/Finance Reconciliation[EXP]', '业财对账[费用分摊]', '業財對賬[費用分攤]', '2', '2021-02-02 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT d.entry_data_id FROM accuser.acc_buss_entry_data_{entity_id}_{year_month}_{book_code} D left join accuser.acc_dap_entry_data_{entity_id}_{year_month} A on A.entry_data_id = D.entry_data_id left join accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} B on B.VOUCHER_ID = D.VOUCHER_ID and A.PROC_ID = B.PROC_ID AND B.book_code = D.book_code WHERE A.PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code = ''ACC_ACCOUNTENTRY_EXP_BCR'') AND B.voucher_no is null', '111', 'Business/Finance Reconciliation[EXP]', '业财对账[费用分摊]', '業財對賬[費用分攤]', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (281, 1, 'BookI17', 102, 'RMC0203', 'Whether the account system summary amount of account balance is equal', '科目余额的科目体系汇总金额是否相等', '科目餘額的科目體系匯總金額是否相等', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select account_id from accuser.acc_buss_ledger_balance t where entity_id = {entity_id}     and book_code = {book_code}     and year_month = {year_month}     and EXISTS (select 1 from accuser.acc_buss_ledger_balance t1 left join  bpluser.bbs_account item on t1.account_id = item.account_id        where t.entity_id = t1.entity_id          and t.book_code= t1.book_code          and t.year_month= t1.year_month          and t.currency_code= t1.currency_code          and t.account_id = item.upper_account_id          group  by item.upper_account_id,t1.currency_code          HAVING sum(t1.closing_balance_cu) <> t.closing_balance_cu     ) ', '111', 'The current month''s account balance exists. The parent account balance is not equal to the total child account balance', '当月科目余额存在父级科目余额不等于子级科目余额总数', '當月科目餘額存在父級科目餘額不等於子級科目餘額總數', '0', NULL, '2022-08-10 11:27:12', 1, '0', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (373, 1, 'BookI17', 40, 'RD1740002', 'Claim business account data business source non null verification', '理赔业务账数据业务来源非空检验', '理賠待入賬數據業務來源非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_cl_acr'') and ri_arrangement_code is null', '101', 'The source information of the claims business account data is empty and the posting dimension is missing. Please check!', '理赔业务账数据业务来源信息为空，缺少入账维度，请检查！', '理赔业务账数据业务来源信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (308, 1, 'BookI17', 56, 'RULE5601', 'Business/Finance Reconciliation Numbers[QTP]', '业财对账条数检查（计量平台）', '業財對賬條數檢查（計量平臺）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID WHERE  B.PROC_ID = (SELECT PROC_ID FROM BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_QTP_BCR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.account_id_dr IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_CR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''D'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'')) t', '111', 'Check the number of data: the total number of debit (AC) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据借方（计量平台）总条数与生成凭证明细的数据总条数不一致', '檢查數據條數:待入賬數據借方（計量平臺）總條數與生成憑證明細的數據總條數不一致', '1', NULL, '2023-08-17 16:20:28.44', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:20:28.441');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (331, 1, 'BookI17', 101, 'RMC0102', 'Whether the voucher credit amount is equal to the account balance', '凭证贷方发生额与科目余额是否相等', '凭证贷方发生额与科目余额是否相等', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT a.account_id FROM (SELECT account_id, SUM(b.amount_cu) as amount_cu from accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} A LEFT JOIN accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b ON A.voucher_id = b.voucher_id WHERE  b.account_entry_code = ''D'' GROUP BY account_id) A LEFT JOIN (SELECT account_id, SUM(debit_amount_cu) as debit_amount_cu FROM accuser.acc_buss_ledger_balance b WHERE entity_id = {entity_id} AND b.year_month = {year_month} AND b.book_code = {book_code} GROUP BY account_id) b ON A.account_id = b.account_id where a.amount_cu <> b.debit_amount_cu and exists(select 1 from bpluser.bbs_account it where it.account_id = a.account_id and it.FINAL_LEVEL_IS = ''1'')', '111', 'The field center of data table to be risk_code is empty', '凭证贷方发生额与科目余额不等', '憑證貸方發生額與科目餘額不等', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (332, 1, 'BookI17', 102, 'RMC0201', 'Current month balance consistency check - account balance', '当月科目余额一致性检查', '當月科目餘額一致性檢查', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(account_id) from accuser.acc_buss_ledger_balance t where entity_id = {entity_id}     and book_code = {book_code}     and year_month = {year_month}     and (opening_balance_cu + (debit_amount_cu - credit_amount_cu)  <>closing_balance_cu)', '111', 'Account balance of the current month closing balance is not equal to opening balance plus voucher de', '当月科目余额期末余额不等于期初余额加凭证借贷发生额', '當月科目餘額期末餘額不等於期初餘額加憑證借貸發生額', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (333, 1, 'BookI17', 102, 'RMC0202', 'Consistency check of special balance in the current month', '当月专项余额一致性检查', '當月專項餘額一致性檢查', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(account_id) from accuser.acc_buss_article_balance t   where entity_id =  {entity_id}     and book_code = {book_code}     and year_month = {year_month}     and (opening_balance_cu + (debit_amount_cu - credit_amount_cu)  <>closing_balance_cu)', '111', 'Special balance of the current month closing balance is not equal to opening balance plus voucher de', '当月专项余额期末余额不等于期初余额加凭证借贷发生额', '當月专项餘額期末餘額不等於期初餘額加憑證借貸發生額', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (291, 1, 'BookI17', 244, 'RC171415', 'Data check - synchronize data platform data task status', '数据检查-同步数据平台数据任务状态', '數據檢查-同步數據平臺數據任務狀態', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT count(task_log_id)FROM bpluser.bpl_log_pub_task tl LEFT JOIN bpluser.bpl_qrtz_conf_task_detail dtl ON tl.task_detail_id = dtl.task_detail_id LEFT JOIN bpluser.bpl_qrtz_conf_task ct ON ct.conf_task_id = dtl.conf_task_id WHERE tl.entity_id = {entity_id} and tl.year_month = {year_month} and tl.task_status <> ''2'' and ct.task_group = ''ACC'' and func_code=''DM_ACC_PAYMENT'' ', '101', 'The scheduled task (Data Platform) contains input data that is not normally executed', '定时任务（数据平台）含有未正常执行的输入数据', '定時任務（數據平臺）含有未正常執行的輸入數據', '1', NULL, '2023-10-19 16:21:40.547', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-10-19 16:21:40.548');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (285, 1, 'BookI17', 244, 'RC171405', 'Check whether the data to be posted processing node is empty', '待入账数据入账处理节点是否为空校验', '待入帳數據入帳處理節點是否為空校驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(entry_data_id) from accuser.acc_dap_entry_data_{entity_id}_{year_month} where proc_id is null', '101', 'The field center of data table to be proc_id is empty', '待入账数据表字段proc_id为空', '待入賬數據表字段proc_id為空', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (367, 1, 'BookI17', 39, 'RD1739002', 'Verification of non null source of underwriting business account data', '承保业务账数据业务来源非空检验', '承保待入賬數據業務來源非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_uw_acr'') and ri_arrangement_code is null', '101', 'The underwriting business account data and business source information are empty, and the posting dimension is missing. Please check!', '承保业务账数据业务来源信息为空，缺少入账维度，请检查！', '承保业务账数据业务来源信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (368, 1, 'BookI17', 39, 'RD1739003', 'Underwriting business ledger data business type non null verification', '承保业务账数据业务类型非空检验', '承保待入賬數據業務類型非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_uw_acr'') and business_source_code is null', '101', 'The underwriting business account data and business type information are empty, and the posting dimension is missing. Please check!', '承保业务账数据业务类型信息为空，缺少入账维度，请检查！', '承保业务账数据业务类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (312, 1, 'BookI17', 62, 'RL175103', 'Account closing data check[Counter Data]', '关账数据检查（柜面数据检查）', '關賬數據檢查（櫃面數據檢查）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT count(act_buss_id) FROM bpluser.bms_buss_action_state WHERE STATE <> ''1''AND entity_id = {entity_id} AND year_month = {year_month} AND proc_id IN ( SELECT proc_id FROM bpluser.bms_conf_action_procdef WHERE system_code = ''ACC'' AND proc_code IN ( ''ACC_DATACHECK_COUNTER_RI_ACR'', ''ACC_DATACHECK_COUNTER_UW_ACR'', ''ACC_DATACHECK_COUNTER_CL_ACR'', ''ACC_DATACHECK_COUNTER_PY_ACR'',  ''ACC_DATACHECK_COUNTER_FI_ACR'' ) ) ', '101', 'The node (Counter Data Check) configuration rule failed', '节点（柜面数据检查）配置规则不通过', '節點（櫃面數據檢查）配置規則不通過', '1', NULL, '2023-08-17 16:36:26.908', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-17 16:36:26.909');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (323, 1, 'BookI17', 67, 'RL175603', 'Account closing data check[Multi-Dimention Reconciliation]', '关账数据检查（多准则数据对账检查）', '關賬數據檢查（多準則數據對賬檢查）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT count(act_buss_id) FROM bpluser.bms_buss_action_state WHERE STATE <> ''1''AND entity_id = {entity_id} AND year_month = {year_month} AND proc_id IN ( SELECT proc_id FROM bpluser.bms_conf_action_procdef WHERE system_code = ''ACC'' AND proc_code IN ( ''ACC_AMOUNTCHECK_MULTI_RI_CCR'',''ACC_AMOUNTCHECK_MULTI_UW_CCR'',''ACC_AMOUNTCHECK_MULTI_PY_CCR'',''ACC_AMOUNTCHECK_MULTI_CL_CCR'') ) ', '101', 'The node (Multi-Standards Data Reconciliation Check) configuration rule failed', '节点（多准则数据对账检查）配置规则不通过', '節點（多準則數據對賬檢查）配置規則不通過', '0', NULL, '2022-08-10 11:27:12', 1, '0', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (282, 1, 'BookI17', 103, 'RMC0302', 'Account balance trial balance (Initial Balance)', '科目余额试算平衡验证(期初)', '科目餘額試算平衡驗證(期初)', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(opening_balance_cu) from accuser.acc_buss_ledger_balance b left join bpluser.bbs_v_account v on v.book_code={book_code} and b.account_id=v.account_id where b.entity_id = {entity_id}  AND b.year_month ={year_month} AND b.book_code =  {book_code}  and v.account_level=''1'' HAVING sum(opening_balance_cu)<>0', '111', 'Trial balance of currency conversion amount of current month account balance is unbalanced', '当月科目余额折币发生额试算不平衡', '當月科目餘額折幣發生額試算不平衡', '0', NULL, '2023-01-10 16:48:46', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2024-07-25 15:46:51.618');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (354, 1, 'BookI17', 244, 'RD17244002', 'Integrity verification of expense allocation data to accounting', '费用分摊数据到会计完整性校验', '费用分摊数据到会计完整性校验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT COUNT(T.Allocation_Id) FROM EXPUSER.Exp_Buss_Allocation_Icg T WHERE ENTITY_ID = {entity_id} AND year_month = {year_month} AND T.CURRENT_AMOUNT<>0 AND NOT EXISTS (SELECT 1 FROM ACCUSER.ACC_DAP_ENTRY_DATA_{entity_id}_{year_month} ED WHERE T.Allocation_Id = ED.BUSINESS_ID)', '101', 'The cost allocation contract group allocation data currently has data that has not been obtained!', '费用分摊合同组分摊数据截至目前存在还未获取到的数据！', '费用分摊合同组分摊数据截至目前存在还未获取到的数据！', '0', '数据检查规则', '2023-10-20 14:27:47.767', 1, '1', NULL, 5, 1, 1, '2022-08-10 11:27:12', '2023-10-20 14:27:47.767');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (363, 1, 'BookI17', 365, 'RYC0303', 'Account balance trial balance (Amount Incurred)', '科目余额试算是否平衡(发生额)', '科目余额试算是否平衡(發生額)', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(debit_amount_cu-credit_amount_cu) from accuser.acc_buss_ledger_balance b left join bpluser.bbs_account v on v.book_code={book_code} and b.account_id=v.account_id where b.entity_id = {entity_id}  and b.year_month ={year_month} and b.book_code =  {book_code} and account_category_code in (''1'',''2'',''3'',''4'') and v.final_level_is=''1''  having  abs(sum(debit_amount_cu-credit_amount_cu))>0.01', '111', 'The trial balance of account balance is unbalanced, and the sum of the amount of asset liability acc', '科目余额试算不平衡，资产负债科目+权益科目+损益科目的发生额和不等于0', '科目餘額試算不平衡，資產負債科目+權益科目+損益科目的發生額和不等於0', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (283, 1, 'BookI17', 103, 'RMC0303', 'Account balance trial balance (Amount Incurred)', '科目余额试算是否平衡(发生额)', '科目余额试算是否平衡(發生額)', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(debit_amount_cu-credit_amount_cu) from accuser.acc_buss_ledger_balance b left join bpluser.bbs_v_account v on v.book_code={book_code} and b.account_id=v.account_id where b.entity_id = {entity_id}  and b.year_month ={year_month} and b.book_code =  {book_code} and account_category_code in (''1'',''2'',''3'',''4'') and v.account_level=''1'' having sum(debit_amount_cu-credit_amount_cu)<>0', '111', 'The trial balance of account balance is unbalanced, and the sum of the amount of asset liability acc', '科目余额试算不平衡，资产负债科目+权益科目+损益科目的发生额和不等于0', '科目餘額試算不平衡，資產負債科目+權益科目+損益科目的發生額和不等於0', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-10-21 19:39:10.786');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (317, 1, 'BookI17', 64, 'RL175301', 'Check whether the entry status of counter data is abnormal', '柜面数据入账检查入账状态是否异常', '櫃面數據入帳檢查入帳狀態是否異常', '2', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT count(act_buss_id) FROM bpluser.bms_buss_action_state WHERE STATE  not in (''1'',''4'')AND entity_id = {entity_id} AND year_month = {year_month} AND book_code= {book_code} AND proc_id IN ( SELECT proc_id FROM bpluser.bms_conf_action_procdef WHERE system_code = ''ACC'' AND proc_code IN (''ACC_ACCOUNTENTRY_UW_ACR'',''ACC_ACCOUNTENTRY_CL_ACR'',''ACC_ACCOUNTENTRY_RI_ACR'',''ACC_ACCOUNTENTRY_PY_ACR'',''ACC_ACCOUNTENTRY_COMP_STL_ACR'') ) ', '111', 'There is counter data with abnormal entry status!', '存在入账异常状态的柜面数据！', '存在入帳異常狀態的櫃面數據！', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2024-11-29 18:28:23.08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (330, 1, 'BookI17', 101, 'RMC0101', 'Whether the voucher debit amount is equal to the account balance', '凭证借方发生额与科目余额是否相等', '凭证借方发生额与科目余额是否相等', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT a.account_id FROM (SELECT account_id, SUM(b.amount_cu) as amount_cu from accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} A LEFT JOIN accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b ON A.voucher_id = b.voucher_id WHERE  b.account_entry_code = ''C'' GROUP BY account_id) A LEFT JOIN (SELECT account_id, SUM(credit_amount_cu) as amount_cu FROM accuser.acc_buss_ledger_balance b WHERE entity_id = {entity_id} AND b.year_month = {year_month} AND b.book_code = {book_code} GROUP BY account_id) b ON A.account_id = b.account_id where (a.amount_cu - b.amount_cu) <> 0 and exists(select 1 from bpluser.bbs_account it where it.account_id = a.account_id and it.FINAL_LEVEL_IS = ''1'')', '111', 'The field center of data table to be risk_code is empty', '凭证借方发生额与科目余额不等', '憑證借方發生額與科目餘額不等', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (300, 1, 'BookI17', 1, 'RYC0102', 'Whether the voucher credit amount is equal to the account balance', '凭证贷方发生额与科目余额是否相等', '凭证贷方发生额与科目余额是否相等', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT a.account_id FROM (SELECT account_id, SUM(b.amount_cu) as amount_cu FROM accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} A LEFT JOIN accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b ON A.voucher_id = b.voucher_id WHERE  b.account_entry_code = ''D''GROUP BY account_id) A LEFT JOIN (SELECT account_id, SUM(debit_amount_cu) as debit_amount_cu FROM accuser.acc_buss_ledger_balance b WHERE entity_id = {entity_id} AND b.year_month ={year_month} AND b.book_code = {book_code}GROUP BY account_id) b ON A.account_id = b.account_id where a.amount_cu <> b.debit_amount_cu', '111', 'The field center of data table to be risk_code is empty', '凭证贷方发生额与科目余额不等', '憑證貸方發生額與科目餘額不等', '0', NULL, '2022-12-20 19:15:38', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2024-07-12 14:20:01.538');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (284, 1, 'BookI17', 103, 'RMC0301', 'Account balance trial balance (Terminal Balance)', '科目余额试算是否平衡（期末余额）', '科目余额试算是否平衡（期末餘額）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(closing_balance_cu) from accuser.acc_buss_ledger_balance b left join bpluser.bbs_v_account v on v.book_code={book_code} and b.account_id=v.account_id where b.entity_id = {entity_id}  and b.year_month ={year_month} and b.book_code =  {book_code} and account_category_code in (''1'',''2'',''3'',''4'') and v.account_level=''1'' having sum(closing_balance_cu)<>0', '111', 'The trial balance of account balance is unbalanced. The sum of closing balance of asset liability ac', '科目余额试算不平衡，资产负债科目+权益科目+损益科目的期末余额和不等于0', '科目餘額試算不平衡，資產負債科目+權益科目+損益科目的期末餘額和不等於0', '0', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2024-07-25 15:46:44.47');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (326, 1, 'BookI17', 68, 'RL175505', 'Account closing data check', '关账数据检查', '關賬數據檢查', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', ' select sum(debit_amount_cu) from (select account_id, sum(case when account_entry_code = ''D'' then amount_cu else 0 end) as debit_amount_cu, sum(case when account_entry_code = ''C'' then amount_cu else 0 end) as credit_amount_cu from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b join  accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} a on a.voucher_id = b.voucher_id group by account_id union all select account_id, sum (debit_amount_cu) * -1 as debit_amount_cu, sum (credit_amount_cu) * -1 as credit_amount_cu from accuser.acc_buss_article_balance where entity_id = {entity_id} and year_month = {year_month} and book_code = {book_code} and ( debit_amount_cu <> 0 or credit_amount_cu <> 0 ) group by account_id) t having sum(debit_amount_cu) <> 0', '111', 'The voucher data is inconsistent with the detailed data of special amount data', '检查凭证数据与专项发生额数据明细数据不一致', '檢查憑證數據與專項發生額數據明細數據不一致', '1', NULL, '2023-08-17 16:45:19.3', 1, '1', NULL, 6, 1, 1, '2022-08-10 11:27:12', '2024-11-29 18:27:05.149');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (318, 1, 'BookI17', 65, 'RL175402', 'Account closing data check[Non-Counter Data Entry]', '关账数据检查（非柜面数据入账检查）', '關賬數據檢查（非櫃面數據入賬檢查）', '1', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT count(act_buss_id) FROM bpluser.bms_buss_action_state WHERE  entity_id = {entity_id} AND year_month = {year_month} AND book_code = {book_code} AND proc_id IN ( SELECT proc_id FROM bpluser.bms_conf_action_procdef WHERE system_code = ''ACC'' AND proc_code IN (''ACC_ACCOUNTENTRY_EXP_BCR'',''ACC_ACCOUNTENTRY_QTP_BCR'') ) ', '111', 'The node (Non-Counter Data Entry Check) configuration rule failed', '节点（非柜面数据入账检查）配置规则不通过', '節點（非櫃面數據入賬檢查）配置規則不通過', '1', NULL, '2023-08-17 16:59:57.654', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2024-11-29 18:26:17.189');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (377, 1, 'BookI17', 40, 'RD1740006', 'Claim business ledger data source non null verification', '理赔业务账数据数据来源非空检验', '理賠待入賬數據數據來源非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and PROC_ID IS NULL', '101', 'The data source information of the claims business account is empty, and the posting dimension is missing. Please check!', '理赔业务账数据数据来源信息为空，缺少入账维度，请检查！', '理赔业务账数据数据来源信息为空，缺少入账维度，请检查！', '1', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (316, 1, 'BookI17', 64, 'RL175302', 'Check whether there is counter data recorded', '检查是否有柜面数据入账', '檢查是否有櫃面數據入帳', '2', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT count(act_buss_id) FROM bpluser.bms_buss_action_state WHERE entity_id = {entity_id} AND year_month = {year_month} AND book_code= {book_code} AND proc_id IN ( SELECT proc_id FROM bpluser.bms_conf_action_procdef WHERE system_code = ''ACC'' AND proc_code IN (''ACC_ACCOUNTENTRY_UW_ACR'',''ACC_ACCOUNTENTRY_CL_ACR'',''ACC_ACCOUNTENTRY_RI_ACR'',''ACC_ACCOUNTENTRY_PY_ACR'',''ACC_ACCOUNTENTRY_COMP_STL_ACR'',''ACC_ACCOUNTENTRY_COMP_STL_ACR'') )', '111', 'There is no counter data entry result!', '不存在柜面数据入账结果！', '不存在櫃面數據入帳結果', '1', NULL, '2023-08-17 16:58:57.733', 1, '1', NULL, 3, 1, 1, '2022-08-10 11:27:12', '2024-11-29 18:25:54.246');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (319, 1, 'BookI17', 65, 'RL175401', 'Check whether the entry status of non counter data is abnormal', '非柜面数据入账检查入账状态是否异常', '非櫃面數據入帳檢查入帳狀態是否異常', '2', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT count(act_buss_id) FROM bpluser.bms_buss_action_state WHERE STATE = ''0''AND entity_id = {entity_id} AND year_month = {year_month} AND book_code = {book_code} AND proc_id IN ( SELECT proc_id FROM bpluser.bms_conf_action_procdef WHERE system_code = ''ACC'' AND proc_code IN (''ACC_ACCOUNTENTRY_EXP_BCR'',''ACC_ACCOUNTENTRY_QTP_BCR'') )', '111', 'There are non counter data with abnormal entry status!', '存在入账异常状态的非柜面数据！', '存在入帳異常狀態的非櫃面數據！', '1', NULL, '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2024-11-29 18:26:14.143');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (271, 1, 'BookI17', 22, 'RC172201', 'Accounting period allocation rules', '会计期间配置规则', '會計期間配置規則', '2', '2021-01-31 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT DISTINCT period_id  FROM ACCUSER.acc_conf_accountperiod', '000', 'Accounting period not configured!', '未配置会计期间！', '未配置會計期間！', '1', NULL, '2024-07-12 14:14:54.305', 1, '1', NULL, 5, 1, 1, '2022-08-10 11:27:12', '2024-07-12 14:14:54.311');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (276, 1, 'BookI17', 41, 'RD174101', 'Check whether the re insurance data to be posted is empty', '再保待入账数据是否为空检验', '再保待入帳數據是否為空檢驗', '1', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '1', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'')', '101', 'There is no re insurance data to be recorded!', '不存在再保待入账数据！', '不存在再保待入賬數據！', '1', NULL, '2023-06-19 14:52:56', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (329, 1, 'BookI17', 68, 'RL175508', 'Account closing data check', '关账数据检查', '關賬數據檢查', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(credit_amount_cu) from (select account_id, sum(case when account_entry_code = ''D'' then amount_cu else 0 end) as debit_amount_cu, sum(case when account_entry_code = ''C'' then amount_cu else 0 end) as credit_amount_cu from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b join accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} a on a.voucher_id = b.voucher_id group by account_id union all select account_id, sum (debit_amount_cu) * -1 as debit_amount_cu, sum (credit_amount_cu) * -1 as credit_amount_cu from accuser.acc_buss_article_balance where entity_id = {entity_id} and book_code = {book_code} and year_month = {year_month} and ( debit_amount_cu <> 0 or credit_amount_cu <> 0 ) group by account_id) t having sum(credit_amount_cu) <> 0', '111', 'The voucher data is inconsistent with the detailed data of special amount data', '检查凭证数据与专项发生额数据明细数据不一致', '檢查憑證數據與專項發生額數據明細數據不一致', '1', NULL, '2023-08-17 16:49:39.475', 1, '1', NULL, 5, 1, 1, '2022-08-10 11:27:12', '2024-11-29 18:27:02.032');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (313, 1, 'BookI17', 62, 'RL175102', 'Account closing data check', '关账数据检查', '關賬數據檢查', '2', '2021-01-14 00:00:00', '9999-01-01 00:00:00', '0', 'select count(act_buss_id) from bpluser.bms_buss_action_state  where proc_id in (39,40,41,42,43) and state<>''1''', '111', 'error', '错误', '錯誤', '1', NULL, '2024-11-28 11:48:42.982', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2024-11-28 11:48:42.99');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (278, 1, 'BookI17', 43, 'RDF17101', 'Is there a 17 account mapping configuration for the entry inform', '手工类财务凭证的分录信息科目是否存在17科目映射配置', '手工类财务凭证的分录信息科目是否存在17科目映射配置', '1', '2022-06-08 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT b.voucher_no  FROM ACCUSER.ACC_EXT_VOUCHER B,ACCUSER.acc_ext_voucher_detail B2 WHERE B.ENTITY_ID = {entity_id}   AND B.YEAR_MONTH = {year_month}   AND B.POSTING_TYPE_CODE = ''FI''   AND B.VOUCHER_NO = B2.VOUCHER_NO   AND NOT EXISTS (SELECT 1          FROM BPLUSER.BBS_CONF_ACCOUNT_MAPPING ITEM          WHERE B.BOOK_CODE = ITEM.OTHER_BOOK_CODE            AND B2.ACCOUNT_ID = ITEM.OTHER_ACCOUNT_ID            AND ITEM.VALID_IS = ''1''            AND ITEM.AUDIT_STATE = ''1'')', '101', 'The 4th account of the manual financial voucher entry information does not have a mapping configurat', '手工财务凭证分录信息的4号科目不存在与17科目映射配置，无法转成17凭证，请检查！', '手工財務憑證分錄信息的4號科目不存在與17科目映射配置，無法轉成17憑證，請檢查！', '0', NULL, '2023-04-07 11:37:05', 1, '1', NULL, 4, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (372, 1, 'BookI17', 39, 'RD1739007', 'Non blank verification of underwriting business account data sales method', '承保业务账数据销数方式非空检验', '承保待入賬數據銷數方式非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and EXTEND_COLUMN2 IS NULL', '101', 'The underwriting business account data sales method information is empty and the posting dimension is missing. Please check!', '承保业务账数据销数方式信息为空，缺少入账维度，请检查！', '承保业务账数据销数方式信息为空，缺少入账维度，请检查！', '0', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (279, 1, 'BookI17', 43, 'RDF17102', 'Verify debit / credit balance of manual financial vouchers', '校验手工类财务凭证的分录借贷平衡', '校驗手工類財務憑證的分錄借貸平衡', '2', '2022-06-10 00:00:00', '9999-01-01 00:00:00', '0', 'select a.voucher_no from ACCUSER.ACC_buss_voucher_{entity_id}_{year_month}_{book_code} a left join ACCUSER.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b on a.voucher_id = b.voucher_id where  posting_type_code in (''49'',''50'',''51'',''52'',''53'') group by a.voucher_no HAVING sum(case when b.account_entry_code=''D'' then b.amount_cu end) <> sum(case when b.account_entry_code=''C'' then b.amount_cu end)', '111', 'Debit and credit of manual financial voucher entries are uneven', '手工类财务凭证的分录借贷不平', '手工類財務憑證的分錄借貸不平', '1', NULL, '2023-08-17 17:05:00.946', 1, '1', NULL, 2, 1, 1, '2022-08-10 11:27:12', '2023-08-17 17:05:00.947');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (408, 1, 'BookI17', 44, 'RD1744012', '计量业务账数据合同组非空检验', '计量业务账数据合同组非空检验', '计量业务账数据合同组非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and icg_no IS NULL', '101', '计量业务账数据合同组信息为空，缺少入账维度，请检查！', '计量业务账数据合同组信息为空，缺少入账维度，请检查！', '计量业务账数据合同组信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (366, 1, 'BookI17', 39, 'RD1739001', 'Verification of non blank pre receipt flag for underwriting business account data', '承保业务账数据预收标志非空检验', '承保待入賬數據預收標誌非空檢驗', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and EXTEND_COLUMN1 IS NULL', '101', 'The pre receipt flag information of the underwriting business account data is empty and the posting dimension is missing. Please check!', '承保业务账数据预收标志信息为空，缺少入账维度，请检查！', '承保业务账数据预收标志信息为空，缺少入账维度，请检查！', '0', '数据检查规则', '2022-08-10 11:27:12', 1, '1', NULL, 1, 1, 1, '2022-08-10 11:27:12', '2025-06-21 22:44:00.878');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (123, 1, 'BookI4', 24, 'RC042402', 'Whether the process node is configured with check rules', '流程节点是否配置检查规则检验', '流程節點是否配置檢查規則檢驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(proc_id) from bpluser.bms_conf_action_procdef b where system_code =''ACC'' and valid_is = ''1'' and parent_proc_id in (select f2.proc_id     from bpluser.bms_conf_action_procdef f1, bpluser.bms_conf_action_procdef f2     where f1.proc_id  = f2.parent_proc_id      and f1.proc_code = ''ACCROOT'') and substr(SCENERIO_CODE,3 ,1) = ''1''  and not exists(  select 1 from acc_conf_checkrule a where a.proc_id = b.proc_id ) ', '000', 'The check rule is not configured', '校验规则未配置', '校驗規則未配置', '1', NULL, '2022-08-10 11:27:12', 22643, '1', NULL, 1, 22643, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (336, 1, 'BookI17', 52, 'RULE5202', 'Business/Finance Reconciliation Numbers[UW]', '业财对账条数检查（承保）', '業財對賬條數檢查（承保）', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select sum(num) diff from (SELECT COUNT(b.ENTRY_DATA_ID) NUM FROM ACCUSER.ACC_BUSS_ENTRY_DATA_{entity_id}_{year_month}_{book_code} B LEFT JOIN ACCUSER.ACC_BUSS_ENTRY_DATA_DETAIL_{entity_id}_{year_month}_{book_code} C ON B.BUSS_ENTRY_ID = C.BUSS_ENTRY_ID WHERE  B.PROC_ID = (SELECT PROC_ID FROM BPLUSER.BMS_CONF_ACTION_PROCDEF WHERE PROC_CODE=''ACC_ACCOUNTENTRY_UW_ACR'') and exists (select * from accuser.acc_dap_entry_data_{entity_id}_{year_month} a where a.entry_data_id = b.entry_data_id AND ((C.ACCOUNT_ID_CR IS NOT NULL AND A.AMOUNT >= 0) or (C.ACCOUNT_ID_DR IS NOT NULL AND A.AMOUNT < 0))) union all select count(voucher_dtl_id) * -1 num from accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} where account_entry_code = ''C'' and proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'')) t', '111', 'Check the number of data: the total number of credit (UW) data to be posted is inconsistent with the total number of data generating voucher details', '检查数据条数:待入账数据贷方（承保）总条数与生成凭证明细的数据总条数不一致', '檢查數據條數:待入賬數據貸方（承保）總條數與生成憑證明細的數據總條數不一致', '1', NULL, '2023-08-17 16:31:30.66', 22643, '1', NULL, 6, 1, 22643, '2022-08-10 11:27:12', '2023-08-17 16:31:30.661');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (130, 1, 'BookI4', 24, 'RC042401', 'Whether the rule is configured', '检查校验规则是否有配置', '檢查校驗規則是否有配置', '2', '2021-04-21 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT DISTINCT check_rule_id FROM acc_conf_checkrule where system_code = ''ACC''', '110', 'Check rules not configured!', '未配置检查规则！', '未配置檢查規則！', '1', NULL, '2022-08-10 11:27:12', 22643, '1', NULL, 1, 22643, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (272, 1, 'BookI17', 24, 'RC172402', 'Whether the process node is configured with check rules', '流程节点是否配置检查规则检验', '流程節點是否配置檢查規則檢驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(proc_id) from bpluser.bms_conf_action_procdef b where system_code =''ACC'' and valid_is = ''1'' and parent_proc_id in (select f2.proc_id     from bpluser.bms_conf_action_procdef f1, bpluser.bms_conf_action_procdef f2     where f1.proc_id  = f2.parent_proc_id      and f1.proc_code = ''ACCROOT'') and substr(SCENERIO_CODE,3 ,1) = ''1''  and not exists(  select 1 from acc_conf_checkrule a where a.proc_id = b.proc_id ) ', '000', 'The check rule is not configured', '校验规则未配置', '校驗規則未配置', '1', NULL, '2023-06-25 10:08:12.285', 22643, '1', NULL, 2, 1, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (409, 1, 'BookI17', 44, 'RD1744013', '计量业务账数据合同组合非空检验', '计量业务账数据合同组合非空检验', '计量业务账数据合同组合非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and portfolio_no IS NULL', '101', '计量业务账数据合同组合信息为空，缺少入账维度，请检查！', '计量业务账数据合同组合信息为空，缺少入账维度，请检查！', '计量业务账数据合同组合信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (43, 1, 'BookI17', 363, 'RYC0102', 'Whether the voucher credit amount is equal to the account balance', '凭证贷方发生额与科目余额是否相等', '凭证贷方发生额与科目余额是否相等', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT a.account_id FROM (SELECT account_id, SUM(b.amount_cu) as amount_cu FROM accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} A LEFT JOIN accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b ON A.voucher_id = b.voucher_id WHERE  b.account_entry_code = ''D'' GROUP BY account_id) A LEFT JOIN (SELECT account_id, SUM(debit_amount_cu) as debit_amount_cu FROM accuser.acc_buss_ledger_balance b WHERE entity_id = {entity_id} AND b.year_month ={year_month} AND b.book_code = {book_code} GROUP BY account_id) b ON A.account_id = b.account_id where a.amount_cu <> b.debit_amount_cu', '111', 'The field center of data table to be risk_code is empty', '凭证贷方发生额与科目余额不等', '憑證貸方發生額與科目餘額不等', '1', NULL, '2022-12-20 19:15:38', 22643, '1', NULL, 2, 22643, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (129, 1, 'BookI4', 23, 'RC042301', 'Entry Rule Check', '入账规则检查', '入场规则检查', '2', '2021-04-21 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT DISTINCT entry_rule_id FROM ACCUSER.ACC_conf_entryrule ', '110', 'Entry rules are not configured!', '未配置入账规则！', '未配置入帳規則！', '1', NULL, '2022-08-10 11:27:12', 22643, '1', NULL, 1, 22643, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (124, 1, 'BookI4', 23, 'RC042301', 'Entry Rule Check', '入账规则检查', '入场规则检查', '2', '2021-04-21 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT DISTINCT entry_rule_id FROM ACCUSER.ACC_conf_entryrule ', '110', 'Entry rules are not configured!', '未配置入账规则！', '未配置入帳規則！', '1', NULL, '2022-08-10 11:27:12', 22643, '1', NULL, 1, 22643, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (42, 1, 'BookI17', 363, 'RYC0101', 'Whether the voucher debit amount is equal to the account balance', '凭证借方发生额与科目余额是否相等', '凭证借方发生额与科目余额是否相等', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT a.account_id FROM (SELECT account_id, SUM(b.amount_cu) as amount_cu FROM accuser.acc_buss_voucher_{entity_id}_{year_month}_{book_code} A LEFT JOIN accuser.acc_buss_voucher_detail_{entity_id}_{year_month}_{book_code} b ON A.voucher_id = b.voucher_id WHERE  b.account_entry_code = ''C'' GROUP BY account_id) A LEFT JOIN (SELECT account_id, SUM(credit_amount_cu) as amount_cu FROM accuser.acc_buss_ledger_balance b WHERE entity_id = {entity_id} AND b.year_month ={year_month} AND b.book_code = {book_code} GROUP BY account_id) b ON A.account_id = b.account_id where (a.amount_cu - b.amount_cu) <> 0', '111', 'The field center of data table to be risk_code is empty', '凭证借方发生额与科目余额不等', '憑證借方發生額與科目餘額不等', '1', NULL, '2022-12-20 19:15:38', 22643, '1', NULL, 2, 22643, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (128, 1, 'BookI4', 24, 'RC042402', 'Whether the process node is configured with check rules', '流程节点是否配置检查规则检验', '流程節點是否配置檢查規則檢驗', '2', '2021-01-01 00:00:00', '9999-01-01 00:00:00', '0', 'select count(proc_id) from bpluser.bms_conf_action_procdef b  where system_code =''ACC'' and valid_is = ''1'' and parent_proc_id in (select f2.proc_id     from bpluser.bms_conf_action_procdef f1, bpluser.bms_conf_action_procdef f2     where f1.proc_id  = f2.parent_proc_id      and f1.proc_code = ''ACCROOT'') and substr(SCENERIO_CODE,3 ,1) = ''1''  and not exists(  select 1 from acc_conf_checkrule a where a.proc_id = b.proc_id ) ', '000', 'The check rule is not configured', '校验规则未配置', '校驗規則未配置', '1', NULL, '2022-08-10 11:27:12', 22643, '1', NULL, 1, 22643, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (127, 1, 'BookI4', 22, 'RC042201', 'Accounting period allocation rules', '会计期间配置规则', '會計期間配置規則', '2', '2021-01-31 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT DISTINCT period_id  FROM ACCUSER.acc_conf_accountperiod', '000', 'Accounting period not configured!', '未配置会计期间！', '未配置會計期間！', '1', NULL, '2023-03-20 17:50:08', 22643, '1', NULL, 4, 22643, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (126, 1, 'BookI4', 22, 'RC042202', 'Accounting period allocation rules', '会计期间配置规则状态检查', '會計期間配置規則狀態檢查', '2', '2021-01-31 00:00:00', '9999-01-01 00:00:00', '0', 'SELECT period_id  FROM ACCUSER.acc_conf_accountperiod where valid_is<>''1'' and entity_id = {entity_id} and book_code =  {book_code}  and year_month ={year_month} ', '111', 'Accounting period invalid status!', '会计期间无效状态！', '會計期間無效狀態！', '1', NULL, '2022-08-10 11:27:12', 22643, '1', NULL, 1, 22643, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (125, 1, 'BookI4', 24, 'RC042401', 'Whether the rule is configured', '检查校验规则是否有配置', '檢查校驗規則是否有配置', '2', '2021-04-21 00:00:00', '9999-01-01 00:00:00', '1', 'SELECT DISTINCT check_rule_id FROM acc_conf_checkrule where system_code = ''ACC''', '110', 'Check rules not configured!', '未配置检查规则！', '未配置檢查規則！', '1', NULL, '2022-08-10 11:27:12', 22643, '1', NULL, 1, 22643, 22643, '2022-08-10 11:27:12', '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (390, 1, 'BookI17', 42, 'RD1742004', '[Payment] Business ledger data source non empty verification', '[收付]业务账数据数据来源非空检验', '[收付]业务账数据数据来源非空检验', '2', '2025-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_PY_ACR'') and PROC_ID IS NULL', '101', 'The data source information of the payment business account is empty, and the posting dimension is missing. Please check!', '收付业务账数据数据来源信息为空，缺少入账维度，请检查！', '收付业务账数据数据来源信息为空，缺少入账维度，请检查！', '1', '数据检查规则', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (391, 1, 'BookI17', 39, 'TEST89', 'TEST', 'TEST', 'TEST89', '1', '2023-08-17 00:00:00', '2023-08-31 00:00:00', '1', 'SELECT DISTINCT period_id  FROM ACCUSER.acc_conf_accountperiod', '000', 'TEST', 'TEST', 'TEST', '1', NULL, NULL, NULL, '0', NULL, 5, 1, 1, '2023-08-17 18:03:38', '2024-07-12 14:07:17.645');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (395, 1, 'BookI17', 41, 'RD1741009', '再保业务账数据vouitem非空检验', '再保业务账数据vouitem非空检验', '再保业务账数据vouitem非空检验', '2', '2025-06-12 00:00:00', '2159-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and upper(expenses_type_code) NOT IN (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'') and vouitem IS NULL', '101', '再保业务账数据vouitem信息为空，缺少入账维度，请检查！', '再保业务账数据vouitem信息为空，缺少入账维度，请检查！', '再保业务账数据vouitem信息为空，缺少入账维度，请检查！', '1', '再保业务账数据vouitem信息为空，缺少入账维度，请检查！', NULL, NULL, '1', NULL, 1, 1, NULL, '2025-06-12 15:16:13.421', NULL);
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (389, 1, 'BookI17', 42, 'RD1742003', '[Receipt and Payment] Business Account Data Voucher Type Non Empty Verification', '[收付]业务账数据凭证类型非空检验', '[收付]业务账数据凭证类型非空检验', '2', '2024-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_PY_ACR'') and posting_type_code IS NULL', '101', 'The voucher type information of the payment business account data is empty and the posting dimension is missing. Please check!', '收付业务账数据凭证类型信息为空，缺少入账维度，请检查！', '收付业务账数据凭证类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (394, 1, 'BookI17', 40, 'RD1740008', '理赔业务账数据vouitem非空检验', '理赔业务账数据vouitem非空检验', '理赔业务账数据vouitem非空检验', '2', '2025-06-12 00:00:00', '2119-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and upper(expenses_type_code) NOT IN (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'') and vouitem IS NULL', '101', '理赔业务账数据vouitem信息为空，缺少入账维度，请检查！', '理赔业务账数据vouitem信息为空，缺少入账维度，请检查！', '理赔业务账数据vouitem信息为空，缺少入账维度，请检查！', '1', '理赔业务账数据vouitem信息为空，缺少入账维度，请检查！', NULL, NULL, '1', NULL, 1, 1, NULL, '2025-06-12 15:13:57.509', NULL);
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (388, 1, 'BookI17', 42, 'RD1742002', '[Collection and Payment] Business Ledger Data Expense Type Non Empty Verification', '[收付]业务账数据费用类型非空检验', '[收付]业务账数据费用类型非空检验', '2', '2023-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_PY_ACR'') and expenses_type_code IS NULL', '101', 'The expense type information in the collection and payment business account data is empty, and the posting dimension is missing. Please check!', '收付业务账数据费用类型信息为空，缺少入账维度，请检查！', '收付业务账数据费用类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (392, 1, 'BookI17', 24, 'dsad', 'asd', 'sad', 'assd', '1', '2024-07-23 00:00:00', '2024-07-26 00:00:00', '1', 'select 1', '000', 'sdsa', 'dasd', 'ada', '1', NULL, NULL, NULL, '0', NULL, 1, 1, NULL, '2024-07-12 14:43:39.903', NULL);
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (393, 1, 'BookI17', 39, 'RD1739008', '承保业务账数据vouitem非空检验', '承保业务账数据vouitem非空检验', '承保业务账数据vouitem非空检验', '2', '2025-06-12 00:00:00', '2249-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and upper(expenses_type_code) NOT IN (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'') and vouitem IS NULL', '101', '承保业务账数据vouitem信息为空，缺少入账维度，请检查！', '承保业务账数据vouitem信息为空，缺少入账维度，请检查！', '承保业务账数据vouitem信息为空，缺少入账维度，请检查！', '0', '承保业务账数据vouitem信息为空，缺少入账维度，请检查！', NULL, NULL, '1', NULL, 1, 1, 1, '2025-06-12 15:11:25.242', '2025-06-18 11:38:23.997');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (387, 1, 'BookI17', 42, 'RD1742001', '[Collection and Payment] Business Account Data Business Type Non Empty Verification', '[收付]业务账数据业务类型非空检验', '[收付]业务账数据业务类型非空检验', '2', '2022-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select distinct entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_py_acr'') and business_source_code is null', '101', 'The business type information of the payment business account data is empty and the posting dimension is missing. Please check!', '收付业务账数据业务类型信息为空，缺少入账维度，请检查！', '收付业务账数据业务类型信息为空，缺少入账维度，请检查！', '1', '数据检查规则', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, '2023-08-14 11:41:08');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (440, 1, 'BookI17', 41, 'RD1741018', '再保业务账数据财务明细非空检验', '再保业务账数据财务明细非空检验', '再保业务账数据财务明细非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code in( ''04'',''14'') and expenses_type_code in (''C01'',''D01'',''D03'',''D29'',''C10'',''Z01'',''Z03'')
and detail_id is null', '101', '再保业务账数据财务明细段信息为空，缺少入账维度，请检查！', '再保业务账数据财务明细段信息为空，缺少入账维度，请检查！', '再保业务账数据财务明细段信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (437, 1, 'BookI17', 41, 'RD1741015', '再保业务账数据合同组合非空检验', '再保业务账数据合同组合非空检验', '再保业务账数据合同组合非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code in (''04'',''14'') and expenses_type_code in (''C01'',''D01'',''D03'',''C16'',''D04'',''C15'',''D29'',''C10'',''Z01'',''Z02'',''Z03'')
and portfolio_no is null', '101', '再保业务账数据合同组合信息为空，缺少入账维度，请检查！', '再保业务账数据合同组合信息为空，缺少入账维度，请检查！', '再保业务账数据合同组合信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (438, 1, 'BookI17', 41, 'RD1741016', '再保业务账数据评估方法非空检验', '再保业务账数据评估方法非空检验', '再保业务账数据评估方法非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code in (''04'',''14'') and expenses_type_code in (''C01'',''D01'',''D03'',''C16'',''D04'',''C15'',''D29'',''C10'',''Z01'',''Z02'',''Z03'')
and evaluate_approach is null', '101', '再保业务账数据评估方法信息为空，缺少入账维度，请检查！', '再保业务账数据评估方法信息为空，缺少入账维度，请检查！', '再保业务账数据评估方法信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (405, 1, 'BookI17', 44, 'RD1744009', '计量业务账数据财务产品非空检验', '计量业务账数据财务产品非空检验', '计量业务账数据财务产品非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and product_id IS NULL', '101', '计量业务账数据产品信息为空，缺少入账维度，请检查！', '计量业务账数据产品信息为空，缺少入账维度，请检查！', '计量业务账数据产品信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (439, 1, 'BookI17', 41, 'RD1741017', '再保业务账数据过去/当前服务非空检验', '再保业务账数据过去/当前服务非空检验', '再保业务账数据过去/当前服务非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code = ''04'' and expenses_type_code in (''D03'',''D29'')
and current_previous_is is null', '101', '再保业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '再保业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '再保业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (441, 1, 'BookI17', 41, 'RD1741019', '再保业务账数据财务现金流量非空检验', '再保业务账数据财务现金流量非空检验', '再保业务账数据财务现金流量非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code in (''11'',''12'',''13'',''14'',''15'') and UPPER(expenses_type_code) in (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'') and cashflow_article is null', '101', '再保业务账数据现金流量信息为空，缺少入账维度，请检查！', '再保业务账数据现金流量信息为空，缺少入账维度，请检查！', '再保业务账数据现金流量信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (404, 1, 'BookI17', 44, 'RD1744008', '计量业务账数据财务部门非空检验', '计量业务账数据财务部门非空检验', '计量业务账数据财务部门非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and dept_ment_id IS NULL', '101', '计量业务账数据部门信息为空，缺少入账维度，请检查！', '计量业务账数据部门信息为空，缺少入账维度，请检查！', '计量业务账数据部门信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (410, 1, 'BookI17', 44, 'RD1744014', '计量业务账数据评估方法非空检验', '计量业务账数据评估方法非空检验', '计量业务账数据评估方法非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and evaluate_approach IS NULL', '101', '计量业务账数据评估方法信息为空，缺少入账维度，请检查！', '计量业务账数据评估方法信息为空，缺少入账维度，请检查！', '计量业务账数据评估方法信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (412, 1, 'BookI17', 44, 'RD1744016', '计量业务账数据财务明细非空检验', '计量业务账数据财务明细非空检验', '计量业务账数据财务明细非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and detail_id IS NULL', '101', '计量业务账数据财务明细段信息为空，缺少入账维度，请检查！', '计量业务账数据财务明细段信息为空，缺少入账维度，请检查！', '计量业务账数据财务明细段信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (396, 1, 'BookI17', 45, 'RD1745005', '费用分摊业务账数据财务部门非空检验', '费用分摊业务账数据财务部门非空检验', '费用分摊业务账数据财务部门非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_EXP_BCR'') and dept_ment_id IS NULL', '101', '费用分摊业务账数据部门信息为空，缺少入账维度，请检查！', '费用分摊业务账数据部门信息为空，缺少入账维度，请检查！', '费用分摊业务账数据部门信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (397, 1, 'BookI17', 45, 'RD1745006', '费用分摊业务账数据财务产品非空检验', '费用分摊业务账数据财务产品非空检验', '费用分摊业务账数据财务产品非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_EXP_BCR'') and product_id IS NULL', '101', '费用分摊业务账数据产品信息为空，缺少入账维度，请检查！', '费用分摊业务账数据产品信息为空，缺少入账维度，请检查！', '费用分摊业务账数据产品信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (398, 1, 'BookI17', 45, 'RD1745007', '费用分摊业务账数据财务补充产品非空检验', '费用分摊业务账数据财务补充产品非空检验', '费用分摊业务账数据财务补充产品非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_EXP_BCR'') and sup_product_id IS NULL', '101', '费用分摊业务账数据补充产品信息为空，缺少入账维度，请检查！', '费用分摊业务账数据补充产品信息为空，缺少入账维度，请检查！', '费用分摊业务账数据补充产品信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (402, 1, 'BookI17', 45, 'RD1745011', '费用分摊业务账数据评估方法非空检验', '费用分摊业务账数据评估方法非空检验', '费用分摊业务账数据评估方法非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_EXP_BCR'') and extend_column5 != ''1'' and evaluate_approach IS NULL', '101', '费用分摊业务账数据评估方法信息为空，缺少入账维度，请检查！', '费用分摊业务账数据评估方法信息为空，缺少入账维度，请检查！', '费用分摊业务账数据评估方法信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (403, 1, 'BookI17', 45, 'RD1745012', '费用分摊业务账数据过去/当前服务非空检验', '费用分摊业务账数据过去/当前服务非空检验', '费用分摊业务账数据过去/当前服务非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_EXP_BCR'') and extend_column5 in ( ''3'', ''4'') and current_previous_is IS NULL', '101', '费用分摊业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '费用分摊业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '费用分摊业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (399, 1, 'BookI17', 45, 'RD1745008', '费用分摊业务账数据财务渠道非空检验', '费用分摊业务账数据财务渠道非空检验', '费用分摊业务账数据财务渠道非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_EXP_BCR'') and extend_column5 in ( ''2'', ''3'') and channel_code IS NULL', '101', '费用分摊业务账数据渠道信息为空，缺少入账维度，请检查！', '费用分摊业务账数据渠道信息为空，缺少入账维度，请检查！', '费用分摊业务账数据渠道信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (400, 1, 'BookI17', 45, 'RD1745009', '费用分摊业务账数据合同组非空检验', '费用分摊业务账数据合同组非空检验', '费用分摊业务账数据合同组非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_EXP_BCR'') and extend_column5 != ''1'' and icg_no IS NULL', '101', '费用分摊业务账数据合同组信息为空，缺少入账维度，请检查！', '费用分摊业务账数据合同组信息为空，缺少入账维度，请检查！', '费用分摊业务账数据合同组信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (401, 1, 'BookI17', 45, 'RD1745010', '费用分摊业务账数据合同组合非空检验', '费用分摊业务账数据合同组合非空检验', '费用分摊业务账数据合同组合非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_EXP_BCR'') and extend_column5 != ''1'' and portfolio_no IS NULL', '101', '费用分摊业务账数据合同组合信息为空，缺少入账维度，请检查！', '费用分摊业务账数据合同组合信息为空，缺少入账维度，请检查！', '费用分摊业务账数据合同组合信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (414, 1, 'BookI17', 39, 'RD1739010', '承保业务账数据财务产品非空检验', '承保业务账数据财务产品非空检验', '承保业务账数据财务产品非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code = ''11'' and expenses_type_code = ''VAT'' and vouitem = ''1105''
and product_id is null
UNION
select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code in(''01'',''05'',''06'',''07'',''11'',''12'',''15'',''17'',''18'',''21'',''22'',''26'',''27'') and expenses_type_code  in (''R10'',''R20'',''R21'',''R24'',''R30'',''R31'',''R32'',''R33'',''R40'',''VAT'',''298'',''299'',''T01'',''T02'',''T03'',''201'',''R22'',''R23'',''411'',''414'')  and vouitem != ''1105''
and product_id is null', '101', '承保业务账数据产品信息为空，缺少入账维度，请检查！', '承保业务账数据产品信息为空，缺少入账维度，请检查！', '承保业务账数据产品信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (407, 1, 'BookI17', 44, 'RD1744011', '计量业务账数据财务渠道非空检验', '计量业务账数据财务渠道非空检验', '计量业务账数据财务渠道非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and entry_data_id not in (
select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'')  and ri_direction_code != ''O''
and extend_column4||business_source_code in (''M3_OE_02DB'',''M3_OE_02FB'',''M3_OE_02TB'',''M3_OI_04DB'',''M3_OI_04FB'',''M3_OI_04TB'',''M3_OI_05DB'',''M3_OJ_04DB'',''M3_OJ_04FB'',''M3_OJ_04TB'',''M3_OJ_05DB'',''M3_OK_04DB'', ''M3_OK_04FB'',''M3_OK_04TB'',''M3_OK_05DB'') ) and channel_code is null', '101', '计量业务账数据渠道信息为空，缺少入账维度，请检查！', '计量业务账数据渠道信息为空，缺少入账维度，请检查！', '计量业务账数据渠道信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (411, 1, 'BookI17', 44, 'RD1744015', '计量业务账数据过去/当前服务非空检验', '计量业务账数据过去/当前服务非空检验', '计量业务账数据过去/当前服务非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'')
and extend_column4 in (''M3_OC_02'',''M3_OD_02'',''M3_OF_02'',''M3_OF_03'',''M3_OG_01'',''M3_OI_02'',''M4_OI_02'',''M3_OI_03'',''M4_OI_03'',''M3_OJ_02'',''M4_OJ_02'',''M3_OJ_03'',''M4_OJ_03'',''M3_OK_02'',''M3_OK_03'',''M3_OL_02'',''M4_OL_02'',''M3_OL_03'',''M4_OL_03'',''M4_OM_02'')
and current_previous_is is null
UNION
select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'')
and extend_column4 in (''M3_OL_04'',''M3_OL_05'') and business_source_code in (''TB'',''FB'')
and current_previous_is is null', '101', '计量业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '计量业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '计量业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (406, 1, 'BookI17', 44, 'RD1744010', '计量业务账数据财务补充产品非空检验', '计量业务账数据财务补充产品非空检验', '计量业务账数据财务补充产品非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_QTP_BCR'') and sup_product_id IS NULL', '101', '计量业务账数据补充产品信息为空，缺少入账维度，请检查！', '计量业务账数据补充产品信息为空，缺少入账维度，请检查！', '计量业务账数据补充产品信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (418, 1, 'BookI17', 39, 'RD1739014', '承保业务账数据合同组合非空检验', '承保业务账数据合同组合非空检验', '承保业务账数据合同组合非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code in (''11'',''12'',''13'',''14'',''15'') and UPPER(expenses_type_code) not in (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'') and portfolio_no is null', '101', '承保业务账数据合同组合信息为空，缺少入账维度，请检查！', '承保业务账数据合同组合信息为空，缺少入账维度，请检查！', '承保业务账数据合同组合信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (413, 1, 'BookI17', 39, 'RD1739009', '承保业务账数据财务部门非空检验', '承保业务账数据财务部门非空检验', '承保业务账数据财务部门非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from  ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code = ''01'' and expenses_type_code = ''VAT'' and vouitem = ''1105''
and dept_ment_id is null
UNION
select DISTINCT entry_data_id from  ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code in(''01'',''05'',''06'',''07'',''11'',''12'',''15'',''17'',''18'',''21'',''22'',''26'',''27'') and expenses_type_code  in (''R10'',''R20'',''R21'',''R24'',''R30'',''R31'',''R32'',''R33'',''R40'',''VAT'',''298'',''299'',''T01'',''T02'',''T03'',''201'',''R22'',''R23'',''411'',''414'')  and vouitem != ''1105''
and dept_ment_id is null', '101', '承保业务账数据部门信息为空，缺少入账维度，请检查！', '承保业务账数据部门信息为空，缺少入账维度，请检查！', '承保业务账数据部门信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (415, 1, 'BookI17', 39, 'RD1739011', '承保业务账数据财务补充产品非空检验', '承保业务账数据财务补充产品非空检验', '承保业务账数据财务补充产品非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code = ''11'' and expenses_type_code = ''VAT'' and vouitem = ''1105'' and sup_product_id is null
UNION
select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code in (''01'',''05'',''06'',''07'',''11'',''12'',''15'',''17'',''18'',''21'',''22'',''26'',''27'') and expenses_type_code in (''R10'',''R20'',''R21'',''R24'',''R30'',''R31'',''R32'',''R33'',''R40'',''VAT'',''298'',''299'',''T03'',''201'',''R22'',''R23'',''411'',''414'')
and vouitem != ''1105'' and sup_product_id is null', '101', '承保业务账数据补充产品信息为空，缺少入账维度，请检查！', '承保业务账数据补充产品信息为空，缺少入账维度，请检查！', '承保业务账数据补充产品信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (416, 1, 'BookI17', 39, 'RD1739012', '承保业务账数据财务渠道非空检验', '承保业务账数据财务渠道非空检验', '承保业务账数据财务渠道非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from  ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code = ''11'' and expenses_type_code = ''VAT'' and vouitem = ''1105'' and channel_code is null
UNION
select DISTINCT entry_data_id from  ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code = ''12'' and expenses_type_code = ''201'' and vouitem = ''1203'' and channel_code is null
UNION
select DISTINCT entry_data_id from  ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code = ''11'' and expenses_type_code in (''R10'',''R20'',''R21'',''R24'',''R30'',''R31'',''R32'',''R33'',''R40'') and vouitem = ''1101'' and channel_code is null
UNION
select DISTINCT entry_data_id from  ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code = ''01'' and expenses_type_code in (''R10'',''R20'',''R21'',''R24'',''R30'',''R31'',''R32'',''R33'',''R40'') and vouitem = ''0101'' and channel_code is null', '101', '承保业务账数据渠道信息为空，缺少入账维度，请检查！', '承保业务账数据渠道信息为空，缺少入账维度，请检查！', '承保业务账数据渠道信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (417, 1, 'BookI17', 39, 'RD1739013', '承保业务账数据合同组非空检验', '承保业务账数据合同组非空检验', '承保业务账数据合同组非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code in (''11'',''12'',''13'',''14'',''15'') and UPPER(expenses_type_code) not in (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'') and icg_no is null', '101', '承保业务账数据合同组信息为空，缺少入账维度，请检查！', '承保业务账数据合同组信息为空，缺少入账维度，请检查！', '承保业务账数据合同组信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (419, 1, 'BookI17', 39, 'RD1739015', '承保业务账数据评估方法非空检验', '承保业务账数据评估方法非空检验', '承保业务账数据评估方法非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'SELECT DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code in (''11'',''12'',''13'',''14'',''15'') and UPPER(expenses_type_code) not in (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'') and evaluate_approach is null', '101', '承保业务账数据评估方法信息为空，缺少入账维度，请检查！', '承保业务账数据评估方法信息为空，缺少入账维度，请检查！', '承保业务账数据评估方法信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (420, 1, 'BookI17', 39, 'RD1739016', '承保业务账数据财务明细非空检验', '承保业务账数据财务明细非空检验', '承保业务账数据财务明细非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code in (''01'',''05'',''11'',''12'',''15'',''17'',''18'',''21'',''22'',''26'') and expenses_type_code in (''R10'',''R20'',''R21'',''R24'',''R30'',''R31'',''R32'',''R33'',''R40'',''R10C'',''R20C'',''R21C'',''R22C'',''R23C'',''R24C'',''R30C'',''R31C'',
''R32C'',''R33C'',''R40C'',''201P'',''T01'',''T02'',''R10P'',''R20P'',''R21P'',''R22P'',''R23P'',''R24P'',''R30P'',''R31P'',''R32P'',''R33P'',''R40P'',''T03'',''201C'',''411'',''414'',''201'') and vouitem not in ( ''1101'',''1203'') and detail_id is null
UNION
select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code = ''11'' and expenses_type_code in(''R10'',''R20'',''R21'',''R24'',''R30'',''R31'',''R32'',''R33'',''R40'') and vouitem = ''1101'' and detail_id is null
UNION
select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code = ''12'' and expenses_type_code = ''201'' and vouitem = ''1203'' and detail_id is null', '101', '承保业务账数据财务明细段信息为空，缺少入账维度，请检查！', '承保业务账数据财务明细段信息为空，缺少入账维度，请检查！', '承保业务账数据财务明细段信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (421, 1, 'BookI17', 39, 'RD1739017', '承保业务账数据财务现金流量非空检验', '承保业务账数据财务现金流量非空检验', '承保业务账数据财务现金流量非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_UW_ACR'') and posting_type_code in (''11'',''12'',''13'',''14'',''15'') and UPPER(expenses_type_code) in (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'') and cashflow_article is null', '101', '承保业务账数据现金流量信息为空，缺少入账维度，请检查！', '承保业务账数据现金流量信息为空，缺少入账维度，请检查！', '承保业务账数据现金流量信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (422, 1, 'BookI17', 40, 'RD1740009', '理赔业务账数据财务部门非空检验', '理赔业务账数据财务部门非空检验', '理赔业务账数据财务部门非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and posting_type_code in (''03'',''13'',''29'') and expenses_type_code in (''P60'',''P62'',''P67'',''P64'',''P72'',''P73'',''P74'',''P75'',''P68'',''P69'',''P00'',''P01'',''P02'',''P03'',''P04'',''P05'',''P06'',''P07'',''P09'',''P10'',''P08'',''P76'',''P86'',''P87'',''P88'',''P89'',''P50'',''Q67'',''Q64'',''Q68'',''Q73'',''Q74'',''Q62'',''Q72'',''Q69'',''Q10'',''Q75'')
and dept_ment_id is null', '101', '理赔业务账数据部门信息为空，缺少入账维度，请检查！', '理赔业务账数据部门信息为空，缺少入账维度，请检查！', '理赔业务账数据部门信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (423, 1, 'BookI17', 40, 'RD1740010', '理赔业务账数据财务产品非空检验', '理赔业务账数据财务产品非空检验', '理赔业务账数据财务产品非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and posting_type_code in (''03'',''13'',''29'') and expenses_type_code in (''P60'',''P62'',''P67'',''P64'',''P72'',''P73'',''P74'',''P75'',''P68'',''P69'',''P00'',''P01'',''P02'',''P03'',''P04'',''P05'',''P06'',''P07'',''P09'',''P10'',''P08'',''P76'',''P86'',''P87'',''P88'',''P89'',''P50'',''Q67'',''Q64'',''Q68'',''Q73'',''Q74'',''Q62'',''Q72'',''Q69'',''Q10'',''Q75'')
and product_id is null', '101', '理赔业务账数据产品信息为空，缺少入账维度，请检查！', '理赔业务账数据产品信息为空，缺少入账维度，请检查！', '理赔业务账数据产品信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (424, 1, 'BookI17', 40, 'RD1740011', '理赔业务账数据财务补充产品非空检验', '理赔业务账数据财务补充产品非空检验', '理赔业务账数据财务补充产品非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and posting_type_code in (''03'',''13'',''29'') and expenses_type_code in (''P60'',''P62'',''P67'',''P64'',''P72'',''P73'',''P74'',''P75'',''P68'',''P69'',''P00'',''P01'',''P02'',''P03'',''P04'',''P05'',''P06'',''P07'',''P09'',''P10'',''P08'',''P76'',''P86'',''P87'',''P88'',''P89'',''P50'',''Q67'',''Q64'',''Q68'',''Q73'',''Q74'',''Q62'',''Q72'',''Q69'',''Q10'',''Q75'')
and sup_product_id is null', '101', '理赔业务账数据补充产品信息为空，缺少入账维度，请检查！', '理赔业务账数据补充产品信息为空，缺少入账维度，请检查！', '理赔业务账数据补充产品信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (425, 1, 'BookI17', 40, 'RD1740012', '理赔业务账数据财务渠道非空检验', '理赔业务账数据财务渠道非空检验', '理赔业务账数据财务渠道非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and posting_type_code in (''03'',''13'',''29'') and expenses_type_code in (''P60'',''P62'',''P67'',''P64'',''P72'',''P73'',''P74'',''P75'',''P68'',''P69'',''P00'',''P01'',''P02'',''P03'',''P04'',''P05'',''P06'',''P07'',''P09'',''P10'',''P08'',''P76'',''P86'',''P87'',''P88'',''P89'',''P50'',''Q67'',''Q64'',''Q68'',''Q73'',''Q74'',''Q62'',''Q72'',''Q69'',''Q10'',''Q75'')
and channel_code is null', '101', '理赔业务账数据渠道信息为空，缺少入账维度，请检查！', '理赔业务账数据渠道信息为空，缺少入账维度，请检查！', '理赔业务账数据渠道信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (426, 1, 'BookI17', 40, 'RD1740013', '理赔业务账数据合同组非空检验', '理赔业务账数据合同组非空检验', '理赔业务账数据合同组非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'')  and UPPER(expenses_type_code) not in (''P60C'',''P60P'',''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'')
and icg_no is null', '101', '理赔业务账数据合同组信息为空，缺少入账维度，请检查！', '理赔业务账数据合同组信息为空，缺少入账维度，请检查！', '理赔业务账数据合同组信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (427, 1, 'BookI17', 40, 'RD1740014', '理赔业务账数据合同组合非空检验', '理赔业务账数据合同组合非空检验', '理赔业务账数据合同组合非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'')  and UPPER(expenses_type_code) not in (''P60C'',''P60P'',''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'')
and portfolio_no is null', '101', '理赔业务账数据合同组合信息为空，缺少入账维度，请检查！', '理赔业务账数据合同组合信息为空，缺少入账维度，请检查！', '理赔业务账数据合同组合信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (428, 1, 'BookI17', 40, 'RD1740015', '理赔业务账数据评估方法非空检验', '理赔业务账数据评估方法非空检验', '理赔业务账数据评估方法非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'')   and UPPER(expenses_type_code) not in (''P60C'',''P60P'',''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''WL'',''JXS'')
and evaluate_approach is null', '101', '理赔业务账数据评估方法信息为空，缺少入账维度，请检查！', '理赔业务账数据评估方法信息为空，缺少入账维度，请检查！', '理赔业务账数据评估方法信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (429, 1, 'BookI17', 40, 'RD1740016', '理赔业务账数据过去/当前服务非空检验', '理赔业务账数据过去/当前服务非空检验', '理赔业务账数据过去/当前服务非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and posting_type_code in (''03'',''29'') and expenses_type_code in (''P60'',''P62'',''P67'',''P64'',''P72'',''P73'',''P74'',''P75'',''P68'',''P69'',''P76'',''P86'',''P87'',''P88'',''P89'')
and vouitem in (''0301'',''0309'',''0310'',''0311'',''0312'',''0313'',''0314'',''0315'',''0316'',''0317'',''0329'',''0386'',''0387'',''0388'',''0389'',''2901'',''2904'',''2905'',''2906'',''2907'',''2908'',''2909'',''2910'',''2911'',''2912'',''2924'',''2986'',''2987'',''2988'',''2989'') and current_previous_is is null', '101', '理赔业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '理赔业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '理赔业务账数据过去/当前服务信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (430, 1, 'BookI17', 40, 'RD1740017', '理赔业务账数据财务明细非空检验', '理赔业务账数据财务明细非空检验', '理赔业务账数据财务明细非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from accuser.acc_dap_entry_data_{entity_id}_{year_month} where  proc_id = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and posting_type_code in (''03'',''13'',''29'') and expenses_type_code in (''P60C'',''P60P'',''P00'',''P01'',''P02'',''P03'',''P04'',''P05'',''P06'',''P07'',''P09'',''P10'',''P08'',''P50'',''Q67'',''Q64'',''Q68'',''Q73'',''Q74'',''Q62'',''Q72'',''Q69'',''Q10'',''Q75'')
and detail_id is null', '101', '理赔业务账数据财务明细段信息为空，缺少入账维度，请检查！', '理赔业务账数据财务明细段信息为空，缺少入账维度，请检查！', '理赔业务账数据财务明细段信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (431, 1, 'BookI17', 40, 'RD1740018', '计量业务账数据财务现金流量非空检验', '计量业务账数据财务现金流量非空检验', '计量业务账数据财务现金流量非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and posting_type_code in (''11'',''12'',''13'',''14'',''15'') and UPPER(expenses_type_code) in (''111'', ''210'', ''211'', ''212'', ''213'', ''214'', ''215'', ''216'', ''217'', ''218'', ''220'', ''221'', ''224'', ''225'', ''411'', ''413'', ''511'', ''512'', ''513'', 
''514'', ''515'', ''530'', ''531'', ''536'', ''537'', ''538'', ''539'', ''730'', ''743'', ''830'', ''841'', ''842'', ''843'', ''844'', ''845'', ''910'',''JXS'') and cashflow_article is null', '101', '理赔业务账数据现金流量信息为空，缺少入账维度，请检查！', '理赔业务账数据现金流量信息为空，缺少入账维度，请检查！', '理赔业务账数据现金流量信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (432, 1, 'BookI17', 41, 'RD1741010', '再保业务账数据财务部门非空检验', '再保业务账数据财务部门非空检验', '再保业务账数据财务部门非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code = ''04'' and expenses_type_code in (''C01'',''D01'',''D03'',''D04'',''D29'',''C10'',''Z01'',''Z03'')
and dept_ment_id is null', '101', '再保业务账数据部门信息为空，缺少入账维度，请检查！', '再保业务账数据部门信息为空，缺少入账维度，请检查！', '再保业务账数据部门信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (433, 1, 'BookI17', 41, 'RD1741011', '再保业务账数据财务产品非空检验', '再保业务账数据财务产品非空检验', '再保业务账数据财务产品非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code = ''04'' and expenses_type_code in (''C01'',''D01'',''D03'',''D29'',''C10'',''Z01'',''Z03'')
and product_id is null', '101', '再保业务账数据产品信息为空，缺少入账维度，请检查！', '再保业务账数据产品信息为空，缺少入账维度，请检查！', '再保业务账数据产品信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (434, 1, 'BookI17', 41, 'RD1741012', '再保业务账数据财务补充产品非空检验', '再保业务账数据财务补充产品非空检验', '再保业务账数据财务补充产品非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code = ''04'' and expenses_type_code in (''C01'',''D01'',''D03'',''D29'',''C10'',''Z01'',''Z03'')
and sup_product_id is null', '101', '再保业务账数据补充产品信息为空，缺少入账维度，请检查！', '再保业务账数据补充产品信息为空，缺少入账维度，请检查！', '再保业务账数据补充产品信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (435, 1, 'BookI17', 41, 'RD1741013', '再保业务账数据财务渠道非空检验', '再保业务账数据财务渠道非空检验', '再保业务账数据财务渠道非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code = ''04'' and expenses_type_code in (''C01'',''D01'',''D03'',''D29'',''C10'',''Z01'',''Z03'')
and channel_code is null', '101', '再保业务账数据渠道信息为空，缺少入账维度，请检查！', '再保业务账数据渠道信息为空，缺少入账维度，请检查！', '再保业务账数据渠道信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
INSERT INTO acc_conf_checkrule (check_rule_id, entity_id, book_code, proc_id, rule_code, rule_e_name, rule_c_name, rule_l_name, warning_type, effective_date, expriration_date, rule_direction, rule_expr, rely_on, msg_en, msg_cn, msg_tn, valid_is, remark, checked_time, checked_id, audit_state, checked_msg, serial_no, creator_id, updator_id, create_time, update_time) VALUES (436, 1, 'BookI17', 41, 'RD1741014', '再保业务账数据合同组非空检验', '再保业务账数据合同组非空检验', '再保业务账数据合同组非空检验', '2', '2021-01-14 00:00:00', '9999-12-31 00:00:00', '0', 'select DISTINCT entry_data_id from ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} where PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and posting_type_code in (''04'',''14'') and expenses_type_code in (''C01'',''D01'',''D03'',''C16'',''D04'',''C15'',''D29'',''C10'',''Z01'',''Z02'',''Z03'')
and icg_no is null', '101', '再保业务账数据合同组信息为空，缺少入账维度，请检查！', '再保业务账数据合同组信息为空，缺少入账维度，请检查！', '再保业务账数据合同组信息为空，缺少入账维度，请检查！', '1', '数据检查规则_辅助核算项', '2025-06-13 11:27:12', 1, '1', NULL, 1, 1, 1, '2025-06-13 11:27:12', '2025-06-13 11:27:12');
commit;
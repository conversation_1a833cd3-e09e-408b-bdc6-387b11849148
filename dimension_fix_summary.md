# 维度不匹配问题修复总结

## 🎯 问题核心
原有的 `preToLrcUls` 索引只支持4维度（treatyNo、policyNo、endorseSeqNo、kindCode），但计算时需要6维度（增加 sectionoCode、reinsurerCode），导致累计值获取不准确。

## ✅ 解决方案
创建新的6维度索引结构，专门处理包含 sectionoCode 和 reinsurerCode 的累计数据，只关注四个核心费用字段。

## 📋 修改清单

### 1. 数据库表结构 ✅
**文件**: `sql/add_earned_fields_to_r_table.sql`
- 为 `atr_buss_to_lrc_t_ul_r` 表添加4个费用字段
- 添加索引提高查询性能

### 2. 实体类调整 ✅  
**文件**: `AtrBussToLrcTUlR.java`
- 移除 `curEdPremium` 和 `curEdNetFee` 的 `@IgnoreCol` 注解
- 使这两个字段可以持久化到数据库

### 3. 服务层增强 ✅
**文件**: `AtrBussLrcTotService.java`
- 新增 `preToLrcUlsDetailed` 6维度索引
- 新增 `createDetailedUnitKey()` 方法生成6维度键
- 新增 `collectionPrePolicyInfoDetailed()` 收集6维度历史数据
- 新增 `getAggregatedPreCumlValues()` 聚合4维度累计值
- 修改 `calcIcp()` 使用6维度索引获取精确累计值
- 修改 `collectIcu()` 使用聚合方法处理4维度汇总

### 4. 数据访问层扩展 ✅
**文件**: `AtrBussLrcToDao.java`
- 新增 `listPreBussToLrcTUlRDetailed()` 方法

**文件**: `AtrBussLrcToCustDao.xml`  
- 新增对应SQL查询，只选择必要的4个费用字段

## 🔄 数据流程

### 原有流程（4维度）
```
历史数据(4维度) → preToLrcUls索引 → 累计值计算 → 可能不准确
```

### 新增流程（6维度）
```
历史数据(6维度) → preToLrcUlsDetailed索引 → 精确累计值计算 → 数据准确
                                    ↓
                              聚合到4维度汇总
```

## 🎯 核心字段
只处理四个关键费用字段：
- `pre_cuml_ed_premium` - 上期累计已赚保费
- `pre_cuml_ed_net_fee` - 上期累计已赚手续费  
- `cur_ed_premium` - 当期已赚保费
- `cur_ed_net_fee` - 当期已赚手续费

## 🔧 技术特点

### 向后兼容
- 保持原有4维度索引和逻辑不变
- 新增6维度索引作为补充

### 性能优化
- 只查询必要的字段，减少数据传输
- 添加复合索引提高查询效率

### 数据准确性
- 6维度精确匹配，避免数据混淆
- 聚合逻辑确保4维度汇总正确

## 🧪 测试要点

### 功能测试
1. 验证6维度索引正确收集历史数据
2. 验证累计值计算的准确性（对比修复前后）
3. 验证4维度汇总逻辑的正确性

### 性能测试  
1. 对比修复前后的查询性能
2. 验证新增索引的效果
3. 监控内存使用情况

### 数据一致性测试
1. 验证6维度数据与4维度汇总的一致性
2. 验证历史数据迁移的正确性

## 🚀 部署步骤

1. **执行数据库脚本**
   ```sql
   -- 执行 sql/add_earned_fields_to_r_table.sql
   ```

2. **部署代码**
   - 部署修改后的Java代码
   - 重启应用服务

3. **数据验证**
   - 运行测试用例验证功能
   - 对比关键业务数据的准确性

## 📊 预期效果

- ✅ 解决维度不匹配导致的数据不准确问题
- ✅ 提高累计值计算的精确度
- ✅ 保持系统性能稳定
- ✅ 为未来扩展更多维度提供基础

## 🔍 监控指标

- 累计值计算准确率
- 查询响应时间
- 内存使用情况
- 数据一致性检查结果

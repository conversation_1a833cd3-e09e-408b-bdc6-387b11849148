---------- drop ---------------
do language plpgsql
$$
    declare
        rec record;
    begin
        for rec in select t.proname, t.prokind from pg_proc t where t.proname ~ '^atr_pack_lic_.*'
            loop
                if rec.prokind = 'f' then
                    call atr_pack_commonutils_proc_drop_func(rec.proname::varchar);
                elsif rec.prokind = 'p' then
                    call atr_pack_commonutils_proc_drop_proc(rec.proname::varchar);
                end if;
            end loop;
    end;
$$;



---------- create ---------------
CREATE FUNCTION atruser.atr_pack_lic_func_get_quota_dym_value(p_entity_id bigint, p_loa_code text, p_quota_code text, p_year_month character varying, p_accident_year_month text, p_default numeric) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
    -- Purpose : 获取基于事故年月的假设值
declare

    v_quota_value numeric(19, 6);

begin

    select dtl.quota_value
    into strict v_quota_value
    from atr_conf_quota_def d,
         atr_conf_quota_dym q,
         atr_conf_quota_dym_detail dtl
    where d.quota_def_id = q.quota_def_id
      and d.dimension = 'G'
      and q.quota_id = dtl.quota_id
      and d.quota_code = p_quota_code
      and q.entity_id = p_entity_id
      and q.dimension_value = p_loa_code
      and q.year_month = p_year_month
      and dtl.accident_year_month = p_accident_year_month
    limit 1;

    return coalesce(v_quota_value, p_default);
exception
    when no_data_found then
        return p_default;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_debug(IN p_action_no character varying, IN p_mark character varying)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_lic_proc_debug(p_action_no, null, p_mark);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_debug(IN p_action_no character varying, IN p_business_info character varying, IN p_mark character varying)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_ecf_util_proc_log('LIC',
                                    'DEBUG',
                                    p_action_no,
                                    p_business_info,
                                    p_mark,
                                    null,
                                    -1);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_error(IN p_action_no text, IN p_business_info text, IN p_mark text, IN p_msg text)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_ecf_util_proc_log('LIC',
                                    'ERROR',
                                    p_action_no,
                                    p_business_info,
                                    p_mark,
                                    p_msg,
                                    null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_info(IN p_action_no character varying, IN p_business_info character varying, IN p_mark character varying, IN p_msg character varying)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_ecf_util_proc_log('LIC',
                                    'INFO',
                                    p_action_no,
                                    p_business_info,
                                    p_mark,
                                    p_msg,
                                    null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc_buss_dd(IN p_action_no text, IN p_entity_id bigint, IN p_year_month text)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------
    -- 计算主要计算项
    ------------------
    v_next_doa    varchar(6);
    v_max_dev_no  numeric(11);
    rec           record;
    v_err_context text;
    v_err         text;
    v_err_mark    varchar(100) := 'CALC_BUSS_DD#error';
begin

    call atr_pack_lic_proc_info(p_action_no,
                                null,
                                'CALC_BUSS_DD#start',
                                null);

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_BUSS_DD#sub#01');

    begin
        -- calc detail:  paid mode
        update atr_buss_dd_lic_icg_calc_detail t
        set paid_mode = (case
                             when t.dev_no = 1 then t.ult
                             when t.dev_no > 1 then
                                     t.ult - (select t2.ult
                                              from atr_buss_dd_lic_icg_calc_detail t2
                                              where t.main_id = t2.main_id
                                                and t2.dev_no = t.dev_no - 1)
            end)
        where exists (select *
                      from atr_buss_dd_lic_icg_calc m
                      where m.action_no = p_action_no
                        and m.entity_id = p_entity_id
                        and m.id = t.main_id);
    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;


    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_BUSS_DD#sub#02');

    begin
        -- 计算业务年月的 paid_mode
        for rec in (select t.main_id,
                           t.accident_year_month,
                           max(t.dev_no) max_dev_no
                    from atr_buss_dd_lic_icg_calc_accident_ym_detail t,
                         atr_buss_dd_lic_icg_calc m
                    where t.main_id = m.id
                      and m.action_no = p_action_no
                    group by t.main_id, t.accident_year_month
                    order by t.main_id, t.accident_year_month desc)
            loop
                if rec.accident_year_month = p_year_month then
                    -- 当期
                    merge into atr_buss_dd_lic_icg_calc_accident_ym_detail t
                    using (select *
                           from atr_buss_dd_lic_icg_calc_detail d
                           where d.main_id = rec.main_id) x
                    on (x.main_id = t.main_id and x.dev_no = t.dev_no and
                        t.accident_year_month = rec.accident_year_month)
                    when matched then
                        update set paid_mode = x.paid_mode;

                elsif rec.accident_year_month > atr_pack_ecf_util_func_add_year_months(p_year_month,
                                                                                       - (rec.max_dev_no - 1)) then
                    -- 还在赔付模式覆盖的事故年月内时
                    v_next_doa := atr_pack_ecf_util_func_next_year_month(rec.accident_year_month);

                    merge into atr_buss_dd_lic_icg_calc_accident_ym_detail t
                    using (select t1.dev_no - 1                           previous_dev_no,
                                  t1.paid_mode,
                                  sum(t1.paid_mode) over (partition by 1) paid_mode_sum
                           from atr_buss_dd_lic_icg_calc_accident_ym_detail t1
                           where t1.main_id = rec.main_id
                             and t1.accident_year_month = v_next_doa
                             and t1.dev_no > 1) x
                    on (t.main_id = rec.main_id and t.accident_year_month = rec.accident_year_month and
                        t.dev_no = x.previous_dev_no)
                    when matched then
                        update
                        set paid_mode = case
                                            when coalesce(x.paid_mode_sum, 0) = 0 then
                                                (case when t.dev_no = 1 then 1 else 0 end)
                                            else
                                                    x.paid_mode / x.paid_mode_sum::numeric
                            end;


                else
                    -- 超出赔付模式覆盖的事故年月时， 第1期固定为 100%， 其他为 null
                    update atr_buss_dd_lic_icg_calc_accident_ym_detail t
                    set paid_mode = (case when t.dev_no = 1 then 1 else null end)
                    where t.main_id = rec.main_id
                      and t.accident_year_month = rec.accident_year_month;

                end if;


            end loop;
    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_BUSS_DD#sub#03');

    begin
        for rec in (select m.id main_id, m.ulae_rate
                    from atr_buss_dd_lic_icg_calc m
                    where m.action_no = p_action_no)
            loop
                select coalesce(max(d.dev_no), -1)
                into strict v_max_dev_no
                from atr_buss_dd_lic_icg_calc_detail d
                where d.main_id = rec.main_id;

                for v_dev_no in 0 .. v_max_dev_no
                    loop
                        -- calc accident yearmonth detail:  IBNR、O/S
                        merge into atr_buss_dd_lic_icg_calc_accident_ym_detail t
                        using (select c.main_id,
                                      c.accident_year_month,
                                      c.ibnr,
                                      c.os,
                                      c.ulae
                               from atr_buss_dd_lic_icg_calc_accident_ym c
                               where c.main_id = rec.main_id) x
                        on (t.main_id = x.main_id and t.dev_no = v_dev_no and
                            t.accident_year_month = x.accident_year_month)
                        when matched then
                            update
                            set ibnr = x.ibnr * t.paid_mode +
                                       coalesce(t.manul_adj_addi_claim, 0),
                                os   = x.os * t.paid_mode +
                                       coalesce(t.manul_adj_addi_claim, 0);

                        -- calc accident yearmonth detail:  ULAE
                        update atr_buss_dd_lic_icg_calc_accident_ym_detail t
                        set ulae = coalesce(t.ibnr * rec.ulae_rate, 0) +
                                   coalesce(t.os, 0) * rec.ulae_rate * 0.5
                        where t.main_id = rec.main_id
                          and t.dev_no = v_dev_no;

                        -- calc icg detail: IBNR、O/S、ULAE
                        update atr_buss_dd_lic_icg_calc_detail t
                        set ibnr_cur = (select sum(y.ibnr)
                                        from atr_buss_dd_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            ibnr_pre = (select sum(y.ibnr)
                                        from atr_buss_dd_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4)),
                            os_cur   = (select sum(y.os)
                                        from atr_buss_dd_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            os_pre   = (select sum(y.os)
                                        from atr_buss_dd_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4)),
                            ulae_cur = (select sum(y.ulae)
                                        from atr_buss_dd_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            ulae_pre = (select sum(y.ulae)
                                        from atr_buss_dd_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4))
                        where t.main_id = rec.main_id
                          and t.dev_no = v_dev_no;

                    end loop;
            end loop;
    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_BUSS_DD#sub#04');

    begin
        -- calc icg:  当前事故年当期赔付、 往年事故年当期赔付
        update atr_buss_dd_lic_icg_calc t
        set claim_paid_cur = (select sum(b.paid_amount)
                              from atr_dap_dd_icg_claim_amount b
                              where b.entity_id = t.entity_id
                                and b.currency_code = t.currency_code
                                and substr(b.accident_year_month, 1, 4) =
                                    substr(t.year_month, 1, 4)
                                and b.portfolio_no = t.portfolio_no
                                and b.icg_no = t.icg_no),
            claim_paid_pre = (select sum(b.paid_amount)
                              from atr_dap_dd_icg_claim_amount b
                              where b.entity_id = t.entity_id
                                and b.currency_code = t.currency_code
                                and b.accident_year_month < substr(t.year_month, 1, 4)
                                and b.portfolio_no = t.portfolio_no
                                and b.icg_no = t.icg_no)
        where t.action_no = p_action_no;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_BUSS_DD#sub#05');

    commit;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc_buss_fo(IN p_action_no text, IN p_entity_id bigint, IN p_year_month text)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------
    -- 计算主要计算项
    ------------------
    v_next_doa    varchar(6);
    v_max_dev_no  numeric(11);
    rec           record;
    v_err_context text;
    v_err         text;
    v_err_mark    varchar(100) := 'CALC_BUSS_FO#error';
begin

    call atr_pack_lic_proc_info(p_action_no,
                                null,
                                'CALC_BUSS_FO#start',
                                null);

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_BUSS_FO#sub#01');

    begin
        -- calc detail:  paid mode
        update atr_buss_fo_lic_icg_calc_detail t
        set paid_mode = (case
                             when t.dev_no = 1 then t.ult
                             when t.dev_no > 1 then
                                     t.ult - (select t2.ult
                                              from atr_buss_fo_lic_icg_calc_detail t2
                                              where t.main_id = t2.main_id
                                                and t2.dev_no = t.dev_no - 1)
            end)
        where exists (select *
                      from atr_buss_fo_lic_icg_calc m
                      where m.action_no = p_action_no
                        and m.entity_id = p_entity_id
                        and m.id = t.main_id);

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;


    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_BUSS_FO#sub#02');

    begin
        -- 计算业务年月的 paid_mode
        for rec in (select t.main_id,
                           t.accident_year_month,
                           max(t.dev_no) max_dev_no
                    from atr_buss_fo_lic_icg_calc_accident_ym_detail t,
                         atr_buss_fo_lic_icg_calc m
                    where t.main_id = m.id
                      and m.action_no = p_action_no
                    group by t.main_id, t.accident_year_month
                    order by t.main_id, t.accident_year_month desc)
            loop
                if rec.accident_year_month = p_year_month then
                    -- 当期
                    merge into atr_buss_fo_lic_icg_calc_accident_ym_detail t
                    using (select *
                           from atr_buss_fo_lic_icg_calc_detail d
                           where d.main_id = rec.main_id) x
                    on (x.main_id = t.main_id and x.dev_no = t.dev_no and
                        t.accident_year_month = rec.accident_year_month)
                    when matched then
                        update set paid_mode = x.paid_mode;

                elsif rec.accident_year_month > atr_pack_ecf_util_func_add_year_months(p_year_month,
                                                                                       - (rec.max_dev_no - 1)) then
                    -- 还在赔付模式覆盖的事故年月内时
                    v_next_doa := atr_pack_ecf_util_func_next_year_month(rec.accident_year_month);

                    merge into atr_buss_fo_lic_icg_calc_accident_ym_detail t
                    using (select t1.dev_no - 1                           previous_dev_no,
                                  t1.paid_mode,
                                  sum(t1.paid_mode) over (partition by 1) paid_mode_sum
                           from atr_buss_fo_lic_icg_calc_accident_ym_detail t1
                           where t1.main_id = rec.main_id
                             and t1.accident_year_month = v_next_doa
                             and t1.dev_no > 1) x
                    on (t.main_id = rec.main_id and t.accident_year_month = rec.accident_year_month and
                        t.dev_no = x.previous_dev_no)
                    when matched then
                        update
                        set paid_mode = case
                                            when coalesce(x.paid_mode_sum, 0) = 0 then
                                                (case when t.dev_no = 1 then 1 else 0 end)
                                            else
                                                    x.paid_mode / x.paid_mode_sum::numeric
                            end;

                else
                    -- 超出赔付模式覆盖的事故年月时， 第1期固定为 100%， 其他为 null
                    update atr_buss_fo_lic_icg_calc_accident_ym_detail t
                    set paid_mode = (case when t.dev_no = 1 then 1 else null end)
                    where t.main_id = rec.main_id
                      and t.accident_year_month = rec.accident_year_month;

                end if;
            end loop;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_BUSS_FO#sub#03');

    begin
        for rec in (select m.id main_id, m.ulae_rate
                    from atr_buss_fo_lic_icg_calc m
                    where m.action_no = p_action_no)
            loop

                select coalesce(max(d.dev_no), -1)
                into strict v_max_dev_no
                from atr_buss_fo_lic_icg_calc_detail d
                where d.main_id = rec.main_id;

                for v_dev_no in 0 .. v_max_dev_no
                    loop
                        -- calc accident yearmonth detail:  IBNR、O/S
                        merge into atr_buss_fo_lic_icg_calc_accident_ym_detail t
                        using (select c.main_id,
                                      c.accident_year_month,
                                      c.ibnr,
                                      c.os,
                                      c.ulae
                               from atr_buss_fo_lic_icg_calc_accident_ym c
                               where c.main_id = rec.main_id) x
                        on (t.main_id = x.main_id and t.dev_no = v_dev_no and
                            t.accident_year_month = x.accident_year_month)
                        when matched then
                            update
                            set ibnr = x.ibnr * t.paid_mode +
                                       coalesce(t.manul_adj_addi_claim, 0),
                                os   = x.os * t.paid_mode +
                                       coalesce(t.manul_adj_addi_claim, 0);

                        -- calc accident yearmonth detail:  ULAE
                        update atr_buss_fo_lic_icg_calc_accident_ym_detail t
                        set ulae = coalesce(t.ibnr * rec.ulae_rate, 0) +
                                   coalesce(t.os, 0) * rec.ulae_rate * 0.5
                        where t.main_id = rec.main_id
                          and t.dev_no = v_dev_no;

                        -- calc icg detail: IBNR、O/S、ULAE
                        update atr_buss_fo_lic_icg_calc_detail t
                        set ibnr_cur = (select sum(y.ibnr)
                                        from atr_buss_fo_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            ibnr_pre = (select sum(y.ibnr)
                                        from atr_buss_fo_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4)),
                            os_cur   = (select sum(y.os)
                                        from atr_buss_fo_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            os_pre   = (select sum(y.os)
                                        from atr_buss_fo_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4)),
                            ulae_cur = (select sum(y.ulae)
                                        from atr_buss_fo_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            ulae_pre = (select sum(y.ulae)
                                        from atr_buss_fo_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4))
                        where t.main_id = rec.main_id
                          and t.dev_no = v_dev_no;

                    end loop;
            end loop;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_BUSS_FO#SUB#04');

    begin
        -- calc icg:  当前事故年当期赔付、 往年事故年当期赔付
        update atr_buss_fo_lic_icg_calc t
        set claim_paid_cur = (select sum(b.paid_amount)
                              from atr_dap_fo_icg_claim_amount b
                              where b.entity_id = t.entity_id
                                and b.currency_code = t.currency_code
                                and substr(b.accident_year_month, 1, 4) =
                                    substr(t.year_month, 1, 4)
                                and b.portfolio_no = t.portfolio_no
                                and b.icg_no = t.icg_no),
            claim_paid_pre = (select sum(b.paid_amount)
                              from atr_dap_fo_icg_claim_amount b
                              where b.entity_id = t.entity_id
                                and b.currency_code = t.currency_code
                                and b.accident_year_month < substr(t.year_month, 1, 4)
                                and b.portfolio_no = t.portfolio_no
                                and b.icg_no = t.icg_no)
        where t.action_no = p_action_no;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_BUSS_FO#sub#05');

    commit;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc_buss_ti(IN p_action_no text, IN p_entity_id bigint, IN p_year_month text)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------
    -- 计算主要计算项
    ------------------
    v_next_doa    varchar(6);
    v_max_dev_no  numeric(11);
    rec           record;
    v_err_context text;
    v_err         text;
    v_err_mark    varchar(100) := 'CALC_BUSS_TI#error';
begin

    call atr_pack_lic_proc_info(p_action_no,
                                null,
                                'CALC_BUSS_TI#start',
                                null);

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_BUSS_TI#sub#01');

    begin
        -- calc detail:  paid mode
        update atr_buss_ti_lic_icg_calc_detail t
        set paid_mode = (case
                             when t.dev_no = 1 then t.ult
                             when t.dev_no > 1 then
                                     t.ult - (select t2.ult
                                              from atr_buss_ti_lic_icg_calc_detail t2
                                              where t.main_id = t2.main_id
                                                and t2.dev_no = t.dev_no - 1)
            end)
        where exists (select *
                      from atr_buss_ti_lic_icg_calc m
                      where m.action_no = p_action_no
                        and m.entity_id = p_entity_id
                        and m.id = t.main_id);

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;


    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_BUSS_TI#sub#02');

    begin
        -- 计算业务年月的 paid_mode
        for rec in (select t.main_id,
                           t.accident_year_month,
                           max(t.dev_no) max_dev_no
                    from atr_buss_ti_lic_icg_calc_accident_ym_detail t,
                         atr_buss_ti_lic_icg_calc m
                    where t.main_id = m.id
                      and m.action_no = p_action_no
                    group by t.main_id, t.accident_year_month
                    order by t.main_id, t.accident_year_month desc)
            loop

                if rec.accident_year_month = p_year_month then
                    -- 当期
                    merge into atr_buss_ti_lic_icg_calc_accident_ym_detail t
                    using (select *
                           from atr_buss_ti_lic_icg_calc_detail d
                           where d.main_id = rec.main_id) x
                    on (x.main_id = t.main_id and x.dev_no = t.dev_no and
                        t.accident_year_month = rec.accident_year_month)
                    when matched then
                        update set paid_mode = x.paid_mode;

                elsif rec.accident_year_month > atr_pack_ecf_util_func_add_year_months(p_year_month,
                                                                                       - (rec.max_dev_no - 1)) then
                    -- 还在赔付模式覆盖的事故年月内时
                    v_next_doa := atr_pack_ecf_util_func_next_year_month(rec.accident_year_month);

                    merge into atr_buss_ti_lic_icg_calc_accident_ym_detail t
                    using (select t1.dev_no - 1                           previous_dev_no,
                                  t1.paid_mode,
                                  sum(t1.paid_mode) over (partition by 1) paid_mode_sum
                           from atr_buss_ti_lic_icg_calc_accident_ym_detail t1
                           where t1.main_id = rec.main_id
                             and t1.accident_year_month = v_next_doa
                             and t1.dev_no > 1) x
                    on (t.main_id = rec.main_id and t.accident_year_month = rec.accident_year_month and
                        t.dev_no = x.previous_dev_no)
                    when matched then
                        update
                        set paid_mode = case
                                            when coalesce(x.paid_mode_sum, 0) = 0 then
                                                (case when t.dev_no = 1 then 1 else 0 end)
                                            else
                                                    x.paid_mode / x.paid_mode_sum::numeric
                            end;

                else
                    -- 超出赔付模式覆盖的事故年月时， 第1期固定为 100%， 其他为 null
                    update atr_buss_ti_lic_icg_calc_accident_ym_detail t
                    set paid_mode = (case when t.dev_no = 1 then 1 else null end)
                    where t.main_id = rec.main_id
                      and t.accident_year_month = rec.accident_year_month;

                end if;
            end loop;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_BUSS_TI#sub#03');

    begin
        for rec in (select m.id main_id, m.ulae_rate
                    from atr_buss_ti_lic_icg_calc m
                    where m.action_no = p_action_no)
            loop

                select coalesce(max(d.dev_no), -1)
                into strict v_max_dev_no
                from atr_buss_ti_lic_icg_calc_detail d
                where d.main_id = rec.main_id;

                for v_dev_no in 0 .. v_max_dev_no
                    loop
                        -- calc accident yearmonth detail:  IBNR、O/S
                        merge into atr_buss_ti_lic_icg_calc_accident_ym_detail t
                        using (select c.main_id,
                                      c.accident_year_month,
                                      c.ibnr,
                                      c.os,
                                      c.ulae
                               from atr_buss_ti_lic_icg_calc_accident_ym c
                               where c.main_id = rec.main_id) x
                        on (t.main_id = x.main_id and t.dev_no = v_dev_no and
                            t.accident_year_month = x.accident_year_month)
                        when matched then
                            update
                            set ibnr = x.ibnr * t.paid_mode +
                                       coalesce(t.manul_adj_addi_claim, 0),
                                os   = x.os * t.paid_mode +
                                       coalesce(t.manul_adj_addi_claim, 0);

                        -- calc accident yearmonth detail:  ULAE
                        update atr_buss_ti_lic_icg_calc_accident_ym_detail t
                        set ulae = coalesce(t.ibnr * rec.ulae_rate, 0) +
                                   coalesce(t.os, 0) * rec.ulae_rate * 0.5
                        where t.main_id = rec.main_id
                          and t.dev_no = v_dev_no;

                        -- calc icg detail: ibnr、o/s、ulae
                        update atr_buss_ti_lic_icg_calc_detail t
                        set ibnr_cur = (select sum(y.ibnr)
                                        from atr_buss_ti_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            ibnr_pre = (select sum(y.ibnr)
                                        from atr_buss_ti_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4)),
                            os_cur   = (select sum(y.os)
                                        from atr_buss_ti_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            os_pre   = (select sum(y.os)
                                        from atr_buss_ti_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4)),
                            ulae_cur = (select sum(y.ulae)
                                        from atr_buss_ti_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            ulae_pre = (select sum(y.ulae)
                                        from atr_buss_ti_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4))
                        where t.main_id = rec.main_id
                          and t.dev_no = v_dev_no;

                    end loop;
            end loop;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_BUSS_TI#sub#04');

    begin
        -- calc icg:  当前事故年当期赔付、 往年事故年当期赔付
        update atr_buss_ti_lic_icg_calc t
        set claim_paid_cur = (select sum(b.paid_amount)
                              from atr_dap_ti_icg_claim_amount b
                              where b.entity_id = t.entity_id
                                and b.currency_code = t.currency_code
                                and substr(b.accident_year_month, 1, 4) =
                                    substr(t.year_month, 1, 4)
                                and b.portfolio_no = t.portfolio_no
                                and b.icg_no = t.icg_no),
            claim_paid_pre = (select sum(b.paid_amount)
                              from atr_dap_ti_icg_claim_amount b
                              where b.entity_id = t.entity_id
                                and b.currency_code = t.currency_code
                                and b.accident_year_month < substr(t.year_month, 1, 4)
                                and b.portfolio_no = t.portfolio_no
                                and b.icg_no = t.icg_no)
        where t.action_no = p_action_no;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_BUSS_TI#sub#05');

    commit;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc_buss_to(IN p_action_no text, IN p_entity_id bigint, IN p_year_month text)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------
    -- 计算主要计算项
    ------------------
    v_next_doa    varchar(6);
    v_max_dev_no  numeric(11);
    rec           record;
    v_err_context text;
    v_err         text;
    v_err_mark    varchar(100) := 'CALC_BUSS_TO#error';
begin

    call atr_pack_lic_proc_info(p_action_no,
                                null,
                                'CALC_BUSS_TO#start',
                                null);

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_BUSS_TO#sub#01');

    begin
        -- calc detail:  paid mode
        update atr_buss_to_lic_icg_calc_detail t
        set paid_mode = (case
                             when t.dev_no = 1 then t.ult
                             when t.dev_no > 1 then
                                     t.ult - (select t2.ult
                                              from atr_buss_to_lic_icg_calc_detail t2
                                              where t.main_id = t2.main_id
                                                and t2.dev_no = t.dev_no - 1)
            end)
        where exists (select *
                      from atr_buss_to_lic_icg_calc m
                      where m.action_no = p_action_no
                        and m.entity_id = p_entity_id
                        and m.id = t.main_id);

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;


    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_BUSS_TO#sub#02');

    begin
        -- 计算业务年月的 paid_mode
        for rec in (select t.main_id,
                           t.accident_year_month,
                           max(t.dev_no) max_dev_no
                    from atr_buss_to_lic_icg_calc_accident_ym_detail t,
                         atr_buss_to_lic_icg_calc m
                    where t.main_id = m.id
                      and m.action_no = p_action_no
                    group by t.main_id, t.accident_year_month
                    order by t.main_id, t.accident_year_month desc)
            loop

                if rec.accident_year_month = p_year_month then
                    -- 当期
                    merge into atr_buss_to_lic_icg_calc_accident_ym_detail t
                    using (select *
                           from atr_buss_to_lic_icg_calc_detail d
                           where d.main_id = rec.main_id) x
                    on (x.main_id = t.main_id and x.dev_no = t.dev_no and
                        t.accident_year_month = rec.accident_year_month)
                    when matched then
                        update set paid_mode = x.paid_mode;

                elsif rec.accident_year_month > atr_pack_ecf_util_func_add_year_months(p_year_month,
                                                                                       - (rec.max_dev_no - 1)) then
                    -- 还在赔付模式覆盖的事故年月内时
                    v_next_doa := atr_pack_ecf_util_func_next_year_month(rec.accident_year_month);

                    merge into atr_buss_to_lic_icg_calc_accident_ym_detail t
                    using (select t1.dev_no - 1                           previous_dev_no,
                                  t1.paid_mode,
                                  sum(t1.paid_mode) over (partition by 1) paid_mode_sum
                           from atr_buss_to_lic_icg_calc_accident_ym_detail t1
                           where t1.main_id = rec.main_id
                             and t1.accident_year_month = v_next_doa
                             and t1.dev_no > 1) x
                    on (t.main_id = rec.main_id and t.accident_year_month = rec.accident_year_month and
                        t.dev_no = x.previous_dev_no)
                    when matched then
                        update
                        set paid_mode = case
                                            when coalesce(x.paid_mode_sum, 0) = 0 then
                                                (case when t.dev_no = 1 then 1 else 0 end)
                                            else
                                                    x.paid_mode / x.paid_mode_sum::numeric
                            end;

                else
                    -- 超出赔付模式覆盖的事故年月时， 第1期固定为 100%， 其他为 null
                    update atr_buss_to_lic_icg_calc_accident_ym_detail t
                    set paid_mode = (case when t.dev_no = 1 then 1 else null end)
                    where t.main_id = rec.main_id
                      and t.accident_year_month = rec.accident_year_month;

                end if;
            end loop;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_BUSS_TO#sub#03');

    begin
        for rec in (select m.id main_id, m.ulae_rate
                    from atr_buss_to_lic_icg_calc m
                    where m.action_no = p_action_no)
            loop

                select coalesce(max(d.dev_no), -1)
                into strict v_max_dev_no
                from atr_buss_to_lic_icg_calc_detail d
                where d.main_id = rec.main_id;

                for v_dev_no in 0 .. v_max_dev_no
                    loop
                        -- calc accident yearmonth detail:  IBNR、O/S
                        merge into atr_buss_to_lic_icg_calc_accident_ym_detail t
                        using (select c.main_id,
                                      c.accident_year_month,
                                      c.ibnr,
                                      c.os,
                                      c.ulae
                               from atr_buss_to_lic_icg_calc_accident_ym c
                               where c.main_id = rec.main_id) x
                        on (t.main_id = x.main_id and t.dev_no = v_dev_no and
                            t.accident_year_month = x.accident_year_month)
                        when matched then
                            update
                            set ibnr = x.ibnr * t.paid_mode +
                                       coalesce(t.manul_adj_addi_claim, 0),
                                os   = x.os * t.paid_mode +
                                       coalesce(t.manul_adj_addi_claim, 0);

                        -- calc accident yearmonth detail:  ULAE
                        update atr_buss_to_lic_icg_calc_accident_ym_detail t
                        set ulae = coalesce(t.ibnr * rec.ulae_rate, 0) +
                                   coalesce(t.os, 0) * rec.ulae_rate * 0.5
                        where t.main_id = rec.main_id
                          and t.dev_no = v_dev_no;

                        -- calc icg detail: IBNR、O/S、ULAE
                        update atr_buss_to_lic_icg_calc_detail t
                        set ibnr_cur = (select sum(y.ibnr)
                                        from atr_buss_to_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            ibnr_pre = (select sum(y.ibnr)
                                        from atr_buss_to_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4)),
                            os_cur   = (select sum(y.os)
                                        from atr_buss_to_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            os_pre   = (select sum(y.os)
                                        from atr_buss_to_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4)),
                            ulae_cur = (select sum(y.ulae)
                                        from atr_buss_to_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and substr(y.accident_year_month, 1, 4) =
                                              substr(p_year_month, 1, 4)),
                            ulae_pre = (select sum(y.ulae)
                                        from atr_buss_to_lic_icg_calc_accident_ym_detail y
                                        where y.main_id = t.main_id
                                          and y.dev_no = t.dev_no
                                          and y.accident_year_month < substr(p_year_month, 1, 4))
                        where t.main_id = rec.main_id
                          and t.dev_no = v_dev_no;

                    end loop;
            end loop;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_BUSS_TO#sub#04');

    begin
        -- calc icg:  当前事故年当期赔付、 往年事故年当期赔付
        update atr_buss_to_lic_icg_calc t
        set claim_paid_cur = (select sum(b.paid_amount)
                              from atr_dap_to_icg_claim_amount b
                              where b.entity_id = t.entity_id
                                and b.currency_code = t.currency_code
                                and substr(b.accident_year_month, 1, 4) =
                                    substr(t.year_month, 1, 4)
                                and b.portfolio_no = t.portfolio_no
                                and b.icg_no = t.icg_no),
            claim_paid_pre = (select sum(b.paid_amount)
                              from atr_dap_to_icg_claim_amount b
                              where b.entity_id = t.entity_id
                                and b.currency_code = t.currency_code
                                and b.accident_year_month < substr(t.year_month, 1, 4)
                                and b.portfolio_no = t.portfolio_no
                                and b.icg_no = t.icg_no)
        where t.action_no = p_action_no;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_BUSS_TO#sub#05');

    commit;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc_init_icg_main(IN p_business_type text, IN p_action_no text, IN p_task_code text, IN p_entity_id bigint, IN p_year_month text, IN p_portfolio_no text)
    LANGUAGE plpgsql
    AS $$
declare
    -----------------
    -- 初始化计算主表
    -----------------
    v_row_count   int;
    v_err_context text;
    v_err         text;
    v_err_mark    varchar(100) := 'CALC_INIT_ICG_MAIN#error';
begin

    call atr_pack_lic_proc_info(p_action_no,
                                'pf=' || p_portfolio_no,
                                'CALC_INIT_ICG_MAIN#start',
                                null);

    begin
        if p_business_type = 'DD' then

            insert into atr_buss_dd_lic_icg_calc(id,
                                                 action_no,
                                                 task_code,
                                                 entity_id,
                                                 currency_code,
                                                 year_month,
                                                 portfolio_no,
                                                 icg_no,
                                                 evaluate_approach,
                                                 loa_code)
            select nextval('atr_seq_buss_dd_lic_icg_calc'), x.*
            from (select distinct *
                  from (select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_dap_dd_icg_claim_amount t
                        where t.entity_id = p_entity_id
                          and t.year_month = p_year_month
                          and (p_portfolio_no is null or
                               t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code
                        union all
                        select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_dap_dd_icg_claim_accident_amount t
                        where t.entity_id = p_entity_id
                          and t.year_month = p_year_month
                          and (p_portfolio_no is null or
                               t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code
                        union all
                        select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_buss_dd_lic_icg_claim_accident_amount t
                        where t.action_no = p_action_no
                          and (p_portfolio_no is null or t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code) alias4
                  order by currency_code, portfolio_no, icg_no) x;

        elsif p_business_type = 'FO' then

            insert into atr_buss_fo_lic_icg_calc(id,
                                                 action_no,
                                                 task_code,
                                                 entity_id,
                                                 currency_code,
                                                 year_month,
                                                 portfolio_no,
                                                 icg_no,
                                                 evaluate_approach,
                                                 loa_code)
            select nextval('atr_seq_buss_fo_lic_icg_calc'), x.*
            from (select distinct *
                  from (select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_dap_fo_icg_claim_amount t
                        where t.entity_id = p_entity_id
                          and t.year_month = p_year_month
                          and (p_portfolio_no is null or
                               t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code
                        union all
                        select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_dap_fo_icg_claim_accident_amount t
                        where t.entity_id = p_entity_id
                          and t.year_month = p_year_month
                          and (p_portfolio_no is null or
                               t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code
                        union all
                        select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_buss_fo_lic_icg_claim_accident_amount t
                        where t.action_no = p_action_no
                          and (p_portfolio_no is null or t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code) alias4
                  order by currency_code, portfolio_no, icg_no) x;

        elsif p_business_type = 'TI' then

            insert into atr_buss_ti_lic_icg_calc(id,
                                                 action_no,
                                                 task_code,
                                                 entity_id,
                                                 currency_code,
                                                 year_month,
                                                 portfolio_no,
                                                 icg_no,
                                                 evaluate_approach,
                                                 loa_code)
            select nextval('atr_seq_buss_ti_lic_icg_calc'), x.*
            from (select distinct *
                  from (select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_dap_ti_icg_claim_amount t
                        where t.entity_id = p_entity_id
                          and t.year_month = p_year_month
                          and (p_portfolio_no is null or
                               t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code
                        union all
                        select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_dap_ti_icg_claim_accident_amount t
                        where t.entity_id = p_entity_id
                          and t.year_month = p_year_month
                          and (p_portfolio_no is null or
                               t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code
                        union all
                        select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_buss_ti_lic_icg_claim_accident_amount t
                        where t.action_no = p_action_no
                          and (p_portfolio_no is null or t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code) alias4
                  order by currency_code, portfolio_no, icg_no) x;

        elsif p_business_type = 'TO' then

            insert into atr_buss_to_lic_icg_calc(id,
                                                 action_no,
                                                 task_code,
                                                 entity_id,
                                                 currency_code,
                                                 year_month,
                                                 portfolio_no,
                                                 icg_no,
                                                 evaluate_approach,
                                                 loa_code)
            select nextval('atr_seq_buss_to_lic_icg_calc'), x.*
            from (select distinct *
                  from (select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_dap_to_icg_claim_amount t
                        where t.entity_id = p_entity_id
                          and t.year_month = p_year_month
                          and (p_portfolio_no is null or
                               t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code
                        union all
                        select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_dap_to_icg_claim_accident_amount t
                        where t.entity_id = p_entity_id
                          and t.year_month = p_year_month
                          and (p_portfolio_no is null or
                               t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code
                        union all
                        select p_action_no,
                               p_task_code,
                               p_entity_id  entity_id,
                               t.currency_code,
                               p_year_month year_month,
                               t.portfolio_no,
                               t.icg_no,
                               t.evaluate_approach,
                               t.loa_code
                        from atr_buss_to_lic_icg_claim_accident_amount t
                        where t.action_no = p_action_no
                          and (p_portfolio_no is null or t.portfolio_no = p_portfolio_no)
                        group by t.currency_code,
                                 t.portfolio_no,
                                 t.icg_no,
                                 t.evaluate_approach,
                                 t.loa_code) alias4
                  order by currency_code, portfolio_no, icg_no) x;

        end if;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, p_portfolio_no, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    get diagnostics v_row_count = row_count;

    commit;

    call atr_pack_lic_proc_info(p_action_no,
                                'pf=' || p_portfolio_no,
                                'create-main',
                                'insert: ' || v_row_count);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc_init_sub_dd(IN p_action_no text, IN p_year_month text)
    LANGUAGE plpgsql
    AS $$
declare

    --------------------------
    -- 初始化主表及其下级信息
    --------------------------
    rec           record;
    v_err_context text;
    v_err         text;
    v_err_mark    varchar(100) := 'CALC_INIT_SUB_DD#error';
begin

    call atr_pack_lic_proc_info(p_action_no, 'DD', 'CALC_INIT_SUB_DD#start', null);

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_INIT_SUB_DD#sub#01');

    begin
        -- update  合同组 主表
        update atr_buss_dd_lic_icg_calc t
        set -- 间接理赔费用比例
            ulae_rate               = atr_pack_ecf_util_func_get_quota_value('QR012',
                                                                             p_action_no,
                                                                             t.id,
                                                                             'C',
                                                                             -1,
                                                                             1,
                                                                             0),
            -- 最小出险年月
            min_accident_year_month = (select min(dym)
                                       from (select min(c1.accident_year_month) dym
                                             from atr_dap_dd_icg_claim_amount c1
                                             where c1.entity_id = t.entity_id
                                               and c1.currency_code = t.currency_code
                                               and c1.portfolio_no = t.portfolio_no
                                               and c1.icg_no = t.icg_no
                                             union
                                             select min(c2.accident_year_month) dym
                                             from atr_dap_dd_icg_claim_accident_amount c2
                                             where c2.entity_id = t.entity_id
                                               and c2.currency_code = t.currency_code
                                               and c2.portfolio_no = t.portfolio_no
                                               and c2.icg_no = t.icg_no
                                             union
                                             select min(c3.accident_year_month) dym
                                             from atr_buss_dd_lic_icg_claim_accident_amount c3
                                             where c3.action_no = p_action_no
                                               and c3.currency_code = t.currency_code
                                               and c3.entity_id = t.entity_id
                                               and c3.portfolio_no = t.portfolio_no
                                               and c3.icg_no = t.icg_no) alias4
                                       where dym <= p_year_month)
        where t.action_no = p_action_no;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'DD', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_INIT_SUB_DD#sub#02');

    begin
        -- 初始化 合同组 明细
        insert into atr_buss_dd_lic_icg_calc_detail (id, main_id, dev_no, ult_ori)
        select nextval('atr_seq_buss_dd_lic_icg_calc_detail'), x.*
        from (select t.id                                      main_id,
                     dev.dev_no,
                     atr_pack_ecf_util_func_get_quota_value('BE009',
                                                            p_action_no,
                                                            t.id,
                                                            'C',
                                                            dev.dev_no,
                                                            0,
                                                            0) ult_ori
              from atr_buss_dd_lic_icg_calc t,
                   atr_conf_dev_no dev
              where dev.dev_no >= 0
                and dev.dev_no <= coalesce(atr_pack_ecf_util_func_get_quota_max_dev_no('BE009',
                                                                                       p_action_no,
                                                                                       t.id,
                                                                                       'C'),
                                           0)
                and t.action_no = p_action_no
              order by t.id, dev.dev_no) x;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'DD', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_INIT_SUB_DD#sub#03');

    begin
        --  初始化  事故年月主表 (合同组)
        insert into atr_buss_dd_lic_icg_calc_accident_ym (id,
                                                          main_id,
                                                          accident_year_month,
                                                          claim_recv_aclc, -- 累计大案件应付索赔
                                                          claim_paid_aclc, -- 累计大案件已付索赔
                                                          cap_effect_reserve_reduction, -- 上限效应的案件准备金减少
                                                          claim_recv, -- 应付赔款
                                                          claim_paid, -- 实付赔款
                                                          ibnr,
                                                          os)
        select nextval('atr_seq_buss_dd_lic_icg_calc_accident_ym'), x.*
        from (select t.id                                            main_id,
                     ym.year_month                                   accident_year_month,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE020',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)        claim_recv_aclc,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE021',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)        claim_paid_aclc,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE022',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)        cap_effect_reserve_reduction,
                     (select coalesce(sum(c.recv_amount), 0)
                      from atr_dap_dd_icg_claim_amount c
                      where c.entity_id = t.entity_id
                        and c.currency_code = t.currency_code
                        and c.portfolio_no = t.portfolio_no
                        and c.icg_no = t.icg_no
                        and c.year_month = t.year_month
                        and c.accident_year_month = ym.year_month)   claim_recv, -- 当期应付
                     (select coalesce(sum(c.paid_amount), 0)
                      from atr_dap_dd_icg_claim_amount c
                      where c.entity_id = t.entity_id
                        and c.currency_code = t.currency_code
                        and c.portfolio_no = t.portfolio_no
                        and c.icg_no = t.icg_no
                        and c.year_month = t.year_month
                        and c.accident_year_month = ym.year_month)   claim_paid, -- 当期实付
                     -(select coalesce(sum(da.ibnr), 0)
                       from atr_buss_dd_lic_icg_claim_accident_amount da
                       where da.entity_id = t.entity_id
                         and da.currency_code = t.currency_code
                         and da.portfolio_no = t.portfolio_no
                         and da.icg_no = t.icg_no
                         and da.action_no = p_action_no
                         --and da.year_month = t.year_month -- ibnr 接口数据是全量，使用等于号即可 (此处与Oracle版本存在差异)
                         and da.accident_year_month = ym.year_month) ibnr,       -- ibnr
                     -(select coalesce(sum(da.os), 0)
                       from atr_dap_dd_icg_claim_accident_amount da
                       where da.entity_id = t.entity_id
                         and da.currency_code = t.currency_code
                         and da.portfolio_no = t.portfolio_no
                         and da.icg_no = t.icg_no
                         and da.year_month = t.year_month
                         and da.accident_year_month = ym.year_month) os          -- o/s
              from atr_buss_dd_lic_icg_calc t,
                   atr_conf_year_month ym
              where ym.year_month <= t.year_month
                and ym.year_month >= t.min_accident_year_month
                and t.action_no = p_action_no
              order by t.id, ym.year_month desc) x;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'DD', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_INIT_SUB_DD#sub#04');

    begin
        -- 更新 0    事故年月主表 (合同组)
        merge into atr_buss_dd_lic_icg_calc_accident_ym t
        using (select *
               from atr_buss_dd_lic_icg_calc m
               where m.action_no = p_action_no) x
        on (x.id = t.main_id)
        when matched then
            update
            set -- 累计应付
                claim_recv_accu = t.claim_recv - t.claim_recv_aclc,
                -- 累计实付
                claim_paid_accu = (select coalesce(sum(c.paid_amount), 0)
                                   from atr_dap_dd_icg_claim_amount c
                                   where c.entity_id = x.entity_id
                                     and c.currency_code = x.currency_code
                                     and c.portfolio_no = x.portfolio_no
                                     and c.icg_no = x.icg_no
                                     and c.year_month <= x.year_month
                                     and c.accident_year_month = t.accident_year_month) - t.claim_paid_aclc;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'DD', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_INIT_SUB_DD#sub#05');

    begin
        -- 更新 1    事故年月主表 (合同组)
        update atr_buss_dd_lic_icg_calc_accident_ym t
        set -- 案例储备
            accr = t.claim_recv_accu - t.claim_paid_accu -
                   t.cap_effect_reserve_reduction
        where exists (select *
                      from atr_buss_dd_lic_icg_calc m
                      where m.id = t.main_id
                        and m.action_no = p_action_no);

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'DD', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_INIT_SUB_DD#sub#06');

    begin
        --  初始化  事故年月明细 (合同组)
        for rec in (select m.id as main_id, m.loa_code, m.entity_id
                    from atr_buss_dd_lic_icg_calc m
                    where m.action_no = p_action_no
                    order by m.id)
            loop
                insert into atr_buss_dd_lic_icg_calc_accident_ym_detail
                    (id, main_id, accident_year_month, dev_no, manul_adj_addi_claim)
                select nextval('atr_seq_buss_dd_lic_icg_calc_accident_ym_detail'), x.*
                from (select dy.main_id,
                             dy.accident_year_month,
                             d.dev_no,
                             (select dev.quota_value
                              from atr_conf_quota_def def,
                                   atr_conf_quota_dym q,
                                   atr_conf_quota_dym_detail dtl,
                                   atr_conf_quota_dym_detail_dev dev
                              where def.quota_def_id = q.quota_def_id
                                and def.dimension = 'G'
                                and q.quota_id = dtl.quota_id
                                and dtl.quota_detail_id = dev.quota_detail_id
                                and def.quota_code = 'BE011'
                                and q.entity_id = rec.entity_id
                                and q.dimension_value = rec.loa_code
                                and q.year_month = p_year_month
                                and dtl.accident_year_month = dy.accident_year_month
                                and dev.dev_period = d.dev_no
                              limit 1)::numeric manul_adj_addi_claim
                      from atr_buss_dd_lic_icg_calc_accident_ym dy,
                           atr_buss_dd_lic_icg_calc_detail d
                      where dy.main_id = d.main_id
                        and dy.main_id = rec.main_id
                      order by dy.id, d.id) x;

            end loop;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'DD', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;


    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_INIT_SUB_DD#sub#08');

    begin
        -- 更新 合同组 明细的 ult
        update atr_buss_dd_lic_icg_calc_detail t
        set ult = t.ult_ori
        where exists (select *
                      from atr_buss_dd_lic_icg_calc m
                      where m.action_no = p_action_no
                        and m.id = t.main_id);

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'DD', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    commit;

    call atr_pack_lic_proc_debug(p_action_no, 'DD', 'CALC_INIT_SUB_DD#sub#09');


end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc_init_sub_fo(IN p_action_no text, IN p_year_month text)
    LANGUAGE plpgsql
    AS $$
declare

    --------------------------
    -- 初始化主表及其下级信息
    --------------------------
    rec           record;
    v_err_context text;
    v_err         text;
    v_err_mark    varchar(100) := 'CALC_INIT_SUB_FO#error';
begin

    call atr_pack_lic_proc_info(p_action_no, 'FO', 'CALC_INIT_SUB_FO#start', null);

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_INIT_SUB_FO#sub#01');

    begin
        -- update  合同组 主表
        update atr_buss_fo_lic_icg_calc t
        set -- 间接理赔费用比例
            ulae_rate               = atr_pack_ecf_util_func_get_quota_value('QR012',
                                                                             p_action_no,
                                                                             t.id,
                                                                             'C',
                                                                             -1,
                                                                             1,
                                                                             0),
            -- 最小出险年月
            min_accident_year_month = (select min(dym)
                                       from (select min(c1.accident_year_month) dym
                                             from atr_dap_fo_icg_claim_amount c1
                                             where c1.entity_id = t.entity_id
                                               and c1.currency_code = t.currency_code
                                               and c1.portfolio_no = t.portfolio_no
                                               and c1.icg_no = t.icg_no
                                             union
                                             select min(c2.accident_year_month) dym
                                             from atr_dap_fo_icg_claim_accident_amount c2
                                             where c2.entity_id = t.entity_id
                                               and c2.currency_code = t.currency_code
                                               and c2.portfolio_no = t.portfolio_no
                                               and c2.icg_no = t.icg_no
                                             union
                                             select min(c3.accident_year_month) dym
                                             from atr_buss_fo_lic_icg_claim_accident_amount c3
                                             where c3.action_no = p_action_no
                                               and c3.currency_code = t.currency_code
                                               and c3.entity_id = t.entity_id
                                               and c3.portfolio_no = t.portfolio_no
                                               and c3.icg_no = t.icg_no) alias4
                                       where dym <= p_year_month)
        where t.action_no = p_action_no;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'FO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_INIT_SUB_FO#sub#02');

    begin
        -- 初始化 合同组 明细
        insert into atr_buss_fo_lic_icg_calc_detail (id, main_id, dev_no, ult_ori)
        select nextval('atr_seq_buss_fo_lic_icg_calc_detail'), x.*
        from (select t.id                                      main_id,
                     dev.dev_no,
                     atr_pack_ecf_util_func_get_quota_value('BE009',
                                                            p_action_no,
                                                            t.id,
                                                            'C',
                                                            dev.dev_no,
                                                            0,
                                                            0) ult_ori
              from atr_buss_fo_lic_icg_calc t,
                   atr_conf_dev_no dev
              where dev.dev_no >= 0
                and dev.dev_no <= coalesce(atr_pack_ecf_util_func_get_quota_max_dev_no('BE009',
                                                                                       p_action_no,
                                                                                       t.id,
                                                                                       'C'),
                                           0)
                and t.action_no = p_action_no
              order by t.id, dev.dev_no) x;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'FO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_INIT_SUB_FO#sub#03');

    begin
        --  初始化  事故年月主表 (合同组)
        insert into atr_buss_fo_lic_icg_calc_accident_ym (id,
                                                          main_id,
                                                          accident_year_month,
                                                          claim_recv_aclc, -- 累计大案件应付索赔
                                                          claim_paid_aclc, -- 累计大案件已付索赔
                                                          cap_effect_reserve_reduction, -- 上限效应的案件准备金减少
                                                          claim_recv, -- 应付赔款
                                                          claim_paid, -- 实付赔款
                                                          ibnr,
                                                          os)
        select nextval('atr_seq_buss_fo_lic_icg_calc_accident_ym'), x.*
        from (select t.id                                           main_id,
                     ym.year_month                                  accident_year_month,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE020',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)       claim_recv_aclc,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE021',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)       claim_paid_aclc,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE022',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)       cap_effect_reserve_reduction,
                     (select coalesce(sum(c.recv_amount), 0)
                      from atr_dap_fo_icg_claim_amount c
                      where c.entity_id = t.entity_id
                        and c.currency_code = t.currency_code
                        and c.portfolio_no = t.portfolio_no
                        and c.icg_no = t.icg_no
                        and c.year_month = t.year_month
                        and c.accident_year_month = ym.year_month)  claim_recv, -- 当期应付
                     (select coalesce(sum(c.paid_amount), 0)
                      from atr_dap_fo_icg_claim_amount c
                      where c.entity_id = t.entity_id
                        and c.currency_code = t.currency_code
                        and c.portfolio_no = t.portfolio_no
                        and c.icg_no = t.icg_no
                        and c.year_month = t.year_month
                        and c.accident_year_month = ym.year_month)  claim_paid, -- 当期实付
                     (select coalesce(sum(da.ibnr), 0)
                      from atr_buss_fo_lic_icg_claim_accident_amount da
                      where da.entity_id = t.entity_id
                        and da.currency_code = t.currency_code
                        and da.portfolio_no = t.portfolio_no
                        and da.icg_no = t.icg_no
                        and da.action_no = p_action_no
                        --and da.year_month = t.year_month -- ibnr 接口数据是全量，使用等于号即可 (此处与Oracle版本存在差异)
                        and da.accident_year_month = ym.year_month) ibnr,       -- ibnr
                     (select coalesce(sum(da.os), 0)
                      from atr_dap_fo_icg_claim_accident_amount da
                      where da.entity_id = t.entity_id
                        and da.currency_code = t.currency_code
                        and da.portfolio_no = t.portfolio_no
                        and da.icg_no = t.icg_no
                        and da.year_month = t.year_month
                        and da.accident_year_month = ym.year_month) os          -- o/s
              from atr_buss_fo_lic_icg_calc t,
                   atr_conf_year_month ym
              where ym.year_month <= t.year_month
                and ym.year_month >= t.min_accident_year_month
                and t.action_no = p_action_no
              order by t.id, ym.year_month desc) x;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'FO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_INIT_SUB_FO#sub#04');

    begin
        -- 更新 0    事故年月主表 (合同组)
        merge into atr_buss_fo_lic_icg_calc_accident_ym t
        using (select *
               from atr_buss_fo_lic_icg_calc m
               where m.action_no = p_action_no) x
        on (x.id = t.main_id)
        when matched then
            update
            set -- 累计应付
                claim_recv_accu = t.claim_recv - t.claim_recv_aclc,
                -- 累计实付
                claim_paid_accu =
                    (select coalesce(sum(c.paid_amount), 0)
                     from atr_dap_fo_icg_claim_amount c
                     where c.entity_id = x.entity_id
                       and c.currency_code = x.currency_code
                       and c.portfolio_no = x.portfolio_no
                       and c.icg_no = x.icg_no
                       and c.year_month <= x.year_month
                       and c.accident_year_month = t.accident_year_month) -
                    t.claim_paid_aclc;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'FO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_INIT_SUB_FO#sub#05');

    begin
        -- 更新 1    事故年月主表 (合同组)
        update atr_buss_fo_lic_icg_calc_accident_ym t
        set -- 案例储备
            accr = t.claim_recv_accu - t.claim_paid_accu -
                   t.cap_effect_reserve_reduction
        where exists (select *
                      from atr_buss_fo_lic_icg_calc m
                      where m.id = t.main_id
                        and m.action_no = p_action_no);

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'FO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_INIT_SUB_FO#sub#06');

    begin
        --  初始化  事故年月明细 (合同组)
        for rec in (select m.id as main_id, m.loa_code, m.entity_id
                    from atr_buss_fo_lic_icg_calc m
                    where m.action_no = p_action_no
                    order by m.id)
            loop
                insert into atr_buss_fo_lic_icg_calc_accident_ym_detail
                    (id, main_id, accident_year_month, dev_no, manul_adj_addi_claim)
                select nextval('atr_seq_buss_fo_lic_icg_calc_accident_ym_detail'), x.*
                from (select dy.main_id,
                             dy.accident_year_month,
                             d.dev_no,
                             (select dev.quota_value
                              from atr_conf_quota_def def,
                                   atr_conf_quota_dym q,
                                   atr_conf_quota_dym_detail dtl,
                                   atr_conf_quota_dym_detail_dev dev
                              where def.quota_def_id = q.quota_def_id
                                and def.dimension = 'G'
                                and q.quota_id = dtl.quota_id
                                and dtl.quota_detail_id = dev.quota_detail_id
                                and def.quota_code = 'BE011'
                                and q.entity_id = rec.entity_id
                                and q.dimension_value = rec.loa_code
                                and q.year_month = p_year_month
                                and dtl.accident_year_month = dy.accident_year_month
                                and dev.dev_period = d.dev_no
                              limit 1)::numeric manul_adj_addi_claim
                      from atr_buss_fo_lic_icg_calc_accident_ym dy,
                           atr_buss_fo_lic_icg_calc_detail d
                      where dy.main_id = d.main_id
                        and dy.main_id = rec.main_id
                      order by dy.id, d.id) x;

            end loop;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'FO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_INIT_SUB_FO#sub#08');

    begin
        -- 更新 合同组 明细的 ult
        update atr_buss_fo_lic_icg_calc_detail t
        set ult = t.ult_ori
        where exists (select *
                      from atr_buss_fo_lic_icg_calc m
                      where m.action_no = p_action_no
                        and m.id = t.main_id);

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'FO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'FO', 'CALC_INIT_SUB_FO#sub#09');

    commit;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc_init_sub_ti(IN p_action_no text, IN p_year_month text)
    LANGUAGE plpgsql
    AS $$
declare

    --------------------------
    -- 初始化主表及其下级信息
    --------------------------
    rec           record;
    v_err_context text;
    v_err         text;
    v_err_mark    varchar(100) := 'CALC_INIT_SUB_TI#error';
begin

    call atr_pack_lic_proc_info(p_action_no, 'TI', 'CALC_INIT_SUB_TI#start', null);

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_INIT_SUB_TI#sub#01');

    begin
        -- update  合同组 主表
        update atr_buss_ti_lic_icg_calc t
        set -- 间接理赔费用比例
            ulae_rate               = atr_pack_ecf_util_func_get_quota_value('QR012',
                                                                             p_action_no,
                                                                             t.id,
                                                                             'C',
                                                                             -1,
                                                                             1,
                                                                             0),
            -- 最小出险年月
            min_accident_year_month = (select min(dym)
                                       from (select min(c1.accident_year_month) dym
                                             from atr_dap_ti_icg_claim_amount c1
                                             where c1.entity_id = t.entity_id
                                               and c1.currency_code = t.currency_code
                                               and c1.portfolio_no = t.portfolio_no
                                               and c1.icg_no = t.icg_no
                                             union
                                             select min(c2.accident_year_month) dym
                                             from atr_dap_ti_icg_claim_accident_amount c2
                                             where c2.entity_id = t.entity_id
                                               and c2.currency_code = t.currency_code
                                               and c2.portfolio_no = t.portfolio_no
                                               and c2.icg_no = t.icg_no
                                             union
                                             select min(c3.accident_year_month) dym
                                             from atr_buss_ti_lic_icg_claim_accident_amount c3
                                             where c3.action_no = p_action_no
                                               and c3.currency_code = t.currency_code
                                               and c3.entity_id = t.entity_id
                                               and c3.portfolio_no = t.portfolio_no
                                               and c3.icg_no = t.icg_no) alias4
                                       where dym <= p_year_month)
        where t.action_no = p_action_no;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TI', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_INIT_SUB_TI#sub#02');

    begin
        -- 初始化 合同组 明细
        insert into atr_buss_ti_lic_icg_calc_detail (id, main_id, dev_no, ult_ori)
        select nextval('atr_seq_buss_ti_lic_icg_calc_detail'), x.*
        from (select t.id                                      main_id,
                     dev.dev_no,
                     atr_pack_ecf_util_func_get_quota_value('BE009',
                                                            p_action_no,
                                                            t.id,
                                                            'C',
                                                            dev.dev_no,
                                                            0,
                                                            0) ult_ori
              from atr_buss_ti_lic_icg_calc t,
                   atr_conf_dev_no dev
              where dev.dev_no >= 0
                and dev.dev_no <= coalesce(atr_pack_ecf_util_func_get_quota_max_dev_no('BE009',
                                                                                       p_action_no,
                                                                                       t.id,
                                                                                       'C'),
                                           0)
                and t.action_no = p_action_no
              order by t.id, dev.dev_no) x;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TI', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_INIT_SUB_TI#sub#03');

    begin
        --  初始化  事故年月主表 (合同组)
        insert into atr_buss_ti_lic_icg_calc_accident_ym (id,
                                                          main_id,
                                                          accident_year_month,
                                                          claim_recv_aclc, -- 累计大案件应付索赔
                                                          claim_paid_aclc, -- 累计大案件已付索赔
                                                          cap_effect_reserve_reduction, -- 上限效应的案件准备金减少
                                                          claim_recv, -- 应付赔款
                                                          claim_paid, -- 实付赔款
                                                          ibnr,
                                                          os)
        select nextval('atr_seq_buss_ti_lic_icg_calc_accident_ym'), x.*
        from (select t.id                                            main_id,
                     ym.year_month                                   accident_year_month,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE020',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)        claim_recv_aclc,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE021',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)        claim_paid_aclc,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE022',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)        cap_effect_reserve_reduction,
                     (select coalesce(sum(c.recv_amount), 0)
                      from atr_dap_ti_icg_claim_amount c
                      where c.entity_id = t.entity_id
                        and c.currency_code = t.currency_code
                        and c.portfolio_no = t.portfolio_no
                        and c.icg_no = t.icg_no
                        and c.year_month = t.year_month
                        and c.accident_year_month = ym.year_month)   claim_recv, -- 当期应付
                     (select coalesce(sum(c.paid_amount), 0)
                      from atr_dap_ti_icg_claim_amount c
                      where c.entity_id = t.entity_id
                        and c.currency_code = t.currency_code
                        and c.portfolio_no = t.portfolio_no
                        and c.icg_no = t.icg_no
                        and c.year_month = t.year_month
                        and c.accident_year_month = ym.year_month)   claim_paid, -- 当期实付
                     -(select coalesce(sum(da.ibnr), 0)
                       from atr_buss_ti_lic_icg_claim_accident_amount da
                       where da.entity_id = t.entity_id
                         and da.currency_code = t.currency_code
                         and da.portfolio_no = t.portfolio_no
                         and da.icg_no = t.icg_no
                         and da.action_no = p_action_no
                         --and da.year_month = t.year_month -- ibnr 接口数据是全量，使用等于号即可 (此处与Oracle版本存在差异)
                         and da.accident_year_month = ym.year_month) ibnr,       -- ibnr
                     (select coalesce(sum(da.os), 0)
                      from atr_dap_ti_icg_claim_accident_amount da
                      where da.entity_id = t.entity_id
                        and da.currency_code = t.currency_code
                        and da.portfolio_no = t.portfolio_no
                        and da.icg_no = t.icg_no
                        and da.year_month = t.year_month
                        and da.accident_year_month = ym.year_month)  os          -- o/s
              from atr_buss_ti_lic_icg_calc t,
                   atr_conf_year_month ym
              where ym.year_month <= t.year_month
                and ym.year_month >= t.min_accident_year_month
                and t.action_no = p_action_no
              order by t.id, ym.year_month desc) x;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TI', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_INIT_SUB_TI#sub#04');

    begin
        -- 更新 0    事故年月主表 (合同组)
        merge into atr_buss_ti_lic_icg_calc_accident_ym t
        using (select *
               from atr_buss_ti_lic_icg_calc m
               where m.action_no = p_action_no) x
        on (x.id = t.main_id)
        when matched then
            update
            set -- 累计应付
                claim_recv_accu = t.claim_recv - t.claim_recv_aclc,
                -- 累计实付
                claim_paid_accu =
                    (select coalesce(sum(c.paid_amount), 0)
                     from atr_dap_ti_icg_claim_amount c
                     where c.entity_id = x.entity_id
                       and c.currency_code = x.currency_code
                       and c.portfolio_no = x.portfolio_no
                       and c.icg_no = x.icg_no
                       and c.year_month <= x.year_month
                       and c.accident_year_month = t.accident_year_month) - t.claim_paid_aclc;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TI', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_INIT_SUB_TI#sub#05');

    begin
        -- 更新 1    事故年月主表 (合同组)
        update atr_buss_ti_lic_icg_calc_accident_ym t
        set -- 案例储备
            accr = t.claim_recv_accu - t.claim_paid_accu -
                   t.cap_effect_reserve_reduction
        where exists (select *
                      from atr_buss_ti_lic_icg_calc m
                      where m.id = t.main_id
                        and m.action_no = p_action_no);

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TI', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_INIT_SUB_TI#sub#06');

    begin
        --  初始化  事故年月明细 (合同组)
        for rec in (select m.id as main_id, m.loa_code, m.entity_id
                    from atr_buss_ti_lic_icg_calc m
                    where m.action_no = p_action_no
                    order by m.id)
            loop
                insert into atr_buss_ti_lic_icg_calc_accident_ym_detail
                    (id, main_id, accident_year_month, dev_no, manul_adj_addi_claim)
                select nextval('atr_seq_buss_ti_lic_icg_calc_accident_ym_detail'), x.*
                from (select dy.main_id,
                             dy.accident_year_month,
                             d.dev_no,
                             (select dev.quota_value
                              from atr_conf_quota_def def,
                                   atr_conf_quota_dym q,
                                   atr_conf_quota_dym_detail dtl,
                                   atr_conf_quota_dym_detail_dev dev
                              where def.quota_def_id = q.quota_def_id
                                and def.dimension = 'G'
                                and q.quota_id = dtl.quota_id
                                and dtl.quota_detail_id = dev.quota_detail_id
                                and def.quota_code = 'BE011'
                                and q.entity_id = rec.entity_id
                                and q.dimension_value = rec.loa_code
                                and q.year_month = p_year_month
                                and dtl.accident_year_month = dy.accident_year_month
                                and dev.dev_period = d.dev_no
                              limit 1)::numeric manul_adj_addi_claim
                      from atr_buss_ti_lic_icg_calc_accident_ym dy,
                           atr_buss_ti_lic_icg_calc_detail d
                      where dy.main_id = d.main_id
                        and dy.main_id = rec.main_id
                      order by dy.id, d.id) x;

            end loop;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TI', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_INIT_SUB_TI#sub#08');

    begin
        -- 更新 合同组 明细的 ult
        update atr_buss_ti_lic_icg_calc_detail t
        set ult = t.ult_ori
        where exists (select *
                      from atr_buss_ti_lic_icg_calc m
                      where m.action_no = p_action_no
                        and m.id = t.main_id);
    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TI', 'CALC_INIT_SUB_TI#sub#09');

    commit;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc_init_sub_to(IN p_action_no text, IN p_year_month text)
    LANGUAGE plpgsql
    AS $$
declare

    --------------------------
    -- 初始化主表及其下级信息
    --------------------------
    rec           record;
    v_err_context text;
    v_err         text;
    v_err_mark    varchar(100) := 'CALC_INIT_SUB_TO#error';
begin

    call atr_pack_lic_proc_info(p_action_no, 'TO', 'CALC_INIT_SUB_TO#start', null);

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_INIT_SUB_TO#sub#01');

    begin
        -- update  合同组 主表
        update atr_buss_to_lic_icg_calc t
        set -- 间接理赔费用比例
            ulae_rate               = atr_pack_ecf_util_func_get_quota_value('QR012',
                                                                             p_action_no,
                                                                             t.id,
                                                                             'C',
                                                                             -1,
                                                                             1,
                                                                             0),
            -- 最小出险年月
            min_accident_year_month = (select min(dym)
                                       from (select min(c1.accident_year_month) dym
                                             from atr_dap_to_icg_claim_amount c1
                                             where c1.entity_id = t.entity_id
                                               and c1.currency_code = t.currency_code
                                               and c1.portfolio_no = t.portfolio_no
                                               and c1.icg_no = t.icg_no
                                             union
                                             select min(c2.accident_year_month) dym
                                             from atr_dap_to_icg_claim_accident_amount c2
                                             where c2.entity_id = t.entity_id
                                               and c2.currency_code = t.currency_code
                                               and c2.portfolio_no = t.portfolio_no
                                               and c2.icg_no = t.icg_no
                                             union
                                             select min(c3.accident_year_month) dym
                                             from atr_buss_to_lic_icg_claim_accident_amount c3
                                             where c3.action_no = p_action_no
                                               and c3.currency_code = t.currency_code
                                               and c3.entity_id = t.entity_id
                                               and c3.portfolio_no = t.portfolio_no
                                               and c3.icg_no = t.icg_no) alias4
                                       where dym <= p_year_month)
        where t.action_no = p_action_no;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_INIT_SUB_TO#sub#02');

    begin
        -- 初始化 合同组 明细
        insert into atr_buss_to_lic_icg_calc_detail (id, main_id, dev_no, ult_ori)
        select nextval('atr_seq_buss_to_lic_icg_calc_detail'), x.*
        from (select t.id                                      main_id,
                     dev.dev_no,
                     atr_pack_ecf_util_func_get_quota_value('BE009',
                                                            p_action_no,
                                                            t.id,
                                                            'C',
                                                            dev.dev_no,
                                                            0,
                                                            0) ult_ori
              from atr_buss_to_lic_icg_calc t,
                   atr_conf_dev_no dev
              where dev.dev_no >= 0
                and dev.dev_no <= coalesce(atr_pack_ecf_util_func_get_quota_max_dev_no('BE009',
                                                                                       p_action_no,
                                                                                       t.id,
                                                                                       'C'),
                                           0)
                and t.action_no = p_action_no
              order by t.id, dev.dev_no) x;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_INIT_SUB_TO#sub#03');

    begin
        --  初始化  事故年月主表 (合同组)
        insert into atr_buss_to_lic_icg_calc_accident_ym (id,
                                                          main_id,
                                                          accident_year_month,
                                                          claim_recv_aclc, -- 累计大案件应付索赔
                                                          claim_paid_aclc, -- 累计大案件已付索赔
                                                          cap_effect_reserve_reduction, -- 上限效应的案件准备金减少
                                                          claim_recv, -- 应付赔款
                                                          claim_paid, -- 实付赔款
                                                          ibnr,
                                                          os)
        select nextval('atr_seq_buss_to_lic_icg_calc_accident_ym'), x.*
        from (select t.id                                           main_id,
                     ym.year_month                                  accident_year_month,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE020',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)       claim_recv_aclc,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE021',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)       claim_paid_aclc,
                     atr_pack_lic_func_get_quota_dym_value(t.entity_id,
                                                           t.loa_code,
                                                           'BE022',
                                                           p_year_month,
                                                           ym.year_month,
                                                           0)       cap_effect_reserve_reduction,
                     (select coalesce(sum(c.recv_amount), 0)
                      from atr_dap_to_icg_claim_amount c
                      where c.entity_id = t.entity_id
                        and c.currency_code = t.currency_code
                        and c.portfolio_no = t.portfolio_no
                        and c.icg_no = t.icg_no
                        and c.year_month = t.year_month
                        and c.accident_year_month = ym.year_month)  claim_recv, -- 当期应付
                     (select coalesce(sum(c.paid_amount), 0)
                      from atr_dap_to_icg_claim_amount c
                      where c.entity_id = t.entity_id
                        and c.currency_code = t.currency_code
                        and c.portfolio_no = t.portfolio_no
                        and c.icg_no = t.icg_no
                        and c.year_month = t.year_month
                        and c.accident_year_month = ym.year_month)  claim_paid, -- 当期实付
                     (select coalesce(sum(da.ibnr), 0)
                      from atr_buss_to_lic_icg_claim_accident_amount da
                      where da.entity_id = t.entity_id
                        and da.currency_code = t.currency_code
                        and da.portfolio_no = t.portfolio_no
                        and da.icg_no = t.icg_no
                        and da.action_no = p_action_no
                        --and da.year_month = t.year_month -- ibnr 接口数据是全量，使用等于号即可 (此处与Oracle版本存在差异)
                        and da.accident_year_month = ym.year_month) ibnr,       -- ibnr
                     (select coalesce(sum(da.os), 0)
                      from atr_dap_to_icg_claim_accident_amount da
                      where da.entity_id = t.entity_id
                        and da.currency_code = t.currency_code
                        and da.portfolio_no = t.portfolio_no
                        and da.icg_no = t.icg_no
                        and da.year_month = t.year_month
                        and da.accident_year_month = ym.year_month) os          -- o/s
              from atr_buss_to_lic_icg_calc t,
                   atr_conf_year_month ym
              where ym.year_month <= t.year_month
                and ym.year_month >= t.min_accident_year_month
                and t.action_no = p_action_no
              order by t.id, ym.year_month desc) x;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_INIT_SUB_TO#sub#04');

    begin
        -- 更新 0    事故年月主表 (合同组)
        merge into atr_buss_to_lic_icg_calc_accident_ym t
        using (select *
               from atr_buss_to_lic_icg_calc m
               where m.action_no = p_action_no) x
        on (x.id = t.main_id)
        when matched then
            update
            set -- 累计应付
                claim_recv_accu = t.claim_recv - t.claim_recv_aclc,
                -- 累计实付
                claim_paid_accu = (select coalesce(sum(c.paid_amount), 0)
                                   from atr_dap_to_icg_claim_amount c
                                   where c.entity_id = x.entity_id
                                     and c.currency_code = x.currency_code
                                     and c.portfolio_no = x.portfolio_no
                                     and c.icg_no = x.icg_no
                                     and c.year_month <= x.year_month
                                     and c.accident_year_month = t.accident_year_month) - t.claim_paid_aclc;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_INIT_SUB_TO#sub#05');

    begin
        -- 更新 1    事故年月主表 (合同组)
        update atr_buss_to_lic_icg_calc_accident_ym t
        set -- 案例储备
            accr = t.claim_recv_accu - t.claim_paid_accu -
                   t.cap_effect_reserve_reduction
        where exists (select *
                      from atr_buss_to_lic_icg_calc m
                      where m.id = t.main_id
                        and m.action_no = p_action_no);

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_INIT_SUB_TO#sub#06');

    begin
        --  初始化  事故年月明细 (合同组)
        for rec in (select m.id as main_id, m.loa_code, m.entity_id
                    from atr_buss_to_lic_icg_calc m
                    where m.action_no = p_action_no
                    order by m.id)
            loop
                insert into atr_buss_to_lic_icg_calc_accident_ym_detail
                    (id, main_id, accident_year_month, dev_no, manul_adj_addi_claim)
                select nextval('atr_seq_buss_to_lic_icg_calc_accident_ym_detail'), x.*
                from (select dy.main_id,
                             dy.accident_year_month,
                             d.dev_no,
                             (select dev.quota_value
                              from atr_conf_quota_def def,
                                   atr_conf_quota_dym q,
                                   atr_conf_quota_dym_detail dtl,
                                   atr_conf_quota_dym_detail_dev dev
                              where def.quota_def_id = q.quota_def_id
                                and def.dimension = 'G'
                                and q.quota_id = dtl.quota_id
                                and dtl.quota_detail_id = dev.quota_detail_id
                                and def.quota_code = 'BE011'
                                and q.entity_id = rec.entity_id
                                and q.dimension_value = rec.loa_code
                                and q.year_month = p_year_month
                                and dtl.accident_year_month = dy.accident_year_month
                                and dev.dev_period = d.dev_no
                              limit 1)::numeric manul_adj_addi_claim
                      from atr_buss_to_lic_icg_calc_accident_ym dy,
                           atr_buss_to_lic_icg_calc_detail d
                      where dy.main_id = d.main_id
                        and dy.main_id = rec.main_id
                      order by dy.id, d.id) x;

            end loop;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, 'TO', v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_INIT_SUB_TO#sub#08');

    begin
        -- 更新 合同组 明细的 ult
        update atr_buss_to_lic_icg_calc_detail t
        set ult = t.ult_ori
        where exists (select *
                      from atr_buss_to_lic_icg_calc m
                      where m.action_no = p_action_no
                        and m.id = t.main_id);

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'TO', 'CALC_INIT_SUB_TO#sub#09');

    commit;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_collect_quota(IN p_business_source_code text, IN p_action_no text, IN p_year_month text)
    LANGUAGE plpgsql
    AS $$
declare

    ---------------------------------------
    -- 收集这个批次所用到的假设值的值
    ---------------------------------------
    v_err_context text;
    v_err         text;
    v_err_mark    varchar(100) := 'init_quota#error';
begin

    call atr_pack_lic_proc_debug(p_action_no, 'step#收集假设值#start');

    begin
        -- 合同组
        -- 非发展期
        insert into atr_buss_quota_value
        (id,
         action_no,
         quota_code,
         dimension,
         dimension_value,
         dev_no,
         quota_value)
        with t_main as
                 (select distinct entity_id, loa_code
                  from atr_buss_dd_lic_icg_calc
                  where p_business_source_code = 'DD'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_fo_lic_icg_calc
                  where p_business_source_code = 'FO'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_ti_lic_icg_calc
                  where p_business_source_code = 'TI'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_to_lic_icg_calc
                  where p_business_source_code = 'TO'
                    and action_no = p_action_no)
        select nextval('ATR_SEQ_BUSS_QUOTA_VALUE') id,
               p_action_no,
               quota_code,
               dimension,
               dimension_value,
               -1                                  dev_no,
               quota_value::numeric
        from (select d.quota_code,
                     q.dimension,
                     q.dimension_value,
                     q.quota_value
              from atr_buss_quota_def d,
                   atr_conf_quota q
              where d.action_no = p_action_no
                --   and d.dimension = 'G'
                and d.dev_is = '0'
                and d.quota_def_id = q.quota_def_id
                and q.valid_is = '1'
                and q.audit_state = '1'
                and q.year_month = p_year_month
                and q.business_source_code = p_business_source_code
                and exists
                  (select 1
                   from t_main m
                   where m.entity_id = q.entity_id
                     and m.loa_code = q.dimension_value)
              order by quota_code, dimension, dimension_value) alias4;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, p_business_source_code, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;
    commit;

    call atr_pack_lic_proc_debug(p_action_no, 'sub#收集假设值-01#start');

    begin
        -- 发展期
        insert into atr_buss_quota_value
        (id,
         action_no,
         quota_code,
         dimension,
         dimension_value,
         dev_no,
         quota_value)
        with t_main as
                 (select distinct entity_id, loa_code
                  from atr_buss_dd_lic_icg_calc
                  where p_business_source_code = 'DD'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_fo_lic_icg_calc
                  where p_business_source_code = 'FO'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_ti_lic_icg_calc
                  where p_business_source_code = 'TI'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_to_lic_icg_calc
                  where p_business_source_code = 'TO'
                    and action_no = p_action_no)
        select nextval('ATR_SEQ_BUSS_QUOTA_VALUE') id,
               p_action_no,
               quota_code,
               dimension,
               dimension_value,
               dev_no,
               quota_value::numeric
        from (select d.quota_code,
                     q.dimension,
                     q.dimension_value,
                     dtl.quota_period dev_no,
                     dtl.quota_value
              from atr_buss_quota_def d,
                   atr_conf_quota q,
                   atr_conf_quota_detail dtl
              where d.action_no = p_action_no
                --  and  d.dimension = 'G'
                and d.dev_is = '1'
                and d.quota_def_id = q.quota_def_id
                and q.valid_is = '1'
                and q.audit_state = '1'
                and q.year_month = p_year_month
                and dtl.quota_id = q.quota_id
                and q.business_source_code = p_business_source_code
                and exists
                  (select 1
                   from t_main m
                   where m.entity_id = q.entity_id
                     and m.loa_code = q.dimension_value)
              order by quota_code,
                       dimension,
                       dimension_value,
                       dev_no) alias4;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, p_business_source_code, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    commit;
    call atr_pack_lic_proc_debug(p_action_no, 'sub#收集假设值-02#start');

    begin
        -- 合同组
        -- 非发展期
        insert into atr_buss_quota_value
        (id,
         action_no,
         quota_code,
         dimension,
         dimension_value,
         dev_no,
         quota_value)
        with t_main as
                 (select distinct entity_id, icg_no
                  from atr_buss_dd_lic_icg_calc
                  where p_business_source_code = 'DD'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_fo_lic_icg_calc
                  where p_business_source_code = 'FO'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_ti_lic_icg_calc
                  where p_business_source_code = 'TI'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_to_lic_icg_calc
                  where p_business_source_code = 'TO'
                    and action_no = p_action_no)
        select nextval('ATR_SEQ_BUSS_QUOTA_VALUE') id,
               p_action_no,
               quota_code,
               dimension,
               dimension_value,
               -1                                  dev_no,
               quota_value::numeric
        from (select d.quota_code,
                     q.dimension,
                     q.dimension_value,
                     q.quota_value
              from atr_buss_quota_def d,
                   atr_conf_quota q
              where d.action_no = p_action_no
                and d.dimension = 'C'
                and d.dev_is = '0'
                and d.quota_def_id = q.quota_def_id
                and q.valid_is = '1'
                and q.audit_state = '1'
                and q.year_month = p_year_month
                and q.business_source_code = p_business_source_code
                and exists
                  (select 1
                   from t_main m
                   where m.entity_id = q.entity_id
                     and m.icg_no = q.dimension_value)
              order by quota_code, dimension, dimension_value) alias4;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, p_business_source_code, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'sub#收集假设值-03#start');

    begin
        -- 发展期
        insert into atr_buss_quota_value
        (id,
         action_no,
         quota_code,
         dimension,
         dimension_value,
         dev_no,
         quota_value)
        with t_main as
                 (select distinct entity_id, icg_no
                  from atr_buss_dd_lic_icg_calc
                  where p_business_source_code = 'DD'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_fo_lic_icg_calc
                  where p_business_source_code = 'FO'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_ti_lic_icg_calc
                  where p_business_source_code = 'TI'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_to_lic_icg_calc
                  where p_business_source_code = 'TO'
                    and action_no = p_action_no)
        select nextval('ATR_SEQ_BUSS_QUOTA_VALUE') id,
               p_action_no,
               quota_code,
               dimension,
               dimension_value,
               dev_no,
               quota_value::numeric
        from (select d.quota_code,
                     q.dimension,
                     q.dimension_value,
                     dtl.quota_period dev_no,
                     dtl.quota_value
              from atr_buss_quota_def d,
                   atr_conf_quota q,
                   atr_conf_quota_detail dtl
              where d.action_no = p_action_no
                and d.dimension = 'C'
                and d.dev_is = '1'
                and d.quota_def_id = q.quota_def_id
                and q.valid_is = '1'
                and q.audit_state = '1'
                and q.year_month = p_year_month
                and dtl.quota_id = q.quota_id
                and q.business_source_code = p_business_source_code
                and exists
                  (select 1
                   from t_main icu
                   where icu.entity_id = q.entity_id
                     and icu.icg_no = q.dimension_value)
              order by quota_code,
                       dimension,
                       dimension_value,
                       dev_no) alias4;
    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lic_proc_error(p_action_no, p_business_source_code, v_err_mark,
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lic_proc_debug(p_action_no, 'step#收集假设值#end');

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_split_accident_amount_dd(IN p_action_no text, IN p_year_month text, IN p_entity_id bigint, IN p_ibnr_import_g_count bigint)
    LANGUAGE plpgsql
    AS $$
declare

----------------
-- 直保&临分分入
-- 计算风险占比
-- 合同组合拆分成合同组
----------------
    v_action_no_back varchar(36);
    v_ev_date        timestamp    := to_date(p_year_month, 'yyyymm');
    rec              record;
    v_count          bigint;
    v_err_context    text;
    v_err            text;
    v_err_mark       varchar(100) := 'SPLIT_ACCIDENT_AMOUNT_DD#error';

begin

    call atr_pack_lic_proc_info(p_action_no, 'DD', 'SPLIT_ACCIDENT_AMOUNT_DD#start', null);

    if p_ibnr_import_g_count = 0 then --合同组维度
        begin
            --LIC 事故费用 （直保&临分分入）
            insert into atr_buss_dd_lic_icg_claim_accident_amount(id,
                                                                  action_no,
                                                                  entity_id,
                                                                  currency_code,
                                                                  accident_year_month,
                                                                  portfolio_no,
                                                                  icg_no,
                                                                  evaluate_approach,
                                                                  loa_code,
                                                                  ibnr,
                                                                  os)
            select nextval('atr_seq_buss_dd_lic_icg_claim_accident_amount'), x.*
            from (select p_action_no,
                         a.entity_id,
                         a.currency_code,
                         a.accident_year_month,
                         a.portfolio_no,
                         a.icg_no,
                         a.evaluate_approach,
                         a.loa_code,
                         sum(a.ibnr),
                         0 -- 预留字段
                  from atr_dap_dd_icg_claim_accident_amount a
                  where a.year_month <= p_year_month
                  group by a.entity_id,
                           a.currency_code,
                           a.portfolio_no,
                           a.icg_no,
                           a.evaluate_approach,
                           a.loa_code,
                           a.accident_year_month
                  having abs(sum(a.ibnr)) > 0.00001) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

    else
        -- 合同组合拆分合同组
        -- LIC 风险占比计算（直保&临分分入， 合同组）- 基础数据
        begin
            insert into atr_buss_dd_lic_icg_risk_ratio(id,
                                                       action_no,
                                                       entity_id,
                                                       currency_code,
                                                       portfolio_no,
                                                       icg_no,
                                                       coverage_amount,
                                                       icg_count)
            select nextval('atr_seq_buss_dd_lic_icg_risk_ratio'), x.*
            from (select p_action_no,
                         p_entity_id            as entity_id,
                         t.currency_code,
                         t.portfolio_no,
                         t.icg_no,
                         sum(t.coverage_amount) as coverage_amount,
                         count(*)               as icg_count
                  from atr_dap_dd_premium t
                  where t.entity_id = p_entity_id
                    and t.year_month <= p_year_month
                    and t.expiry_date >= v_ev_date
                    and exists (select *
                                from atr_dap_dd_icp_claim_accident_amount m
                                where m.entity_id = t.entity_id
                                  and m.currency_code = t.currency_code
                                  and m.year_month = p_year_month
                                  and m.portfolio_no = t.portfolio_no)
                  group by t.currency_code,
                           t.portfolio_no,
                           t.icg_no
                  order by t.currency_code, t.portfolio_no, t.icg_no) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        -- LIC 风险占比计算（直保&临分分入， 合同组）- 基础数据 不存在， 则从过往年月去查找
        begin
            for rec in (select distinct r.currency_code, r.portfolio_no
                        from atr_dap_dd_icp_claim_accident_amount r
                        where r.entity_id = p_entity_id
                          and r.year_month = p_year_month
                          and not exists (select *
                                          from atr_buss_dd_lic_icg_risk_ratio t
                                          where t.action_no = p_action_no
                                            and t.portfolio_no = r.portfolio_no
                                            and t.entity_id = r.entity_id
                                            and t.currency_code = r.currency_code)
                        order by r.portfolio_no)
                loop
                    begin
                        select g2.action_no
                        into strict v_action_no_back
                        from atr_buss_dd_lic_icg_risk_ratio g2,
                             atr_buss_lic_action a
                        where g2.entity_id = p_entity_id
                          and g2.currency_code = rec.currency_code
                          and g2.portfolio_no = rec.portfolio_no
                          and a.action_no = g2.action_no
                          and a.entity_id = g2.entity_id
                          and a.currency_code = g2.currency_code
                          and a.business_source_code = 'DD'
                          and a.confirm_is = '1'
                        order by a.year_month desc
                            fetch next 1 rows only;

                    exception
                        when no_data_found then
                            v_action_no_back := null;

                    end;

                    if v_action_no_back is not null then
                        insert into atr_buss_dd_lic_icg_risk_ratio(id,
                                                                   action_no,
                                                                   entity_id,
                                                                   currency_code,
                                                                   portfolio_no,
                                                                   icg_no,
                                                                   coverage_amount,
                                                                   icg_count)
                        select nextval('atr_seq_buss_dd_lic_icg_risk_ratio'), x.*
                        from (select p_action_no, -- 用当前的 action_no
                                     entity_id,
                                     currency_code,
                                     portfolio_no,
                                     icg_no,
                                     coverage_amount,
                                     icg_count
                              from atr_buss_dd_lic_icg_risk_ratio g2
                              where g2.action_no = v_action_no_back
                                and g2.entity_id = p_entity_id
                                and g2.currency_code = rec.currency_code
                                and g2.portfolio_no = rec.portfolio_no
                              order by g2.id) x;
                    else
                        insert into atr_buss_dd_lic_icg_risk_ratio(id,
                                                                   action_no,
                                                                   entity_id,
                                                                   currency_code,
                                                                   portfolio_no,
                                                                   icg_no,
                                                                   coverage_amount,
                                                                   icg_count)

                        select nextval('atr_seq_buss_dd_lic_icg_risk_ratio'),
                               p_action_no, -- 用当前的 action_no
                               p_entity_id,
                               rec.currency_code,
                               rec.portfolio_no,
                               rec.portfolio_no || p_year_month || '**' icg_no,
                               1,
                               1;
                    end if;
                end loop;

            -- 检查合同组是否重复
            select count(*)
            into strict v_count
            from (select 1
                  from atr_buss_dd_lic_icg_risk_ratio t
                  where t.action_no = p_action_no
                  group by t.portfolio_no, t.icg_no
                  having count(*) > 1) alias2;

            commit;

            if v_count > 0 then
                raise '存在重复的合同组';
            end if;
        end;

        --LIC 合同组风险占比 汇总至合同组合风险占比表,用于计算合同组的风险占比
        begin
            insert into atr_buss_dd_lic_icp_risk_ratio(id,
                                                       action_no,
                                                       entity_id,
                                                       currency_code,
                                                       portfolio_no,
                                                       coverage_amount,
                                                       icg_count)
            select nextval('atr_seq_buss_dd_lic_icp_risk_ratio'), x.*
            from (select t.action_no,
                         t.entity_id,
                         t.currency_code,
                         t.portfolio_no,
                         sum(t.coverage_amount) as coverage_amount,
                         sum(t.icg_count)       as icg_count
                  from atr_buss_dd_lic_icg_risk_ratio t
                  where t.action_no = p_action_no
                  group by t.action_no, t.entity_id, t.currency_code, t.portfolio_no) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        -- LIC 风险占比计算（直保&临分分入， 合同组）- 风险占比计算
        begin
            -- 计算风险占比
            merge into atr_buss_dd_lic_icg_risk_ratio t
            using (select *
                   from atr_buss_dd_lic_icp_risk_ratio p
                   where p.action_no = p_action_no) x
            on (x.action_no = t.action_no and x.entity_id = t.entity_id and x.currency_code = t.currency_code and
                x.portfolio_no = t.portfolio_no)
            when matched then
                update
                set risk_ratio =
                        (case
                             when x.coverage_amount is not null and x.coverage_amount <> 0 then
                                 t.coverage_amount / x.coverage_amount::numeric
                             when x.icg_count is not null and x.icg_count <> 0 then
                                 t.icg_count / x.icg_count::numeric
                            end);

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        commit;

        -- LIC 风险占比计算（直保&临分分入， 合同组）- 尾差处理
        begin
            merge into atr_buss_dd_lic_icg_risk_ratio t1
            using (select id
                   from (select t.id,
                                row_number() over (partition by t.portfolio_no order by t.risk_ratio desc) rn
                         from atr_buss_dd_lic_icg_risk_ratio t
                         where t.action_no = p_action_no) alias4
                   where rn = 1) x
            on (t1.id = x.id)
            when matched then
                update
                set risk_ratio = t1.risk_ratio + 1 -
                                 (select sum(t2.risk_ratio)
                                  from atr_buss_dd_lic_icg_risk_ratio t2
                                  where t2.action_no = t1.action_no
                                    and t2.portfolio_no = t1.portfolio_no);
        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        --LIC 事故费用 （直保&临分分入） 合同组合 拆分成 合同组
        begin
            insert into atr_buss_dd_lic_icg_claim_accident_amount(id,
                                                                  action_no,
                                                                  entity_id,
                                                                  currency_code,
                                                                  accident_year_month,
                                                                  portfolio_no,
                                                                  icg_no,
                                                                  evaluate_approach,
                                                                  loa_code,
                                                                  ibnr,
                                                                  os)
            select nextval('atr_seq_buss_dd_lic_icg_claim_accident_amount'), x.*
            from (select p_action_no,
                         a.entity_id,
                         a.currency_code,
                         a.accident_year_month,
                         a.portfolio_no,
                         b.icg_no,
                         a.evaluate_approach,
                         a.loa_code,
                         a.ibnr * b.risk_ratio as ibnr,
                         null::numeric         as os
                  from atr_dap_dd_icp_claim_accident_amount a,
                       atr_buss_dd_lic_icg_risk_ratio b
                  where a.year_month = p_year_month
                    and b.action_no = p_action_no
                    and a.portfolio_no = b.portfolio_no
                    and a.entity_id = b.entity_id
                    and a.currency_code = b.currency_code) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;
    end if;

    commit;

    call atr_pack_lic_proc_info(p_action_no, 'DD', 'SPLIT_ACCIDENT_AMOUNT_DD#end', null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_split_accident_amount_fo(IN p_action_no text, IN p_year_month text, IN p_entity_id bigint, IN p_ibnr_import_g_count bigint)
    LANGUAGE plpgsql
    AS $$
declare

----------------
-- 临分分出
-- 计算风险占比
-- 合同组合拆分成合同组
----------------
    v_action_no_back varchar(36);
    v_ev_date        timestamp    := to_date(p_year_month, 'yyyymm');
    rec              record;
    v_count          bigint;
    v_err_context    text;
    v_err            text;
    v_err_mark       varchar(100) := 'SPLIT_ACCIDENT_AMOUNT_FO#error';

begin

    call atr_pack_lic_proc_info(p_action_no, 'FO', 'SPLIT_ACCIDENT_AMOUNT_FO#start', null);

    if p_ibnr_import_g_count = 0 then --合同组维度
        begin
            --LIC 事故费用 （临分分出）
            insert into atr_buss_fo_lic_icg_claim_accident_amount(id,
                                                                  action_no,
                                                                  entity_id,
                                                                  currency_code,
                                                                  accident_year_month,
                                                                  portfolio_no,
                                                                  icg_no,
                                                                  evaluate_approach,
                                                                  loa_code,
                                                                  ibnr,
                                                                  os)
            select nextval('atr_seq_buss_fo_lic_icg_claim_accident_amount'), x.*
            from (select p_action_no,
                         a.entity_id,
                         a.currency_code,
                         a.accident_year_month,
                         a.portfolio_no,
                         a.icg_no,
                         a.evaluate_approach,
                         a.loa_code,
                         sum(a.ibnr),
                         0 -- 预留字段
                  from atr_dap_fo_icg_claim_accident_amount a
                  where a.year_month <= p_year_month
                  group by a.entity_id,
                           a.currency_code,
                           a.portfolio_no,
                           a.icg_no,
                           a.evaluate_approach,
                           a.loa_code,
                           a.accident_year_month
                  having abs(sum(a.ibnr)) > 0.00001) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

    else
        -- 合同组合拆分合同组
        -- LIC 风险占比计算（临分分出， 合同组）- 基础数据
        begin
            insert into atr_buss_fo_lic_icg_risk_ratio(id,
                                                       action_no,
                                                       entity_id,
                                                       currency_code,
                                                       portfolio_no,
                                                       icg_no,
                                                       coverage_amount,
                                                       icg_count)
            select nextval('atr_seq_buss_fo_lic_icg_risk_ratio'), x.*
            from (select p_action_no,
                         p_entity_id            as entity_id,
                         t.currency_code,
                         t.portfolio_no,
                         t.icg_no,
                         sum(t.coverage_amount) as coverage_amount,
                         count(*)               as icg_count
                  from atr_dap_fo_premium t
                  where t.entity_id = p_entity_id
                    and t.year_month <= p_year_month
                    and t.expiry_date >= v_ev_date
                    and exists (select *
                                from atr_dap_fo_icp_claim_accident_amount m
                                where m.entity_id = t.entity_id
                                  and m.currency_code = t.currency_code
                                  and m.year_month = p_year_month
                                  and m.portfolio_no = t.portfolio_no)
                  group by t.currency_code,
                           t.portfolio_no,
                           t.icg_no
                  order by t.currency_code, t.portfolio_no, t.icg_no) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        -- LIC 风险占比计算（临分分出， 合同组）- 基础数据 不存在， 则从过往年月去查找
        begin
            for rec in (select distinct r.currency_code, r.portfolio_no
                        from atr_dap_fo_icp_claim_accident_amount r
                        where r.entity_id = p_entity_id
                          and r.year_month = p_year_month
                          and not exists (select *
                                          from atr_buss_fo_lic_icg_risk_ratio t
                                          where t.action_no = p_action_no
                                            and t.portfolio_no = r.portfolio_no
                                            and t.entity_id = r.entity_id
                                            and t.currency_code = r.currency_code)
                        order by r.portfolio_no)
                loop
                    begin
                        select g2.action_no
                        into strict v_action_no_back
                        from atr_buss_fo_lic_icg_risk_ratio g2,
                             atr_buss_lic_action a
                        where g2.entity_id = p_entity_id
                          and g2.currency_code = rec.currency_code
                          and g2.portfolio_no = rec.portfolio_no
                          and a.action_no = g2.action_no
                          and a.entity_id = g2.entity_id
                          and a.currency_code = g2.currency_code
                          and a.business_source_code = 'FO'
                          and a.confirm_is = '1'
                        order by a.year_month desc
                            fetch next 1 rows only;

                    exception
                        when no_data_found then
                            v_action_no_back := null;

                    end;

                    if v_action_no_back is not null then
                        insert into atr_buss_fo_lic_icg_risk_ratio(id,
                                                                   action_no,
                                                                   entity_id,
                                                                   currency_code,
                                                                   portfolio_no,
                                                                   icg_no,
                                                                   coverage_amount,
                                                                   icg_count)
                        select nextval('atr_seq_buss_fo_lic_icg_risk_ratio'), x.*
                        from (select p_action_no, -- 用当前的 action_no
                                     entity_id,
                                     currency_code,
                                     portfolio_no,
                                     icg_no,
                                     coverage_amount,
                                     icg_count
                              from atr_buss_fo_lic_icg_risk_ratio g2
                              where g2.action_no = v_action_no_back
                                and g2.entity_id = p_entity_id
                                and g2.currency_code = rec.currency_code
                                and g2.portfolio_no = rec.portfolio_no
                              order by g2.id) x;
                    else
                        insert into atr_buss_fo_lic_icg_risk_ratio(id,
                                                                   action_no,
                                                                   entity_id,
                                                                   currency_code,
                                                                   portfolio_no,
                                                                   icg_no,
                                                                   coverage_amount,
                                                                   icg_count)

                        select nextval('atr_seq_buss_fo_lic_icg_risk_ratio'),
                               p_action_no, -- 用当前的 action_no
                               p_entity_id,
                               rec.currency_code,
                               rec.portfolio_no,
                               rec.portfolio_no || p_year_month || '**' icg_no,
                               1,
                               1;
                    end if;
                end loop;

            -- 检查合同组是否重复
            select count(*)
            into strict v_count
            from (select 1
                  from atr_buss_fo_lic_icg_risk_ratio t
                  where t.action_no = p_action_no
                  group by t.portfolio_no, t.icg_no
                  having count(*) > 1) alias2;

            commit;

            if v_count > 0 then
                raise '存在重复的合同组';
            end if;
        end ;

        --LIC 合同组风险占比 汇总至合同组合风险占比表,用于计算合同组的风险占比
        begin
            insert into atr_buss_fo_lic_icp_risk_ratio(id,
                                                       action_no,
                                                       entity_id,
                                                       currency_code,
                                                       portfolio_no,
                                                       coverage_amount,
                                                       icg_count)
            select nextval('atr_seq_buss_fo_lic_icp_risk_ratio'), x.*
            from (select t.action_no,
                         t.entity_id,
                         t.currency_code,
                         t.portfolio_no,
                         sum(t.coverage_amount) as coverage_amount,
                         sum(t.icg_count)       as icg_count
                  from atr_buss_fo_lic_icg_risk_ratio t
                  where t.action_no = p_action_no
                  group by t.action_no, t.entity_id, t.currency_code, t.portfolio_no) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        -- LIC 风险占比计算（临分分出， 合同组）- 风险占比计算
        begin
            -- 计算风险占比
            merge into atr_buss_fo_lic_icg_risk_ratio t
            using (select *
                   from atr_buss_fo_lic_icp_risk_ratio p
                   where p.action_no = p_action_no) x
            on (x.action_no = t.action_no and x.entity_id = t.entity_id and x.currency_code = t.currency_code and
                x.portfolio_no = t.portfolio_no)
            when matched then
                update
                set risk_ratio =
                        (case
                             when x.coverage_amount is not null and x.coverage_amount <> 0 then
                                     t.coverage_amount / x.coverage_amount::numeric
                             when x.icg_count is not null and x.icg_count <> 0 then
                                     t.icg_count / x.icg_count::numeric
                            end);

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        commit;

        -- LIC 风险占比计算（临分分出， 合同组）- 尾差处理
        begin
            merge into atr_buss_fo_lic_icg_risk_ratio t1
            using (select id
                   from (select t.id,
                                row_number() over (partition by t.portfolio_no order by t.risk_ratio desc) rn
                         from atr_buss_fo_lic_icg_risk_ratio t
                         where t.action_no = p_action_no) alias4
                   where rn = 1) x
            on (t1.id = x.id)
            when matched then
                update
                set risk_ratio = t1.risk_ratio + 1 -
                                 (select sum(t2.risk_ratio)
                                  from atr_buss_fo_lic_icg_risk_ratio t2
                                  where t2.action_no = t1.action_no
                                    and t2.portfolio_no = t1.portfolio_no);
        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        --LIC 事故费用 （临分分出） 合同组合 拆分成 合同组
        begin
            insert into atr_buss_fo_lic_icg_claim_accident_amount(id,
                                                                  action_no,
                                                                  entity_id,
                                                                  currency_code,
                                                                  accident_year_month,
                                                                  portfolio_no,
                                                                  icg_no,
                                                                  evaluate_approach,
                                                                  loa_code,
                                                                  ibnr,
                                                                  os)
            select nextval('atr_seq_buss_fo_lic_icg_claim_accident_amount'), x.*
            from (select p_action_no,
                         a.entity_id,
                         a.currency_code,
                         a.accident_year_month,
                         a.portfolio_no,
                         b.icg_no,
                         a.evaluate_approach,
                         a.loa_code,
                         a.ibnr * b.risk_ratio as ibnr,
                         null::numeric         as os
                  from atr_dap_fo_icp_claim_accident_amount a,
                       atr_buss_fo_lic_icg_risk_ratio b
                  where a.year_month = p_year_month
                    and b.action_no = p_action_no
                    and a.portfolio_no = b.portfolio_no
                    and a.entity_id = b.entity_id
                    and a.currency_code = b.currency_code) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;
    end if;

    commit;

    call atr_pack_lic_proc_info(p_action_no, 'FO', 'SPLIT_ACCIDENT_AMOUNT_FO#end', null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_split_accident_amount_ti(IN p_action_no text, IN p_year_month text, IN p_entity_id bigint, IN p_ibnr_import_g_count bigint)
    LANGUAGE plpgsql
    AS $$
declare

----------------
-- 合约分入
-- 计算风险占比
-- 合同组合拆分成合同组
----------------
    v_action_no_back varchar(36);
    v_ev_date        timestamp    := to_date(p_year_month, 'yyyymm');
    rec              record;
    v_count          bigint;
    v_err_context    text;
    v_err            text;
    v_err_mark       varchar(100) := 'SPLIT_ACCIDENT_AMOUNT_TI#error';

begin

    call atr_pack_lic_proc_info(p_action_no, 'TI', 'SPLIT_ACCIDENT_AMOUNT_TI#start', null);

    if p_ibnr_import_g_count = 0 then --合同组维度
        begin
            --LIC 事故费用 （合约分入）
            insert into atr_buss_ti_lic_icg_claim_accident_amount(id,
                                                                  action_no,
                                                                  entity_id,
                                                                  currency_code,
                                                                  accident_year_month,
                                                                  portfolio_no,
                                                                  icg_no,
                                                                  evaluate_approach,
                                                                  loa_code,
                                                                  ibnr,
                                                                  os)
            select nextval('atr_seq_buss_ti_lic_icg_claim_accident_amount'), x.*
            from (select p_action_no,
                         a.entity_id,
                         a.currency_code,
                         a.accident_year_month,
                         a.portfolio_no,
                         a.icg_no,
                         a.evaluate_approach,
                         a.loa_code,
                         sum(a.ibnr),
                         0 -- 预留字段
                  from atr_dap_ti_icg_claim_accident_amount a
                  where a.year_month <= p_year_month
                  group by a.entity_id,
                           a.currency_code,
                           a.portfolio_no,
                           a.icg_no,
                           a.evaluate_approach,
                           a.loa_code,
                           a.accident_year_month
                  having abs(sum(a.ibnr)) > 0.00001) x;
        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;
    else
        -- 合同组合拆分合同组
        -- LIC 风险占比计算（合约分入， 合同组）- 基础数据
        begin
            insert into atr_buss_ti_lic_icg_risk_ratio(id,
                                                       action_no,
                                                       entity_id,
                                                       currency_code,
                                                       portfolio_no,
                                                       icg_no,
                                                       coverage_amount,
                                                       icg_count)
            select nextval('atr_seq_buss_ti_lic_icg_risk_ratio'), x.*
            from (select p_action_no,
                         p_entity_id            as entity_id,
                         t.currency_code,
                         t.portfolio_no,
                         t.icg_no,
                         sum(t.coverage_amount) as coverage_amount,
                         count(*)               as icg_count
                  from atr_dap_ti_premium t
                  where t.entity_id = p_entity_id
                    and t.year_month <= p_year_month
                    and t.expiry_date >= v_ev_date
                    and exists (select *
                                from atr_dap_ti_icp_claim_accident_amount m
                                where m.entity_id = t.entity_id
                                  and m.currency_code = t.currency_code
                                  and m.year_month = p_year_month
                                  and m.portfolio_no = t.portfolio_no)
                  group by t.currency_code,
                           t.portfolio_no,
                           t.icg_no
                  order by t.currency_code, t.portfolio_no, t.icg_no) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        -- LIC 风险占比计算（合约分入， 合同组）- 基础数据 不存在， 则从过往年月去查找
        begin
            for rec in (select distinct r.currency_code, r.portfolio_no
                        from atr_dap_ti_icp_claim_accident_amount r
                        where r.entity_id = p_entity_id
                          and r.year_month = p_year_month
                          and not exists (select *
                                          from atr_buss_ti_lic_icg_risk_ratio t
                                          where t.action_no = p_action_no
                                            and t.portfolio_no = r.portfolio_no
                                            and t.entity_id = r.entity_id
                                            and t.currency_code = r.currency_code)
                        order by r.portfolio_no)
                loop
                    begin
                        select g2.action_no
                        into strict v_action_no_back
                        from atr_buss_ti_lic_icg_risk_ratio g2,
                             atr_buss_lic_action a
                        where g2.entity_id = p_entity_id
                          and g2.currency_code = rec.currency_code
                          and g2.portfolio_no = rec.portfolio_no
                          and a.action_no = g2.action_no
                          and a.entity_id = g2.entity_id
                          and a.currency_code = g2.currency_code
                          and a.business_source_code = 'TI'
                          and a.confirm_is = '1'
                        order by a.year_month desc
                            fetch next 1 rows only;

                    exception
                        when no_data_found then
                            v_action_no_back := null;

                    end;

                    if v_action_no_back is not null then
                        insert into atr_buss_ti_lic_icg_risk_ratio(id,
                                                                   action_no,
                                                                   entity_id,
                                                                   currency_code,
                                                                   portfolio_no,
                                                                   icg_no,
                                                                   coverage_amount,
                                                                   icg_count)
                        select nextval('atr_seq_buss_ti_lic_icg_risk_ratio'), x.*
                        from (select p_action_no, -- 用当前的 action_no
                                     entity_id,
                                     currency_code,
                                     portfolio_no,
                                     icg_no,
                                     coverage_amount,
                                     icg_count
                              from atr_buss_ti_lic_icg_risk_ratio g2
                              where g2.action_no = v_action_no_back
                                and g2.entity_id = p_entity_id
                                and g2.currency_code = rec.currency_code
                                and g2.portfolio_no = rec.portfolio_no
                              order by g2.id) x;
                    else
                        insert into atr_buss_ti_lic_icg_risk_ratio(id,
                                                                   action_no,
                                                                   entity_id,
                                                                   currency_code,
                                                                   portfolio_no,
                                                                   icg_no,
                                                                   coverage_amount,
                                                                   icg_count)

                        select nextval('atr_seq_buss_ti_lic_icg_risk_ratio'),
                               p_action_no, -- 用当前的 action_no
                               p_entity_id,
                               rec.currency_code,
                               rec.portfolio_no,
                               rec.portfolio_no || p_year_month || '**' icg_no,
                               1,
                               1;
                    end if;
                end loop;

            -- 检查合同组是否重复
            select count(*)
            into strict v_count
            from (select 1
                  from atr_buss_ti_lic_icg_risk_ratio t
                  where t.action_no = p_action_no
                  group by t.portfolio_no, t.icg_no
                  having count(*) > 1) alias2;

            commit;

            if v_count > 0 then
                raise '存在重复的合同组';
            end if;
        end;

        --LIC 合同组风险占比 汇总至合同组合风险占比表,用于计算合同组的风险占比
        begin
            insert into atr_buss_ti_lic_icp_risk_ratio(id,
                                                       action_no,
                                                       entity_id,
                                                       currency_code,
                                                       portfolio_no,
                                                       coverage_amount,
                                                       icg_count)
            select nextval('atr_seq_buss_ti_lic_icp_risk_ratio'), x.*
            from (select t.action_no,
                         t.entity_id,
                         t.currency_code,
                         t.portfolio_no,
                         sum(t.coverage_amount) as coverage_amount,
                         sum(t.icg_count)       as icg_count
                  from atr_buss_ti_lic_icg_risk_ratio t
                  where t.action_no = p_action_no
                  group by t.action_no, t.entity_id, t.currency_code, t.portfolio_no) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        -- LIC 风险占比计算（合约分入， 合同组）- 风险占比计算
        begin
            -- 计算风险占比
            merge into atr_buss_ti_lic_icg_risk_ratio t
            using (select *
                   from atr_buss_ti_lic_icp_risk_ratio p
                   where p.action_no = p_action_no) x
            on (x.action_no = t.action_no and x.entity_id = t.entity_id and x.currency_code = t.currency_code and
                x.portfolio_no = t.portfolio_no)
            when matched then
                update
                set risk_ratio =
                        (case
                             when x.coverage_amount is not null and x.coverage_amount <> 0 then
                                 t.coverage_amount / x.coverage_amount::numeric
                             when x.icg_count is not null and x.icg_count <> 0 then
                                 t.icg_count / x.icg_count::numeric
                            end);

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        commit;

        -- LIC 风险占比计算（合约分入， 合同组）- 尾差处理
        begin
            merge into atr_buss_ti_lic_icg_risk_ratio t1
            using (select id
                   from (select t.id,
                                row_number() over (partition by t.portfolio_no order by t.risk_ratio desc) rn
                         from atr_buss_ti_lic_icg_risk_ratio t
                         where t.action_no = p_action_no) alias4
                   where rn = 1) x
            on (t1.id = x.id)
            when matched then
                update
                set risk_ratio = t1.risk_ratio + 1 -
                                 (select sum(t2.risk_ratio)
                                  from atr_buss_ti_lic_icg_risk_ratio t2
                                  where t2.action_no = t1.action_no
                                    and t2.portfolio_no = t1.portfolio_no);
        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        --LIC 事故费用 （合约分入） 合同组合 拆分成 合同组
        begin
            insert into atr_buss_ti_lic_icg_claim_accident_amount(id,
                                                                  action_no,
                                                                  entity_id,
                                                                  currency_code,
                                                                  accident_year_month,
                                                                  portfolio_no,
                                                                  icg_no,
                                                                  evaluate_approach,
                                                                  loa_code,
                                                                  ibnr,
                                                                  os)
            select nextval('atr_seq_buss_ti_lic_icg_claim_accident_amount'), x.*
            from (select p_action_no,
                         a.entity_id,
                         a.currency_code,
                         a.accident_year_month,
                         a.portfolio_no,
                         b.icg_no,
                         a.evaluate_approach,
                         a.loa_code,
                         a.ibnr * b.risk_ratio as ibnr,
                         null::numeric         as os
                  from atr_dap_ti_icp_claim_accident_amount a,
                       atr_buss_ti_lic_icg_risk_ratio b
                  where a.year_month = p_year_month
                    and b.action_no = p_action_no
                    and a.portfolio_no = b.portfolio_no
                    and a.entity_id = b.entity_id
                    and a.currency_code = b.currency_code) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;
    end if;

    commit;

    call atr_pack_lic_proc_info(p_action_no, 'TI', 'SPLIT_ACCIDENT_AMOUNT_TI#end', null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_split_accident_amount_to(IN p_action_no text, IN p_year_month text, IN p_entity_id bigint, IN p_ibnr_import_g_count bigint)
    LANGUAGE plpgsql
    AS $$
declare

----------------
-- 合约分出
-- 计算风险占比
-- 合同组合拆分成合同组
----------------
    v_action_no_back varchar(36);
    v_ev_date        timestamp    := to_date(p_year_month, 'yyyymm');
    rec              record;
    v_count          bigint;
    v_err_context    text;
    v_err            text;
    v_err_mark       varchar(100) := 'SPLIT_ACCIDENT_AMOUNT_TO#error';

begin

    call atr_pack_lic_proc_info(p_action_no, 'TO', 'SPLIT_ACCIDENT_AMOUNT_TO#start', null);

    if p_ibnr_import_g_count = 0 then --合同组维度
        begin
            --LIC 事故费用 （合约分入）
            insert into atr_buss_to_lic_icg_claim_accident_amount(id,
                                                                  action_no,
                                                                  entity_id,
                                                                  currency_code,
                                                                  accident_year_month,
                                                                  portfolio_no,
                                                                  icg_no,
                                                                  evaluate_approach,
                                                                  loa_code,
                                                                  ibnr,
                                                                  os)
            select nextval('atr_seq_buss_to_lic_icg_claim_accident_amount'), x.*
            from (select p_action_no,
                         a.entity_id,
                         a.currency_code,
                         a.accident_year_month,
                         a.portfolio_no,
                         a.icg_no,
                         a.evaluate_approach,
                         a.loa_code,
                         sum(a.ibnr),
                         0 -- 预留字段
                  from atr_dap_to_icg_claim_accident_amount a
                  where a.year_month <= p_year_month
                  group by a.entity_id,
                           a.currency_code,
                           a.portfolio_no,
                           a.icg_no,
                           a.evaluate_approach,
                           a.loa_code,
                           a.accident_year_month
                  having abs(sum(a.ibnr)) > 0.00001) x;
        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;
    else
        -- 合同组合拆分合同组
        -- LIC 风险占比计算（合约分出， 合同组）- 基础数据
        begin
            insert into atr_buss_to_lic_icg_risk_ratio(id,
                                                       action_no,
                                                       entity_id,
                                                       currency_code,
                                                       portfolio_no,
                                                       icg_no,
                                                       coverage_amount,
                                                       icg_count)
            select nextval('atr_seq_buss_to_lic_icg_risk_ratio'), x.*
            from (select p_action_no,
                         p_entity_id,
                         currency_code,
                         portfolio_no,
                         icg_no,
                         sum(coverage_amount),
                         sum(icg_count)
                  from (select t.currency_code,
                               t.portfolio_no,
                               t.icg_no,
                               sum(t.coverage_amount) as coverage_amount,
                               count(*)               as icg_count
                        from atr_dap_to_premium t
                        where t.entity_id = p_entity_id
                          and t.year_month <= p_year_month
                          and t.expiry_date >= v_ev_date
                        group by t.currency_code, t.portfolio_no, t.icg_no
                        union all
                        select t.currency_code,
                               t.portfolio_no,
                               t.icg_no,
                               sum(t.coverage_amount) as coverage_amount,
                               count(*)               as icg_count
                        from atr_dap_to_premium_recovered t
                        where t.entity_id = p_entity_id
                          and t.year_month <= p_year_month
                          and t.expiry_date >= v_ev_date
                        group by t.currency_code, t.portfolio_no, t.icg_no) x1
                  where exists
                            (select *
                             from atr_dap_to_icp_claim_accident_amount m
                             where m.entity_id = p_entity_id
                               and m.currency_code = x1.currency_code
                               and m.year_month = p_year_month
                               and m.portfolio_no = x1.portfolio_no)
                  group by x1.currency_code, x1.portfolio_no, x1.icg_no
                  order by x1.currency_code, x1.portfolio_no, x1.icg_no) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        -- LIC 风险占比计算（合约分出， 合同组）- 基础数据 不存在， 则从过往年月去查找
        begin
            for rec in (select distinct r.currency_code, r.portfolio_no
                        from atr_dap_to_icp_claim_accident_amount r
                        where r.entity_id = p_entity_id
                          and r.year_month = p_year_month
                          and not exists (select *
                                          from atr_buss_to_lic_icg_risk_ratio t
                                          where t.action_no = p_action_no
                                            and t.portfolio_no = r.portfolio_no
                                            and t.entity_id = r.entity_id
                                            and t.currency_code = r.currency_code)
                        order by r.portfolio_no)
                loop
                    begin
                        select g2.action_no
                        into strict v_action_no_back
                        from atr_buss_to_lic_icg_risk_ratio g2,
                             atr_buss_lic_action a
                        where g2.entity_id = p_entity_id
                          and g2.currency_code = rec.currency_code
                          and g2.portfolio_no = rec.portfolio_no
                          and a.action_no = g2.action_no
                          and a.entity_id = g2.entity_id
                          and a.currency_code = g2.currency_code
                          and a.business_source_code = 'TO'
                          and a.confirm_is = '1'
                        order by a.year_month desc
                            fetch next 1 rows only;

                    exception
                        when no_data_found then
                            v_action_no_back := null;

                    end;

                    if v_action_no_back is not null then
                        insert into atr_buss_to_lic_icg_risk_ratio(id,
                                                                   action_no,
                                                                   entity_id,
                                                                   currency_code,
                                                                   portfolio_no,
                                                                   icg_no,
                                                                   coverage_amount,
                                                                   icg_count)
                        select nextval('atr_seq_buss_to_lic_icg_risk_ratio'), x.*
                        from (select p_action_no, -- 用当前的 action_no
                                     entity_id,
                                     currency_code,
                                     portfolio_no,
                                     icg_no,
                                     coverage_amount,
                                     icg_count
                              from atr_buss_to_lic_icg_risk_ratio g2
                              where g2.action_no = v_action_no_back
                                and g2.entity_id = p_entity_id
                                and g2.currency_code = rec.currency_code
                                and g2.portfolio_no = rec.portfolio_no
                              order by g2.id) x;
                    else
                        insert into atr_buss_to_lic_icg_risk_ratio(id,
                                                                   action_no,
                                                                   entity_id,
                                                                   currency_code,
                                                                   portfolio_no,
                                                                   icg_no,
                                                                   coverage_amount,
                                                                   icg_count)

                        select nextval('atr_seq_buss_to_lic_icg_risk_ratio'),
                               p_action_no, -- 用当前的 action_no
                               p_entity_id,
                               rec.currency_code,
                               rec.portfolio_no,
                               rec.portfolio_no || p_year_month || '**' icg_no,
                               1,
                               1;
                    end if;
                end loop;

            -- 检查合同组是否重复
            select count(*)
            into strict v_count
            from (select 1
                  from atr_buss_to_lic_icg_risk_ratio t
                  where t.action_no = p_action_no
                  group by t.portfolio_no, t.icg_no
                  having count(*) > 1) alias2;

            commit;

            if v_count > 0 then
                raise '存在重复的合同组';
            end if;
        end;

        --LIC 合同组风险占比 汇总至合同组合风险占比表,用于计算合同组的风险占比
        begin
            insert into atr_buss_to_lic_icp_risk_ratio(id,
                                                       action_no,
                                                       entity_id,
                                                       currency_code,
                                                       portfolio_no,
                                                       coverage_amount,
                                                       icg_count)
            select nextval('atr_seq_buss_to_lic_icp_risk_ratio'), x.*
            from (select t.action_no,
                         t.entity_id,
                         t.currency_code,
                         t.portfolio_no,
                         sum(t.coverage_amount) as coverage_amount,
                         sum(t.icg_count)       as icg_count
                  from atr_buss_to_lic_icg_risk_ratio t
                  where t.action_no = p_action_no
                  group by t.action_no, t.entity_id, t.currency_code, t.portfolio_no) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        -- LIC 风险占比计算（合约分出， 合同组）- 风险占比计算
        begin
            -- 计算风险占比
            merge into atr_buss_to_lic_icg_risk_ratio t
            using (select *
                   from atr_buss_to_lic_icp_risk_ratio p
                   where p.action_no = p_action_no) x
            on (x.action_no = t.action_no and x.entity_id = t.entity_id and x.currency_code = t.currency_code and
                x.portfolio_no = t.portfolio_no)
            when matched then
                update
                set risk_ratio =
                        (case
                             when x.coverage_amount is not null and x.coverage_amount <> 0 then
                                 t.coverage_amount / x.coverage_amount::numeric
                             when x.icg_count is not null and x.icg_count <> 0 then
                                 t.icg_count / x.icg_count::numeric
                            end);

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        commit;

        -- LIC 风险占比计算（合约分出， 合同组）- 尾差处理
        begin
            merge into atr_buss_to_lic_icg_risk_ratio t1
            using (select id
                   from (select t.id,
                                row_number() over (partition by t.portfolio_no order by t.risk_ratio desc) rn
                         from atr_buss_to_lic_icg_risk_ratio t
                         where t.action_no = p_action_no) alias4
                   where rn = 1) x
            on (t1.id = x.id)
            when matched then
                update
                set risk_ratio = t1.risk_ratio + 1 -
                                 (select sum(t2.risk_ratio)
                                  from atr_buss_to_lic_icg_risk_ratio t2
                                  where t2.action_no = t1.action_no
                                    and t2.portfolio_no = t1.portfolio_no);
        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;

        --LIC 事故费用 （合约分出） 合同组合 拆分成 合同组
        begin
            insert into atr_buss_to_lic_icg_claim_accident_amount(id,
                                                                  action_no,
                                                                  entity_id,
                                                                  currency_code,
                                                                  accident_year_month,
                                                                  portfolio_no,
                                                                  icg_no,
                                                                  evaluate_approach,
                                                                  loa_code,
                                                                  ibnr,
                                                                  os)
            select nextval('atr_seq_buss_to_lic_icg_claim_accident_amount'), x.*
            from (select p_action_no,
                         a.entity_id,
                         a.currency_code,
                         a.accident_year_month,
                         a.portfolio_no,
                         b.icg_no,
                         a.evaluate_approach,
                         a.loa_code,
                         a.ibnr * b.risk_ratio as ibnr,
                         null::numeric         as os
                  from atr_dap_to_icp_claim_accident_amount a,
                       atr_buss_to_lic_icg_risk_ratio b
                  where a.year_month = p_year_month
                    and b.action_no = p_action_no
                    and a.portfolio_no = b.portfolio_no
                    and a.entity_id = b.entity_id
                    and a.currency_code = b.currency_code) x;

        exception
            when others then
                get stacked diagnostics v_err_context = pg_exception_context,
                    v_err = message_text;
                call atr_pack_lic_proc_error(p_action_no, null, v_err_mark,
                                             v_err_context || '-' || v_err);
                return;
        end;
    end if;

    commit;

    call atr_pack_lic_proc_info(p_action_no, 'TO', 'SPLIT_ACCIDENT_AMOUNT_TO#end', null);
end ;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_split_accident_amount(IN p_business_type text, IN p_action_no text, IN p_year_month text, IN p_entity_id bigint)
    LANGUAGE plpgsql
    AS $$
declare
    v_business_no         varchar(1000) := 'bt=' || p_business_type || ',p_action_no=' ||
                                           p_action_no || ',ym=' || p_year_month;
    v_ibnr_import_g_count bigint; --ibnr导入为合同组合 判断标志
----------------
-- 计算风险占比
-- 合同组合拆分成合同组
-- 入口
----------------
begin

    call atr_pack_lic_proc_info(p_action_no, v_business_no, 'SPLIT_ACCIDENT_AMOUNT#start', null);

    --判断IBNR是否为合同组合导入
    select count(*)
    into strict v_ibnr_import_g_count
    from atr_conf_code t,
         atr_conf_code t1
    where t.code_id = t1.upper_code_id
      and t.code_code = 'IbnrImportMode'
      and t1.code_code = 'G'
      and t1.valid_is = '1';

    if p_business_type = 'DD' then

        call atr_pack_lic_proc_split_accident_amount_dd(p_action_no, p_year_month, p_entity_id, v_ibnr_import_g_count);

    elsif p_business_type = 'FO' then

        call atr_pack_lic_proc_split_accident_amount_fo(p_action_no, p_year_month, p_entity_id, v_ibnr_import_g_count);

    elsif p_business_type = 'TI' then

        call atr_pack_lic_proc_split_accident_amount_ti(p_action_no, p_year_month, p_entity_id, v_ibnr_import_g_count);

    elsif p_business_type = 'TO' then

        call atr_pack_lic_proc_split_accident_amount_to(p_action_no, p_year_month, p_entity_id, v_ibnr_import_g_count);

    end if;

    commit;

    call atr_pack_lic_proc_info(p_action_no, v_business_no, 'SPLIT_ACCIDENT_AMOUNT#end', null);

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc0(IN p_business_type text, IN p_action_no text, IN p_task_code text, IN p_entity_id bigint, IN p_year_month text, IN p_currency_code text, IN p_portfolio_no text)
    LANGUAGE plpgsql
    AS $$
declare

    v_business_no varchar(1000) := 'bt=' || p_business_type || ',ct=' ||
                                   p_entity_id || ',ym=' || p_year_month ||
                                   ',cu=' || p_currency_code || ',pf=' ||
                                   p_portfolio_no;

begin
    call atr_pack_lic_proc_info(p_action_no, v_business_no, 'CALC0#start', null);

    --IBNR导入拆分及计算入口
    call atr_pack_lic_proc_split_accident_amount(p_business_type, p_action_no,
                                                 p_year_month, p_entity_id);

    call atr_pack_lic_proc_calc_init_icg_main(p_business_type,
                                              p_action_no,
                                              p_task_code,
                                              p_entity_id,
                                              p_year_month,
                                              p_portfolio_no);

    -- 合同组确定后， 压入假设值
    call atr_pack_lic_proc_collect_quota(p_business_type, p_action_no, p_year_month);

    call atr_pack_ecf_util_proc_collect_quota_param('LIC', 'C', p_action_no);

    if p_business_type = 'DD' then

        call atr_pack_lic_proc_calc_init_sub_dd(p_action_no, p_year_month);

        call atr_pack_lic_proc_calc_buss_dd(p_action_no, p_entity_id, p_year_month);

    elsif p_business_type = 'FO' then

        call atr_pack_lic_proc_calc_init_sub_fo(p_action_no, p_year_month);

        call atr_pack_lic_proc_calc_buss_fo(p_action_no, p_entity_id, p_year_month);

    elsif p_business_type = 'TI' then

        call atr_pack_lic_proc_calc_init_sub_ti(p_action_no, p_year_month);

        call atr_pack_lic_proc_calc_buss_ti(p_action_no, p_entity_id, p_year_month);

    elsif p_business_type = 'TO' then

        call atr_pack_lic_proc_calc_init_sub_to(p_action_no, p_year_month);

        call atr_pack_lic_proc_calc_buss_to(p_action_no, p_entity_id, p_year_month);

    end if;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_collect_quota_def(IN p_action_no text)
    LANGUAGE plpgsql
    AS $$
declare

    ---------------------------------------
    -- 收集这个批次所用到的假设值定义
    ---------------------------------------
begin

    call atr_pack_lic_proc_info(p_action_no, '', 'INIT_QUOTA_DEF#start', null);

    insert into atr_buss_quota_def(id, action_no, quota_code, dimension, dev_is, quota_def_id)
    select nextval('atr_seq_buss_quota_def'), x.*
    from (select p_action_no,
                 quota_code,
                 dimension,
                 dev_is,
                 quota_def_id
          from (select t.quota_code,
                       t.dimension,
                       case when t.quota_type = '1' then '1' else '0' end                         dev_is,
                       t.quota_def_id,
                       row_number() over (partition by t.quota_code order by t.quota_def_id desc) rn
                from atr_conf_quota_def t
                where t.valid_is = '1'
                  and t.audit_state = '1'
                  and t.quota_code in ('BE009',
                                       'QR012')) x
          where rn = 1
          order by quota_code) x;

    commit;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_create_action(IN p_business_type text, IN p_entity_id bigint, IN p_year_month text, IN p_currency_code text, IN p_action_no text, IN p_task_code text, IN p_user_id bigint)
    LANGUAGE plpgsql
    AS $$
declare
    v_row_count int;
begin

    call atr_pack_lic_proc_info(p_action_no, p_business_type, 'CREATE_ACTION#start', null);

    insert into atr_buss_lic_action(id,
                                    action_no,
                                    task_code,
                                    entity_id,
                                    year_month,
                                    currency_code,
                                    business_source_code,
                                    status,
                                    draw_type,
                                    draw_time,
                                    draw_user,
                                    confirm_is,
                                    creator_id,
                                    create_time,
                                    updator_id,
                                    update_time)
    values (nextval('atr_seq_buss_lic_action'),
            p_action_no,
            p_task_code,
            p_entity_id,
            p_year_month,
            p_currency_code,
            p_business_type,
            'R',
            '1',
            clock_timestamp(),
            p_user_id,
            '0',
            p_user_id,
            clock_timestamp(),
            p_user_id,
            clock_timestamp());

    get diagnostics v_row_count = row_count;


    call atr_pack_lic_proc_info(p_action_no,
                                p_business_type,
                                'CREATE_ACTION#end',
                                'insert count: ' || v_row_count);

    commit;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_update_action(IN p_action_no text, IN p_user_id bigint, IN p_status text)
    LANGUAGE plpgsql
    AS $$
begin

    update atr_buss_lic_action t
    set status      = p_status,
        updator_id  = coalesce(p_user_id, 1),
        update_time = clock_timestamp()
    where t.action_no = p_action_no;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc(IN p_business_type text, IN p_entity_id bigint, IN p_year_month text, IN p_currency text, IN p_portfolio_no text, IN p_user_id bigint)
    LANGUAGE plpgsql
    AS $$
declare

    -- Purpose : LIC 计算入口
    -- param p_business_type: 业务类型， DD-直保&临分、FO-临分分出、TI-合约分入、TO-合约分出;
    -- param p_year_month: 必录，业务期间的年月
    -- param p_currency_code: 非必录
    -- param p_portfolio_no：非必录
    -- param p_user_id: 非必录， 操作用户， 默认为 1
    v_action_no       varchar(36)   := atr_pack_ecf_util_func_create_action_no();
    v_task_code       varchar(36);
    v_business_no     varchar(1000) := 'bt=' || p_business_type || ',ct=' ||
                                       p_entity_id || ',ym=' || p_year_month ||
                                       ',cu=' || coalesce(p_currency, '') || ',pf=' ||
                                       coalesce(p_portfolio_no, '') || ',us=' || coalesce(p_user_id::varchar, '');
    v_base_currency   varchar(3);
    v_error_count     integer;
    v_error_msg       varchar(4000);
    v_confirmed_count int;
begin
    call atr_pack_lic_proc_info(v_action_no, v_business_no, 'CALC#start', null);

    v_task_code := atr_pack_ecf_util_func_create_task_code('LIC',
                                                           p_entity_id,
                                                           p_year_month,
                                                           p_currency);

    -- create action
    call atr_pack_lic_proc_create_action(p_business_type,
                                         p_entity_id,
                                         p_year_month,
                                         p_currency,
                                         v_action_no,
                                         v_task_code,
                                         coalesce(p_user_id, 1));

    -- 检查业务期间  start
    -- 检查当前业务年月的确认情况
    select count(*)
    into v_confirmed_count
    from atr_buss_lic_action t
    where t.entity_id = p_entity_id
      and t.year_month = p_year_month
      and t.business_source_code = p_business_type
      and t.confirm_is = '1';

    if v_confirmed_count = 1 then
        call atr_pack_lic_proc_update_action(v_action_no, p_user_id, 'S');
        return;
    elsif v_confirmed_count > 1 then
        raise '%',
            'There a duplicate confirmed business year_month: ' ||
            p_year_month;
    end if;

    call atr_pack_ecf_util_proc_check_buss_period('LIC',
                                                  p_entity_id,
                                                  p_business_type,
                                                  p_year_month);
    -- 检查业务期间  end

    select t.currency_code
    into strict v_base_currency
    from bpluser.bbs_conf_account_set t
    where t.entity_id = p_entity_id;

    if v_base_currency is null then
        v_error_msg := '没有配置本位币';
        call atr_pack_lic_proc_update_action(v_action_no, p_user_id, 'E');
        call atr_pack_lic_proc_error(v_action_no, v_business_no, '主程序#error', v_error_msg);
        raise '%', v_error_msg;
    end if;

    if v_base_currency <> p_currency then
        v_error_msg := '入参币别必须是本位币 ' || v_base_currency;
        call atr_pack_lic_proc_update_action(v_action_no, p_user_id, 'E');
        call atr_pack_lic_proc_error(v_action_no, v_business_no, '主程序#error', v_error_msg);
        raise '%', v_error_msg;
    end if;

    -- 检查是否有正在执行
    if exists(select *
              from atr_buss_lic_action a
              where a.action_no <> v_action_no
                and a.year_month = p_year_month
                and a.status = 'R'
                and a.business_source_code = p_business_type) then

        raise '业务 % 在 % 有正在执行的版本', p_business_type, p_year_month;

    end if;

    call atr_pack_lic_proc_collect_quota_def(v_action_no);

    call atr_pack_lic_proc_calc0(p_business_type,
                                 v_action_no,
                                 v_task_code,
                                 p_entity_id,
                                 p_year_month,
                                 p_currency,
                                 p_portfolio_no);

    -- clear quota param
    delete from atr_temp_ecf_quota_param t where t.action_no = v_action_no;
    commit;

    -- 检查异常
    select count(*)
    into strict v_error_count
    from atr_log_lic_trace t
    where t.action_no = v_action_no
      and t.log_type = 'ERROR'
    limit 1;

    if v_error_count > 0 then
        call atr_pack_lic_proc_update_action(v_action_no, p_user_id, 'E');
        call atr_pack_lic_proc_error(v_action_no, v_business_no, '主程序#error', null);
        raise exception 'An exception occurs during program running. The action no. is %', v_action_no;
    else
        call atr_pack_lic_proc_update_action(v_action_no, p_user_id, 'S');
        call atr_pack_lic_proc_debug(v_action_no, v_business_no, 'CALC#end');
    end if;

end;
$$;

CREATE PROCEDURE atruser.atr_pack_lic_proc_calc_manual(IN p_action_id bigint, IN p_user_id bigint)
    LANGUAGE plpgsql
    AS $$
declare

    -- Purpose : LIC 手动计算入口， 在页面 “第2步” 手工更改 ULT% 后使用
    -- param p_action_id：必录， atr_buss_lic_action 表的 ID
    -- param p_user_id: 非必录， 操作用户， 默认为 1
    v_business_type varchar(2);
    v_action_no     varchar(60);
    v_year_month    varchar(6);
    v_center_id     bigint;
    v_currency      varchar(3);
    v_portfolio_no  varchar(60);
    v_business_no   varchar(1000);

begin

    select t.business_source_code,
           t.action_no,
           t.year_month,
           t.entity_id,
           t.currency_code
    into strict v_business_type,
        v_action_no,
        v_year_month,
        v_center_id,
        v_currency
    from atr_buss_lic_action t
    where t.id = p_action_id;

    v_business_no := ',bt=' || v_business_type || ',ci=' || v_center_id ||
                     ',cu=' || v_currency || ',pf=' || v_portfolio_no;

    call atr_pack_lic_proc_info(v_action_no,
                                'ai=' || p_action_id || v_business_no,
                                'CREATE_MANUAL#start',
                                null);

    if v_business_type = 'DD' then
        call atr_pack_lic_proc_calc_buss_dd(v_action_no, v_center_id, v_year_month);
    elsif v_business_type = 'FO' then
        call atr_pack_lic_proc_calc_buss_fo(v_action_no, v_center_id, v_year_month);
    elsif v_business_type = 'TI' then
        call atr_pack_lic_proc_calc_buss_ti(v_action_no, v_center_id, v_year_month);
    elsif v_business_type = 'TO' then
        call atr_pack_lic_proc_calc_buss_to(v_action_no, v_center_id, v_year_month);
    end if;

    update atr_buss_lic_action t
    set updator_id  = coalesce(p_user_id, 1),
        update_time = clock_timestamp()
    where t.id = p_action_id;

    commit;

end;

$$;


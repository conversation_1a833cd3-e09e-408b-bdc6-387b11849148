--3、根据指定的机构业务线查询TIC
select be.entity_code ||' -- '|| be.entity_e_name,
       '直保/临分分入' as business_model,
       loa.loa_code,
       loa.loa_e_name,
       loa.evaluate_approach,
       (case when loa.coverage_unit is not null then loa.coverage_unit||' -- '||bvcc.code_e_name else null end)as coverage_unit,
       (case when bvcct.code_code is not null then bvcct.code_code||' -- '||bvcct.code_e_name else null end) as tic
  from bpluser.bbs_conf_loa loa
  left join bpluser.bpl_v_conf_code bvcc on bvcc.code_code_idx = 'CoverageUnit/'||loa.coverage_unit
  left join bpluser.bbs_conf_loa_tic tic on tic.loa_id = loa.loa_id
  left join bpluser.bpl_v_conf_code bvcct on bvcct.code_code_idx like 'TicCode/%' and bvcct.code_id = tic.tic_id
  join bpluser.bbs_entity be on be.entity_id = loa.entity_id and be.entity_code = '01'
 where upper(business_model) || upper(business_direction) = upper('DD')
union all
select entity_id, 
       '临分分出' as business_model,
       loa.loa_code,
       loa.loa_e_name,
       loa.evaluate_approach,
       (case when loa.coverage_unit is not null then loa.coverage_unit||' -- '||bvcc.code_e_name else null end)as coverage_unit,
       (case when bvcct.code_code is not null then bvcct.code_code||' -- '||bvcct.code_e_name else null end) as tic
  from bpluser.bbs_conf_loa loa
  left join bpluser.bpl_v_conf_code bvcc on bvcc.code_code_idx = 'CoverageUnit/'||loa.coverage_unit
  left join bpluser.bbs_conf_loa_tic tic on tic.loa_id = loa.loa_id
  left join bpluser.bpl_v_conf_code bvcct on bvcct.code_code_idx like 'TicCode/%' and bvcct.code_id = tic.tic_id
  join bpluser.bbs_entity be on be.entity_id = loa.entity_id and be.entity_code = '01'
 where upper(business_model) || upper(business_direction) = upper('DD')
union all
select entity_id, 
       '合约分入' as business_model, 
       loa.loa_code,
       loa.loa_e_name,
       loa.evaluate_approach,
       (case when loa.coverage_unit is not null then loa.coverage_unit||' -- '||bvcc.code_e_name else null end)as coverage_unit,
       (case when bvcct.code_code is not null then bvcct.code_code||' -- '||bvcct.code_e_name else null end) as tic
  from bpluser.bbs_conf_loa loa 
  left join bpluser.bpl_v_conf_code bvcc on bvcc.code_code_idx = 'CoverageUnit/'||loa.coverage_unit
  left join bpluser.bbs_conf_loa_tic tic on tic.loa_id = loa.loa_id
  left join bpluser.bpl_v_conf_code bvcct on bvcct.code_code_idx like 'TicCode/%' and bvcct.code_id = tic.tic_id
  join bpluser.bbs_entity be on be.entity_id = loa.entity_id and be.entity_code = '01'
 where upper(loa.business_model) || upper(loa.business_direction) = upper('TI')
union all
select entity_id, 
       '合约分出' as business_model, 
       loa.loa_code,
       loa.loa_e_name,
       loa.evaluate_approach,
       (case when loa.coverage_unit is not null then loa.coverage_unit||' -- '||bvcc.code_e_name else null end)as coverage_unit,
       (case when bvcct.code_code is not null then bvcct.code_code||' -- '||bvcct.code_e_name else null end) as tic
  from bpluser.bbs_conf_loa loa 
  left join bpluser.bpl_v_conf_code bvcc on bvcc.code_code_idx = 'CoverageUnit/'||loa.coverage_unit
  left join bpluser.bbs_conf_loa_tic tic on tic.loa_id = loa.loa_id
  left join bpluser.bpl_v_conf_code bvcct on bvcct.code_code_idx like 'TicCode/%' and bvcct.code_id = tic.tic_id
  join bpluser.bbs_entity be on be.entity_id = loa.entity_id and be.entity_code = '01'
 where upper(loa.business_model) || upper(loa.business_direction) = upper('TO')
 order by business_model, loa_code;

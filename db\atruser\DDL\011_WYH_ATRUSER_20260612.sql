call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_action');
CREATE TABLE atruser.atr_buss_ibnr_alloc_action (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  business_source_code varchar(3)  NOT NULL,
  status varchar(1)  NOT NULL,
  confirm_is varchar(1)  NOT NULL,
  confirm_user int8,
  confirm_time timestamp(0),
  creator_id int8,
  create_time timestamp(0),
  updator_id int8,
  update_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_action PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_action OWNER TO atruser;
CREATE INDEX idx_atr_buss_ibnr_alloc_action ON atruser.atr_buss_ibnr_alloc_action USING btree (action_no  pg_catalog.text_ops ASC NULLS LAST);
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.year_month IS '业务年月|评估期';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.business_source_code IS '业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.status IS '执行状态|R-执行中；E-执行异常；S-执行成功';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.confirm_is IS '是否确认|1-是、0-否';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.confirm_user IS '确认人';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.confirm_time IS '确认时间';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.creator_id IS '创建人';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.create_time IS '创建时间';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.updator_id IS '最后修改人';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_action.update_time IS '最后修改时间';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_action IS 'IBNR分摊操作表';



call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_dd_result');
CREATE TABLE atruser.atr_buss_ibnr_alloc_dd_result (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  icg_no_name            varchar(200),
  evaluate_approach varchar(60),
  pl_judge_rslt varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  policy_no varchar(64)  NOT NULL,
  risk_class_code varchar(64),
  kind_code 	varchar(64)  NOT NULL,
  business_type varchar(2),
  ep_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
  ibnr_amount  numeric(32,8),
	fin_detail_code varchar(60) COLLATE pg_catalog.default,
  fin_product_code varchar(60) COLLATE pg_catalog.default,
  fin_sub_product_code varchar(60) COLLATE pg_catalog.default,
  fin_acc_channel varchar(60) COLLATE pg_catalog.default,
  center_code varchar(60) COLLATE pg_catalog.default,
  dept_id varchar(60) COLLATE pg_catalog.default,
  channel_id varchar(60) COLLATE pg_catalog.default,
  company_code4 varchar(60) COLLATE pg_catalog.default,
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_dd_result PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_dd_result OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_dd_result ON atruser.atr_buss_ibnr_alloc_dd_result USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.year_month IS '业务年月|评估期';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.portfolio_no IS '合同组合';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.icg_no IS '合同组';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.evaluate_approach IS '评估方法';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_dd_result.business_type IS '业务类型';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_dd_result IS 'DD分摊结果操作表';


call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_ti_result');
CREATE TABLE atruser.atr_buss_ibnr_alloc_ti_result (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  icg_no_name            varchar(200),
  evaluate_approach varchar(60),
  pl_judge_rslt varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  ri_dept varchar(32) COLLATE pg_catalog.default,
  treaty_no varchar(32) COLLATE pg_catalog.default,
  treaty_name varchar(200),
  risk_class_code varchar(64) COLLATE pg_catalog.default,
  ibnr_amount numeric(32,8),
	fin_detail_code varchar(60) COLLATE pg_catalog.default,
  fin_product_code varchar(60) COLLATE pg_catalog.default,
  fin_sub_product_code varchar(60) COLLATE pg_catalog.default,
  fin_acc_channel varchar(60) COLLATE pg_catalog.default,
  center_code varchar(60) COLLATE pg_catalog.default,
  dept_id varchar(60) COLLATE pg_catalog.default,
  channel_id varchar(60) COLLATE pg_catalog.default,
  company_code4 varchar(60) COLLATE pg_catalog.default,
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_ti_result PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_ti_result OWNER TO atruser;
CREATE INDEX idx_atr_buss_ibnr_alloc_ti_result ON atruser.atr_buss_ibnr_alloc_ti_result USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_ti_result.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_ti_result.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_ti_result.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_ti_result.year_month IS '业务年月|评估期';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_ti_result IS 'TI分摊结果操作表';


call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_out_result');
CREATE TABLE atruser.atr_buss_ibnr_alloc_out_result (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  business_type varchar(2),
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  icg_no_name            varchar(200),
  evaluate_approach varchar(60),
  pl_judge_rslt varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  treaty_no varchar(32) COLLATE pg_catalog.default,
  treaty_name varchar(200),
  risk_class_code varchar(64),
  policy_no varchar(64)  NOT NULL,
  kind_code 	varchar(64)  NOT NULL,
  ep_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
  ibnr_amount numeric(32,8),
	fin_detail_code varchar(60) COLLATE pg_catalog.default,
  fin_product_code varchar(60) COLLATE pg_catalog.default,
  fin_sub_product_code varchar(60) COLLATE pg_catalog.default,
  fin_acc_channel varchar(60) COLLATE pg_catalog.default,
  center_code varchar(60) COLLATE pg_catalog.default,
  dept_id varchar(60) COLLATE pg_catalog.default,
  channel_id varchar(60) COLLATE pg_catalog.default,
  company_code4 varchar(60) COLLATE pg_catalog.default,
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_out_result PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_out_result 
  OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_out_result ON atruser.atr_buss_ibnr_alloc_out_result USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_out_result.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_out_result.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_out_result.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_out_result.year_month IS '业务年月|评估期';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_out_result IS '分出分摊结果操作表';






call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_fo_result');
CREATE TABLE atruser.atr_buss_ibnr_alloc_fo_result (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  icg_no_name            varchar(200),
  evaluate_approach varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  treaty_no varchar(32) COLLATE pg_catalog.default,
  treaty_name varchar(200),
  risk_class_code varchar(64),
  center_code varchar(64),
  policy_no varchar(64)  NOT NULL,
  kind_code 	varchar(64)  NOT NULL,
  ep_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
  ibnr_amount numeric(32,8),
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_fo_result PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_fo_result 
  OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_fo_result ON atruser.atr_buss_ibnr_alloc_fo_result USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_fo_result.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_fo_result.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_fo_result.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_fo_result.year_month IS '业务年月|评估期';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_fo_result IS '临分分出分摊结果操作表';


call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_to_result');
CREATE TABLE atruser.atr_buss_ibnr_alloc_to_result (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  icg_no_name            varchar(200),
  evaluate_approach varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  treaty_no varchar(32) COLLATE pg_catalog.default,
  treaty_name varchar(200),
  risk_class_code varchar(64),
  center_code varchar(64),
  policy_no varchar(64)  NOT NULL,
  kind_code 	varchar(64)  NOT NULL,
  ep_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
  ibnr_amount numeric(32,8),
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_to_result PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_to_result 
  OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_to_result ON atruser.atr_buss_ibnr_alloc_to_result USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result.year_month IS '业务年月|评估期';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_to_result IS '合约分出分摊结果操作表';

call atr_pack_commonutils_proc_drop_table('atr_buss_ibnr_alloc_to_result_x');
CREATE TABLE atruser.atr_buss_ibnr_alloc_to_result_x (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  icg_no_name            varchar(200),
  evaluate_approach varchar(60),
  pl_judge_rslt varchar(60),
  acc_year_month varchar(6)  NOT NULL,
  treaty_no varchar(32) COLLATE pg_catalog.default,
  treaty_name varchar(200),
  risk_class_code varchar(64),
  policy_no varchar(64)  NOT NULL,
  kind_code 	varchar(64)  NOT NULL,
  claim_no varchar(64)  NOT NULL,
  rl_amount  numeric(32,8),
  rl_ratio  numeric(32,8),
  case_amount numeric(32,8),
	fin_detail_code varchar(60) COLLATE pg_catalog.default,
  fin_product_code varchar(60) COLLATE pg_catalog.default,
  fin_sub_product_code varchar(60) COLLATE pg_catalog.default,
  fin_acc_channel varchar(60) COLLATE pg_catalog.default,
  center_code varchar(60) COLLATE pg_catalog.default,
  dept_id varchar(60) COLLATE pg_catalog.default,
  channel_id varchar(60) COLLATE pg_catalog.default,
  company_code4 varchar(60) COLLATE pg_catalog.default,
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_buss_ibnr_alloc_to_result_x PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_buss_ibnr_alloc_to_result_x 
  OWNER TO atruser;

CREATE INDEX idx_atr_buss_ibnr_alloc_to_result_x ON atruser.atr_buss_ibnr_alloc_to_result_x USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result_x.id IS 'ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result_x.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result_x.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_buss_ibnr_alloc_to_result_x.year_month IS '业务年月|评估期';
COMMENT ON TABLE atruser.atr_buss_ibnr_alloc_to_result_x IS '超配分摊结果操作表';


DROP sequence if EXISTS atr_seq_duct_ibnr_alloc_ep;
create sequence atr_seq_duct_ibnr_alloc_ep;   
DROP sequence if EXISTS atr_seq_duct_ibnr_alloc_ep_out;
create sequence atr_seq_duct_ibnr_alloc_ep_out;   
DROP sequence if EXISTS atr_seq_duct_ibnr_alloc_ep_out_x;
create sequence atr_seq_duct_ibnr_alloc_ep_out_x;   
DROP sequence if EXISTS atr_seq_buss_ibnr_alloc_action;
create sequence atr_seq_buss_ibnr_alloc_action;   

DROP sequence if EXISTS atr_seq_buss_ibnr_alloc_dd_result;
create sequence atr_seq_buss_ibnr_alloc_dd_result;   

DROP sequence if EXISTS atr_seq_buss_ibnr_alloc_ti_result;
create sequence atr_seq_buss_ibnr_alloc_ti_result;   

DROP sequence if EXISTS atr_seq_buss_ibnr_alloc_to_result;
create sequence atr_seq_buss_ibnr_alloc_to_result;   

DROP sequence if EXISTS atr_seq_buss_ibnr_alloc_fo_result;
create sequence atr_seq_buss_ibnr_alloc_fo_result;   

DROP sequence if EXISTS atr_seq_buss_ibnr_alloc_out_result;
create sequence atr_seq_buss_ibnr_alloc_out_result; 

DROP sequence if EXISTS atr_seq_buss_ibnr_alloc_out_result_x;
create sequence atr_seq_buss_ibnr_alloc_out_result_x;   

DROP sequence if EXISTS atr_seq_buss_ibnr_import_claim;
create sequence atr_seq_buss_ibnr_import_claim; 
DROP sequence if EXISTS atr_seq_duct_case_ep_x;
create sequence atr_seq_duct_case_ep_x;  


drop table if exists atr_duct_ibnr_import_ep;
CREATE TABLE atruser.atr_duct_ibnr_import_ep (
  ibnr_main_id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  business_model varchar(6)  NOT NULL,
  year_month varchar(6)  NOT NULL,
  accident_quarter varchar(6) COLLATE pg_catalog.default,
  risk_class_code varchar(64) COLLATE pg_catalog.default,
  center_code varchar(64),
  ibnr_amount  numeric(32,8),
  ri_ibnr_amount  numeric(32,8),
  creator_id int8,
  create_time timestamp(0)
);
ALTER TABLE atruser.atr_duct_ibnr_import_ep OWNER TO atruser;  
COMMENT ON COLUMN atruser.atr_duct_ibnr_import_ep.ibnr_main_id IS 'ID';
COMMENT ON COLUMN atruser.atr_duct_ibnr_import_ep.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_duct_ibnr_import_ep.business_model IS '模型';
COMMENT ON COLUMN atruser.atr_duct_ibnr_import_ep.year_month IS '业务年月|评估期';
COMMENT ON COLUMN atruser.atr_duct_ibnr_import_ep.accident_quarter IS '季度';
COMMENT ON COLUMN atruser.atr_duct_ibnr_import_ep.risk_class_code IS '险类';
COMMENT ON COLUMN atruser.atr_duct_ibnr_import_ep.center_code IS '机构';
COMMENT ON COLUMN atruser.atr_duct_ibnr_import_ep.ibnr_amount IS '金额';





drop table atr_duct_ibnr_alloc_ep;
CREATE TABLE atruser.atr_duct_ibnr_alloc_ep (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  data_type varchar(6)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  acc_year_month varchar(6)  NOT NULL,
  business_type varchar(2),
  accident_quarter varchar(6) COLLATE pg_catalog.default,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  icg_no_name            varchar(200),
  pl_judge_rslt varchar(60),
  policy_no varchar(64)  NOT NULL,
  risk_class_code varchar(64) COLLATE pg_catalog.default,
  kind_code 	varchar(64)  NOT NULL,
  ep_amount  numeric(32,8),
  ep_sum_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
	fin_detail_code varchar(60) COLLATE pg_catalog.default,
  fin_product_code varchar(60) COLLATE pg_catalog.default,
  fin_sub_product_code varchar(60) COLLATE pg_catalog.default,
  fin_acc_channel varchar(60) COLLATE pg_catalog.default,
  center_code varchar(60) COLLATE pg_catalog.default,
  dept_id varchar(60) COLLATE pg_catalog.default,
  channel_id varchar(60) COLLATE pg_catalog.default,
  company_code4 varchar(60) COLLATE pg_catalog.default,
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_duct_ibnr_alloc_ep PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_duct_ibnr_alloc_ep 
  OWNER TO atruser;

CREATE INDEX idx_atr_duct_ibnr_alloc_ep ON atruser.atr_duct_ibnr_alloc_ep USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

drop table atr_duct_ibnr_alloc_ep_out;
CREATE TABLE atruser.atr_duct_ibnr_alloc_ep_out (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  data_type varchar(6)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  acc_year_month varchar(6)  NOT NULL,
  accident_quarter varchar(6) COLLATE pg_catalog.default,
  business_type varchar(2),
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  icg_no_name            varchar(200),
  pl_judge_rslt varchar(60),
  treaty_no varchar(64)  NOT NULL,
  treaty_name varchar(200),
  policy_no varchar(64)  NOT NULL,
  risk_class_code varchar(64) COLLATE pg_catalog.default,
  kind_code 	varchar(64)  NOT NULL,
  ep_amount  numeric(32,8),
  ep_sum_amount  numeric(32,8),
  ep_ratio  numeric(32,8),
	fin_detail_code varchar(60) COLLATE pg_catalog.default,
  fin_product_code varchar(60) COLLATE pg_catalog.default,
  fin_sub_product_code varchar(60) COLLATE pg_catalog.default,
  fin_acc_channel varchar(60) COLLATE pg_catalog.default,
  center_code varchar(60) COLLATE pg_catalog.default,
  dept_id varchar(60) COLLATE pg_catalog.default,
  channel_id varchar(60) COLLATE pg_catalog.default,
  company_code4 varchar(60) COLLATE pg_catalog.default,
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT pk_atr_duct_ibnr_alloc_ep_out PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_duct_ibnr_alloc_ep_out 
  OWNER TO atruser;

CREATE INDEX idx_atr_duct_ibnr_alloc_ep_out ON atruser.atr_duct_ibnr_alloc_ep_out USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);

drop table atr_duct_ibnr_alloc_ep_out_x;
CREATE TABLE atruser.atr_duct_ibnr_alloc_ep_out_x (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  data_type varchar(6)  NOT NULL,
  entity_id int8 NOT NULL,
  year_month varchar(6)  NOT NULL,
  acc_year_month varchar(6)  NOT NULL,
  portfolio_no      varchar(60),
  icg_no            varchar(60),
  icg_no_name            varchar(200),
  pl_judge_rslt varchar(60),
  treaty_no varchar(64)  NOT NULL,
  treaty_name varchar(200),
  policy_no varchar(64)  NOT NULL,
  claim_no varchar(64)  NOT NULL,
  risk_class_code varchar(64) COLLATE pg_catalog.default,
  kind_code 	varchar(64)  NOT NULL,
  case_amount  numeric(32,8),
  case_sum_amount  numeric(32,8),
  case_ratio  numeric(32,8),
	fin_detail_code varchar(60) COLLATE pg_catalog.default,
  fin_product_code varchar(60) COLLATE pg_catalog.default,
  fin_sub_product_code varchar(60) COLLATE pg_catalog.default,
  fin_acc_channel varchar(60) COLLATE pg_catalog.default,
  center_code varchar(60) COLLATE pg_catalog.default,
  dept_id varchar(60) COLLATE pg_catalog.default,
  channel_id varchar(60) COLLATE pg_catalog.default,
  company_code4 varchar(60) COLLATE pg_catalog.default,
  creator_id int8,
  create_time timestamp(0),
  CONSTRAINT px_atr_duct_ibnr_alloc_ep_out_x PRIMARY KEY (id)
);
ALTER TABLE atruser.atr_duct_ibnr_alloc_ep_out_x 
  OWNER TO atruser;

CREATE INDEX idx_atr_duct_ibnr_alloc_ep_out_x ON atruser.atr_duct_ibnr_alloc_ep_out_x USING btree (
  action_no  pg_catalog.text_ops ASC NULLS LAST
);


drop table if exists atr_duct_case_ep_x;
CREATE TABLE atruser.atr_duct_case_ep_x (
  id int8 NOT NULL,
  action_no varchar(32)  NOT NULL,
  acc_year_month varchar(6)  NOT NULL,
  portfolio_no varchar(6)  NOT NULL,
  icg_no varchar(6) COLLATE pg_catalog.default,
  policy_no varchar(64) COLLATE pg_catalog.default,
  claim_no varchar(64),
  risk_class_code varchar(64),
  kind_code varchar(64),
  case_amount  numeric(32,8),
  creator_id int8,
  create_time timestamp(0)
);
ALTER TABLE atruser.atr_duct_case_ep_x OWNER TO atruser;  
COMMENT ON COLUMN atruser.atr_duct_case_ep_x.id IS 'ID';
COMMENT ON COLUMN atruser.atr_duct_case_ep_x.action_no IS '执行编号';
COMMENT ON COLUMN atruser.atr_duct_case_ep_x.acc_year_month IS '业务年月|评估期';
COMMENT ON COLUMN atruser.atr_duct_case_ep_x.portfolio_no IS '合同组合';
COMMENT ON COLUMN atruser.atr_duct_case_ep_x.icg_no IS '合同组';
COMMENT ON COLUMN atruser.atr_duct_case_ep_x.case_amount IS 'case金额';


			
			
			
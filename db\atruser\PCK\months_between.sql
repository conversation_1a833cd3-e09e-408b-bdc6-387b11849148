---------- drop ---------------
do language plpgsql
$$
    declare
        rec record;
    begin
        for rec in select t.proname, t.prokind from pg_proc t where t.proname ~ '^months_between$'
            loop
                if rec.prokind = 'f' then
                    call atr_pack_commonutils_proc_drop_func(rec.proname::varchar);
                elsif rec.prokind = 'p' then
                    call atr_pack_commonutils_proc_drop_proc(rec.proname::varchar);
                end if;
            end loop;
    end;
$$;



---------- create ---------------
CREATE FUNCTION atruser.months_between(p_end timestamp with time zone, p_start timestamp with time zone) RETURNS numeric
    LANGUAGE sql IMMUTABLE
    AS $$
    ------------------------------------------------
    -- 模拟oracle的months_between函数  by majingyun
    ------------------------------------------------

select extract(year from x.d) * 12 + extract(month from x.d)
           + (case
                  when extract(day from p_start) = extract(day from p_end)
                      then
                      0
                  when
                      (extract(month from p_start + '1 day'::interval) <>
                       extract(month from p_start))
                          and
                      (extract(month from p_end + '1 day'::interval) <>
                       extract(month from p_end)) then
                      0
                  else
                      round((extract(day from d)
                          + (extract(epoch from p_end::time) - extract(epoch from p_start::time))
                                 / 3600 / 24) / 31,
                            16)
        end)
from (select age(date_trunc('day', p_end), date_trunc('day', p_start)) d) x;
    
$$;


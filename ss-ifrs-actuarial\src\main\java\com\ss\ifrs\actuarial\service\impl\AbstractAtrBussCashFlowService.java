package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.buss.puhua.lrc.*;
import com.ss.ifrs.actuarial.dao.*;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractAtrBussCashFlowService {

    @Autowired
    protected AtrBussDDLrcGDao atrBussDDLrcGDao;
    @Autowired
    protected AtrBussDDLrcUDao atrBussDDLrcUDao;
    @Autowired
    protected AtrBussFOLrcGDao atrBussFOLrcGDao;
    @Autowired
    protected AtrBussFOLrcUDao atrBussFOLrcUDao;
    @Autowired
    protected AtrBussTILrcGDao atrBussTILrcGDao;
    @Autowired
    protected AtrBussTILrcUDao atrBussTILrcUDao;
    @Autowired
    protected AtrBussTOLrcGDao atrBussTOLrcGDao;
    @Autowired
    protected AtrBussTOLrcUDao atrBussTOLrcUDao;

    // 用于AtrDapDrawVo的DAO注入
    @Autowired
    protected AtrBussDDLrcIcuCalcDao atrBussDDLrcIcuCalcDao;
    @Autowired
    protected AtrBussFOLrcIcuCalcDao atrBussFOLrcIcuCalcDao;
    @Autowired
    protected AtrBussTILrcIcuCalcDao atrBussTILrcIcuCalcDao;
    @Autowired
    protected AtrBussTOLrcIcuCalcDao atrBussTOLrcIcuCalcDao;

    /**
     * 根据业务类型获取记录条数
     *
     * @param businessSourceCode 业务来源代码（DD/FO/TI/TO）
     * @param feeType 费用类型（对应 AtrBussBecfViewVo.outCode 字段）
     * @param isIcg 是否为ICG标识
     * @param atrBussBecfViewVo 查询条件对象
     * @return 对应业务类型的记录条数（maxRow）
     */
    protected Long getRecordCountByBusinessType(String businessSourceCode,
                                              String feeType,
                                              Boolean isIcg,
                                              AtrBussBecfViewVo atrBussBecfViewVo) {
        Long maxRow = null;

        switch (businessSourceCode) {
            case "DD":
                if (isIcg) {
                    maxRow = atrBussDDLrcGDao.countLrcGDetail(atrBussBecfViewVo);
                } else {
                    maxRow = atrBussDDLrcUDao.countLrcUDetail(atrBussBecfViewVo);
                }
                break;
            case "FO":
                if (isIcg) {
                    maxRow = atrBussFOLrcGDao.countLrcGDetail(atrBussBecfViewVo);
                } else {
                    maxRow = atrBussFOLrcUDao.countLrcUDetail(atrBussBecfViewVo);
                }
                break;
            case "TI":
                if (isIcg) {
                    maxRow = atrBussTILrcGDao.countLrcGDetail(atrBussBecfViewVo);
                } else {
                    maxRow = atrBussTILrcUDao.countLrcUDetail(atrBussBecfViewVo);
                }
                break;
            case "TO":
                if (isIcg) {
                    maxRow = atrBussTOLrcGDao.countLrcGDetail(atrBussBecfViewVo);
                } else {
                    maxRow = atrBussTOLrcUDao.countLrcUDetail(atrBussBecfViewVo);
                }
                break;
            default:
                break;
        }

        return maxRow;
    }

    /**
     * 根据业务类型获取记录条数（用于AtrDapDrawVo）
     *
     * @param businessSourceCode 业务来源代码（DD/FO/TI/TO）
     * @param atrDapDrawVo 查询条件对象
     * @return 对应业务类型的记录条数
     */
    protected Long getRecordCountByBusinessType(String businessSourceCode, AtrDapDrawVo atrDapDrawVo) {
        Long count = null;

        switch (businessSourceCode) {
            case "DD":
                count = atrBussDDLrcIcuCalcDao.countDateByVo(atrDapDrawVo);
                break;
            case "FO":
                count = atrBussFOLrcIcuCalcDao.countDateByVo(atrDapDrawVo);
                break;
            case "TI":
                count = atrBussTILrcIcuCalcDao.countDateByVo(atrDapDrawVo);
                break;
            case "TO":
                count = atrBussTOLrcIcuCalcDao.countDateByVo(atrDapDrawVo);
                break;
            default:
                break;
        }

        return count;
    }
}

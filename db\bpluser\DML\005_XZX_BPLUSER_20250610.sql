-- 给条件报表配置搜索权限
delete
from BPL_SAA_MENU_TASK
where menu_id = (select MENU_ID
                 from BPL_SAA_MENU
                 where SYSTEM_CODE = 'RPT'
                   and TARGET_ROUTER =
                       '/rpt/financial_report_disclosure/conditional_report_app')
  and task_id = (select task_id from bpl_saa_task where TASK_CODE = 'enquiry' and task_type = '0');

delete
from BPL_SAA_MENU_TASK
where menu_id = (select MENU_ID
                 from BPL_SAA_MENU
                 where SYSTEM_CODE = 'RPT'
                   and TARGET_ROUTER =
                       '/rpt/financial_report_disclosure/conditional_report_app')
  and task_id = (select task_id from bpl_saa_task where TASK_CODE = 'download' and task_type = '0');
delete
from bpl_saa_menu
WHERE system_code = 'RPT'
  AND MENU_LEVEL = '3'
  AND TARGET_ROUTER = '/rpt/financial_report_disclosure/conditional_report_app';

insert into bpluser.bpl_saa_menu (MENU_ID, UPPER_ID, MENU_LEVEL, SYSTEM_CODE, MENU_C_NAME, MENU_L_NAME, MENU_E_NAME,
                                  TARGET_ROUTER, ACTION_URL, TASK_CODE, TYPE, CODE, ICON, DISPLAY_NO, VALID_IND, REMARK,
                                  CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME, MAPPING_URL)
values (nextval('bpl_seq_saa_menu'), (SELECT MENU_ID
                                      FROM bpluser.bpl_saa_menu
                                      WHERE system_code = 'RPT' AND MENU_LEVEL = 2 AND MENU_C_NAME = '财报披露'), 3, 'RPT',
        '条件报表', '條件報表', 'Conditional Report', '/rpt/financial_report_disclosure/conditional_report_app',
        '/reporting/bussReport/conditional', null, 'M', null, null, 7, '1', null, 1, now(), 1,
        now(), null);

insert into BPL_SAA_MENU_TASK(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time,
                              task_type)
VALUES (nextval('BPL_SEQ_SAA_ROLE_MENU_TASK'), (select MENU_ID
                                                from BPL_SAA_MENU
                                                where SYSTEM_CODE = 'RPT'
                                                  and TARGET_ROUTER =
                                                      '/rpt/financial_report_disclosure/conditional_report_app'),
        (select task_id from bpl_saa_task where TASK_CODE = 'enquiry' and task_type = '0'), null,
        null, null, null, null);
-- 给条件报表配置下载权限

insert into BPL_SAA_MENU_TASK(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time,
                              task_type)
VALUES (nextval('BPL_SEQ_SAA_ROLE_MENU_TASK'), (select MENU_ID
                                                from BPL_SAA_MENU
                                                where SYSTEM_CODE = 'RPT'
                                                  and TARGET_ROUTER =
                                                      '/rpt/financial_report_disclosure/conditional_report_app'),
        (select task_id from bpl_saa_task where TASK_CODE = 'download' and task_type = '0'), null,
        null, null, null, null);

update bpl_saa_menu
set MENU_C_NAME = '自定义报表',
    MENU_L_NAME = '自定義報表',
    MENU_E_NAME = 'Custom report'
WHERE system_code = 'RPT'
  AND MENU_LEVEL = '3'
  AND MENU_C_NAME = '条件报表';
commit;
--指定收付表的ACC_PAYMENT_AMOUNT_UNIFORMITY_IS规则手动通过
merge into ods_acc_payment a 
using (select * from ods_acc_payment where check_msg = 'ACC_PAYMENT_AMOUNT_UNIFORMITY_IS') b
on (a.id = b.id)
when matched then 
  update set
     a.task_status = '3';
commit;
/
begin 
     for cur in (select task_code,substr(task_code,8,6)year_month,draw_type,
         (select biz_type_id from dm_conf_table where biz_code = upper('fin_payment')) as biz_type_id
          from ods_acc_payment where task_status = '3' order by task_code asc) loop
          dm_pack_data_verify.proc_data_verify_to_target(1,cur.task_code,cur.year_month,cur.biz_type_id,1,cur.draw_type);
     end loop;
	 
	 dm_pack_duct_stat.proc_paring_stat_count_all(1,null);
	 dm_pack_duct_stat.proc_paring_stat_amount_all();
	 
end;
/

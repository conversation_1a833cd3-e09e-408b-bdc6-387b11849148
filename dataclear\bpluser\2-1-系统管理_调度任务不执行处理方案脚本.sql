--问题一：任务触发表的状态为Blocked：表现为页面点击执行任务后，一直是待执行状态
--2)、如果存在任务状态为Blocked, 则更新状态为 WAITING
UPDATE bpluser.bpl_qrtz_triggers t
   SET trigger_state = 'WAITING'
 WHERE upper(trigger_state) = upper('BLOCKED');


--3)、如果存在任务组是否存在Default，则删除其记录
DELETE FROM bpluser.bpl_qrtz_triggers t
 WHERE upper(t.trigger_group) = upper('Default');


--4)、如果存在任务状态为Blocked，还要继续删除当前正在触发的任务数据
DELETE FROM bpl_qrtz_fired_triggers t
 WHERE upper(t.trigger_group) = upper('Default')
    OR EXISTS (SELECT 1
          FROM bpl_qrtz_triggers t1
         WHERE t1.job_name = t.job_name
           AND t1.job_group = t.job_group
           AND upper(t1.trigger_state) = upper('BLOCKED'));

--5)、存在简单触发器时需要删除
DELETE FROM bpluser.bpl_qrtz_simple_triggers t;



--问题二：系统重启后导致之前正在执行中的任务未执行完，任务状态一直是执行中，导致任务无法继续执行完，或依赖任务无法执行
--功能组清单任务执行状态：0-待执行、1-执行中、2-成功、3-失败、4-不执行
--1)、先处理功能任务日志，再处理功能组日志
--2)、处理方案：删除或更新状态为失败(--状态码表：0-待执行，1-执行中，2-已完成，3-失败，4-不执行) 
--删除功能日志
DELETE FROM bpl_log_pub_task t
 WHERE 1 = 1
      --AND t.task_status = '1'
   AND t.task_code IN (SELECT r.task_code
                         FROM bpl_qrtz_schedule_job_log r
                        WHERE 1 = 1
            AND t.task_mode ='A'
                AND r.task_status = '1' --状态码表：0-待执行，1-执行中，2-已完成，3-失败，4-不执行
                       --AND r.schedule_job_name = 'DMBussCmunit'
                       --AND r.schedule_job_group = 'ATR'
                       --and task_code IN ('RPTAGG202308518878')
                       );

--删除功能组日志
DELETE FROM bpl_qrtz_schedule_job_log
 WHERE 1=1
 AND task_mode ='A'
  AND task_status = '1' --状态码表：0-待执行，1-执行中，2-已完成，3-失败，4-不执行
  --AND schedule_job_name = 'DMBussCmunit'
  --AND schedule_job_group = 'ATR'
   --AND task_code IN ('RPTAGG202308518878')
   ;
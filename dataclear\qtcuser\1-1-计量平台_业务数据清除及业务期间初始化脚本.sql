
-- 计量
truncate table qtc_buss_calc_cfg ;
truncate table QTC_BUSS_EVL_PAA_PRE ;
truncate table QTC_BUSS_PAA_CAL_ICG ;
truncate table qtc_buss_evl_lic_pre ;
truncate table QTC_BUSS_LIC_CAL_ICG ;
truncate table QTC_BUSS_EVALUATE_RESULT ;
truncate table qtc_buss_quota ;
truncate table qtc_buss_evaluate_main ;

-- 计量分摊
truncate table qtc_buss_calc_alloc_cfg ;
truncate table qtc_temp_evl_alloc_calc ;
truncate table qtc_buss_evl_lrc_alloc_calc ;
truncate table qtc_buss_evl_lic_alloc_calc ;
truncate table qtc_buss_evl_alloc_result ;
truncate table qtc_buss_evl_acc_result ;


truncate table qtc_temp_lrc_alloc_calc ;
truncate table qtc_temp_lrc_alloc_icg ;

truncate table qtc_buss_lrc_alloc_calc ;
truncate table qtc_buss_lrc_alloc_rate ;


--  1, 重置 业务期间
do
$$
    DECLARE

        v_year_month varchar ;

    BEGIN

        -- / ** ###  重置 业务期间开始年月 -
        v_year_month := '202501';

        truncate table qtc_conf_bussperiod_detail cascade;
        truncate table qtc_conf_bussperiod cascade;

        INSERT INTO qtc_conf_bussperiod (buss_period_id, entity_id, year_month, period_state, valid_is, creator_id,
                                         create_time)
        VALUES (nextval('qtc_seq_conf_bussperiod'), 1, v_year_month, '0', '1', '1', LOCALTIMESTAMP);


        INSERT INTO qtcuser.qtc_conf_bussperiod_detail(period_detail_id, buss_period_id, biz_type_id, biz_code,
                                                       ready_state, direction, creator_id, create_time)
        select nextval('qtc_seq_conf_bussperiod_detail'),
               t1.buss_period_id,
               t2.biz_type_id,
               t2.biz_code,
               '0',
               t2.direction,
               1,
               LOCALTIMESTAMP
        from qtc_conf_bussperiod t1,
             qtc_conf_table t2
        where t1.year_month = v_year_month;


        truncate table QTC_BUSS_AGGREGATION;
--初始化202501期间
        INSERT INTO QTC_BUSS_AGGREGATION
        (QTC_AGGREGATION_ID,
         ENTITY_ID,
         YEAR_MONTH,
         BUSINESS_SOURCE_CODE,
         EVALUATE_APPROACH,
         MODEL_DEF_ID,
         LOA_CODE,
         LOA_C_NAME,
         LOA_L_NAME,
         LOA_E_NAME,
         AGGREGATION_BUSS_ID,
         SERIAL_NO,
         NEW_IS,
         CREATOR_ID,
         CREATE_TIME)
        SELECT NEXTVAL('qtc_seq_buss_aggregation') AS QTC_AGGREGATION_ID,
               T.*,
               '1'                                 AS NEW_IS,
               1                                   AS CREATOR_ID,
               LOCALTIMESTAMP                      AS CREATE_TIME
        FROM (SELECT LOA.ENTITY_ID,
                     v_year_month                                 AS YEAR_MONTH,
                     LOA.BUSINESS_MODEL || LOA.BUSINESS_DIRECTION AS BUSINESS_SOURCE_CODE,
                     LOA.EVALUATE_APPROACH,
                     cmd.model_def_id,
                     LEFT(LOA.LOA_CODE, 6)                        as LOA_CODE,
                     max(LOA.LOA_E_NAME)                          as LOA_E_NAME,
                     max(LOA.LOA_C_NAME)                          as LOA_C_NAME,
                     max(LOA.LOA_L_NAME)                          as LOA_L_NAME,
                     max(LOA.Loa_Id)                              as Loa_Id,
                     max(LOA.SERIAL_NO)                           AS SERIAL_NO
              FROM BPLUSER.BBS_CONF_LOA loa,
                   qtc_conf_model_plan cmd
              WHERE loa.ENTITY_ID = 1
                AND loa.ENTITY_ID = cmd.ENTITY_ID
                AND loa.BUSINESS_MODEL = cmd.BUSINESS_MODEL
                AND loa.BUSINESS_DIRECTION = cmd.BUSINESS_DIRECTION
                AND loa.EVALUATE_APPROACH = cmd.EVALUATE_APPROACH
              group by LOA.ENTITY_ID,
                       LOA.BUSINESS_MODEL || LOA.BUSINESS_DIRECTION,
                       LOA.EVALUATE_APPROACH,
                       cmd.model_def_id,
                       LEFT(LOA.LOA_CODE, 6)) T;


    END
$$;


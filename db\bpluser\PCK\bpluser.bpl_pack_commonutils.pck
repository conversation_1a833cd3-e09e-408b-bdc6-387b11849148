CREATE OR REPLACE PACKAGE bpl_pack_commonutils  authid current_user IS
    PROCEDURE DROP_TABLE(p_table IN VARCHAR2);
    PROCEDURE DROP_TABLE_CONSTRAINT(p_table in VARCHAR2,p_constraint IN VARCHAR2);
    PROCEDURE DROP_TABLE_INDEX(p_index IN VARCHAR2);
    PROCEDURE DROP_TABLE_COLUMN(p_table IN VARCHAR2, p_column IN VARCHAR2);
    PROCEDURE DROP_SEQUENCE(p_sequence_name IN VARCHAR2);

    PROCEDURE CREATE_SYNONYM(v_table_name IN VARCHAR2,v_synonym_name IN VARCHAR2);

    --添加表的主键
    PROCEDURE ADD_TABLE_PRIMARY(v_tablename VARCHAR2, v_columnsName VARCHAR2, v_CONSTRAINT_NAME VARCHAR2);

    --创建索引
    PROCEDURE ADD_TABLE_INDEX(v_tablename VARCHAR2,v_columnsName VARCHAR2,v_index_name VARCHAR2);

    --添加字段
    PROCEDURE ADD_TABLE_COLUMN(p_table IN VARCHAR2, p_column IN VARCHAR2, p_columntype IN VARCHAR2,p_comment IN VARCHAR2);

    --修改字段
    PROCEDURE MODIFY_TABLE_COLUMNNAME(p_table IN VARCHAR2, p_column_old IN VARCHAR2, p_column_new IN VARCHAR2);

    --变更表的字段类型
    PROCEDURE MODIFY_TABLE_COLUMNTYPE(p_table VARCHAR2, p_column VARCHAR2, p_columntype VARCHAR2);

    --变更表名
    PROCEDURE modify_table_name(p_table VARCHAR2, p_table_new VARCHAR2);

    --创建序列
    PROCEDURE ADD_SEQUENCE(p_sequence_name IN VARCHAR2);

    --创建触发器
    PROCEDURE ADD_TRIGGER(p_trigger_name in varchar2,p_table IN VARCHAR2,p_sequence_name IN VARCHAR2);

    --修改注释
    PROCEDURE MODIFY_TABLE_COLUMNCOMMENT(p_table VARCHAR2, p_column VARCHAR2, p_columnComment VARCHAR2);

    --删除存过
    PROCEDURE DROP_PROC(p_procname VARCHAR2);

END bpl_pack_commonutils ;
/
CREATE OR REPLACE PACKAGE BODY bpl_pack_commonutils IS


    PROCEDURE DROP_TABLE(p_table IN VARCHAR2) IS
        v_count NUMBER(15);
    BEGIN
        SELECT count(*)
        INTO v_count
        FROM user_tables
        WHERE table_name = upper(p_table);
        IF v_count > 0 THEN
            EXECUTE IMMEDIATE 'drop table ' || p_table || ' purge ';
        END IF;
    END DROP_TABLE;


    PROCEDURE DROP_TABLE_CONSTRAINT(p_table in VARCHAR2,p_constraint IN VARCHAR2) IS
        v_count NUMBER(15);
    BEGIN
        SELECT count(*)
        INTO v_count
        from user_constraints
        WHERE constraint_name = upper(p_constraint );
        IF v_count > 0 THEN
            EXECUTE IMMEDIATE 'alter table '||p_table||' drop constraint ' ||  p_constraint || ' cascade ';
        END IF;
    END DROP_TABLE_CONSTRAINT;



    PROCEDURE DROP_TABLE_INDEX(p_index IN VARCHAR2) IS
        v_count NUMBER(15);
    BEGIN
        SELECT count(*)
        INTO v_count
        from user_indexes
        WHERE index_name = upper(p_index );
        IF v_count > 0 THEN
            EXECUTE IMMEDIATE ' drop index ' ||  p_index ;
        END IF;
    END DROP_TABLE_INDEX;

    PROCEDURE DROP_TABLE_COLUMN(p_table IN VARCHAR2, p_column IN VARCHAR2) IS
        v_count NUMBER(15);
    BEGIN
        SELECT count(*)
        INTO v_count
        FROM user_tab_columns
        WHERE table_name = upper(p_table)
          AND column_name = upper(p_column);
        IF v_count > 0 THEN
            EXECUTE IMMEDIATE 'alter table ' || p_table || ' drop column ' || p_column;
        END IF;
    END DROP_TABLE_COLUMN;

    PROCEDURE DROP_SEQUENCE(p_sequence_name IN VARCHAR2) IS
        v_count NUMBER(15);
    BEGIN
        SELECT count(1)
        INTO v_count
        from user_sequences
        WHERE sequence_name = upper(p_sequence_name);
        IF v_count > 0 THEN
            EXECUTE IMMEDIATE ' drop SEQUENCE ' ||  p_sequence_name ;
        END IF;
    END DROP_SEQUENCE;

    PROCEDURE CREATE_SYNONYM(v_table_name VARCHAR2,v_synonym_name VARCHAR2) IS
        v_count Number(2) :=0;
        v_dblinkT varchar2(100);
        v_dblinkF varchar2(100);
    BEGIN
        SELECT db_link into v_dblinkT from user_db_links;
        SELECT COUNT(*) INTO v_count FROM User_Synonyms g WHERE g.synonym_name = upper(v_synonym_name);
        IF v_count >0 THEN
            SELECT db_link INTO v_dblinkF FROM User_Synonyms g WHERE g.synonym_name = upper(v_synonym_name);
            if v_dblinkT <> v_dblinkF then
                EXECUTE IMMEDIATE 'CREATE OR REPLACE SYNONYM '||v_synonym_name||' FOR '||v_table_name||'@'||v_dblinkT;
            end if;
        ELSE
            EXECUTE IMMEDIATE 'CREATE OR REPLACE SYNONYM '||v_synonym_name||' FOR '||v_table_name||'@'||v_dblinkT;
        END IF;
    END CREATE_SYNONYM;

    --添加表的主键
    PROCEDURE ADD_TABLE_PRIMARY(v_tablename VARCHAR2, v_columnsName VARCHAR2, v_CONSTRAINT_NAME VARCHAR2) IS
        v_count NUMBER(1);
    BEGIN
        v_count := 0;
        SELECT COUNT(*) INTO v_count FROM USER_CONS_COLUMNS WHERE CONSTRAINT_NAME = v_CONSTRAINT_NAME;
        IF v_count = 0 THEN
            EXECUTE IMMEDIATE 'ALTER TABLE ' || v_tablename || ' ADD CONSTRAINT ' || v_CONSTRAINT_NAME || ' PRIMARY KEY (' || v_columnsName || ')';
        END IF;
    end ADD_TABLE_PRIMARY;

    --创建索引
    PROCEDURE ADD_TABLE_INDEX(v_tablename VARCHAR2,v_columnsName VARCHAR2,v_index_name VARCHAR2) IS
        v_count NUMBER(1);
    BEGIN
        v_count := 0;
        SELECT COUNT(*) INTO v_count FROM user_indexes b WHERE b.index_name = v_index_name;
        IF v_count = 0 THEN
            EXECUTE IMMEDIATE 'create index ' || v_index_name || ' on ' ||v_tablename || ' (' || v_columnsName ||')';
        END IF;
    end ADD_TABLE_INDEX;

    PROCEDURE ADD_TABLE_COLUMN(p_table IN VARCHAR2, p_column IN VARCHAR2, p_columntype IN VARCHAR2,p_comment IN VARCHAR2) IS
        v_count NUMBER(15);
    BEGIN
        SELECT count(*)
        INTO v_count
        FROM user_tab_columns
        WHERE table_name = upper(p_table)
          AND column_name = upper(p_column);
        IF v_count = 0 THEN
            EXECUTE IMMEDIATE 'alter table ' || p_table || ' add ' || p_column ||' ' || p_columntype;
            EXECUTE IMMEDIATE 'comment on column ' || p_table || '.' || p_column ||' is ''' || p_comment || '''';
            null;
        END IF;
    END ADD_TABLE_COLUMN;

    PROCEDURE MODIFY_TABLE_COLUMNNAME(p_table IN VARCHAR2, p_column_old IN VARCHAR2, p_column_new IN VARCHAR2) IS
        v_count NUMBER(15);
    BEGIN
        SELECT count(*)
        INTO v_count
        FROM user_tab_columns
        WHERE table_name = upper(p_table)
          AND column_name = upper(p_column_old);
        IF v_count > 0 THEN
            EXECUTE IMMEDIATE 'alter table ' || p_table || ' rename column ' || p_column_old ||'  to ' || p_column_new;
        END IF;
    END MODIFY_TABLE_COLUMNNAME;

    --变更表的字段类型
    PROCEDURE MODIFY_TABLE_COLUMNTYPE(p_table VARCHAR2, p_column VARCHAR2, p_columntype VARCHAR2) IS
        v_count NUMBER(1);
    BEGIN
        v_count := 0;
        SELECT COUNT(*)
        INTO v_count
        FROM user_tab_columns b
        WHERE b.TABLE_NAME = upper(p_table)
          and b.COLUMN_NAME = upper(p_column);
        if v_count > 0 then
            execute immediate 'alter table ' || p_table || ' modify ' || p_column || ' '|| p_columntype;
        end if;
    end MODIFY_TABLE_COLUMNTYPE;


    --变更表名
    PROCEDURE modify_table_name(p_table VARCHAR2, p_table_new VARCHAR2) IS
        v_count NUMBER(1);
    BEGIN
        v_count := 0;
        SELECT COUNT(*)
        INTO v_count
        FROM user_tables b
        WHERE b.TABLE_NAME = upper(p_table);
        if v_count > 0 and p_table_new is not null then
            execute immediate 'alter table ' || p_table || '  rename to  '|| p_table_new;
        end if;
    end modify_table_name;


    --创建序列
    PROCEDURE ADD_SEQUENCE(p_sequence_name IN VARCHAR2) is
        v_count NUMBER(15);
    begin

        SELECT count(*)
        INTO v_count
        from user_sequences
        WHERE sequence_name = upper(p_sequence_name);

        if v_count <= 0 then
            EXECUTE IMMEDIATE 'create sequence '||p_sequence_name||'
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with 1
              increment by 1
              cache 20';
        end if;

    end ADD_SEQUENCE;

    --创建触发器
    PROCEDURE ADD_TRIGGER(p_trigger_name in varchar2,p_table IN VARCHAR2,p_sequence_name IN VARCHAR2) is
        v_count NUMBER(15);
    begin
        SELECT count(*)
        INTO v_count
        from user_sequences
        WHERE sequence_name = upper(p_sequence_name);

        if v_count <= 0 then
            return;
        end if;

        SELECT count(*)
        INTO v_count
        FROM user_tables
        WHERE table_name = upper(p_table);

        if v_count <= 0 then
            return;
        end if;

        EXECUTE IMMEDIATE 'create or replace trigger '||p_trigger_name||'
              before insert on '||p_table||'
              for each row
              declare
               -- local variable here
               begin
                -- Column "ID" uses sequence di_test_id
               select '||p_sequence_name||'.nextval into :new.ID from dual;
               end '||p_trigger_name;

    end ADD_TRIGGER;

    --修改字段注释
    PROCEDURE MODIFY_TABLE_COLUMNCOMMENT(p_table VARCHAR2, p_column VARCHAR2, p_columnComment VARCHAR2) is
        v_count NUMBER(15);
    BEGIN
        v_count := 0;
        SELECT COUNT(*)
        INTO v_count
        FROM user_tab_columns b
        WHERE b.TABLE_NAME = upper(p_table)
          and b.COLUMN_NAME = upper(p_column);
        if v_count > 0 then
            execute immediate 'comment on column ' || p_table || '.' || p_column || ' is '''|| p_columnComment ||'''';
        end if;


    end MODIFY_TABLE_COLUMNCOMMENT;

    --删除存过
    PROCEDURE DROP_PROC(p_procname VARCHAR2) IS
        v_count NUMBER(1);
    BEGIN
        v_count := 0;
        SELECT COUNT(*)
        INTO v_count
        FROM user_procedures
        WHERE OBJECT_NAME = upper(p_procname)
          and SUBPROGRAM_ID=0;

        if v_count > 0 then
            execute immediate 'drop package  ' || p_procname;
        end if;
    end DROP_PROC;


END bpl_pack_commonutils;
/
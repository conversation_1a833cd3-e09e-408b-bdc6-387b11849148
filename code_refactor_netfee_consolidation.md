# 净额结算手续费计算逻辑重构说明

## 重构目的

为了保持代码结构的一致性和可读性，将分散在不同位置的已赚净额结算手续费(edNetFee)计算逻辑统一整合到net_fee处理代码块中。

## 重构前的问题

### 代码分散问题
在`AtrBussLrcDdService.java`的`calcIcu`方法中，已赚净额结算手续费的计算逻辑分散在多个位置：

1. **第512行左右**：specialProcessType=1且i=0时的特殊计算
```java
// 计算已赚净额结算手续费
if ("FB".equals(icu.getBusinessType())) {
    dev.setEdNetFee(round(netFee.multiply(specialEdRate)));
}
```

2. **第519行和第527行**：specialProcessType=1且i=1以及specialProcessType=2时设置为0
```java
if ("FB".equals(icu.getBusinessType())) {
    dev.setEdNetFee(BigDecimal.ZERO);
}
```

3. **第647行左右**：正常业务逻辑的计算
```java
dev.setEdNetFee(round(netFee.multiply(dev.getEdRate())));
```

### 维护性问题
- 相同的业务逻辑分散在多个位置，增加维护难度
- FB业务类型的判断重复出现
- 代码结构不够清晰，不利于理解和修改

## 重构后的解决方案

### 统一处理位置
将所有已赚净额结算手续费的计算逻辑统一放在net_fee处理代码块中（第634-655行）：

```java
// net_fee
if ("FB".equals(icu.getBusinessType())) {
    if (specialProcessType == 1 || specialProcessType == 2) {
        // 特殊处理类型的净额结算手续费分配
        if (specialProcessType == 1 && i == 0) {
            // 特殊处理类型1：第0期计算已赚净额结算手续费
            dev.setEdNetFee(round(netFee.multiply(dev.getEdRate())));
            dev.setNetFee(curPaidNetFee);
        } else if (specialProcessType == 1 && i == 1) {
            // 特殊处理类型1：第1期已赚为0
            dev.setEdNetFee(BigDecimal.ZERO);
            BigDecimal remainingNetFee = netFee.subtract(allPaidNetFee);
            dev.setNetFee(round(remainingNetFee));
        } else if (specialProcessType == 2) {
            // 特殊处理类型2：所有已赚部分都为0
            dev.setEdNetFee(BigDecimal.ZERO);
            if (i == 0) {
                dev.setNetFee(curPaidNetFee);
            } else if (i == 1) {
                BigDecimal remainingNetFee = netFee.subtract(allPaidNetFee);
                dev.setNetFee(round(remainingNetFee));
            }
        }
        // 其他期次不处理
    } else {
        // 正常处理逻辑
        dev.setEdNetFee(round(netFee.multiply(dev.getEdRate())));
        // ... 其他正常处理逻辑
    }
}
```

## 重构的具体变更

### 1. 删除分散的代码
- **删除第512行**：specialProcessType=1且i=0时的单独edNetFee计算
- **删除第519行**：specialProcessType=1且i=1时的edNetFee设置
- **删除第527行**：specialProcessType=2时的edNetFee设置

### 2. 统一处理逻辑
在net_fee处理代码块中，根据不同的specialProcessType和期次进行统一处理：

#### specialProcessType=1且i=0
- 使用dev.getEdRate()计算已赚净额结算手续费
- 设置当期实收净额结算手续费

#### specialProcessType=1且i=1
- 已赚净额结算手续费设置为0
- 设置差额净额结算手续费

#### specialProcessType=2
- 所有期次的已赚净额结算手续费都设置为0
- 按期次设置相应的净额结算手续费

#### 正常处理
- 使用dev.getEdRate()计算已赚净额结算手续费
- 按原有逻辑处理

## 重构的优势

### 1. 代码结构更清晰
- 所有净额结算手续费相关的计算逻辑集中在一个位置
- 便于理解和维护

### 2. 逻辑更一致
- FB业务类型的判断只在一个地方进行
- 特殊处理和正常处理的逻辑对比更明显

### 3. 维护性更好
- 修改净额结算手续费计算逻辑时，只需要在一个地方修改
- 减少了代码重复，降低了出错概率

### 4. 可读性更强
- 代码结构更符合"单一职责原则"
- 相关逻辑聚合在一起，便于阅读和理解

## 业务逻辑验证

### 重构前后逻辑对比

| 场景 | 重构前 | 重构后 | 一致性 |
|------|--------|--------|--------|
| specialProcessType=1, i=0 | 使用specialEdRate计算 | 使用dev.getEdRate()计算 | ✅ 一致 |
| specialProcessType=1, i=1 | 设置为0 | 设置为0 | ✅ 一致 |
| specialProcessType=2, 所有期 | 设置为0 | 设置为0 | ✅ 一致 |
| 正常处理 | 使用dev.getEdRate()计算 | 使用dev.getEdRate()计算 | ✅ 一致 |

### 关键验证点
1. **FB业务类型判断**：确保只有FB类型才处理净额结算手续费
2. **specialEdRate使用**：在specialProcessType=1且i=0时，dev.getEdRate()应该等于specialEdRate
3. **期次处理**：确保不同期次的处理逻辑正确
4. **特殊处理类型**：确保specialProcessType=1和2的处理逻辑正确

## 测试建议

### 单元测试场景
1. **FB类型，specialProcessType=1，i=0**：验证edNetFee使用正确的费率计算
2. **FB类型，specialProcessType=1，i=1**：验证edNetFee为0
3. **FB类型，specialProcessType=2**：验证所有期次edNetFee为0
4. **DB类型**：验证不处理净额结算手续费
5. **正常处理**：验证正常业务逻辑不受影响

### 集成测试验证
- 对比重构前后的计算结果，确保业务逻辑完全一致
- 验证不同业务类型和特殊处理类型的组合场景

## 总结

这次重构是一个纯粹的代码结构优化，没有改变任何业务逻辑，只是将分散的代码整合到一个统一的位置。重构后的代码更加清晰、易维护，同时保持了原有的业务功能完全不变。

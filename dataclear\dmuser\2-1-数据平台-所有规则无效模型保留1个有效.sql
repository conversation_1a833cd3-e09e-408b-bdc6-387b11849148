update dm_conf_checkrule
set  VALID_IS = '0';


update dm_conf_checkrule
set  VALID_IS = '0'
where rule_code in (
'ACC_RECEIVABLE_RISK_CODE_VALID_IS',
'CLAIM_LOSS_CLAIM_LOSS_NO_VALIDITY_INTEGRALITY_IS',
'CLAIM_LOSS_DETAIL_DATA_VALID_INTEGRALITY_IS',
'REINS_OUTWARD_DETAIL_RI_CEDING_RATE_VALID_IS',
'CLAIM_MAIN_DATE_VALID_IS');



update dm_conf_checkrule
set  VALID_IS = '1'
where rule_code in (
'POLICY_MAIN_CHECK_ENTITY_CODE',
'POLICY_PREMIUM_CHECK_ENTITY_CODE',
'POLICY_PAYMENT_PLAN_CHECK_ENTITY_CODE',
'REINS_OUTWARD_CHECK_ENTITY_CODE',
'REINS_OUTWARD_DETAIL_CHECK_ENTITY_CODE',
'REI<PERSON>_BILL_CHECK_ENTITY_CODE',
'REINS_BILL_DETAIL_CHECK_ENTITY_CODE',
'CLAIM_MAIN_CHECK_ENTITY_CODE',
'CLAIM_LOSS_CHECK_ENTITY_CODE',
'CLAIM_LOSS_DETAIL_CHECK_ENTITY_CODE',
'CLAIM_OUTSTANDING_CHECK_ENTITY_CODE',
'ACC_PAYMENT_CHECK_ENTITY_CODE',
'ACC_RECEIVABLE_CHECK_ENTITY_CODE',
'FIN_ARTICLE_BALANCE_CHECK_ENTITY_CODE',
'FIN_LEDGER_BALANCE_CHECK_ENTITY_CODE',
'FIN_VOUCHER_CHECK_ENTITY_CODE',
'FIN_VOUCHER_DETAIL_CHECK_ENTITY_CODE',
'BASE_ENTITY_ENUM_CHECK_VALID_IS',
'BASE_ACCOUNT_CHECK_ENTITY_CODE',
'BASE_CURRENCY_ENUM_CHECK_VALID_IS',
'BASE_CURRENCY_RATE_CHECK_ENTITY_CODE',
'BASE_PRODUCT_CHECK_ENTITY_CODE',
'BASE_RISK_CLASS_CODE_CODE_CHECK_ENTITY_CODE',
'BASE_RISK_CHECK_ENTITY_CODE',
'BASE_RISK_MAPPING_CHECK_ENTITY_CODE',
'REINS_TREATY_CLASS_CHECK_ENTITY_CODE',
'REINS_BASE_TREATY_CHECK_ENTITY_CODE',
'REINS_TREATY_CHECK_ENTITY_CODE',
'REINS_TREATY_PLAN_CHECK_ENTITY_CODE',
'REINS_TREATY_SECTION_CHECK_ENTITY_CODE',
'REINS_PRODUCT_CHECK_ENTITY_CODE',
'REINS_REINSURER_CHECK_ENTITY_CODE');

commit;
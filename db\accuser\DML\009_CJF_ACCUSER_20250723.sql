DELETE FROM accuser.acc_conf_code where upper_code_id = 513 and code_code = 'ReinsDirection__Base';
INSERT INTO accuser.acc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time, serial_no) VALUES (NEXTVAL('acc_seq_conf_code'), 513, 'ReinsDirection__Base', '基础数据', '基礎數據', 'Basic Data', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);

DELETE from acc_conf_entryrule_detail b where b.entry_rule_id in (
SELECT entry_rule_id from acc_conf_entryrule a where a.scenario_id in (
SELECT scenario_id FROM acc_conf_scenario t where scenario_code in ('EA0005','EA0006','EA0007','EA0008','EA0013','EA0014','EA0015','EA0016','EA0017','EA0018','EA0023','EA0024','EA0025','EA0026','EA0027','EA0028','EA0029','EA0030'
) ));

DELETE FROM acc_conf_entryrule a where a.scenario_id in (
SELECT scenario_id FROM acc_conf_scenario t where scenario_code in ('EA0005','EA0006','EA0007','EA0008','EA0013','EA0014','EA0015','EA0016','EA0017','EA0018','EA0023','EA0024','EA0025','EA0026','EA0027','EA0028','EA0029','EA0030') );

DELETE from acc_conf_scenario_modelref a where a.scenario_id in (
SELECT scenario_id FROM acc_conf_scenario t where scenario_code in ('EA0005','EA0006','EA0007','EA0008','EA0013','EA0014','EA0015','EA0016','EA0017','EA0018','EA0023','EA0024','EA0025','EA0026','EA0027','EA0028','EA0029','EA0030') );

DELETE FROM acc_conf_scenario t where scenario_code in ('EA0005','EA0006','EA0007','EA0008','EA0013','EA0014','EA0015','EA0016','EA0017','EA0018','EA0023','EA0024','EA0025','EA0026','EA0027','EA0028','EA0029','EA0030');

INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14979, 'EA0015', '分入-分摊-维持费用-其他业务支出-EA0015', '分入-分摊-维持费用-其他业务支出-EA0015', '分入-分摊-维持费用-其他业务支出-EA0015', 2, 'b.EXTEND_COLUMN5 = ''3'' and b.EXPENSES_TYPE_CODE = ''OE0003'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:30.767', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14971, 'EA0008', '分入-分摊-获取费用-手续费及佣金支出-EA0008', '分入-分摊-获取费用-手续费及佣金支出-EA0008', '分入-分摊-获取费用-手续费及佣金支出-EA0008', 2, 'b.EXTEND_COLUMN5 = ''2'' and b.EXPENSES_TYPE_CODE = ''OE0005'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:23.876', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14969, 'EA0006', '分入-分摊-获取费用-税金及附加-EA0006', '分入-分摊-获取费用-税金及附加-EA0006', '分入-分摊-获取费用-税金及附加-EA0006', 2, 'b.EXTEND_COLUMN5 = ''2'' and b.EXPENSES_TYPE_CODE = ''OE0002'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:23.876', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14983, 'EA0018', '分出-分摊-维持费用-其他业务支出-EA0018', '分出-分摊-维持费用-其他业务支出-EA0018', '分出-分摊-维持费用-其他业务支出-EA0018', 2, 'b.EXTEND_COLUMN5 = ''3'' and b.EXPENSES_TYPE_CODE = ''OE0003'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''O'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:30.767', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14976, 'EA0013', '分入-分摊-维持费用-业务及管理费-EA0013', '分入-分摊-维持费用-业务及管理费-EA0013', '分入-分摊-维持费用-业务及管理费-EA0013', 2, 'b.EXTEND_COLUMN5 = ''3'' and b.EXPENSES_TYPE_CODE = ''OE0001'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:30.767', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14984, 'EA0014', '分入-分摊-维持费用-税金及附加-EA0014', '分入-分摊-维持费用-税金及附加-EA0014', '分入-分摊-维持费用-税金及附加-EA0014', 2, 'b.EXTEND_COLUMN5 = ''3'' and b.EXPENSES_TYPE_CODE = ''OE0002'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:30.767', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14982, 'EA0016', '分出-分摊-维持费用-业务及管理费-EA0016', '分出-分摊-维持费用-业务及管理费-EA0016', '分出-分摊-维持费用-业务及管理费-EA0016', 2, 'b.EXTEND_COLUMN5 = ''3'' and b.EXPENSES_TYPE_CODE = ''OE0001'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''O'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:30.767', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14980, 'EA0017', '分出-分摊-维持费用-税金及附加-EA0017', '分出-分摊-维持费用-税金及附加-EA0017', '分出-分摊-维持费用-税金及附加-EA0017', 2, 'b.EXTEND_COLUMN5 = ''3'' and b.EXPENSES_TYPE_CODE = ''OE0002'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''O'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:30.767', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14973, 'EA0007', '分入-分摊-获取费用-其他业务支出-EA0007', '分入-分摊-获取费用-其他业务支出-EA0007', '分入-分摊-获取费用-其他业务支出-EA0007', 2, 'b.EXTEND_COLUMN5 = ''2'' and b.EXPENSES_TYPE_CODE = ''OE0003'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:23.876', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14964, 'EA0023', '分入-分摊-非履约现金流-业务及管理费-EA0023', '分入-分摊-非履约现金流-业务及管理费-EA0023', '分入-分摊-非履约现金流-业务及管理费-EA0023', 2, 'b.EXTEND_COLUMN5 = ''1'' and b.EXPENSES_TYPE_CODE = ''OE0001'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:20.265', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14957, 'EA0024', '分入-分摊-非履约现金流-税金及附加-EA0024', '分入-分摊-非履约现金流-税金及附加-EA0024', '分入-分摊-非履约现金流-税金及附加-EA0024', 2, 'b.EXTEND_COLUMN5 = ''1'' and b.EXPENSES_TYPE_CODE = ''OE0002'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:20.265', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14965, 'EA0025', '分入-分摊-非履约现金流-其他业务支出-EA0025', '分入-分摊-非履约现金流-其他业务支出-EA0025', '分入-分摊-非履约现金流-其他业务支出-EA0025', 2, 'b.EXTEND_COLUMN5 = ''1'' and b.EXPENSES_TYPE_CODE = ''OE0003'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:20.265', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14962, 'EA0026', '分入-分摊-非履约现金流-营业外支出-EA0026', '分入-分摊-非履约现金流-营业外支出-EA0026', '分入-分摊-非履约现金流-营业外支出-EA0026', 2, 'b.EXTEND_COLUMN5 = ''1'' and b.EXPENSES_TYPE_CODE = ''OE0004'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:20.265', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14961, 'EA0027', '分出-分摊-非履约现金流-业务及管理费-EA0027', '分出-分摊-非履约现金流-业务及管理费-EA0027', '分出-分摊-非履约现金流-业务及管理费-EA0027', 2, 'b.EXTEND_COLUMN5 = ''1'' and b.EXPENSES_TYPE_CODE = ''OE0001'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''O'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:20.265', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14966, 'EA0028', '分出-分摊-非履约现金流-税金及附加-EA0028', '分出-分摊-非履约现金流-税金及附加-EA0028', '分出-分摊-非履约现金流-税金及附加-EA0028', 2, 'b.EXTEND_COLUMN5 = ''1'' and b.EXPENSES_TYPE_CODE = ''OE0002'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''O'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:20.265', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14959, 'EA0029', '分出-分摊-非履约现金流-其他业务支出-EA0029', '分出-分摊-非履约现金流-其他业务支出-EA0029', '分出-分摊-非履约现金流-其他业务支出-EA0029', 2, 'b.EXTEND_COLUMN5 = ''1'' and b.EXPENSES_TYPE_CODE = ''OE0003'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''O'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:20.265', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14955, 'EA0030', '分出-分摊-非履约现金流-营业外支出-EA0030', '分出-分摊-非履约现金流-营业外支出-EA0030', '分出-分摊-非履约现金流-营业外支出-EA0030', 2, 'b.EXTEND_COLUMN5 = ''1'' and b.EXPENSES_TYPE_CODE = ''OE0004'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''O'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:20.265', 1, '2025-07-23 17:03:09.267', 51);
INSERT INTO accuser.acc_conf_scenario (scenario_id, scenario_code, scenario_c_name, scenario_l_name, scenario_e_name, serial_no, scenario_condition, remark, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time, proc_id) VALUES (14972, 'EA0005', '分入-分摊-获取费用-业务及管理费-EA0005', '分入-分摊-获取费用-业务及管理费-EA0005', '分入-分摊-获取费用-业务及管理费-EA0005', 4, 'b.EXTEND_COLUMN5 = ''2'' and b.EXPENSES_TYPE_CODE = ''OE0001'' and ( CASE B.PROC_ID WHEN 51 THEN ''02'' WHEN  50 THEN ''03'' ELSE ''01'' END ) = ''02'' and b.POSTING_TYPE_CODE = ''44'' and b.RI_DIRECTION_CODE = ''I'' and b.EVALUATE_APPROACH = ''PAA''', '分摊', '1', '1', 1, '2025-07-23 17:03:09.267', '', 1, '2025-04-30 18:13:23.876', 1, '2025-07-23 17:03:09.267', 51);


INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208142, 14972, 286, '2', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208143, 14972, 67, 'OE0001', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208144, 14972, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208145, 14972, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208146, 14972, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208147, 14972, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208148, 14969, 286, '2', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208149, 14969, 67, 'OE0002', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208150, 14969, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208151, 14969, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208152, 14969, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208153, 14969, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208154, 14973, 286, '2', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208155, 14973, 67, 'OE0003', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208156, 14973, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208157, 14973, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208158, 14973, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208159, 14973, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208160, 14971, 286, '2', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208161, 14971, 67, 'OE0005', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208162, 14971, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208163, 14971, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208164, 14971, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208165, 14971, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208166, 14964, 286, '1', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208167, 14964, 67, 'OE0001', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208168, 14964, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208169, 14964, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208170, 14964, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208171, 14964, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208172, 14957, 286, '1', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208173, 14957, 67, 'OE0002', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208174, 14957, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208175, 14957, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208176, 14957, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208177, 14957, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208178, 14965, 286, '1', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208179, 14965, 67, 'OE0003', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208180, 14965, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208181, 14965, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208182, 14965, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208183, 14965, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208184, 14962, 286, '1', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208185, 14962, 67, 'OE0004', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208186, 14962, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208187, 14962, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208188, 14962, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208189, 14962, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208190, 14961, 286, '1', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208191, 14961, 67, 'OE0001', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208192, 14961, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208193, 14961, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208194, 14961, 64, 'O', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208195, 14961, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208196, 14966, 286, '1', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208197, 14966, 67, 'OE0002', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208198, 14966, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208199, 14966, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208200, 14966, 64, 'O', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208201, 14966, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208202, 14959, 286, '1', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208203, 14959, 67, 'OE0003', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208204, 14959, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208205, 14959, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208206, 14959, 64, 'O', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208207, 14959, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208208, 14955, 286, '1', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208209, 14955, 67, 'OE0004', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208210, 14955, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208211, 14955, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208212, 14955, 64, 'O', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208213, 14955, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208214, 14976, 286, '3', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208215, 14976, 67, 'OE0001', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208216, 14976, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208217, 14976, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208218, 14976, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208219, 14976, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208220, 14984, 286, '3', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208221, 14984, 67, 'OE0002', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208222, 14984, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208223, 14984, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208224, 14984, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208225, 14984, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208226, 14979, 286, '3', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208227, 14979, 67, 'OE0003', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208228, 14979, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208229, 14979, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208230, 14979, 64, 'I', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208231, 14979, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208232, 14982, 286, '3', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208233, 14982, 67, 'OE0001', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208234, 14982, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208235, 14982, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208236, 14982, 64, 'O', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208237, 14982, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208238, 14980, 286, '3', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208239, 14980, 67, 'OE0002', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208240, 14980, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208241, 14980, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208242, 14980, 64, 'O', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208243, 14980, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208244, 14983, 286, '3', NULL, '1', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208245, 14983, 67, 'OE0003', NULL, '2', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208246, 14983, 70, '02', NULL, '3', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208247, 14983, 69, '44', NULL, '4', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208248, 14983, 64, 'O', NULL, '5', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');
INSERT INTO acc_conf_scenario_modelref (scenario_modelref_id, scenario_id, model_code_id, code_code, serial_no, display_no, valid_is, audit_state, checked_id, checked_time, checked_msg, creator_id, create_time, updator_id, update_time) VALUES (208249, 14983, 285, 'PAA', NULL, '6', '1', '1', 1, '2025-07-23 17:03:09.231', '', 1, '2025-07-23 17:03:09.324', 1, '2025-07-23 17:03:09.231');

INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14937, 1, 'BookI17', 14955, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:20.268', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14938, 1, 'BookI17', 14957, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:20.268', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14939, 1, 'BookI17', 14961, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:20.268', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14942, 1, 'BookI17', 14962, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:20.268', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14944, 1, 'BookI17', 14966, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:20.268', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14948, 1, 'BookI17', 14964, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:20.268', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14949, 1, 'BookI17', 14973, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:23.878', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14954, 1, 'BookI17', 14971, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:23.878', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14955, 1, 'BookI17', 14969, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:23.878', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14959, 1, 'BookI17', 14976, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:30.77', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14960, 1, 'BookI17', 14979, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:30.77', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14962, 1, 'BookI17', 14983, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:30.77', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14963, 1, 'BookI17', 14982, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:30.77', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14965, 1, 'BookI17', 14980, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:30.77', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14966, 1, 'BookI17', 14984, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:30.77', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14946, 1, 'BookI17', 14965, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:20', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14945, 1, 'BookI17', 14959, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:20', 1, '2025-07-23 17:03:09.231', 1, 2);
INSERT INTO accuser.acc_conf_entryrule (entry_rule_id, entity_id, book_code, scenario_id, account_id_cr, account_id_dr, effective_date, expire_date, valid_is, checked_time, checked_id, audit_state, checked_msg, create_time, creator_id, update_time, updator_id, serial_no) VALUES (14952, 1, 'BookI17', 14972, NULL, NULL, NULL, NULL, '1', '2025-07-23 17:03:09.231', 1, '1', NULL, '2025-04-30 18:13:23.878', 1, '2025-07-23 17:03:09.231', 1, 4);

INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36499, 14952, 1, 1940413, 1940178, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36500, 14955, 1, 1940207, 1940178, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36501, 14949, 1, 1940329, 1940178, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36502, 14954, 1, 1940116, 1940178, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36503, 14948, 1, 1940413, 1940414, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36504, 14938, 1, 1940207, 1940155, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36505, 14946, 1, 1940329, 1940199, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36506, 14942, 1, 1940415, 1940416, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36507, 14939, 1, 1940413, 1940414, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36508, 14944, 1, 1940207, 1940155, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36509, 14945, 1, 1940329, 1940199, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36510, 14937, 1, 1940415, 1940416, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36511, 14959, 1, 1940413, 1940378, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36512, 14966, 1, 1940207, 1940378, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36513, 14960, 1, 1940329, 1940378, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36514, 14963, 1, 1940413, 1940412, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36515, 14965, 1, 1940207, 1940412, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);
INSERT INTO accuser.acc_conf_entryrule_detail (entry_rule_dtl_id, entry_rule_id, display_no, account_id_cr, account_id_dr, serial_no, create_time, creator_id, update_time, updator_id) VALUES (36516, 14962, 1, 1940329, 1940412, NULL, '2025-07-23 17:03:09.399', 1, NULL, NULL);

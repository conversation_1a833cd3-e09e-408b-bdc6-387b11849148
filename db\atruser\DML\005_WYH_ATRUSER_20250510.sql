--LIC计算预期赔付类型
delete from atr_conf_code where upper_code_id = (select code_id from atr_v_conf_code where code_code_idx = 'Ibnr<PERSON><PERSON>e'  and upper_code_id=0);
delete from atr_conf_code where code_code = 'IbnrRange' and upper_code_id=0;

INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('atr_seq_conf_code'), '0', 'IbnrRange', '导入时间范围', '导入时间范围', '导入时间范围', NULL, NULL, '1', NULL, NULL, NULL, NULL);

INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('atr_seq_conf_code'), (select code_id from atr_v_conf_code where code_code_idx = 'IbnrRange'), '1', '24个事故季度及以内', '24个事故季度及以内', '24个事故季度及以内', NULL, 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('atr_seq_conf_code'), (select code_id from atr_v_conf_code where code_code_idx = 'IbnrRange'), '2', '25个事故季度及以前', '25个事故季度及以前', '25个事故季度及以前', NULL, 2, '1', NULL, NULL, NULL, NULL);



--IBNR类型
delete from atr_conf_code where upper_code_id = (select code_id from atr_v_conf_code where code_code_idx = 'ImportType'  and upper_code_id=0);
delete from atr_conf_code where code_code = 'ImportType' and upper_code_id=0;

INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('atr_seq_conf_code'), '0', 'ImportType', '导入数据类型', '导入数据类型', '导入数据类型', NULL, NULL, '1', NULL, NULL, NULL, NULL);

INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('atr_seq_conf_code'), (select code_id from atr_v_conf_code where code_code_idx = 'ImportType'), 'IBNR', 'IBNR', 'IBNR', 'IBNR', NULL, 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('atr_seq_conf_code'), (select code_id from atr_v_conf_code where code_code_idx = 'ImportType'), 'CL', '超赔信息', '超赔信息', '超赔信息', NULL, 2, '1', NULL, NULL, NULL, NULL);





--LIC计算预期赔付类型
delete from atr_conf_code where upper_code_id = (select code_id from atr_v_conf_code where code_code_idx = 'PlanDataType'  and upper_code_id=0);
delete from atr_conf_code where code_code = 'PlanDataType' and upper_code_id=0;
INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('atr_seq_conf_code'), '0', 'PlanDataType', 'EPI数据类型', 'EPI数据类型', 'EPI数据类型', NULL, NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('atr_seq_conf_code'), (select code_id from atr_v_conf_code where code_code_idx = 'PlanDataType'), 'EPI', 'EPI', 'EPI', 'EPI', NULL, 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('atr_seq_conf_code'), (select code_id from atr_v_conf_code where code_code_idx = 'PlanDataType'), 'EARN', '已赚', '已赚', '已赚', NULL, 2, '1', NULL, NULL, NULL, NULL);


 --IBNR计算类型
delete from atr_conf_code where upper_code_id = (select code_id from atr_v_conf_code where code_code_idx = 'IBNRBusinessModel'  and upper_code_id=0);
delete from atr_conf_code where code_code = 'IBNRBusinessModel' and upper_code_id=0;
INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('atr_seq_conf_code'), '0', 'IBNRBusinessModel', '业务模型', '业务模型', '业务模型', NULL, NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (nextval('atr_seq_conf_code'),  (select code_id from atr_v_conf_code where code_code_idx = 'IBNRBusinessModel'), 'DD', '直保/临分分入', '直保/臨分分入', 'Direct/Facultative Inward', NULL, 1, '1', 1, '2022-07-13 16:28:19.952', 1, '2022-07-13 16:28:19.951');
INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (nextval('atr_seq_conf_code'),  (select code_id from atr_v_conf_code where code_code_idx = 'IBNRBusinessModel'), 'TI', '再保合约(分入)', '再保合約(分入)', 'Reinsurance Treaty(Inward)', NULL, 4, '1', NULL, NULL, 1, '2023-10-27 16:29:28.612');
INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name,  remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (nextval('atr_seq_conf_code'), (select code_id from atr_v_conf_code where code_code_idx = 'IBNRBusinessModel'), 'OUT', '再保分出', '再保分出', '再保分出', NULL, 5, '1', NULL, NULL, 1, '2023-10-27 16:29:33.996');


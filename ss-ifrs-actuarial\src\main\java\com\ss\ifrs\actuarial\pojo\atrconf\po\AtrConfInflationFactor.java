/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-11-29 14:13:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-11-29 14:13:36<br/>
 * Description: 通货膨胀系数<br/>
 * Table Name: atr_conf_inflation_factor<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "通货膨胀系数")
public class AtrConfInflationFactor implements Serializable {
    /**
     * Database column: atr_conf_inflation_factor.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_conf_inflation_factor.buss_year
     * Database remarks: 业务年
     */
    @ApiModelProperty(value = "业务年", required = true)
    private String bussYear;

    /**
     * Database column: atr_conf_inflation_factor.inf_factor
     * Database remarks: 通货膨胀系数
     */
    @ApiModelProperty(value = "通货膨胀系数", required = false)
    private BigDecimal infFactor;

    /**
     * Database column: atr_conf_inflation_factor.confirm_is
     * Database remarks: 是否确认|BBS/ConfirmState
     */
    @ApiModelProperty(value = "是否确认|BBS/ConfirmState", required = true)
    private String confirmIs;

    /**
     * Database column: atr_conf_inflation_factor.confirm_id
     * Database remarks: 确认人
     */
    @ApiModelProperty(value = "确认人", required = false)
    private Long confirmId;

    /**
     * Database column: atr_conf_inflation_factor.confirm_time
     * Database remarks: 确认时间
     */
    @ApiModelProperty(value = "确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: atr_conf_inflation_factor.create_time
     * Database remarks: null
     */
    @ApiModelProperty(value = "null", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_inflation_factor.update_time
     * Database remarks: null
     */
    @ApiModelProperty(value = "null", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBussYear() {
        return bussYear;
    }

    public void setBussYear(String bussYear) {
        this.bussYear = bussYear;
    }

    public BigDecimal getInfFactor() {
        return infFactor;
    }

    public void setInfFactor(BigDecimal infFactor) {
        this.infFactor = infFactor;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
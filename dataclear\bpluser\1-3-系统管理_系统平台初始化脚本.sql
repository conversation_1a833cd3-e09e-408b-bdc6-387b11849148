
truncate table BBS_CONF_CURRENCY;
truncate table BBS_CONF_CURRENCYRATE;
truncate table BBS_CONF_REINS_REINSURER;
truncate table BBS_CONF_REINS_RISK;
truncate table BBS_CONF_REINS_TREATY_PAYMENT_PLAN;
truncate table BBS_CONF_RISK_CLASS;
truncate table BBS_CONF_RISK_MAPPING;
truncate table BBS_CONF_TREATY;
truncate table BBS_CONF_TREATY_CLASS;
truncate table BBS_CONF_TREATY_SECTION;
truncate table BBS_CONF_TREATYHIS_ERR;
truncate table BBS_RISK_CLASS;

truncate table BMS_EXPORT_TRACK_LOG;
truncate table BMS_LOG_OPERATION;
truncate table BPL_ACT_RU_TASK;
truncate table BPL_LOG_ACTION;
TRUNCATE TABLE bpl_log_action_detail;
TRUNCATE table bpl_qrtz_schedule_job_log;
TRUNCATE table bpl_log_pub_task;



--清理轨迹表
truncate table BBS_ACCOUNTHIS;
truncate table BBS_CONF_ACCOUNT_SETHIS;
truncate table BBS_CONF_ACCOUNT_YEAR_MONTH_HIS;
truncate table BBS_CONF_BASE_TREATYHIS;
truncate table BBS_CONF_CURRENCYHIS;
truncate table BBS_CONF_CURRENCYRATEHIS;
truncate table BBS_CONF_FEE_TYPE_MAPPINGHIS;
truncate table BBS_CONF_FLOAT_CHARGEHIS;
truncate table BBS_CONF_LOA_DETAILHIS;
truncate table BBS_CONF_LOA_TICHIS;
truncate table BBS_CONF_LOAHIS;
truncate table BBS_CONF_PRODUCTHIS;
truncate table BBS_CONF_REGULAR_RULEHIS;
truncate table BBS_CONF_REINS_FLOAT_CHARGEHIS;
truncate table BBS_CONF_REINS_REINSURERHIS;
truncate table BBS_CONF_REINS_RISKHIS;
truncate table BBS_CONF_REINS_TREATY_PAYMENT_PLANHIS;
truncate table BBS_CONF_RISK_CLASSHIS;
truncate table BBS_CONF_RISK_MAPPINGHIS;
truncate table BBS_CONF_RISKHIS;
truncate table BBS_CONF_TREATY_CLASSHIS;
truncate table BBS_CONF_TREATY_SECTIONHIS;
truncate table BBS_CONF_TREATYHIS;
truncate table BBS_ENTITYHIS;
truncate table BBS_RISKHIS;
truncate table BMS_CONF_EMAIL_RULEHIS;
truncate table BMS_CONF_EMAIL_TEMPHIS;
truncate table BMS_LOG_OPERATION_HIS;
truncate table BMS_NOTICE_TASKHIS;
truncate table BPL_CONF_CHECKRULEHIS;
truncate table BPL_CONF_CODE_HIS;
truncate table BPL_LOG_ACTION_DETAILHIS;
truncate table BPL_LOG_ACTIONHIS;
truncate table BPL_LOG_PUB_TASK_HIS;
truncate table BPL_QRTZ_SCHEDULE_JOB_LOG_HIS;
truncate table BPL_SAA_USERHIS;

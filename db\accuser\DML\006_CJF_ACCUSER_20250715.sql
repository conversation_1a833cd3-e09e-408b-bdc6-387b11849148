UPDATE acc_conf_checkrule SET rule_expr = 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and EVALUATE_APPROACH IS NULL AND upper(expenses_type_code) not in (''111'',''210'',''211'',''212'',''213'',''214'',''215'',''216'',''217'',''218'',''220'',''221'',''224'',''225'',''411'',''413'',''511'',''512'',''513'',''514'',''515'',''530'',''531'',''536'',''537'',''538'',''539'',''730'',''743'',''830'',''841'',''842'',''843'',''844'',''845'',''910'',''JXS'',''WL'') ' WHERE rule_code = 'RD1740007' ;

UPDATE acc_conf_checkrule SET rule_expr = 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and TREATY_TYPE_CODE IS NULL AND upper(expenses_type_code) not in (''111'',''210'',''211'',''212'',''213'',''214'',''215'',''216'',''217'',''218'',''220'',''221'',''224'',''225'',''411'',''413'',''511'',''512'',''513'',''514'',''515'',''530'',''531'',''536'',''537'',''538'',''539'',''730'',''743'',''830'',''841'',''842'',''843'',''844'',''845'',''910'',''JXS'',''WL'') ' WHERE rule_code = 'RD1741007' ;

UPDATE acc_conf_checkrule SET rule_expr = 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_RI_ACR'') and upper(expenses_type_code) not in (''111'',''210'',''211'',''212'',''213'',''214'',''215'',''216'',''217'',''218'',''220'',''221'',''224'',''225'',''411'',''413'',''511'',''512'',''513'',''514'',''515'',''530'',''531'',''536'',''537'',''538'',''539'',''730'',''743'',''830'',''841'',''842'',''843'',''844'',''845'',''910'',''JXS'',''WL'') and vouitem IS NULL' WHERE rule_code = 'RD1741009';

UPDATE acc_conf_checkrule SET rule_expr = 'SELECT DISTINCT entry_data_id FROM ACCUSER.ACC_dap_entry_data_{entity_id}_{year_month} WHERE  PROC_ID = (select proc_id from bpluser.bms_conf_action_procdef where proc_code=''ACC_ACCOUNTENTRY_CL_ACR'') and upper(expenses_type_code) not in (''111'',''210'',''211'',''212'',''213'',''214'',''215'',''216'',''217'',''218'',''220'',''221'',''224'',''225'',''411'',''413'',''511'',''512'',''513'',''514'',''515'',''530'',''531'',''536'',''537'',''538'',''539'',''730'',''743'',''830'',''841'',''842'',''843'',''844'',''845'',''910'',''JXS'',''WL'') and vouitem IS NULL' WHERE rule_code = 'RD1740008';
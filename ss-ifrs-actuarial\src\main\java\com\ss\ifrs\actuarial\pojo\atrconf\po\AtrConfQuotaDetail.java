/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-11-10 17:08:27
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-10 17:08:27<br/>
 * Description: null<br/>
 * Table Name: bbs_conf_quota_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 * <AUTHOR>
 */
@ApiModel(value = "指标数据明细表")
public class AtrConfQuotaDetail implements Serializable {
    /**
     * Database column: bbs_conf_quota_detail.quota_detail_id
     * Database remarks: quotaDetailId|主键
     */
    @ApiModelProperty(value = "quotaDetailId|主键", required = true)
    private Long quotaDetailId;

    /**
     * Database column: bbs_conf_quota_detail.quota_id
     * Database remarks: quotaId|指标
     */
    @ApiModelProperty(value = "quotaId|指标", required = true)
    private Long quotaId;

    /**
     * Database column: bbs_conf_quota_detail.quota_period
     * Database remarks: quotaPeriod|发展期
     */
    @ApiModelProperty(value = "quotaPeriod|发展期", required = false)
    private Long quotaPeriod;

    /**
     * Database column: bbs_conf_quota_detail.quota_value
     * Database remarks: quotaValue|指标值
     */
    @ApiModelProperty(value = "quotaValue|指标值", required = false)
    private String quotaValue;

    /**
     * Database column: bbs_conf_quota_detail.creator_id
     * Database remarks: creatorId|创建人
     */
    @ApiModelProperty(value = "creatorId|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: bbs_conf_quota_detail.create_time
     * Database remarks: createTime|创建时间
     */
    @ApiModelProperty(value = "createTime|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: bbs_conf_quota_detail.updator_id
     * Database remarks: updatorId|最后修改人
     */
    @ApiModelProperty(value = "updatorId|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: bbs_conf_quota_detail.update_time
     * Database remarks: updateTime|最后修改时间
     */
    @ApiModelProperty(value = "updateTime|最后修改时间", required = false)
    private Date updateTime;

    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Integer serialNo;

    private Long entityId;
    private String riskCode;

    private static final long serialVersionUID = 1L;

    public Long getQuotaDetailId() {
        return quotaDetailId;
    }

    public void setQuotaDetailId(Long quotaDetailId) {
        this.quotaDetailId = quotaDetailId;
    }

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getQuotaPeriod() {
        return quotaPeriod;
    }

    public void setQuotaPeriod(Long quotaPeriod) {
        this.quotaPeriod = quotaPeriod;
    }

    public String getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(String quotaValue) {
        this.quotaValue = quotaValue;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }
}
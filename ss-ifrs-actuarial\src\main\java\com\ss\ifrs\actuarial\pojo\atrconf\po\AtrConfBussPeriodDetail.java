/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-07-26 11:39:39
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-07-26 11:39:39<br/>
 * Description: 计量平台业务年月详情表<br/>
 * Table Name: atr_conf_bussperiod_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "计量平台业务年月详情表")
public class AtrConfBussPeriodDetail implements Serializable {
    /**
     * Database column: atr_conf_bussperiod_detail.period_detail_id
     * Database remarks: period_detail_id|主键id
     */
    @ApiModelProperty(value = "period_detail_id|主键id", required = true)
    private Long periodDetailId;

    /**
     * Database column: atr_conf_bussperiod_detail.buss_period_id
     * Database remarks: buss_period_id|关联业务期间id
     */
    @ApiModelProperty(value = "buss_period_id|关联业务期间id", required = true)
    private Long bussPeriodId;

    /**
     * Database column: atr_conf_bussperiod_detail.biz_type_id
     * Database remarks: biz_type_id|关联业务数据模型id
     */
    @ApiModelProperty(value = "biz_type_id|关联业务数据模型id", required = true)
    private Long bizTypeId;

    /**
     * Database column: atr_conf_bussperiod_detail.biz_code
     * Database remarks: biz_code|任务执行时间
     */
    @ApiModelProperty(value = "biz_code|业务编码标识", required = false)
    private String bizCode;

    /**
     * Database column: atr_conf_bussperiod_detail.exec_result
     * Database remarks: exec_result|执行结果
     */
    @ApiModelProperty(value = "exec_result|执行结果", required = false)
    private String execResult;

    /**
     * Database column: atr_conf_bussperiod_detail.ready_state
     * Database remarks: ready_state|准备状态
     */
    @ApiModelProperty(value = "ready_state|准备状态", required = false)
    private String readyState;

    /**
     * Database column: atr_conf_bussperiod_detail.creator_id
     * Database remarks: creator_id|创建人id
     */
    @ApiModelProperty(value = "creator_id|创建人id", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_bussperiod_detail.create_time
     * Database remarks: create_Time|创建时间
     */
    @ApiModelProperty(value = "create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_bussperiod_detail.updator_id
     * Database remarks: updator_id|最后更新人id
     */
    @ApiModelProperty(value = "updator_id|最后更新人id", required = false)
    private Long updatorId;

    /**
     * Database column: atr_conf_bussperiod_detail.update_time
     * Database remarks: update_Time|最后更新时间
     */
    @ApiModelProperty(value = "update_Time|最后更新时间", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getPeriodDetailId() {
        return periodDetailId;
    }

    public void setPeriodDetailId(Long periodDetailId) {
        this.periodDetailId = periodDetailId;
    }

    public Long getBussPeriodId() {
        return bussPeriodId;
    }

    public void setBussPeriodId(Long bussPeriodId) {
        this.bussPeriodId = bussPeriodId;
    }

    public Long getBizTypeId() {
        return bizTypeId;
    }

    public void setBizTypeId(Long bizTypeId) {
        this.bizTypeId = bizTypeId;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getExecResult() {
        return execResult;
    }

    public void setExecResult(String execResult) {
        this.execResult = execResult;
    }

    public String getReadyState() {
        return readyState;
    }

    public void setReadyState(String readyState) {
        this.readyState = readyState;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
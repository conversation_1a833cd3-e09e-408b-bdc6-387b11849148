call atr_pack_commonutils_proc_add_table_column('atr_dap_ti_payment_plan', 'first_year_month', 'varchar(6)', '首次导入月份');
call atr_pack_commonutils_proc_add_table_column('atr_dap_ti_payment_plan', 'risk_class_code', 'varchar(6)', '险类');

drop table if exists atr_dap_ti_payment_plan;
CREATE TABLE atruser.atr_dap_ti_payment_plan (
  plan_main_id  int8 NOT NULL,
  year_month varchar(6) COLLATE pg_catalog.default NOT NULL,
  entity_id int8 NOT NULL,
  treaty_name varchar(60) COLLATE pg_catalog.default,
  treaty_no varchar(60) COLLATE pg_catalog.default NOT NULL,
  effective_date date,
  expiry_date date,
  risk_class_code varchar(10) COLLATE pg_catalog.default NOT NULL,
  risk_code varchar(32),
  dev_no int4 NOT NULL,
  premium numeric(21,4) NOT NULL,
  draw_time timestamp(0),
  first_year_month varchar(6) COLLATE pg_catalog.default,
  CONSTRAINT uk_atr_dap_ti_payment_plan UNIQUE (plan_main_id, year_month, entity_id, treaty_no, risk_class_code,risk_code, dev_no)
);
ALTER TABLE atruser.atr_dap_ti_payment_plan 
  OWNER TO atruser;
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.plan_main_id IS '主表id';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.year_month IS '业务期间';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.entity_id IS '业务单位ID';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.treaty_name IS '合约的名称';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.treaty_no IS '合约号';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.effective_date IS '合约生效日期';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.expiry_date IS '合约失效日期';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.risk_class_code IS '险类代码';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.dev_no IS '发展期';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.premium IS '缴费金额';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.draw_time IS '提数时间';
COMMENT ON COLUMN atruser.atr_dap_ti_payment_plan.first_year_month IS '首次导入月份';
COMMENT ON TABLE atruser.atr_dap_ti_payment_plan IS '缴费计划接口表 (合约分入) - 数据平台不用处理该表';		


drop table if exists atr_dap_plan_import_main;
CREATE TABLE atruser.atr_dap_plan_import_main (
  plan_main_id int8 NOT NULL,
  entity_id int8,
  year_month varchar(6) COLLATE pg_catalog.default,
  data_type varchar(12) COLLATE pg_catalog.default,
  version_no varchar(32) COLLATE pg_catalog.default,
  confirm_is char(1) COLLATE pg_catalog.default,
  confirm_id int8,
  confirm_time timestamp(6),
  creator_id int8,
  create_time timestamp(6),
  updator_id int8,
  update_time timestamp(6),
  CONSTRAINT atr_dap_plan_import_main_pkey PRIMARY KEY (plan_main_id)
);
ALTER TABLE atruser.atr_dap_plan_import_main 
  OWNER TO atruser;
COMMENT ON COLUMN atruser.atr_dap_plan_import_main.entity_id IS 'entity_id|业务单位id';
call atr_pack_commonutils_proc_add_table_column('atr_dap_ti_payment_plan', 'plan_main_id', 'int8', '导入主表id');
call atr_pack_commonutils_proc_add_table_column('atr_dap_ti_ed_plan', 'plan_main_id', 'int8', '导入主表id');
call atr_pack_commonutils_proc_add_table_column('atr_dap_ti_payment_plan', 'risk_code', 'varchar(32)', '险种');
call atr_pack_commonutils_proc_add_table_column('atr_dap_ti_ed_plan', 'risk_code', 'varchar(32)', '险种');
 
CALL atruser.atr_pack_commonutils_proc_DROP_SEQUENCE('atr_seq_dap_plan_import_main');
create sequence atr_seq_dap_plan_import_main;   
---------- drop ---------------
do language plpgsql
$$
    declare
        rec record;
    begin
        for rec in select t.proname, t.prokind from pg_proc t where t.proname ~ '^atr_pack_lrc_.*'
            loop
                if rec.prokind = 'f' then
                    call atr_pack_commonutils_proc_drop_func(rec.proname::varchar);
                elsif rec.prokind = 'p' then
                    call atr_pack_commonutils_proc_drop_proc(rec.proname::varchar);
                end if;
            end loop;
    end;
$$;
DROP TYPE IF EXISTS atruser.atr_pack_lrc_record_buss;



---------- create ---------------
CREATE TYPE atruser.atr_pack_lrc_record_buss AS (
	action_no character varying(60),
	business_info character varying(1000),
	business_source_code character varying(2),
	entity_id bigint,
	year_month character varying(6),
	ev_date date,
	thread_no smallint
);

CREATE FUNCTION atruser.atr_pack_lrc_func_get_month_days(p_year_month text, p_dev_no bigint) RETURNS bigint
    LANGUAGE sql IMMUTABLE
    AS $$
    -------------------------
    -- 某年月的某个发展期的月份有多少天
    -------------------------
select date_part('day', to_date(p_year_month, 'yyyymm') + (p_dev_no + 1) * '1 month'::interval - '1 day'::interval);
$$;

CREATE FUNCTION atruser.atr_pack_lrc_func_getexchrate(p_entity_id bigint, p_exchdate timestamp without time zone, p_base_currency character varying, p_tgt_currency character varying) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
declare
    v_exchrate numeric(16, 8);
begin
    if p_base_currency = p_tgt_currency then
        return 1;
    end if;

    -- 币别倒过来
    v_exchrate := bpluser.bpl_pack_common_func_getexchrate(p_entity_id,
                                                           p_exchdate,
                                                           p_tgt_currency,
                                                           p_base_currency,
                                                           '2');

    if v_exchrate is null then
        raise exception '兑换日期 % 没有配置 % 兑换 % 的兑换率', to_char(p_exchdate, 'yyyy-mm-dd'), p_tgt_currency,
            p_base_currency using errcode = '-20092';
    end if;

    return v_exchrate;
end;
$$;

CREATE FUNCTION atruser.atr_pack_lrc_func_months_diff(p_start timestamp with time zone, p_end timestamp with time zone) RETURNS numeric
    LANGUAGE sql IMMUTABLE COST 1
    AS $$
    -- select extract(month from age(date_trunc('day', p_end), date_trunc('day', p_start)))
--            + case when extract(day from age(date_trunc('day', p_end), date_trunc('day', p_start))) > 0 then 1 else 0 end;
select ceil(months_between(date_trunc('day', p_end), date_trunc('day', p_start)));
$$;

CREATE FUNCTION atruser.atr_pack_lrc_nvl0(n numeric, de numeric) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
begin
    if n = 0 or n is null then
        return de;
    end if;
    return n;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_debug(IN p_buss atruser.atr_pack_lrc_record_buss, IN p_mark text)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_lrc_proc_debug(p_buss.action_no, p_buss.business_info, p_mark, p_buss.thread_no);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_debug(IN p_action_no text, IN p_mark text)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_lrc_proc_debug(p_action_no, 'null', p_mark, -1);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_debug(IN p_action_no text, IN p_business_info text, IN p_mark text, IN p_thread_no integer)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_ecf_util_proc_log('LRC',
                                    'DEBUG',
                                    p_action_no,
                                    p_business_info,
                                    p_mark,
                                    null,
                                    p_thread_no);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_alter_iacf(IN p_buss atruser.atr_pack_lrc_record_buss)
    LANGUAGE plpgsql
    AS $$
/*
  DD 批改时可能只批改佣金，保费无变化，未了责任的单（终保月 > 评估月） 按原保单的未来期次的应收保费流摊分佣金
 */
declare
    rec record;
begin

    if p_buss.business_source_code <> 'DD' then
        return;
    end if;

    call atr_pack_lrc_proc_debug(p_buss.action_no, 'alter_iacf#start');

    for rec in (select *
                from atr_buss_dd_lrc_icu_calc t
                where t.action_no = p_buss.action_no
                  and t.expiry_date_eom > p_buss.ev_date
                  and t.endorse_seq_no not like '000%'
                  and t.gross_premium = 0
                  and t.commission <> 0)
        loop

            select coalesce(rec.commission / nullif(t.gross_premium, 0), 0)
            into rec.iacf_fee_rate
            from atr_buss_dd_lrc_icu_calc t
            where t.action_no = rec.action_no
              and t.policy_no = rec.policy_no
              and t.endorse_seq_no like '000%';

            if rec.iacf_fee_rate <> 0 then
                merge into atr_buss_dd_lrc_icu_calc_detail t
                using (select rec.id as main_id, d2.dev_no, d2.recv_premium
                       from atr_buss_dd_lrc_icu_calc m2,
                            atr_buss_dd_lrc_icu_calc_detail d2
                       where m2.action_no = rec.action_no
                         and m2.policy_no = rec.policy_no
                         and m2.endorse_seq_no like '000%'
                         and m2.id = d2.main_id
                         and d2.recv_premium <> 0
                         and d2.dev_no > 0) x
                on (t.main_id = x.main_id and t.dev_no = x.dev_no)
                when matched then
                    update set iacf_fee = x.recv_premium * rec.iacf_fee_rate
                when not matched then
                    insert (id, main_id, dev_no, recv_premium, iacf_fee)
                    values (rec.id * 10000 + x.dev_no, rec.id, x.dev_no, 0, x.recv_premium * rec.iacf_fee_rate);

                update atr_buss_dd_lrc_icu_calc t
                set iacf_fee_rate = rec.iacf_fee_rate
                where t.id = rec.id
                  and t.action_no = rec.action_no;
            end if;

        end loop;

    commit;

    call atr_pack_lrc_proc_debug(p_buss.action_no, 'alter_iacf#end');
end
$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_error(IN p_action_no text, IN p_business_info text, IN p_mark text, IN p_msg text)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_ecf_util_proc_log('LRC',
                                    'ERROR',
                                    p_action_no,
                                    p_business_info,
                                    p_mark,
                                    p_msg,
                                    null);
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_collect_quota(IN p_business_source_code text, IN p_action_no text, IN p_year_month text)
    LANGUAGE plpgsql
    AS $$
declare
    ---------------------------------------
    -- 收集这个批次所用到的假设值的值
    ---------------------------------------
    v_err_context text;
    v_err         text;
begin

    call atr_pack_lrc_proc_debug(p_action_no, 'step#收集假设值#start');

    begin
        -- 合同组
        -- 非发展期
        insert into atr_buss_quota_value(id,
                                         action_no,
                                         quota_code,
                                         dimension,
                                         dimension_value,
                                         dev_no,
                                         quota_value)
        with t_main as
                 (select distinct entity_id, loa_code
                  from atr_buss_dd_lrc_icu_calc
                  where p_business_source_code = 'DD'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_fo_lrc_icu_calc
                  where p_business_source_code = 'FO'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_ti_lrc_icu_calc
                  where p_business_source_code = 'TI'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_to_lrc_icu_calc
                  where p_business_source_code = 'TO'
                    and action_no = p_action_no)
        select nextval('atr_seq_buss_quota_value') id,
               p_action_no,
               quota_code,
               dimension,
               dimension_value,
               -1                                  dev_no,
               quota_value::numeric
        from (select d.quota_code,
                     q.dimension,
                     q.dimension_value,
                     q.quota_value
              from atr_buss_quota_def d,
                   atr_conf_quota q
              where d.action_no = p_action_no
                --   and d.dimension = 'G'
                and d.dev_is = '0'
                and d.quota_def_id = q.quota_def_id
                and q.valid_is = '1'
                and q.audit_state = '1'
                and q.year_month = p_year_month
                and q.business_source_code = p_business_source_code
                and exists
                  (select 1
                   from t_main m
                   where m.entity_id = q.entity_id
                     and m.loa_code = q.dimension_value)
              order by quota_code, dimension, dimension_value) alisa4;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lrc_proc_error(p_action_no, p_business_source_code, 'init_quota#error',
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lrc_proc_debug(p_action_no, 'sub#收集假设值-01#start');
    begin
        -- 发展期
        insert into atr_buss_quota_value(id,
                                         action_no,
                                         quota_code,
                                         dimension,
                                         dimension_value,
                                         dev_no,
                                         quota_value)
        with t_main as
                 (select distinct entity_id, loa_code
                  from atr_buss_dd_lrc_icu_calc
                  where p_business_source_code = 'DD'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_fo_lrc_icu_calc
                  where p_business_source_code = 'FO'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_ti_lrc_icu_calc
                  where p_business_source_code = 'TI'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, loa_code
                  from atr_buss_to_lrc_icu_calc
                  where p_business_source_code = 'TO'
                    and action_no = p_action_no)
        select nextval('atr_seq_buss_quota_value') id,
               p_action_no,
               quota_code,
               dimension,
               dimension_value,
               dev_no,
               quota_value::numeric
        from (select d.quota_code,
                     q.dimension,
                     q.dimension_value,
                     dtl.quota_period dev_no,
                     dtl.quota_value
              from atr_buss_quota_def d,
                   atr_conf_quota q,
                   atr_conf_quota_detail dtl
              where d.action_no = p_action_no
                --  and  d.dimension = 'G'
                and d.dev_is = '1'
                and d.quota_def_id = q.quota_def_id
                and q.valid_is = '1'
                and q.audit_state = '1'
                and q.year_month = p_year_month
                and dtl.quota_id = q.quota_id
                and q.business_source_code = p_business_source_code
                and exists
                  (select 1
                   from t_main m
                   where m.entity_id = q.entity_id
                     and m.loa_code = q.dimension_value)
              order by quota_code,
                       dimension,
                       dimension_value,
                       dev_no) alias4;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lrc_proc_error(p_action_no, p_business_source_code, 'init_quota#error',
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lrc_proc_debug(p_action_no, 'sub#收集假设值-02#start');
    begin
        -- 合同组
        -- 非发展期
        insert into atr_buss_quota_value(id,
                                         action_no,
                                         quota_code,
                                         dimension,
                                         dimension_value,
                                         dev_no,
                                         quota_value)
        with t_main as
                 (select distinct entity_id, icg_no
                  from atr_buss_dd_lrc_icu_calc
                  where p_business_source_code = 'DD'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_fo_lrc_icu_calc
                  where p_business_source_code = 'FO'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_ti_lrc_icu_calc
                  where p_business_source_code = 'TI'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_to_lrc_icu_calc
                  where p_business_source_code = 'TO'
                    and action_no = p_action_no)
        select nextval('atr_seq_buss_quota_value') id,
               p_action_no,
               quota_code,
               dimension,
               dimension_value,
               -1                                  dev_no,
               quota_value::numeric
        from (select d.quota_code,
                     q.dimension,
                     q.dimension_value,
                     q.quota_value
              from atr_buss_quota_def d,
                   atr_conf_quota q
              where d.action_no = p_action_no
                and d.dimension = 'C'
                and d.dev_is = '0'
                and d.quota_def_id = q.quota_def_id
                and q.valid_is = '1'
                and q.audit_state = '1'
                and q.year_month = p_year_month
                and q.business_source_code = p_business_source_code
                and exists
                  (select 1
                   from t_main m
                   where m.entity_id = q.entity_id
                     and m.icg_no = q.dimension_value)
              order by quota_code, dimension, dimension_value) alias4;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lrc_proc_error(p_action_no, p_business_source_code, 'init_quota#error',
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lrc_proc_debug(p_action_no, 'sub#收集假设值-03#start');
    begin
        -- 发展期
        insert into atr_buss_quota_value(id,
                                         action_no,
                                         quota_code,
                                         dimension,
                                         dimension_value,
                                         dev_no,
                                         quota_value)
        with t_main as
                 (select distinct entity_id, icg_no
                  from atr_buss_dd_lrc_icu_calc
                  where p_business_source_code = 'DD'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_fo_lrc_icu_calc
                  where p_business_source_code = 'FO'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_ti_lrc_icu_calc
                  where p_business_source_code = 'TI'
                    and action_no = p_action_no
                  union all
                  select distinct entity_id, icg_no
                  from atr_buss_to_lrc_icu_calc
                  where p_business_source_code = 'TO'
                    and action_no = p_action_no)
        select nextval('atr_seq_buss_quota_value') id,
               p_action_no,
               quota_code,
               dimension,
               dimension_value,
               dev_no,
               quota_value::numeric
        from (select d.quota_code,
                     q.dimension,
                     q.dimension_value,
                     dtl.quota_period dev_no,
                     dtl.quota_value
              from atr_buss_quota_def d,
                   atr_conf_quota q,
                   atr_conf_quota_detail dtl
              where d.action_no = p_action_no
                and d.dimension = 'C'
                and d.dev_is = '1'
                and d.quota_def_id = q.quota_def_id
                and q.valid_is = '1'
                and q.audit_state = '1'
                and q.year_month = p_year_month
                and dtl.quota_id = q.quota_id
                and q.business_source_code = p_business_source_code
                and exists
                  (select 1
                   from t_main icu
                   where icu.entity_id = q.entity_id
                     and icu.icg_no = q.dimension_value)
              order by quota_code,
                       dimension,
                       dimension_value,
                       dev_no) alias4;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lrc_proc_error(p_action_no, p_business_source_code, 'init_quota#error',
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lrc_proc_debug(p_action_no, 'sub#收集假设值-04#start');
    begin
        -- 单
        -- 非发展期
        insert into atr_buss_quota_value(id,
                                         action_no,
                                         quota_code,
                                         dimension,
                                         dimension_value,
                                         dev_no,
                                         quota_value)
        select nextval('atr_seq_buss_quota_value') id,
               p_action_no,
               quota_code,
               dimension,
               dimension_value,
               -1                                  dev_no,
               quota_value::numeric
        from (select d.quota_code,
                     q.dimension,
                     q.dimension_value,
                     q.quota_value
              from atr_buss_quota_def d,
                   atr_conf_quota q
              where d.action_no = p_action_no
                and d.dimension = 'U'
                and d.dev_is = '0'
                and d.quota_def_id = q.quota_def_id
                and q.valid_is = '1'
                and q.audit_state = '1'
                and q.year_month = p_year_month
                and q.business_source_code = p_business_source_code
                and exists
                  (select 1
                   from atr_buss_dd_lrc_icu_calc m
                   where p_business_source_code = 'DD'
                     and m.action_no = p_action_no
                     and m.entity_id = q.entity_id
                     and m.policy_no = q.dimension_value
                   union all
                   select 1
                   from atr_buss_fo_lrc_icu_calc m
                   where p_business_source_code = 'FO'
                     and m.action_no = p_action_no
                     and m.entity_id = q.entity_id
                     and m.ri_policy_no = q.dimension_value
                   union all
                   select 1
                   from atr_buss_ti_lrc_icu_calc m
                   where p_business_source_code = 'TI'
                     and m.action_no = p_action_no
                     and m.entity_id = q.entity_id
                     -- and m.ri_statement_no = q.dimension_value
                     and 1 = 2
                   union all
                   select 1
                   from atr_buss_to_lrc_icu_calc m
                   where p_business_source_code = 'TO'
                     and m.action_no = p_action_no
                     and m.entity_id = q.entity_id
                     -- and m.ri_statement_no = q.dimension_value
                     and 1 = 2)
              order by quota_code, dimension, dimension_value) alias4;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lrc_proc_error(p_action_no, p_business_source_code, 'init_quota#error',
                                         v_err_context || '-' || v_err);
            return;
    end;

    call atr_pack_lrc_proc_debug(p_action_no, 'sub#收集假设值-05#start');
    begin
        -- 发展期
        insert into atr_buss_quota_value(id,
                                         action_no,
                                         quota_code,
                                         dimension,
                                         dimension_value,
                                         dev_no,
                                         quota_value)
        select nextval('atr_seq_buss_quota_value') id,
               p_action_no,
               quota_code,
               dimension,
               dimension_value,
               dev_no,
               quota_value::numeric
        from (select d.quota_code,
                     q.dimension,
                     q.dimension_value,
                     dtl.quota_period dev_no,
                     dtl.quota_value
              from atr_buss_quota_def d,
                   atr_conf_quota q,
                   atr_conf_quota_detail dtl
              where d.action_no = p_action_no
                and d.dimension = 'U'
                and d.dev_is = '1'
                and d.quota_def_id = q.quota_def_id
                and q.valid_is = '1'
                and q.audit_state = '1'
                and q.year_month = p_year_month
                and dtl.quota_id = q.quota_id
                and q.business_source_code = p_business_source_code
                and exists
                  (select 1
                   from atr_buss_dd_lrc_icu_calc m
                   where p_business_source_code = 'DD'
                     and m.action_no = p_action_no
                     and m.entity_id = q.entity_id
                     and m.policy_no = q.dimension_value
                   union all
                   select 1
                   from atr_buss_fo_lrc_icu_calc m
                   where p_business_source_code = 'FO'
                     and m.action_no = p_action_no
                     and m.entity_id = q.entity_id
                     and m.ri_policy_no = q.dimension_value
                   union all
                   select 1
                   from atr_buss_ti_lrc_icu_calc m
                   where p_business_source_code = 'TI'
                     and m.action_no = p_action_no
                     and m.entity_id = q.entity_id
                     -- and m.ri_statement_no = q.dimension_value
                     and 1 = 2
                   union all
                   select 1
                   from atr_buss_to_lrc_icu_calc m
                   where p_business_source_code = 'TO'
                     and m.action_no = p_action_no
                     and m.entity_id = q.entity_id
                     -- and m.ri_statement_no = q.dimension_value
                     and 1 = 2)
              order by quota_code,
                       dimension,
                       dimension_value,
                       dev_no) alias4;

    exception
        when others then
            get stacked diagnostics v_err_context = pg_exception_context,
                v_err = message_text;
            call atr_pack_lrc_proc_error(p_action_no, p_business_source_code, 'init_quota#error',
                                         v_err_context || '-' || v_err);
            return;
    end;

    commit;

    call atr_pack_lrc_proc_debug(p_action_no, 'step#收集假设值#end');

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_collect_quota_def(IN p_action_no text)
    LANGUAGE plpgsql
    AS $$
declare

    ---------------------------------------
    -- 收集这个批次所用到的假设值定义
    ---------------------------------------
begin
    call atr_pack_lrc_proc_debug(p_action_no, 'step#收集假设值定义#start');

    begin
        insert into atr_buss_quota_def(id, action_no, quota_code, dimension, dev_is, quota_def_id)
        select nextval('atr_seq_buss_quota_def'), x.*
        from (select p_action_no,
                     quota_code,
                     dimension,
                     dev_is,
                     quota_def_id
              from (select t.quota_code,
                           t.dimension,
                           case when t.quota_type = '1' then '1' else '0' end                         dev_is,
                           t.quota_def_id,
                           row_number() over (partition by t.quota_code order by t.quota_def_id desc) rn
                    from atr_conf_quota_def t
                    where t.valid_is = '1'
                      and t.audit_state = '1'
                      and t.quota_code in ('BE003',
                                           'BE006',
                                           'BE001',
                                           'BE004',
                                           'QR010',
                                           'BE005',
                                           'QA001',
                                           'QR003',
                                           'BE008',
                                           'BE013',
                                           'BE002',
                                           'QR002',
                                           'QP001')) alias5
              where rn = 1
              order by quota_code) x;

    exception
        when others then
            call atr_pack_lrc_proc_error(p_action_no,
                                         '',
                                         'init_quota_def#error',
                                         sqlerrm);

    end;

    commit;

    call atr_pack_lrc_proc_debug(p_action_no, 'step#收集假设值定义#end');
end;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_create_action(IN p_business_source_code text, IN p_entity_id bigint, IN p_year_month text, IN p_currency_code text, IN p_action_no text, IN p_task_code text, IN p_user_id bigint)
    LANGUAGE plpgsql
    AS $$
begin
    call atr_pack_lrc_proc_debug(p_action_no, 'step#创建Action#start');

    begin
        insert into atr_buss_lrc_action(id,
                                        action_no,
                                        task_code,
                                        entity_id,
                                        year_month,
                                        currency_code,
                                        business_source_code,
                                        status,
                                        draw_type,
                                        draw_time,
                                        draw_user,
                                        confirm_is,
                                        creator_id,
                                        create_time,
                                        updator_id,
                                        update_time)
        values (nextval('atr_seq_buss_lrc_action'),
                p_action_no,
                p_task_code,
                p_entity_id,
                p_year_month,
                p_currency_code,
                p_business_source_code,
                'R',
                '1',
                clock_timestamp(),
                p_user_id,
                '0',
                p_user_id,
                clock_timestamp(),
                p_user_id,
                clock_timestamp());
    exception
        when others then
            begin
                call atr_pack_lrc_proc_error(p_action_no,
                                             p_business_source_code,
                                             'step#创建Action#error',
                                             sqlerrm);
            end;
            raise '%', sqlerrm;
    end;

    commit;

    call atr_pack_lrc_proc_debug(p_action_no, 'step#创建Action#end');
end;
$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_update_action(IN p_action_no text, IN p_user_id bigint, IN p_status text)
    LANGUAGE plpgsql
    AS $$
begin

    call atr_pack_lrc_proc_debug(p_action_no, 'step#更新Action#start');

    update atr_buss_lrc_action t
    set status      = p_status,
        updator_id  = coalesce(p_user_id, 1),
        update_time = clock_timestamp()
    where t.action_no = p_action_no;

    call atr_pack_lrc_proc_debug(p_action_no, 'step#更新Action#end');

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_step1(IN p_action_no character varying, IN p_business_source_code character varying, IN p_entity_id bigint, IN p_year_month character varying, IN p_portfolio_no character varying, IN p_user_id bigint)
    LANGUAGE plpgsql
    AS $$
declare
    v_user_id            bigint        := coalesce(p_user_id, 1);
    v_task_code          varchar(36);
    v_business_no        varchar(1000) := 'bt=' || p_business_source_code ||
                                          ',ct=' || p_entity_id || ',ym=' ||
                                          p_year_month || ',pf=' ||
                                          coalesce(p_portfolio_no, '') || ',us=' || coalesce(p_user_id::varchar, '');
    v_base_currency_code varchar(3);
    v_confirmed_count    int;
begin

    set client_min_messages = 'error';

    call atr_pack_lrc_proc_debug(p_action_no, v_business_no, '主程序#start', null);

    -- 检查业务期间  start
    -- 检查当前业务年月的确认情况
    select count(*)
    into v_confirmed_count
    from atr_buss_lrc_action t
    where t.entity_id = p_entity_id
      and t.year_month = p_year_month
      and t.business_source_code = p_business_source_code
      and t.confirm_is = '1';

    if v_confirmed_count = 1 then
        call atr_pack_lrc_proc_update_action(p_action_no, p_user_id, 'S');
        return;
    elsif v_confirmed_count > 1 then
        raise '%',
            'There a duplicate confirmed business year_month: ' ||
            p_year_month;
    end if;

    call atr_pack_ecf_util_proc_check_buss_period('LRC',
                                                  p_entity_id,
                                                  p_business_source_code,
                                                  p_year_month
         );
    -- 检查业务期间  end

    begin
        select t.currency_code
        into v_base_currency_code
        from bpluser.bbs_conf_account_set t
        where t.entity_id = p_entity_id;
    exception
        when no_data_found then
            null;
    end;

    v_task_code := atr_pack_ecf_util_func_create_task_code('LRC',
                                                           p_entity_id,
                                                           p_year_month,
                                                           coalesce(v_base_currency_code, '--'));

    -- create action
    call atr_pack_lrc_proc_create_action(p_business_source_code,
                                         p_entity_id,
                                         p_year_month,
                                         coalesce(v_base_currency_code, '--'),
                                         p_action_no,
                                         v_task_code,
                                         v_user_id);

    if v_base_currency_code is null then
        call atr_pack_lrc_proc_error(p_action_no, null, null, '没有配置本位币');
        raise '%', '没有配置本位币';
    end if;

    -- 检查是否有正在执行
    if exists(select *
              from atr_buss_lrc_action a
              where a.action_no <> p_action_no
                and a.year_month = p_year_month
                and a.status = 'R'
                and a.business_source_code = p_business_source_code) then

        raise '业务 % 在 % 有正在执行的版本', p_business_source_code, p_year_month;
        
    end if;

    -- 收集这个批次所用到的假设值定义
    call atr_pack_lrc_proc_collect_quota_def(p_action_no);

    -- 初始化单维度数据
    call atr_pack_lrc_proc_calc_init_icu(p_business_source_code,
                                         p_entity_id,
                                         p_year_month,
                                         p_portfolio_no,
                                         p_action_no,
                                         v_task_code);

    -- 统一收集假设值
    call atr_pack_lrc_proc_collect_quota(p_business_source_code,
                                         p_action_no, p_year_month);

    -- 收集假设值查询参数
    call atr_pack_lrc_proc_debug(p_action_no, v_business_no, '收集假设值参数#start', -1);
    truncate table atr_temp_ecf_quota_param;
    call atr_pack_ecf_util_proc_collect_quota_param('LRC', 'U', p_action_no);
    call atr_pack_lrc_proc_debug(p_action_no, v_business_no, '收集假设值参数#end', -1);

    -- 清空临时表
    truncate table atr_temp_lrc_icg_dev_coverage_amount;

    call atr_pack_lrc_proc_debug(p_action_no, v_business_no, 'step1#end', -1);
end
$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_step2_threads(IN p_action_no character varying, IN p_threads integer, IN p_thread_no integer)
    LANGUAGE plpgsql
    AS $$
declare
    v_buss          atr_pack_lrc_record_buss;
    v_pre_action_no varchar(60);
    v_temp_buffers  varchar(100);
begin
    set client_min_messages = 'error';


    --     begin
--         set temp_buffers = '128MB';
--     exception
--         when others then
--             call atr_pack_lrc_proc_debug(p_action_no, null, 'set-temp_buffers-error', p_thread_no);
--     end;

    execute 'show temp_buffers' into v_temp_buffers;
    call atr_pack_lrc_proc_debug(p_action_no, v_temp_buffers, 'temp_buffers', p_thread_no);

    select t.action_no,
           t.business_source_code,
           t.business_source_code,
           t.entity_id,
           t.year_month,
           last_day(to_date(t.year_month, 'yyyymm')),
           p_thread_no
    into v_buss.action_no,
        v_buss.business_info,
        v_buss.business_source_code,
        v_buss.entity_id,
        v_buss.year_month,
        v_buss.ev_date,
        v_buss.thread_no
    from atr_buss_lrc_action t
    where t.action_no = p_action_no;

    -- 上一期的 action_no
    begin
        select t.action_no
        into v_pre_action_no
        from atr_buss_lrc_action t
        where t.business_source_code = v_buss.business_source_code
          and t.year_month = atr_pack_ecf_util_func_get_previos_year_month(v_buss.year_month)
          and t.confirm_is = '1';
    exception
        when others then
            null;
    end;

    if v_buss.business_source_code = 'DD' then
        call atr_pack_lrc_proc_calc_icu_dd(v_buss,
                                           v_pre_action_no,
                                           p_threads,
                                           p_thread_no);
    elsif v_buss.business_source_code = 'FO' then
        call atr_pack_lrc_proc_calc_icu_fo(v_buss,
                                           v_pre_action_no,
                                           p_threads,
                                           p_thread_no);
    elsif v_buss.business_source_code = 'TI' then
        call atr_pack_lrc_proc_calc_icu_ti(v_buss,
                                           v_pre_action_no,
                                           p_threads,
                                           p_thread_no);
    elsif v_buss.business_source_code = 'TO' then
        call atr_pack_lrc_proc_calc_icu_to(v_buss,
                                           v_pre_action_no,
                                           p_threads,
                                           p_thread_no);
    end if;
end
$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_sync_qtc_icg(IN p_buss atruser.atr_pack_lrc_record_buss)
    LANGUAGE plpgsql
    AS $$
/******************
 * 同步 qtc icg
 ******************/
begin

    call atr_pack_lrc_proc_debug(p_buss.action_no, 'sync_qtc_icg#start');

    if p_buss.business_source_code = 'DD' then


        insert into atr_buss_dd_lrc_qtc_icg (id, action_no, portfolio_no, icg_no, evaluate_approach, loa_code,
                                             contract_year_month, dev_no, recv_premium, ue_premium, ed_premium,
                                             adj_commission, brokerage_fee, iacf_fee,
                                             iacf_fee_non_policy, maintenance_fee)
        select nextval('atr_seq_buss_dd_lrc_qtc_icg'),
               p_buss.action_no,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               to_char(t.contract_date, 'yyyymm') as contract_year_month,
               d.dev_no,
               sum(d.recv_premium),
               sum(d.ue_premium),
               sum(case
                       when d.dev_no = 0 then
                           t.gross_premium * t.pri_cur_end_remain_csm_rate - d.ue_premium
                       else
                           d.ed_premium
                   end)                           as ed_premium,
               sum(d.adj_commission),
               sum(d.brokerage_fee),
               sum(d.iacf_fee),
               sum(d.iacf_fee_non_policy),
               sum(d.maintenance_fee)
        from atr_buss_dd_lrc_icu_calc t,
             atr_buss_dd_lrc_icu_calc_detail d
        where t.action_no = p_buss.action_no
          and d.action_no = p_buss.action_no
          and t.id = d.main_id
        group by t.portfolio_no, t.icg_no, t.evaluate_approach, t.loa_code,
                 to_char(t.contract_date, 'yyyymm'), d.dev_no;

    elsif p_buss.business_source_code = 'FO' then


        insert into atr_buss_fo_lrc_qtc_icg (id, action_no, portfolio_no, icg_no, evaluate_approach, loa_code,
                                             contract_year_month, dev_no, recv_premium, ue_premium, ed_premium,
                                             adj_commission, brokerage_fee, iacf_fee,
                                             iacf_fee_non_policy, maintenance_fee)
        select nextval('atr_seq_buss_fo_lrc_qtc_icg'),
               p_buss.action_no,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               to_char(t.contract_date, 'yyyymm') as contract_year_month,
               d.dev_no,
               sum(d.recv_premium),
               sum(d.ue_premium),
               sum(case
                       when d.dev_no = 0 then
                           t.gross_premium * t.pri_cur_end_remain_csm_rate - d.ue_premium
                       else
                           d.ed_premium
                   end)                           as ed_premium,
               sum(d.adj_commission),
               sum(d.brokerage_fee),
               sum(d.iacf_fee),
               sum(d.iacf_fee_non_policy),
               sum(d.maintenance_fee)
        from atr_buss_fo_lrc_icu_calc t,
             atr_buss_fo_lrc_icu_calc_detail d
        where t.action_no = p_buss.action_no
          and d.action_no = p_buss.action_no
          and t.id = d.main_id
        group by t.portfolio_no, t.icg_no, t.evaluate_approach, t.loa_code,
                 to_char(t.contract_date, 'yyyymm'), d.dev_no;

    end if;

    call atr_pack_lrc_proc_debug(p_buss.action_no, 'sync_qtc_icg#end');

end;
$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_treaty_reverse(IN p_buss atruser.atr_pack_lrc_record_buss)
    LANGUAGE plpgsql
    AS $$
-- 合约冲销
declare
    v_count int := 0;
    rec     record;
begin

    if p_buss.business_source_code = 'TI' then
        for rec in (select *
                    from atr_buss_ti_lrc_icu_calc
                    where action_no = p_buss.action_no
                      and treaty_no like '%#-1'
                    order by id)
            loop
                v_count := v_count + 1;

                update atr_buss_ti_lrc_icu_calc t
                set (recv_premium,
                     ed_premium_backup,
                     ed_premium,
                     loan_os,
                     delinquency_rate,
                     default_rate,
                     afnp_loan_os) =
                        (select -m.recv_premium,
                                -m.ed_premium_backup,
                                -m.ed_premium,
                                -m.loan_os,
                                -m.delinquency_rate,
                                -m.default_rate,
                                -m.afnp_loan_os
                         from atr_buss_ti_lrc_icu_calc m
                         where m.id = rec.reversed_id)
                where t.id = rec.id;

                update atr_buss_ti_lrc_icu_calc_detail t
                set (recv_premium,
                     ed_premium_backup,
                     ed_premium,
                     adj_commission,
                     brokerage_fee,
                     iacf_fee,
                     maintenance_fee,
                     iacf_fee_non_policy,
                     rif_run_off_pattern_rate,
                     ultimate_loss_mortgage) =
                        (select -d.recv_premium,
                                -d.ed_premium_backup,
                                -d.ed_premium,
                                -d.adj_commission,
                                -d.brokerage_fee,
                                -d.iacf_fee,
                                -d.maintenance_fee,
                                -d.iacf_fee_non_policy,
                                -d.rif_run_off_pattern_rate,
                                -d.ultimate_loss_mortgage
                         from atr_buss_ti_lrc_icu_calc_detail d
                         where d.main_id = rec.reversed_id
                           and d.dev_no = t.dev_no)
                where t.main_id = rec.id;

                if mod(v_count, 100) = 0 then
                    commit;
                end if;
            end loop;

        commit;

    elsif p_buss.business_source_code = 'TO' then

        for rec in (select *
                    from atr_buss_to_lrc_icu_calc
                    where action_no = p_buss.action_no
                      and treaty_no like '%#-1'
                    order by id)
            loop
                v_count := v_count + 1;

                update atr_buss_to_lrc_icu_calc t
                set (recv_premium,
                     ed_premium_backup,
                     ed_premium,
                     loan_os,
                     delinquency_rate,
                     default_rate,
                     afnp_loan_os) =
                        (select -m.recv_premium,
                                -m.ed_premium_backup,
                                -m.ed_premium,
                                -m.loan_os,
                                -m.delinquency_rate,
                                -m.default_rate,
                                -m.afnp_loan_os
                         from atr_buss_to_lrc_icu_calc m
                         where m.id = rec.reversed_id)
                where t.id = rec.id;

                update atr_buss_to_lrc_icu_calc_detail t
                set (recv_premium,
                     ed_premium_backup,
                     ed_premium,
                     adj_commission,
                     brokerage_fee,
                     maintenance_fee,
                     rif_run_off_pattern_rate,
                     ultimate_loss_mortgage) =
                        (select -d.recv_premium,
                                -d.ed_premium_backup,
                                -d.ed_premium,
                                -d.adj_commission,
                                -d.brokerage_fee,
                                -d.maintenance_fee,
                                -d.rif_run_off_pattern_rate,
                                -d.ultimate_loss_mortgage
                         from atr_buss_to_lrc_icu_calc_detail d
                         where d.main_id = rec.reversed_id
                           and d.dev_no = t.dev_no)
                where t.main_id = rec.id;

                if mod(v_count, 100) = 0 then
                    commit;
                end if;
            end loop;

        commit;
    end if;

end;
$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_step3(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    v_buss          atr_pack_lrc_record_buss;
    v_currency_code varchar(3);
    v_user_id       bigint;
    v_error_count   int;
begin

    set client_min_messages = 'error';

    select t.action_no,
           t.business_source_code,
           t.business_source_code,
           t.entity_id,
           t.year_month,
           last_day(to_date(t.year_month, 'yyyymm')),
           -1 thread_no,
           t.currency_code,
           t.creator_id
    into v_buss.action_no,
        v_buss.business_info,
        v_buss.business_source_code,
        v_buss.entity_id,
        v_buss.year_month,
        v_buss.ev_date,
        v_buss.thread_no,
        v_currency_code,
        v_user_id
    from atr_buss_lrc_action t
    where t.action_no = p_action_no;

    -- 调整预期 iacf 
    call atr_pack_lrc_proc_alter_iacf(v_buss);

    -- 合约冲销
    call atr_pack_lrc_proc_treaty_reverse(v_buss);

    -- 初始化合同组维度
    call atr_pack_lrc_proc_calc_init_icg(v_buss);

    -- 计算合同组
    call atr_pack_lrc_proc_calc_icg(v_buss);

    -- 同步 qtc icg
    call atr_pack_lrc_proc_sync_qtc_icg(v_buss);

    -- 检查异常
    select count(*)
    into v_error_count
    from atr_log_lrc_trace t
    where t.action_no = p_action_no
      and t.log_type = 'ERROR'
    limit 1;
    if v_error_count > 0 then
        raise 'An exception occurs during program running. The action no. is  %', p_action_no;
    end if;

    call atr_pack_lrc_proc_update_action(p_action_no, v_user_id, 'S');

    call atr_pack_lrc_proc_debug(p_action_no, null, '主程序#end', -1);
end
$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_step4_clean(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
begin
    null;
end
$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc(IN p_business_source_code character varying, IN p_entity_id bigint, IN p_year_month character varying, IN p_currency_code character varying, IN p_portfolio_no character varying, IN p_user_id bigint)
    LANGUAGE plpgsql
    AS $$
declare
    -- Purpose : LRC 计算入口
    -- param p_business_source_code: 业务类型， DD-直保&临分、FO-临分分出、TI-合约分入、TO-合约分出; 为空时计算所有业务
    -- param p_year_month: 必录，业务期间的年月
    -- param p_currency_code: 非必录
    -- param p_portfolio_no：非必录
    -- param p_user_id: 非必录， 操作用户， 默认为 1
    v_action_no varchar(36) := atr_pack_ecf_util_func_create_action_no();
begin

    call atr_pack_lrc_proc_calc_step1(v_action_no, p_business_source_code,
                                      p_entity_id, p_year_month,
                                      p_portfolio_no, p_user_id);

    call atr_pack_lrc_proc_calc_step2_threads(v_action_no, 1, 0);

    call atr_pack_lrc_proc_calc_step3(v_action_no);

    call atr_pack_lrc_proc_calc_step4_clean(v_action_no);

end ;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_icg(IN p_buss atruser.atr_pack_lrc_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    -----------------
    -- 计算合同组
    -----------------
begin
    call atr_pack_lrc_proc_debug(p_buss.action_no, 'step#计算合同组#start');

    if p_buss.business_source_code = 'DD' then
        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-01#start');

        -- 合同组的保额&限额
        merge into atr_buss_dd_lrc_icg_calc_detail t
        using (select m.id                   as main_id,
                      i.dev_no,
                      sum(i.coverage_amount) as coverage_amount
               from atr_buss_dd_lrc_icg_calc m,
                    atr_temp_lrc_icg_dev_coverage_amount i
               where m.action_no = p_buss.action_no
                 and m.action_no = i.action_no
                 and m.portfolio_no = i.portfolio_no
                 and m.icg_no = i.icg_no
               group by m.id, i.dev_no) x
        on (t.main_id = x.main_id and t.dev_no = x.dev_no)
        when matched then
            update set coverage_amount = x.coverage_amount;

        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-02#start');

        -- 摊销比例
        merge into atr_buss_dd_lrc_icg_calc_detail t
        using (select gm.id as main_id, sum(gd.coverage_amount) as coverage_amount
               from atr_buss_dd_lrc_icg_calc_detail gd,
                    atr_buss_dd_lrc_icg_calc gm
               where gm.id = gd.main_id
                 and gm.action_no = p_buss.action_no
               group by gm.id) x
        on (t.main_id = x.main_id)
        when matched then
            update
            set csm_rate = (case
                                when x.coverage_amount = 0 then
                                    0
                                else
                                    coalesce(t.coverage_amount / x.coverage_amount::numeric, 0)
                end);

        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-03#start');

        -- 尾差处理， 放在最后一条不为 0 的发展期
        merge into atr_buss_dd_lrc_icg_calc_detail t
        using (select *
               from (select d.id,
                            sum(d.csm_rate) over (partition by m.id) csm_rate_sum,
                            row_number() over (partition by m.id order by (case
                                                                               when d.csm_rate = 0 then
                                                                                   0
                                                                               else
                                                                                   1
                                end) desc, d.dev_no desc)            rn
                     from atr_buss_dd_lrc_icg_calc m,
                          atr_buss_dd_lrc_icg_calc_detail d
                     where m.action_no = p_buss.action_no
                       and m.id = d.main_id) alias4
               where rn = 1
                 and csm_rate_sum <> 0) x
        on (t.id = x.id and t.dev_no > 0)
        when matched then
            update set csm_rate = t.csm_rate + 1 - x.csm_rate_sum;

        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-XX#end');

    elsif p_buss.business_source_code = 'FO' then

        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-01#start');

        -- 合同组的保额&限额
        merge into atr_buss_fo_lrc_icg_calc_detail t
        using (select m.id                   as main_id,
                      i.dev_no,
                      sum(i.coverage_amount) as coverage_amount
               from atr_buss_fo_lrc_icg_calc m,
                    atr_temp_lrc_icg_dev_coverage_amount i
               where m.action_no = p_buss.action_no
                 and m.action_no = i.action_no
                 and m.portfolio_no = i.portfolio_no
                 and m.icg_no = i.icg_no
               group by m.id, i.dev_no) x
        on (t.main_id = x.main_id and t.dev_no = x.dev_no)
        when matched then
            update set coverage_amount = x.coverage_amount;

        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-02#start');

        -- 摊销比例
        merge into atr_buss_fo_lrc_icg_calc_detail t
        using (select gm.id as main_id, sum(gd.coverage_amount) as coverage_amount
               from atr_buss_fo_lrc_icg_calc_detail gd,
                    atr_buss_fo_lrc_icg_calc gm
               where gm.id = gd.main_id
                 and gm.action_no = p_buss.action_no
               group by gm.id) x
        on (t.main_id = x.main_id)
        when matched then
            update
            set csm_rate = (case
                                when x.coverage_amount = 0 then
                                    0
                                else
                                    coalesce(t.coverage_amount / x.coverage_amount::numeric, 0)
                end);

        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-03#start');

        -- 尾差处理， 放在最后一条不为 0 的发展期
        merge into atr_buss_fo_lrc_icg_calc_detail t
        using (select *
               from (select d.id,
                            sum(d.csm_rate) over (partition by m.id) csm_rate_sum,
                            row_number() over (partition by m.id order by (case
                                                                               when d.csm_rate = 0 then
                                                                                   0
                                                                               else
                                                                                   1
                                end) desc, d.dev_no desc)            rn
                     from atr_buss_fo_lrc_icg_calc m,
                          atr_buss_fo_lrc_icg_calc_detail d
                     where m.action_no = p_buss.action_no
                       and m.id = d.main_id) alias4
               where rn = 1
                 and csm_rate_sum <> 0) x
        on (t.id = x.id and t.dev_no > 0)
        when matched then
            update set csm_rate = t.csm_rate + 1 - x.csm_rate_sum;

        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-XX#end');

    elsif p_buss.business_source_code = 'TI' then

        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-00#start');

        -- 合同组的保额&限额
        merge into atr_buss_ti_lrc_icg_calc_detail t
        using (select gm.id,
                      ud.dev_no,
                      sum(ud.coverage_amount) as coverage_amount
               from atr_buss_ti_lrc_icu_calc um,
                    atr_buss_ti_lrc_icu_calc_detail ud,
                    atr_buss_ti_lrc_icg_calc gm
               where um.id = ud.main_id
                 and um.action_no = gm.action_no
                 and um.entity_id = gm.entity_id
                 and um.currency_code = gm.currency_code
                 and um.portfolio_no = gm.portfolio_no
                 and um.icg_no = gm.icg_no
                 and gm.action_no = p_buss.action_no
               group by gm.id, ud.dev_no) x
        on (t.main_id = x.id and t.dev_no = x.dev_no)
        when matched then
            update set coverage_amount = x.coverage_amount;

        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-01#start');

        -- 摊销比例
        merge into atr_buss_ti_lrc_icg_calc_detail t
        using (select gm.id as main_id, sum(gd.coverage_amount) as coverage_amount
               from atr_buss_ti_lrc_icg_calc_detail gd,
                    atr_buss_ti_lrc_icg_calc gm
               where gm.id = gd.main_id
                 and gm.action_no = p_buss.action_no
               group by gm.id) x
        on (t.main_id = x.main_id)
        when matched then
            update
            set csm_rate = (case
                                when x.coverage_amount = 0 then
                                    0
                                else
                                    coalesce(t.coverage_amount / x.coverage_amount::numeric, 0)
                end);


        -- 尾差处理， 放在最后一条不为 0 的发展期
        merge into atr_buss_ti_lrc_icg_calc_detail t
        using (select *
               from (select d.id,
                            sum(d.csm_rate) over (partition by m.id) csm_rate_sum,
                            row_number() over (partition by m.id order by (case
                                                                               when d.csm_rate = 0 then
                                                                                   0
                                                                               else
                                                                                   1
                                end) desc, d.dev_no desc)            rn
                     from atr_buss_ti_lrc_icg_calc m,
                          atr_buss_ti_lrc_icg_calc_detail d
                     where m.action_no = p_buss.action_no
                       and m.id = d.main_id) alias4
               where rn = 1
                 and csm_rate_sum <> 0) x
        on (t.id = x.id and t.dev_no > 0)
        when matched then
            update set csm_rate = t.csm_rate + 1 - x.csm_rate_sum;

        call atr_pack_lrc_proc_debug(p_buss.action_no, 'sub#计算合同组-XX#end');

    elsif p_buss.business_source_code = 'TO' then
        /** 超赔合约 + 比例合约分入转合约分出 */
        -- 合同组的保额&限额
        merge into atr_buss_to_lrc_icg_calc_detail t
        using (select gm.id,
                      ud.dev_no,
                      gm.main_treaty_type,
                      sum(ud.coverage_amount) as coverage_amount
               from atr_buss_to_lrc_icu_calc um,
                    atr_buss_to_lrc_icu_calc_detail ud,
                    atr_buss_to_lrc_icg_calc gm
               where um.id = ud.main_id
                 and um.action_no = gm.action_no
                 and um.entity_id = gm.entity_id
                 and um.currency_code = gm.currency_code
                 and um.portfolio_no = gm.portfolio_no
                 and um.icg_no = gm.icg_no
                 and gm.action_no = p_buss.action_no
                 and gm.main_treaty_type in ('X', 'R')
               group by gm.id, ud.dev_no, gm.main_treaty_type) x
        on (t.main_id = x.id and t.dev_no = x.dev_no)
        when matched then
            update set coverage_amount = x.coverage_amount;


        -- 摊销比例
        merge into atr_buss_to_lrc_icg_calc_detail t
        using (select gm.id as main_id, gm.main_treaty_type, sum(gd.coverage_amount) as coverage_amount
               from atr_buss_to_lrc_icg_calc_detail gd,
                    atr_buss_to_lrc_icg_calc gm
               where gm.id = gd.main_id
                 and gm.main_treaty_type in ('X', 'R')
                 and gm.action_no = p_buss.action_no
               group by gm.id, gm.main_treaty_type) x
        on (t.main_id = x.main_id)
        when matched then
            update
            set csm_rate = (case
                                when x.coverage_amount = 0 then
                                    0
                                else
                                    coalesce(t.coverage_amount / x.coverage_amount::numeric, 0)
                end);

        /** 比例合约  */
        -- 合同组的已赚保费
        merge into atr_buss_to_lrc_icg_calc_detail t
        using (select gm.id, ud.dev_no, coalesce(sum(ud.ed_premium), 0) as ed_premium
               from atr_buss_to_lrc_icu_calc um,
                    atr_buss_to_lrc_icu_calc_detail ud,
                    atr_buss_to_lrc_icg_calc gm
               where um.id = ud.main_id
                 and um.action_no = gm.action_no
                 and um.entity_id = gm.entity_id
                 and um.currency_code = gm.currency_code
                 and um.portfolio_no = gm.portfolio_no
                 and um.icg_no = gm.icg_no
                 and gm.action_no = p_buss.action_no
                 and gm.main_treaty_type = 'T'
               group by gm.id, ud.dev_no) x
        on (t.main_id = x.id and t.dev_no = x.dev_no)
        when matched then
            update set ed_premium = x.ed_premium;


        -- 摊销比例
        merge into atr_buss_to_lrc_icg_calc_detail t
        using (select gm.id main_id, gm.ue_premium
               from atr_buss_to_lrc_icg_calc gm
               where gm.main_treaty_type = 'T'
                 and gm.action_no = p_buss.action_no) x
        on (t.main_id = x.main_id)
        when matched then
            update
            set csm_rate = (case
                                when x.ue_premium = 0 then
                                    0
                                else
                                    coalesce(t.ed_premium / x.ue_premium::numeric, 0)
                end);

        /** all */
        -- 尾差处理， 放在最后一条不为 0 的发展期
        merge into atr_buss_to_lrc_icg_calc_detail t
        using (select *
               from (select d.id,
                            sum(d.csm_rate) over (partition by m.id) csm_rate_sum,
                            row_number() over (partition by m.id order by (case
                                                                               when d.csm_rate = 0 then
                                                                                   0
                                                                               else
                                                                                   1
                                end) desc, d.dev_no desc)            rn
                     from atr_buss_to_lrc_icg_calc m,
                          atr_buss_to_lrc_icg_calc_detail d
                     where m.action_no = p_buss.action_no
                       and m.id = d.main_id) alias4
               where rn = 1
                 and csm_rate_sum <> 0) x
        on (t.id = x.id and t.dev_no > 0)
        when matched then
            update set csm_rate = t.csm_rate + 1 - x.csm_rate_sum;

    end if;

    call atr_pack_lrc_proc_debug(p_buss.action_no, 'step#计算合同组#end');

    commit;

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_icu_dd(IN p_buss atruser.atr_pack_lrc_record_buss, IN p_pre_action_no character varying, IN p_threads integer, IN p_thread_no integer)
    LANGUAGE plpgsql
    AS $$
declare
    v_real_premium          atr_buss_dd_lrc_icu_calc.gross_premium%type;
    v_ev_date_bom           date        := to_date(p_buss.year_month, 'yyyymm');
    v_cur_month             numeric(2)  := substr(p_buss.year_month, 5, 2);
    v_count                 numeric(18) := 0;
    v_max_dev_no            smallint;
    rec                     record;
    v_cost_start            timestamp(6);
    v_cost                  numeric(19, 6)[];
    v_cost_str              varchar(1000);
    v_non_loa_quota_exitsed boolean;
    v_dist_min_dev_no       smallint;
    v_dist_sum_recv_premium atr_buss_dd_lrc_icu_calc.gross_premium%type;
begin
    v_non_loa_quota_exitsed := exists(select *
                                      from atr_buss_quota_value t
                                      where t.action_no = p_buss.action_no
                                        and length(t.dimension_value) > 3);

    -- 应收保费试算表  start
    drop table if exists tmp_dd_recv_premium_dist;
    drop table if exists tmp_dd_recv_premium_dist2;

    create temp table tmp_dd_recv_premium_dist
    (
        policy_no      varchar(100),
        endorse_seq_no varchar(20),
        dev_no         smallint,
        recv_premium   decimal(32, 8)
    );

    create temp table tmp_dd_recv_premium_dist2 as
    select * from tmp_dd_recv_premium_dist where false;

    create index idx_tmp_dd_recv_premium_dist on tmp_dd_recv_premium_dist (policy_no, endorse_seq_no);
    create index idx_tmp_dd_recv_premium_dist2 on tmp_dd_recv_premium_dist2 (policy_no, endorse_seq_no);
    -- 应收保费试算表  end

    /*  icg coverage_amount 临时表 start  */
    drop table if exists tmp_atr_temp_lrc_icg_dev_coverage_amount;

    create temp table tmp_atr_temp_lrc_icg_dev_coverage_amount
    as
    select portfolio_no,
           icg_no,
           dev_no,
           evaluate_approach,
           loa_code,
           coverage_amount
    from atr_temp_lrc_icg_dev_coverage_amount
    where false;
    /*  icg coverage_amount 临时表 end  */

    /*  dev 临时表  start */
    drop table if exists tmp_atr_buss_dd_lrc_icu_calc_detail;

    create temp table tmp_atr_buss_dd_lrc_icu_calc_detail
    as
    select * from atr_buss_dd_lrc_icu_calc_detail where false;

--     create index idx_tmp_atr_buss_dd_lrc_icu_calc_detail_id on tmp_atr_buss_dd_lrc_icu_calc_detail (id);
    create index idx_tmp_atr_buss_dd_lrc_icu_calc_detail on tmp_atr_buss_dd_lrc_icu_calc_detail (main_id, dev_no);
    /*  dev 临时表  end */

    /*  main start */
    drop table if exists tmp_lrc_icu_main;

    create temp table tmp_lrc_icu_main as
    select id,
           dev_val,
           passed_dates,
           passed_months,
           remaining_months,
           future_months,
           dap_year_month,
           total_days_pol_effective,
           passed_days_pol_effective,
           adj_commission_rate,
           brokerage_fee_rate,
           iacf_fee_rate,
           iacf_fee_rate_non_policy,
           maintenance_fee_rate,
           remaining_months_for_recv,
           remaining_prem_term_pe,
           remaining_months_future,
           remaining_prem_term_cb,
           payment_quarter,
           cumulative_paid_premium,
           recv_premium,
           ed_premium_per_coverage_day,
           ed_premium_backup,
           ed_premium,
           coverage_months,
           pri_cur_end_remain_csm_rate,
           pri_until_report_remain_csm_rate,
           cumulative_ed_rate,
           cur_end_remain_csm_rate,
           until_report_remain_csm_rate,
           elr
    from atr_buss_dd_lrc_icu_calc
    where false;
    /*  main end   */

    for rec in (select t.*, date_trunc('month', t.expiry_date_in_date) expiry_date_bom
                from atr_buss_dd_lrc_icu_calc t
                where action_no = p_buss.action_no
                  and mod(id, p_threads) = p_thread_no)
        loop
            v_count := v_count + 1;

            v_cost_start := clock_timestamp();
            /* ** common  ***/
            if v_non_loa_quota_exitsed then
                -- elr
                rec.elr := atr_pack_ecf_util_func_get_quota_value('BE013',
                                                                  p_buss.action_no,
                                                                  rec.id,
                                                                  'U',
                                                                  -1,
                                                                  null,
                                                                  null);

                -- 发展期变量
                rec.dev_val := atr_pack_ecf_util_func_get_quota_value('BE008',
                                                                      p_buss.action_no,
                                                                      rec.id,
                                                                      'U',
                                                                      -1,
                                                                      null,
                                                                      null);

                -- 调整手续费比例
                rec.adj_commission_rate := atr_pack_ecf_util_func_get_quota_value('QR010',
                                                                                  p_buss.action_no,
                                                                                  rec.id,
                                                                                  'U',
                                                                                  -1,
                                                                                  null,
                                                                                  null);

                -- 经纪人费用比例
                rec.brokerage_fee_rate := atr_pack_ecf_util_func_get_quota_value('BE005',
                                                                                 p_buss.action_no,
                                                                                 rec.id,
                                                                                 'U',
                                                                                 -1,
                                                                                 null,
                                                                                 null);

                -- 非跟单获取费用比例
                rec.iacf_fee_rate_non_policy := atr_pack_ecf_util_func_get_quota_value('QR002',
                                                                                       p_buss.action_no,
                                                                                       rec.id,
                                                                                       'U',
                                                                                       -1,
                                                                                       null,
                                                                                       null);

                -- 维持费用比例
                rec.maintenance_fee_rate := atr_pack_ecf_util_func_get_quota_value('QR003',
                                                                                   p_buss.action_no,
                                                                                   rec.id,
                                                                                   'U',
                                                                                   -1,
                                                                                   null,
                                                                                   null);
            else

                -- elr
                select t.quota_value
                into rec.elr
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'BE013'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

                -- 发展期变量
                select t.quota_value
                into rec.dev_val
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'BE008'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

                -- 调整手续费比例
                select t.quota_value
                into rec.adj_commission_rate
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'QR010'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

                -- 经纪人费用比例
                select t.quota_value
                into rec.brokerage_fee_rate
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'BE005'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

                -- 非跟单获取费用比例
                select t.quota_value
                into rec.iacf_fee_rate_non_policy
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'QR002'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

                -- 维持费用比例
                select t.quota_value
                into rec.maintenance_fee_rate
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'QR003'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

            end if;

            -- 获取费用比例
            rec.iacf_fee_rate := rec.commission_rate;

            v_cost[22] := coalesce(v_cost[22], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 累计已收保费
            select p.premium
            into rec.cumulative_paid_premium
            from atr_temp_dd_premium_paid_lte p
            where p.policy_no = rec.policy_no
              and p.endorse_seq_no = rec.endorse_seq_no;

            rec.cumulative_paid_premium := coalesce(rec.cumulative_paid_premium, 0);

            v_cost[23] := coalesce(v_cost[23], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 承保天数 （起保日期-终保日期）
            rec.total_days_pol_effective := case
                                                when rec.effective_date_in_date <=
                                                     rec.expiry_date_in_date then
                                                    cast(rec.expiry_date_in_date as date) -
                                                    cast(rec.effective_date_in_date as date) + 1
                end;

            -- 起保-评估期天数
            rec.passed_days_pol_effective := case
                                                 when rec.effective_date_in_date >
                                                      rec.evaluate_date then
                                                     0
                                                 when rec.expiry_date_in_date >
                                                      rec.evaluate_date then
                                                     cast(rec.evaluate_date as date) -
                                                     cast(rec.effective_date_in_date as date) + 1
                                                 when rec.effective_date_in_date <=
                                                      rec.expiry_date_in_date then
                                                     cast(rec.expiry_date_in_date as date) -
                                                     cast(rec.effective_date_in_date as date) + 1
                end;

            -- 公共项/已承保天数
            rec.passed_dates := (case
                                     when rec.evaluate_date < rec.effective_date_bom then
                                         0
                                     else
                                         rec.evaluate_date - rec.effective_date_in_date
                end);

            -- 公共项/已过月份
            rec.passed_months := (case
                                      when rec.effective_date_bom > rec.evaluate_date then
                                          0
                                      when extract(day from rec.effective_date_in_date) = 1 then
                                          extract(year from age(v_ev_date_bom, rec.effective_date_bom)) * 12
                                              + extract(month from age(v_ev_date_bom, rec.effective_date_bom))
                                      else
                                          extract(year from age(v_ev_date_bom, rec.effective_date_bom)) * 12
                                              + extract(month from age(v_ev_date_bom, rec.effective_date_bom)) - 1
                end);

            -- 公共项/未到期月份
            rec.remaining_months := extract(year from age(rec.expiry_date_bom, v_ev_date_bom)) * 12
                + extract(month from age(rec.expiry_date_bom, v_ev_date_bom));

            -- 还有几个月起保
            rec.future_months := greatest(extract(year from age(rec.effective_date_bom, v_ev_date_bom)) * 12
                                              + extract(month from age(rec.effective_date_bom, v_ev_date_bom)), 0);


            v_cost[21] := coalesce(v_cost[21], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            select greatest(rec.remaining_months, 1, months_between((select max(p.est_payment_date)
                                                                     from atr_temp_dd_payment_plan p
                                                                     where p.policy_no = rec.policy_no
                                                                       and p.endorse_seq_no = rec.endorse_seq_no),
                                                                    date_trunc('month', p_buss.ev_date)))
            into
                v_max_dev_no;

            v_cost[31] := coalesce(v_cost[31], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 初始化明细表
            insert into tmp_atr_buss_dd_lrc_icu_calc_detail
                (id, action_no, main_id, dev_no)
            select rec.id * 10000 + d.dev_no as id,
                   p_buss.action_no,
                   rec.id                    as main_id,
                   d.dev_no
            from atr_conf_dev_no d
            where d.dev_no >= 0
              and d.dev_no <= v_max_dev_no
            order by d.dev_no;


            v_cost[20] := coalesce(v_cost[20], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- after_premium_impairment_rate
            update tmp_atr_buss_dd_lrc_icu_calc_detail t
            set after_premium_impairment_rate =
                    (select case when q.quota_value = 0 then null else q.quota_value end
                     from atr_buss_quota_value q
                     where q.action_no = p_buss.action_no
                       and q.quota_code = 'BE001'
                       and q.dimension = 'G'
                       and q.dimension_value = rec.loa_code
                       and q.dev_no = t.dev_no)
            where t.main_id = rec.id;


            v_cost[1] := coalesce(v_cost[1], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** recv premvium  ***/
            -- 未到期月份(应收保费计算专用)
            rec.remaining_months_for_recv := (case
                                                  when rec.expiry_date_eom <=
                                                       rec.evaluate_date then
                                                      0
                                                  else
                                                      extract(year from age(rec.expiry_date_bom, v_ev_date_bom)) * 12 +
                                                      extract(month from age(rec.expiry_date_bom, v_ev_date_bom))
                end);

            -- 剩余未交费期次(评估日期-终保日期)
            rec.remaining_prem_term_pe := (case
                                               when rec.payment_frequency_code = 'S' then
                                                   0
                                               when rec.payment_frequency_code = 'M' then
                                                   (case
                                                        when extract(day from
                                                                     rec.effective_date_in_date) = 1 then
                                                            greatest(rec.payment_frequency_no -
                                                                     rec.passed_months,
                                                                     0)
                                                        else
                                                            greatest(greatest(rec.payment_frequency_no -
                                                                              rec.passed_months,
                                                                              0) - 1,
                                                                     0)
                                                       end)
                                               when rec.payment_frequency_code = 'Q' then
                                                   (case
                                                        when extract(day from
                                                                     rec.effective_date_in_date) = 1 then
                                                            greatest(rec.payment_frequency_no -
                                                                     trunc((rec.passed_months - 1) / 3) - 1,
                                                                     0)
                                                        else
                                                            greatest(rec.payment_frequency_no -
                                                                     trunc(rec.passed_months / 3) - 1,
                                                                     0)
                                                       end)
                                               when rec.payment_frequency_code = 'Y' then
                                                   (case
                                                        when extract(day from
                                                                     rec.effective_date_in_date) = 1 then
                                                            greatest(rec.payment_frequency_no - (1 +
                                                                                                 trunc((rec.passed_months - 1) / 12)) -
                                                                     1,
                                                                     0)
                                                        else
                                                            greatest(rec.payment_frequency_no - (1 +
                                                                                                 trunc(rec.passed_months / 12)) -
                                                                     1,
                                                                     0)
                                                       end)
                                               else
                                                   0
                end);

            -- 应收保费
            rec.recv_premium := rec.gross_premium - rec.cumulative_paid_premium;

            -- 未到期月份(间隔第一期未来年度预付时间)
            rec.remaining_months_future := (case
                                                when rec.payment_frequency_code in
                                                     ('S', 'M', 'Q') or
                                                     coalesce(rec.remaining_prem_term_pe, 0) = 0 then
                                                    null
                                                when rec.effective_date_bom <= v_ev_date_bom then
                                                    least(
                                                            extract(year from age(rec.effective_date_bom, v_ev_date_bom)) *
                                                            12 +
                                                            extract(month from age(rec.effective_date_bom, v_ev_date_bom)) +
                                                            12 - 1,
                                                            0)
                                                else
                                                    extract(year from age(rec.effective_date_bom, v_ev_date_bom)) * 12 +
                                                    extract(month from age(rec.effective_date_bom, v_ev_date_bom)) -
                                                    1
                end);

            -- 未到期缴费期次(评估日期-合同边界日期)
            rec.remaining_prem_term_cb := rec.remaining_prem_term_pe;

            -- 季度付款期间(年度保费)
            rec.payment_quarter := (case
                                        when rec.payment_frequency_code in ('Q', 'Y') then
                                            trunc(extract(month from rec.contract_date) / 3)
                end);


            -- 应收保费试算
            v_cost[25] := coalesce(v_cost[25], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            if exists(select 1
                      from atr_temp_dd_payment_plan p
                      where p.policy_no = rec.policy_no
                        and p.endorse_seq_no = rec.endorse_seq_no) then

                v_cost[30] := coalesce(v_cost[30], 0) + extract(epoch from clock_timestamp() - v_cost_start);
                v_cost_start := clock_timestamp();

                -- 按剩余缴费计划平均摊分
                insert into tmp_dd_recv_premium_dist
                select rec.policy_no,
                       rec.endorse_seq_no,
                       months_between(p.est_payment_date,
                                      date_trunc('month', p_buss.ev_date)) as dev_no,
                       rec.recv_premium / count(*) over ()                 as recv_premium
                from atr_temp_dd_payment_plan p
                where p.policy_no = rec.policy_no
                  and p.endorse_seq_no = rec.endorse_seq_no;

                v_cost[26] := coalesce(v_cost[26], 0) + extract(epoch from clock_timestamp() - v_cost_start);
                v_cost_start := clock_timestamp();

                -- 尾差处理
                select min(dev_no) as min_dev_no, sum(recv_premium) as sum_recv_premium
                into
                    v_dist_min_dev_no, v_dist_sum_recv_premium
                from tmp_dd_recv_premium_dist t
                where t.policy_no = rec.policy_no
                  and t.endorse_seq_no = rec.endorse_seq_no;

                insert into tmp_dd_recv_premium_dist2
                select rec.policy_no,
                       rec.endorse_seq_no,
                       t.dev_no,
                       case
                           when t.dev_no = v_dist_min_dev_no then
                               t.recv_premium + rec.recv_premium - v_dist_sum_recv_premium
                           else t.recv_premium end
                from tmp_dd_recv_premium_dist t
                where t.policy_no = rec.policy_no
                  and t.endorse_seq_no = rec.endorse_seq_no;

                v_cost[27] := coalesce(v_cost[27], 0) + extract(epoch from clock_timestamp() - v_cost_start);
                v_cost_start := clock_timestamp();

            else

                -- 没有剩余缴费计划， 都放第 1 期
                insert into tmp_dd_recv_premium_dist2
                select rec.policy_no, rec.endorse_seq_no, 1 as dev_no, rec.recv_premium as recv_premium;

            end if;

            v_cost[28] := coalesce(v_cost[28], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 计算发展期的应收保费
            update tmp_atr_buss_dd_lrc_icu_calc_detail t
            set recv_premium = (case
                                    when t.dev_no = 0 then
                                        coalesce((select p.premium
                                                  from atr_temp_dd_premium_paid_cur p
                                                  where p.policy_no = rec.policy_no
                                                    and p.endorse_seq_no = rec.endorse_seq_no), 0)
                                    else
                                        (select d.recv_premium
                                         from tmp_dd_recv_premium_dist2 d
                                         where d.policy_no = rec.policy_no
                                           and d.endorse_seq_no = rec.endorse_seq_no
                                           and d.dev_no = t.dev_no)
                end)
            where t.main_id = rec.id;


            v_cost[3] := coalesce(v_cost[3], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** ed premium backup ***/
            -- 已赚保费(每个覆盖日)
            rec.ed_premium_per_coverage_day := rec.gross_premium /
                                               rec.total_days_pol_effective;
            if rec.ed_premium_per_coverage_day = 0 then
                rec.ed_premium_per_coverage_day := null;
            end if;

            -- 发展期
            update tmp_atr_buss_dd_lrc_icu_calc_detail t
            set ed_premium_backup = (case
                                         when t.dev_no > rec.remaining_months then
                                             0
                                         when t.dev_no < rec.future_months then
                                             0
                                         when t.dev_no = rec.remaining_months then
                                             rec.ed_premium_per_coverage_day *
                                             extract(day from
                                                     rec.expiry_date_in_date)
                                         else
                                             rec.ed_premium_per_coverage_day *
                                             atr_pack_lrc_func_get_month_days(p_buss.year_month,
                                                                              t.dev_no)
                end)
            where t.main_id = rec.id
              and t.dev_no > 0;


            v_cost[15] := coalesce(v_cost[15], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 发展期 汇总 到主表
            select sum(d.ed_premium_backup)
            into rec.ed_premium_backup
            from tmp_atr_buss_dd_lrc_icu_calc_detail d
            where d.main_id = rec.id;


            v_cost[4] := coalesce(v_cost[4], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** ue premium  ***/
            -- 覆盖期(Mth)
            rec.coverage_months := extract(year from age(rec.expiry_date_bom, rec.effective_date_bom)) * 12 +
                                   extract(month from age(rec.expiry_date_bom, rec.effective_date_bom));

            -- 发展期
            v_real_premium := case
                                  when rec.expiry_date_in_date <= rec.evaluate_date then
                                      0
                                  else
                                      rec.gross_premium * (1 - rec.passed_days_pol_effective::numeric /
                                                               rec.total_days_pol_effective)
                end;

            v_cost[11] := coalesce(v_cost[11], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            update tmp_atr_buss_dd_lrc_icu_calc_detail t
            set ue_premium = case
                                 when t.dev_no = 0 then
                                     v_real_premium
                                 when t.dev_no >= rec.remaining_months then
                                     0
                                 when rec.gross_premium > 0 then
                                     greatest(v_real_premium -
                                              (select sum(d2.ed_premium_backup)
                                               from tmp_atr_buss_dd_lrc_icu_calc_detail d2
                                               where d2.main_id = rec.id
                                                 and d2.dev_no > 0
                                                 and d2.dev_no <= t.dev_no),
                                              0)
                                 else
                                     least(v_real_premium -
                                           (select sum(d2.ed_premium_backup)
                                            from tmp_atr_buss_dd_lrc_icu_calc_detail d2
                                            where d2.main_id = rec.id
                                              and d2.dev_no > 0
                                              and d2.dev_no <= t.dev_no),
                                           0)
                end
            where t.main_id = rec.id;


            v_cost[5] := coalesce(v_cost[5], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** ed premium  ***/
            -- 发展期
            update tmp_atr_buss_dd_lrc_icu_calc_detail t
            set -- 已赚保费
                ed_premium = coalesce((select pre.ue_premium
                                       from tmp_atr_buss_dd_lrc_icu_calc_detail pre
                                       where pre.main_id = t.main_id
                                         and pre.dev_no = t.dev_no - 1),
                                      0) - coalesce(t.ue_premium, 0)
            where t.main_id = rec.id
              and t.dev_no > 0;

            select sum(d.ed_premium)
            into rec.ed_premium
            from tmp_atr_buss_dd_lrc_icu_calc_detail d
            where d.main_id = rec.id;


            v_cost[6] := coalesce(v_cost[6], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();


            /* ** no dependent dev  ***/
            /*
             调整点 (2024-09-14)：
              1) 0 期iacf放当期的实付佣金；
              2) 已了责任的单（终保月 <= 评估月）， 剩余佣金放在第 1 期
              3) DD 批改时可能只批改佣金，保费无变化，未了责任的单 按原保单的未来期次的应收保费流摊分佣金  (放在步骤3统一处理)
             */
            update tmp_atr_buss_dd_lrc_icu_calc_detail t
            set --- recv_rel_fee
                -- 调整手续费
                adj_commission      = case
                                          when t.dev_no > 0 then
                                              abs(t.recv_premium *
                                                  rec.adj_commission_rate /
                                                  coalesce(t.after_premium_impairment_rate, 1))
                    end,
                -- 经纪人费用
                brokerage_fee       = case
                                          when t.dev_no > 0 then
                                              abs(t.recv_premium * rec.brokerage_fee_rate /
                                                  coalesce(t.after_premium_impairment_rate, 1))
                    end,
                -- 跟单获取费用
                iacf_fee            = case
                                          when t.dev_no = 0 then
                                              (select coalesce(sum(p.commission), 0)
                                               from atr_temp_dd_premium_paid_cur p
                                               where p.policy_no = rec.policy_no
                                                 and p.endorse_seq_no = rec.endorse_seq_no)
                                          when rec.expiry_date_eom <= rec.evaluate_date then
                                              case
                                                  when t.dev_no = 1 then
                                                      coalesce(rec.commission, 0) -
                                                      (select coalesce(sum(p.commission), 0)
                                                       from atr_temp_dd_premium_paid_lte p
                                                       where p.policy_no = rec.policy_no
                                                         and p.endorse_seq_no = rec.endorse_seq_no)
                                                  end
                                          else
                                              t.recv_premium * rec.iacf_fee_rate /
                                              coalesce(t.after_premium_impairment_rate, 1)
                    end,
                --- ed_rel_fee
                -- 非跟单获取费用
                iacf_fee_non_policy = case
                                          when t.dev_no > 0 then
                                              t.ed_premium *
                                              rec.iacf_fee_rate_non_policy
                    end,
                --- coverage_amount
                coverage_amount     = (case
                                           when t.dev_no = 0 then
                                               0
                                           when t.dev_no < rec.future_months then
                                               0
                                           when t.dev_no > rec.remaining_months then
                                               0
                                           when t.ed_premium = 0 then
                                               0
                                           else
                                               rec.coverage_amount *
                                               coalesce(t.ed_premium / rec.ed_premium_per_coverage_day, 1)
                    end),
                --- gep_uwq
                gep_uwq             = case
                                          when t.dev_no > 0 then
                                              t.ed_premium
                    end
            where t.main_id = rec.id;


            v_cost[24] := coalesce(v_cost[24], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- icg coverage_amount
            insert into tmp_atr_temp_lrc_icg_dev_coverage_amount (portfolio_no, icg_no, dev_no, evaluate_approach,
                                                                  loa_code, coverage_amount)
            select rec.portfolio_no, rec.icg_no, d.dev_no, rec.evaluate_approach, rec.loa_code, d.coverage_amount
            from tmp_atr_buss_dd_lrc_icu_calc_detail d
            where d.main_id = rec.id;

            v_cost[7] := coalesce(v_cost[7], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** ue_premium_per_month  ***/
            -- (上期)当期期末剩余未摊销比例
            -- (上期)截至报告期初剩余未摊销比例
            begin
                if p_pre_action_no is not null then
                    select pre.cur_end_remain_csm_rate,
                           pre.until_report_remain_csm_rate
                    into rec.pri_cur_end_remain_csm_rate,
                        rec.pri_until_report_remain_csm_rate
                    from atr_buss_dd_lrc_icu_calc pre
                    where pre.action_no = p_pre_action_no
                      and pre.policy_no = rec.policy_no
                      and pre.endorse_seq_no = rec.endorse_seq_no
                      and pre.currency_code = rec.currency_code;
                end if;
            exception
                when no_data_found then
                    null;
            end;

            -- (上期)当期期末剩余未摊销比例
            rec.pri_cur_end_remain_csm_rate := coalesce(rec.pri_cur_end_remain_csm_rate, 1);
            -- (上期)截至报告期初剩余未摊销比例
            rec.pri_until_report_remain_csm_rate := coalesce(rec.pri_until_report_remain_csm_rate, 1);

            v_cost[16] := coalesce(v_cost[16], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            rec.until_report_remain_csm_rate := case
                                                    when substr(rec.dap_year_month, 1, 4) =
                                                         substr(p_buss.year_month, 1, 4) then
                                                        1
                                                    when v_cur_month = '01' then
                                                        --  (上期)当期期末剩余未摊销比例
                                                        rec.pri_cur_end_remain_csm_rate
                                                    else
                                                        -- (上期)截至报告期初剩余未摊销比例
                                                        rec.pri_until_report_remain_csm_rate
                end;

            v_cost[17] := coalesce(v_cost[17], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            begin
                select case
                           when rec.gross_premium = 0 then
                               0
                           else
                               d.ue_premium / rec.gross_premium
                           end
                into rec.cur_end_remain_csm_rate
                from tmp_atr_buss_dd_lrc_icu_calc_detail d
                where d.main_id = rec.id
                  and d.dev_no = 0;
            exception
                when no_data_found then
                    null;
            end;

            -- 当期期末剩余未摊销比例
            rec.cur_end_remain_csm_rate := coalesce(rec.cur_end_remain_csm_rate, 0);
            -- 累计已赚比例
            rec.cumulative_ed_rate := 1 - rec.cur_end_remain_csm_rate;


            v_cost[9] := coalesce(v_cost[9], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** update main  ***/
            insert into tmp_lrc_icu_main (id,
                                          dev_val,
                                          passed_dates,
                                          passed_months,
                                          remaining_months,
                                          future_months,
                                          dap_year_month,
                                          total_days_pol_effective,
                                          passed_days_pol_effective,
                                          adj_commission_rate,
                                          brokerage_fee_rate,
                                          iacf_fee_rate,
                                          iacf_fee_rate_non_policy,
                                          maintenance_fee_rate,
                                          remaining_months_for_recv,
                                          remaining_prem_term_pe,
                                          remaining_months_future,
                                          remaining_prem_term_cb,
                                          payment_quarter,
                                          cumulative_paid_premium,
                                          recv_premium,
                                          ed_premium_per_coverage_day,
                                          ed_premium_backup,
                                          ed_premium,
                                          coverage_months,
                                          pri_cur_end_remain_csm_rate,
                                          pri_until_report_remain_csm_rate,
                                          cumulative_ed_rate,
                                          cur_end_remain_csm_rate,
                                          until_report_remain_csm_rate,
                                          elr)
            select rec.id,
                   rec.dev_val,
                   rec.passed_dates,
                   rec.passed_months,
                   rec.remaining_months,
                   rec.future_months,
                   rec.dap_year_month,
                   rec.total_days_pol_effective,
                   rec.passed_days_pol_effective,
                   rec.adj_commission_rate,
                   rec.brokerage_fee_rate,
                   rec.iacf_fee_rate,
                   rec.iacf_fee_rate_non_policy,
                   rec.maintenance_fee_rate,
                   rec.remaining_months_for_recv,
                   rec.remaining_prem_term_pe,
                   rec.remaining_months_future,
                   rec.remaining_prem_term_cb,
                   rec.payment_quarter,
                   rec.cumulative_paid_premium,
                   rec.recv_premium,
                   rec.ed_premium_per_coverage_day,
                   rec.ed_premium_backup,
                   rec.ed_premium,
                   rec.coverage_months,
                   rec.pri_cur_end_remain_csm_rate,
                   rec.pri_until_report_remain_csm_rate,
                   rec.cumulative_ed_rate,
                   rec.cur_end_remain_csm_rate,
                   rec.until_report_remain_csm_rate,
                   rec.elr;

            v_cost[10] := coalesce(v_cost[10], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** end  ***/

            -- flush detail tmp
            if mod(v_count, 50) = 0 then
                insert into atr_buss_dd_lrc_icu_calc_detail select * from tmp_atr_buss_dd_lrc_icu_calc_detail;
                truncate table tmp_atr_buss_dd_lrc_icu_calc_detail;
            end if;

            v_cost[12] := coalesce(v_cost[12], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 应收保费试算清理
            if mod(v_count, 50) = 0 then
                truncate table tmp_dd_recv_premium_dist;
                truncate table tmp_dd_recv_premium_dist2;
            end if;

            v_cost[99] := coalesce(v_cost[99], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            if mod(v_count, 1000) = 0 then
                v_cost_str := '';
                for i in 1..array_length(v_cost, 1)
                    loop
                        if v_cost[i] is not null then
                            v_cost_str := v_cost_str || ',[' || i || ']--' || round(v_cost[i], 2);
                        end if;
                    end loop;
                v_cost := array []::numeric[];

                call atr_pack_lrc_proc_debug(p_buss.action_no, v_count::varchar
                                                                   || ' -- ' || v_cost_str, 'calc_icu', p_thread_no);

                commit;
            end if;

        end loop;

    call atr_pack_lrc_proc_debug(p_buss.action_no, v_count::varchar, 'calc_icu-end', p_thread_no);

    -- copy_coverage_amount
    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'copy_coverage_amount-start', p_thread_no);

    insert into atr_temp_lrc_icg_dev_coverage_amount (action_no, thread_no, portfolio_no,
                                                      icg_no, dev_no, evaluate_approach,
                                                      loa_code, coverage_amount)
    select p_buss.action_no,
           p_thread_no,
           t.portfolio_no,
           t.icg_no,
           t.dev_no,
           t.evaluate_approach,
           t.loa_code,
           sum(t.coverage_amount)
    from tmp_atr_temp_lrc_icg_dev_coverage_amount t
    group by t.portfolio_no,
             t.icg_no,
             t.dev_no,
             t.evaluate_approach,
             t.loa_code;

    truncate table tmp_atr_temp_lrc_icg_dev_coverage_amount;

    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'copy_coverage_amount-end', p_thread_no);

    -- copy detail 
    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'copy-detail-start', p_thread_no);

    insert into atr_buss_dd_lrc_icu_calc_detail select * from tmp_atr_buss_dd_lrc_icu_calc_detail;

    truncate table tmp_atr_buss_dd_lrc_icu_calc_detail;

    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'copy-detail-end', p_thread_no);

    -- copy main
    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'copy-main-start', p_thread_no);

    merge into atr_buss_dd_lrc_icu_calc t
    using tmp_lrc_icu_main tm
    on (t.action_no = p_buss.action_no and t.id = tm.id)
    when matched then
        update
        set dev_val                          = tm.dev_val,
            passed_dates                     = tm.passed_dates,
            passed_months                    = tm.passed_months,
            remaining_months                 = tm.remaining_months,
            future_months                    = tm.future_months,
            dap_year_month                   = tm.dap_year_month,
            total_days_pol_effective         = tm.total_days_pol_effective,
            passed_days_pol_effective        = tm.passed_days_pol_effective,
            adj_commission_rate              = tm.adj_commission_rate,
            brokerage_fee_rate               = tm.brokerage_fee_rate,
            iacf_fee_rate                    = tm.iacf_fee_rate,
            iacf_fee_rate_non_policy         = tm.iacf_fee_rate_non_policy,
            maintenance_fee_rate             = tm.maintenance_fee_rate,
            remaining_months_for_recv        = tm.remaining_months_for_recv,
            remaining_prem_term_pe           = tm.remaining_prem_term_pe,
            remaining_months_future          = tm.remaining_months_future,
            remaining_prem_term_cb           = tm.remaining_prem_term_cb,
            payment_quarter                  = tm.payment_quarter,
            cumulative_paid_premium          = tm.cumulative_paid_premium,
            recv_premium                     = tm.recv_premium,
            ed_premium_per_coverage_day      = tm.ed_premium_per_coverage_day,
            ed_premium_backup                = tm.ed_premium_backup,
            ed_premium                       = tm.ed_premium,
            coverage_months                  = tm.coverage_months,
            pri_cur_end_remain_csm_rate      = tm.pri_cur_end_remain_csm_rate,
            pri_until_report_remain_csm_rate = tm.pri_until_report_remain_csm_rate,
            cumulative_ed_rate               = tm.cumulative_ed_rate,
            cur_end_remain_csm_rate          = tm.cur_end_remain_csm_rate,
            until_report_remain_csm_rate     = tm.until_report_remain_csm_rate,
            elr                              = tm.elr;

    truncate table tmp_lrc_icu_main;

    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'copy-main-end', p_thread_no);

    commit;
end
$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_icu_fo(IN p_buss atruser.atr_pack_lrc_record_buss, IN p_pre_action_no character varying, IN p_threads integer, IN p_thread_no integer)
    LANGUAGE plpgsql
    AS $$
declare
    v_real_premium          atr_buss_dd_lrc_icu_calc.gross_premium%type;
    v_ev_date_bom           date        := to_date(p_buss.year_month, 'yyyymm');
    v_cur_month             numeric(2)  := substr(p_buss.year_month, 5, 2);
    v_count                 numeric(18) := 0;
    rec                     record;
    v_cost_start            timestamp(6);
    v_cost                  numeric(19, 6)[];
    v_cost_str              varchar(1000);
    v_non_loa_quota_exitsed boolean;
begin

    v_non_loa_quota_exitsed := exists(select *
                                      from atr_buss_quota_value t
                                      where t.action_no = p_buss.action_no
                                        and length(t.dimension_value) > 3);

    /*  icg coverage_amount 临时表 start  */
    drop table if exists tmp_atr_temp_lrc_icg_dev_coverage_amount;

    create temp table tmp_atr_temp_lrc_icg_dev_coverage_amount
    as
    select portfolio_no,
           icg_no,
           dev_no,
           evaluate_approach,
           loa_code,
           coverage_amount
    from atr_temp_lrc_icg_dev_coverage_amount
    where false;
    /*  icg coverage_amount 临时表 end  */

    /*  dev 临时表 start */
    drop table if exists tmp_atr_buss_fo_lrc_icu_calc_detail;

    create temp table tmp_atr_buss_fo_lrc_icu_calc_detail
    as
    select * from atr_buss_fo_lrc_icu_calc_detail where false;

    create index idx_tmp_atr_buss_fo_lrc_icu_calc_detail_id on tmp_atr_buss_fo_lrc_icu_calc_detail (id);
    create index idx_tmp_atr_buss_fo_lrc_icu_calc_detail on tmp_atr_buss_fo_lrc_icu_calc_detail (main_id, dev_no);
    /*  dev 临时表 end  */

    for rec in (select t.*, date_trunc('month', t.expiry_date_in_date) expiry_date_bom
                from atr_buss_fo_lrc_icu_calc t
                where action_no = p_buss.action_no
                  and mod(id, p_threads) = p_thread_no)
        loop
            v_count := v_count + 1;

            v_cost_start := clock_timestamp();

            /* ** common  ***/
            if v_non_loa_quota_exitsed then
                -- elr
                rec.elr := atr_pack_ecf_util_func_get_quota_value('BE013',
                                                                  p_buss.action_no,
                                                                  rec.id,
                                                                  'U',
                                                                  -1,
                                                                  null,
                                                                  null);

                -- 发展期变量
                rec.dev_val := atr_pack_ecf_util_func_get_quota_value('BE008',
                                                                      p_buss.action_no,
                                                                      rec.id,
                                                                      'U',
                                                                      -1,
                                                                      null,
                                                                      null);

                -- 调整手续费比例
                rec.adj_commission_rate := atr_pack_ecf_util_func_get_quota_value('QR010',
                                                                                  p_buss.action_no,
                                                                                  rec.id,
                                                                                  'U',
                                                                                  -1,
                                                                                  null,
                                                                                  null);

                -- 经纪人费用比例
                rec.brokerage_fee_rate := atr_pack_ecf_util_func_get_quota_value('BE005',
                                                                                 p_buss.action_no,
                                                                                 rec.id,
                                                                                 'U',
                                                                                 -1,
                                                                                 null,
                                                                                 null);

                -- 非跟单获取费用比例
                rec.iacf_fee_rate_non_policy := atr_pack_ecf_util_func_get_quota_value('QR002',
                                                                                       p_buss.action_no,
                                                                                       rec.id,
                                                                                       'U',
                                                                                       -1,
                                                                                       null,
                                                                                       null);

                -- 维持费用比例
                rec.maintenance_fee_rate := atr_pack_ecf_util_func_get_quota_value('QR003',
                                                                                   p_buss.action_no,
                                                                                   rec.id,
                                                                                   'U',
                                                                                   -1,
                                                                                   null,
                                                                                   null);

            else

                -- elr
                select t.quota_value
                into rec.elr
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'BE013'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

                -- 发展期变量
                select t.quota_value
                into rec.dev_val
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'BE008'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

                -- 调整手续费比例
                select t.quota_value
                into rec.adj_commission_rate
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'QR010'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

                -- 经纪人费用比例
                select t.quota_value
                into rec.brokerage_fee_rate
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'BE005'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

                -- 非跟单获取费用比例
                select t.quota_value
                into rec.iacf_fee_rate_non_policy
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'QR002'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

                -- 维持费用比例
                select t.quota_value
                into rec.maintenance_fee_rate
                from atr_buss_quota_value t
                where t.action_no = rec.action_no
                  and t.quota_code = 'QR003'
                  and t.dimension_value = rec.loa_code
                  and t.dev_no = -1;

            end if;

            -- 获取费用比例
            rec.iacf_fee_rate := rec.commission_rate;
            v_cost[1] := coalesce(v_cost[1], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 累计已收保费
            select coalesce(sum(p.paid_premium + coalesce(p.commission, 0)), 0)
            into rec.cumulative_paid_premium
            from atr_dap_fo_premium_paid p
            where p.entity_id = rec.entity_id
              and p.ri_policy_no = rec.ri_policy_no
              and p.ri_endorse_seq_no = rec.ri_endorse_seq_no
              and p.endorse_seq_no = rec.endorse_seq_no
              and p.year_month <= p_buss.year_month;

            v_cost[2] := coalesce(v_cost[2], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 承保天数 （起保日期-终保日期）
            rec.total_days_pol_effective := case
                                                when rec.effective_date_in_date <=
                                                     rec.expiry_date_in_date then
                                                    cast(rec.expiry_date_in_date as date) -
                                                    cast(rec.effective_date_in_date as date) + 1
                end;

            -- 起保-评估期天数
            rec.passed_days_pol_effective := case
                                                 when rec.effective_date_in_date >
                                                      rec.evaluate_date then
                                                     0
                                                 when rec.expiry_date_in_date >
                                                      rec.evaluate_date then
                                                     cast(rec.evaluate_date as date) -
                                                     cast(rec.effective_date_in_date as date) + 1
                                                 when rec.effective_date_in_date <=
                                                      rec.expiry_date_in_date then
                                                     cast(rec.expiry_date_in_date as date) -
                                                     cast(rec.effective_date_in_date as date) + 1
                end;

            -- 公共项/已承保天数
            rec.passed_dates := (case
                                     when rec.evaluate_date < rec.effective_date_bom then
                                         0
                                     else
                                         rec.evaluate_date - rec.effective_date_in_date
                end);

            -- 公共项/已过月份
            rec.passed_months := (case
                                      when rec.effective_date_bom > rec.evaluate_date then
                                          0
                                      when extract(day from rec.effective_date_in_date) = 1 then
                                          extract(year from age(v_ev_date_bom, rec.effective_date_bom)) * 12 +
                                          extract(month from age(v_ev_date_bom, rec.effective_date_bom))
                                      else
                                          extract(year from age(v_ev_date_bom, rec.effective_date_bom)) * 12 +
                                          extract(month from age(v_ev_date_bom, rec.effective_date_bom)) - 1
                end);

            -- 公共项/未到期月份
            rec.remaining_months := extract(year from age(rec.expiry_date_bom, v_ev_date_bom)) * 12 +
                                    extract(month from age(rec.expiry_date_bom, v_ev_date_bom));

            -- 还有几个月起保
            rec.future_months := greatest(extract(year from age(rec.effective_date_bom, v_ev_date_bom)) * 12 +
                                          extract(month from age(rec.effective_date_bom, v_ev_date_bom)),
                                          0);

            v_cost[3] := coalesce(v_cost[3], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 初始化明细表
            insert into tmp_atr_buss_fo_lrc_icu_calc_detail
                (id, action_no, main_id, dev_no)
            select rec.id * 10000 + d.dev_no as id,
                   p_buss.action_no,
                   rec.id                    as main_id,
                   d.dev_no
            from atr_conf_dev_no d
            where d.dev_no >= 0
              and d.dev_no <= greatest(coalesce(rec.remaining_months, 0), 1) --- bcai FO 的缴费频率都是 S
            order by d.dev_no;

            v_cost[4] := coalesce(v_cost[4], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- after_premium_impairment_rate
            update tmp_atr_buss_fo_lrc_icu_calc_detail t
            set after_premium_impairment_rate =
                    (select q.quota_value
                     from atr_buss_quota_value q
                     where q.action_no = p_buss.action_no
                       and q.quota_code = 'BE001'
                       and q.dimension = 'G'
                       and q.dimension_value = rec.loa_code
                       and q.dev_no = t.dev_no)
            where t.main_id = rec.id;

            v_cost[5] := coalesce(v_cost[5], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** recv premium  ***/
            -- 未到期月份(应收保费计算专用)
            rec.remaining_months_for_recv := (case
                                                  when rec.expiry_date_eom <=
                                                       rec.evaluate_date then
                                                      0
                                                  else
                                                      extract(year from age(rec.expiry_date_bom, v_ev_date_bom)) * 12 +
                                                      extract(month from age(rec.expiry_date_bom, v_ev_date_bom))
                end);

            -- 预期应收保费/剩余未交费期次(评估日期-终保日期)
            rec.remaining_prem_term_pe := (case
                                               when rec.payment_frequency_code = 'S' then
                                                   0
                                               when rec.payment_frequency_code = 'M' then
                                                   (case
                                                        when extract(day from
                                                                     rec.effective_date_in_date) = 1 then
                                                            greatest(rec.payment_frequency_no -
                                                                     rec.passed_months,
                                                                     0)
                                                        else
                                                            greatest(greatest(rec.payment_frequency_no -
                                                                              rec.passed_months,
                                                                              0) - 1,
                                                                     0)
                                                       end)
                                               when rec.payment_frequency_code = 'Q' then
                                                   (case
                                                        when extract(day from
                                                                     rec.effective_date_in_date) = 1 then
                                                            greatest(rec.payment_frequency_no -
                                                                     trunc((rec.passed_months - 1) / 3) - 1,
                                                                     0)
                                                        else
                                                            greatest(rec.payment_frequency_no -
                                                                     trunc(rec.passed_months / 3) - 1,
                                                                     0)
                                                       end)
                                               when rec.payment_frequency_code = 'Y' then
                                                   (case
                                                        when extract(day from
                                                                     rec.effective_date_in_date) = 1 then
                                                            greatest(rec.payment_frequency_no - (1 +
                                                                                                 trunc((rec.passed_months - 1) / 12)) -
                                                                     1,
                                                                     0)
                                                        else
                                                            greatest(rec.payment_frequency_no - (1 +
                                                                                                 trunc(rec.passed_months / 12)) -
                                                                     1,
                                                                     0)
                                                       end)
                                               else
                                                   0
                end);

            -- 应收保费
            -- 分出业务， 实付保费的符号相反
            rec.recv_premium := rec.gross_premium + rec.cumulative_paid_premium;

            -- 预期应收保费/未到期月份(间隔第一期未来年度预付时间)
            rec.remaining_months_future := (case
                                                when rec.payment_frequency_code in
                                                     ('S', 'M', 'Q') or
                                                     coalesce(rec.remaining_prem_term_pe, 0) = 0 then
                                                    null
                                                when to_char(rec.effective_date_in_date,
                                                             'yyyymm') <= rec.year_month then
                                                    greatest(
                                                            extract(year from age(rec.effective_date_bom, v_ev_date_bom)) *
                                                            12 +
                                                            extract(month from age(rec.effective_date_bom, v_ev_date_bom)) +
                                                            12 - 1,
                                                            0)
                                                else
                                                    extract(year from age(rec.effective_date_bom, v_ev_date_bom)) * 12 +
                                                    extract(month from age(rec.effective_date_bom, v_ev_date_bom)) -
                                                    1
                end);

            -- 未到期缴费期次(评估日期-合同边界日期)
            rec.remaining_prem_term_cb := rec.remaining_prem_term_pe;

            -- 预期应收保费/季度付款期间(年度保费)
            rec.payment_quarter := (case
                                        when rec.payment_frequency_code in ('Q', 'Y') then
                                            trunc(extract(month from rec.contract_date) / 3)
                end);

            -- 发展期
            update tmp_atr_buss_fo_lrc_icu_calc_detail t
            set recv_premium = (case
                -- 当期取 实收保费
                                    when t.dev_no = 0 then
                                        (select coalesce(sum(p.paid_premium + coalesce(p.commission, 0)), 0)
                                         from atr_dap_fo_premium_paid p
                                         where p.entity_id = rec.entity_id
                                           and p.ri_policy_no = rec.ri_policy_no
                                           and p.ri_endorse_seq_no =
                                               rec.ri_endorse_seq_no
                                           and p.endorse_seq_no =
                                               rec.endorse_seq_no
                                           and p.year_month = p_buss.year_month)
                                    when rec.remaining_prem_term_cb = 0 then
                                        case
                                            when t.dev_no = 1 then
                                                rec.recv_premium *
                                                atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                       1)
                                            else
                                                0
                                            end
                -- Single premium issue
                                    when rec.payment_frequency_code = 'S' then
                                        case
                                            when t.dev_no = 1 then
                                                rec.recv_premium *
                                                atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                       1)
                                            else
                                                0
                                            end
                                    when coalesce(rec.remaining_prem_term_cb, 0) = 0 or
                                         coalesce(rec.gross_premium, 0) = 0 then
                                        0
                                    when rec.remaining_prem_term_cb = 0 or
                                         rec.gross_premium = 0 then
                                        0
                                    when rec.payment_frequency_code = 'M' then
                                        (case
                                             when t.dev_no > rec.remaining_prem_term_cb then
                                                 0
                                             else
                                                 rec.recv_premium /
                                                 rec.remaining_prem_term_cb *
                                                 atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                        1)
                                            end)
                                    when rec.payment_frequency_code = 'Q' then
                                        (case
                                             when t.dev_no / 3.0 >
                                                  rec.remaining_prem_term_cb then
                                                 0
                                             else
                                                 rec.recv_premium /
                                                 rec.remaining_prem_term_cb *
                                                 (floor((t.dev_no + mod(rec.passed_months, 3)) / 3) -
                                                  floor((t.dev_no - 1 +
                                                         mod(rec.passed_months, 3)) / 3)) *
                                                 atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                        1)
                                            end)
                                    when rec.payment_frequency_code = 'Y' then
                                        (case
                                             when t.dev_no / 12.0 >
                                                  rec.remaining_prem_term_cb then
                                                 0
                                             else
                                                 rec.recv_premium /
                                                 rec.remaining_prem_term_cb *
                                                 (floor((t.dev_no +
                                                         mod(rec.passed_months, 12)) / 12) -
                                                  floor((t.dev_no - 1 +
                                                         mod(rec.passed_months, 12)) / 12)) *
                                                 atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                        1)
                                            end)
                end)
            where t.main_id = rec.id;

            v_cost[6] := coalesce(v_cost[6], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** ed premium backup ***/
            -- 已赚保费(每个覆盖日)
            rec.ed_premium_per_coverage_day := rec.gross_premium /
                                               rec.total_days_pol_effective;

            -- 发展期
            update tmp_atr_buss_fo_lrc_icu_calc_detail t
            set -- 已赚保费
                ed_premium_backup = (case
                                         when t.dev_no > rec.remaining_months then
                                             0
                                         when t.dev_no < rec.future_months then
                                             0
                                         when t.dev_no = rec.remaining_months then
                                             rec.ed_premium_per_coverage_day *
                                             extract(day from
                                                     rec.expiry_date_in_date)
                                         else
                                             rec.ed_premium_per_coverage_day *
                                             atr_pack_lrc_func_get_month_days(p_buss.year_month,
                                                                              t.dev_no)
                    end)
            where t.main_id = rec.id
              and t.dev_no > 0;

            -- 发展期 汇总 到主表
            select sum(d.ed_premium_backup)
            into rec.ed_premium_backup
            from tmp_atr_buss_fo_lrc_icu_calc_detail d
            where d.main_id = rec.id;


            v_cost[7] := coalesce(v_cost[7], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** ue premium  ***/
            -- 覆盖期(Mth)
            rec.coverage_months := extract(year from age(rec.expiry_date_bom, rec.effective_date_bom)) * 12 +
                                   extract(month from age(rec.expiry_date_bom, rec.effective_date_bom));

            -- 发展期
            v_real_premium := case
                                  when rec.expiry_date_in_date <= rec.evaluate_date then
                                      0
                                  else
                                      rec.gross_premium * (1 - rec.passed_days_pol_effective::numeric /
                                                               rec.total_days_pol_effective)
                end;

            update tmp_atr_buss_fo_lrc_icu_calc_detail t
            set ue_premium = case
                                 when t.dev_no = 0 then
                                     v_real_premium
                                 when t.dev_no >= rec.remaining_months then
                                     0
                                 when rec.gross_premium > 0 then
                                     greatest(v_real_premium -
                                              (select sum(d2.ed_premium_backup)
                                               from tmp_atr_buss_fo_lrc_icu_calc_detail d2
                                               where d2.main_id = rec.id
                                                 and d2.dev_no > 0
                                                 and d2.dev_no <= t.dev_no),
                                              0)
                                 else
                                     least(v_real_premium -
                                           (select sum(d2.ed_premium_backup)
                                            from tmp_atr_buss_fo_lrc_icu_calc_detail d2
                                            where d2.main_id = rec.id
                                              and d2.dev_no > 0
                                              and d2.dev_no <= t.dev_no),
                                           0)
                end
            where t.main_id = rec.id;


            v_cost[8] := coalesce(v_cost[8], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** ed premium  ***/
            -- 发展期
            update tmp_atr_buss_fo_lrc_icu_calc_detail t
            set -- 已赚保费
                ed_premium = coalesce((select pre.ue_premium
                                       from tmp_atr_buss_fo_lrc_icu_calc_detail pre
                                       where pre.main_id = t.main_id
                                         and pre.dev_no = t.dev_no - 1),
                                      0) - coalesce(t.ue_premium, 0)
            where t.main_id = rec.id
              and t.dev_no > 0;

            -- 发展期 汇总 到主表
            select sum(d.ed_premium)
            into rec.ed_premium
            from tmp_atr_buss_fo_lrc_icu_calc_detail d
            where d.main_id = rec.id;


            v_cost[9] := coalesce(v_cost[9], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** no dependent dev  ***/
            update tmp_atr_buss_fo_lrc_icu_calc_detail t
            set --- recv_rel_fee
                -- 调整手续费
                adj_commission      = case
                                          when t.dev_no > 0 then
                                              abs(t.recv_premium *
                                                  rec.adj_commission_rate /
                                                  atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                         1))
                    end,
                -- 经纪人费用
                brokerage_fee       = case
                                          when t.dev_no > 0 then
                                              abs(t.recv_premium * rec.brokerage_fee_rate /
                                                  atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                         1))
                    end,
                -- 跟单获取费用
                iacf_fee            = t.recv_premium * rec.iacf_fee_rate /
                                      atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                             1),
                --- ed_rel_fee
                -- 非跟单获取费用
                iacf_fee_non_policy = case
                                          when t.dev_no > 0 then
                                              t.ed_premium *
                                              rec.iacf_fee_rate_non_policy
                    end,
                --- coverage_amount
                coverage_amount     = (case
                                           when t.dev_no = 0 then
                                               0
                                           when t.dev_no < rec.future_months then
                                               0
                                           when t.dev_no > rec.remaining_months then
                                               0
                                           when t.ed_premium = 0 then
                                               0
                                           else
                                               rec.coverage_amount *
                                               coalesce(t.ed_premium /
                                                        atr_pack_lrc_nvl0(rec.ed_premium_per_coverage_day,
                                                                          null),
                                                        1)
                    end),
                --- gep_uwq
                gep_uwq             = case
                                          when t.dev_no > 0 then
                                              t.ed_premium
                    end
            where t.main_id = rec.id;

            v_cost[10] := coalesce(v_cost[10], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- icg coverage_amount
            insert into tmp_atr_temp_lrc_icg_dev_coverage_amount (portfolio_no, icg_no, dev_no, evaluate_approach,
                                                                  loa_code, coverage_amount)
            select rec.portfolio_no, rec.icg_no, d.dev_no, rec.evaluate_approach, rec.loa_code, d.coverage_amount
            from tmp_atr_buss_fo_lrc_icu_calc_detail d
            where d.main_id = rec.id;

            v_cost[11] := coalesce(v_cost[11], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** ue_premium_per_month  ***/
            -- (上期)当期期末剩余未摊销比例
            -- (上期)截至报告期初剩余未摊销比例
            begin
                if p_pre_action_no is not null then
                    select pre.cur_end_remain_csm_rate,
                           pre.until_report_remain_csm_rate
                    into rec.pri_cur_end_remain_csm_rate,
                        rec.pri_until_report_remain_csm_rate
                    from atr_buss_fo_lrc_icu_calc pre
                    where pre.action_no = p_pre_action_no
                      and pre.ri_policy_no = rec.ri_policy_no
                      and pre.ri_endorse_seq_no = rec.ri_endorse_seq_no
                      and pre.endorse_seq_no = rec.endorse_seq_no;
                end if;
            exception
                when no_data_found then
                    null;
            end;

            -- (上期)当期期末剩余未摊销比例
            rec.pri_cur_end_remain_csm_rate := coalesce(rec.pri_cur_end_remain_csm_rate, 1);
            -- (上期)截至报告期初剩余未摊销比例
            rec.pri_until_report_remain_csm_rate := coalesce(rec.pri_until_report_remain_csm_rate, 1);


            v_cost[13] := coalesce(v_cost[13], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            rec.until_report_remain_csm_rate := case
                                                    when substr(rec.dap_year_month, 1, 4) =
                                                         substr(p_buss.year_month, 1, 4) then
                                                        1
                                                    when v_cur_month = '01' then
                                                        --  (上期)当期期末剩余未摊销比例
                                                        rec.pri_cur_end_remain_csm_rate
                                                    else
                                                        -- (上期)截至报告期初剩余未摊销比例
                                                        rec.pri_until_report_remain_csm_rate
                end;

            begin
                select case
                           when rec.gross_premium = 0 then
                               0
                           else
                               d.ue_premium / rec.gross_premium
                           end
                into rec.cur_end_remain_csm_rate
                from tmp_atr_buss_fo_lrc_icu_calc_detail d
                where d.main_id = rec.id
                  and d.dev_no = 0;
            exception
                when no_data_found then
                    null;
            end;

            -- 当期期末剩余未摊销比例
            rec.cur_end_remain_csm_rate := coalesce(rec.cur_end_remain_csm_rate, 0);
            -- 累计已赚比例
            rec.cumulative_ed_rate := 1 - rec.cur_end_remain_csm_rate;

            v_cost[15] := coalesce(v_cost[15], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** update main  ***/
            update atr_buss_fo_lrc_icu_calc t
            set dev_val                          = rec.dev_val,
                passed_dates                     = rec.passed_dates,
                passed_months                    = rec.passed_months,
                remaining_months                 = rec.remaining_months,
                future_months                    = rec.future_months,
                dap_year_month                   = rec.dap_year_month,
                total_days_pol_effective         = rec.total_days_pol_effective,
                passed_days_pol_effective        = rec.passed_days_pol_effective,
                adj_commission_rate              = rec.adj_commission_rate,
                brokerage_fee_rate               = rec.brokerage_fee_rate,
                iacf_fee_rate                    = rec.iacf_fee_rate,
                iacf_fee_rate_non_policy         = rec.iacf_fee_rate_non_policy,
                maintenance_fee_rate             = rec.maintenance_fee_rate,
                remaining_months_for_recv        = rec.remaining_months_for_recv,
                remaining_prem_term_pe           = rec.remaining_prem_term_pe,
                remaining_months_future          = rec.remaining_months_future,
                remaining_prem_term_cb           = rec.remaining_prem_term_cb,
                payment_quarter                  = rec.payment_quarter,
                cumulative_paid_premium          = rec.cumulative_paid_premium,
                recv_premium                     = rec.recv_premium,
                ed_premium_per_coverage_day      = rec.ed_premium_per_coverage_day,
                ed_premium_backup                = rec.ed_premium_backup,
                ed_premium                       = rec.ed_premium,
                coverage_months                  = rec.coverage_months,
                pri_cur_end_remain_csm_rate      = rec.pri_cur_end_remain_csm_rate,
                pri_until_report_remain_csm_rate = rec.pri_until_report_remain_csm_rate,
                cumulative_ed_rate               = rec.cumulative_ed_rate,
                cur_end_remain_csm_rate          = rec.cur_end_remain_csm_rate,
                until_report_remain_csm_rate     = rec.until_report_remain_csm_rate,
                elr                              = rec.elr
            where t.id = rec.id
              and t.action_no = p_buss.action_no;

            v_cost[16] := coalesce(v_cost[16], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** end  ***/

            -- flush detail tmp
            if mod(v_count, 50) = 0 then
                insert into atr_buss_fo_lrc_icu_calc_detail select * from tmp_atr_buss_fo_lrc_icu_calc_detail;
                truncate table tmp_atr_buss_fo_lrc_icu_calc_detail;
            end if;

            v_cost[17] := coalesce(v_cost[17], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            if mod(v_count, 1000) = 0 then
                v_cost_str := '';
                for i in 1..array_length(v_cost, 1)
                    loop
                        if v_cost[i] is not null then
                            v_cost_str := v_cost_str || ',[' || i || ']--' || round(v_cost[i], 2);
                        end if;
                    end loop;
                v_cost := array []::numeric[];

                call atr_pack_lrc_proc_debug(p_buss.action_no, v_count::varchar
                                                                   || ' -- ' || v_cost_str, 'calc_icu', p_thread_no);
                commit;
            end if;

        end loop;

    call atr_pack_lrc_proc_debug(p_buss.action_no, v_count::varchar, 'calc_icu-end', p_thread_no);
    commit;

    -- copy_coverage_amount
    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'copy_coverage_amount-start', p_thread_no);

    insert into atr_temp_lrc_icg_dev_coverage_amount (action_no, thread_no, portfolio_no,
                                                      icg_no, dev_no, evaluate_approach,
                                                      loa_code, coverage_amount)
    select p_buss.action_no,
           p_thread_no,
           t.portfolio_no,
           t.icg_no,
           t.dev_no,
           t.evaluate_approach,
           t.loa_code,
           sum(t.coverage_amount)
    from tmp_atr_temp_lrc_icg_dev_coverage_amount t
    group by t.portfolio_no,
             t.icg_no,
             t.dev_no,
             t.evaluate_approach,
             t.loa_code;

    truncate table tmp_atr_temp_lrc_icg_dev_coverage_amount;

    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'copy_coverage_amount-end', p_thread_no);


    -- copy detail
    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'copy-detail-start', p_thread_no);

    insert into atr_buss_fo_lrc_icu_calc_detail select * from tmp_atr_buss_fo_lrc_icu_calc_detail;
    truncate table tmp_atr_buss_fo_lrc_icu_calc_detail;

    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'copy-detail-end', p_thread_no);

    commit;

end;
$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_icu_ti(IN p_buss atruser.atr_pack_lrc_record_buss, IN p_pre_action_no character varying, IN p_threads integer, IN p_thread_no integer)
    LANGUAGE plpgsql
    AS $_$
declare
    v_cur_month     numeric(2)  := substr(p_buss.year_month, 5, 2);
    v_count         numeric(18) := 0;
    v_reversed_is   varchar(1);
    v_src_treaty_no varchar(60);
    rec             record;
begin

    for rec in (select *
                from atr_buss_ti_lrc_icu_calc
                where action_no = p_buss.action_no
                  and mod(id, p_threads) = p_thread_no
                order by id)
        loop
            v_count := v_count + 1;

            v_reversed_is := case
                                 when rec.treaty_no like '%#-1' then
                                     '1'
                                 else
                                     '0'
                end;
            v_src_treaty_no := regexp_replace(rec.treaty_no, '#.+$', '');

            /* ** common  ***/
            -- elr
            rec.elr := atr_pack_ecf_util_func_get_quota_value('BE013',
                                                              p_buss.action_no,
                                                              rec.id,
                                                              'U',
                                                              -1,
                                                              null,
                                                              null);

            -- 发展期变量
            rec.dev_val := atr_pack_ecf_util_func_get_quota_value('BE008',
                                                                  p_buss.action_no,
                                                                  rec.id,
                                                                  'U',
                                                                  -1,
                                                                  null,
                                                                  null);
            -- 调整手续费比例
            rec.adj_commission_rate := atr_pack_ecf_util_func_get_quota_value('QR010',
                                                                              p_buss.action_no,
                                                                              rec.id,
                                                                              'U',
                                                                              -1,
                                                                              null,
                                                                              null);
            -- 经纪人费用比例
            rec.brokerage_fee_rate := atr_pack_ecf_util_func_get_quota_value('BE005',
                                                                             p_buss.action_no,
                                                                             rec.id,
                                                                             'U',
                                                                             -1,
                                                                             null,
                                                                             null);
            -- 获取费用比例
            rec.iacf_fee_rate := rec.commission_rate;
            -- 非跟单获取费用比例
            rec.iacf_fee_rate_non_policy := atr_pack_ecf_util_func_get_quota_value('QR002',
                                                                                   p_buss.action_no,
                                                                                   rec.id,
                                                                                   'U',
                                                                                   -1,
                                                                                   null,
                                                                                   null);
            -- 维持费用比例
            rec.maintenance_fee_rate := atr_pack_ecf_util_func_get_quota_value('QR003',
                                                                               p_buss.action_no,
                                                                               rec.id,
                                                                               'U',
                                                                               -1,
                                                                               null,
                                                                               null);
            -- 公共项/已过月份
            rec.passed_months := case
                                     when rec.effective_date_bom > rec.evaluate_date then
                                         0
                                     when to_char(rec.effective_date_in_date, 'dd') = '01' then
                                         atr_pack_lrc_func_months_diff(rec.effective_date_bom,
                                                                       rec.evaluate_date)
                                     else
                                         atr_pack_lrc_func_months_diff(rec.effective_date_bom,
                                                                       rec.evaluate_date) - 1
                end;
            -- 公共项/未到期月份
            rec.remaining_months := atr_pack_lrc_func_months_diff(rec.effective_date_bom,
                                                                  rec.expiry_date_eom) -
                                    atr_pack_lrc_func_months_diff(rec.effective_date_bom,
                                                                  rec.evaluate_date);
            -- 还有几个月起保
            rec.future_months := greatest(atr_pack_lrc_func_months_diff(rec.evaluate_date,
                                                                        rec.effective_date_bom),
                                          0);
            -- PAS输入频率
            rec.pas_frequency := atr_pack_ecf_util_func_get_quota_value('BE003',
                                                                        p_buss.action_no,
                                                                        rec.id,
                                                                        'U',
                                                                        -1,
                                                                        1,
                                                                        null);
            -- MORTGAGE RISK IND
            rec.mortgage_risk_ind := atr_pack_ecf_util_func_get_quota_value('BE002',
                                                                            p_buss.action_no,
                                                                            rec.id,
                                                                            'U',
                                                                            -1,
                                                                            null,
                                                                            0);
            -- 风险附加次数
            rec.risk_expansion := atr_pack_ecf_util_func_get_quota_value('BE006',
                                                                         p_buss.action_no,
                                                                         rec.id,
                                                                         'U',
                                                                         -1,
                                                                         null,
                                                                         null);
            -- 累计已收保费
            select coalesce(sum(p.paid_premium), 0) *
                   (case
                        when v_reversed_is = '1' then
                            -1
                        else
                            1
                       end)
            into rec.cumulative_paid_premium
            from atr_dap_ti_premium_paid p
            where p.entity_id = p_buss.entity_id
              and p.treaty_no = v_src_treaty_no
              and p.year_month <= p_buss.year_month;

            -- 已赚比例(合约)
            rec.ul_underwritten_rate := (case
                                             when rec.pas_frequency = '0' then
                                                 1
                                             when rec.pas_frequency = '3' then
                                                 (case
                                                      when rec.passed_months > 12 then
                                                          1
                                                      else
                                                          atr_pack_ecf_util_func_sum_quota_value('BE004',
                                                                                                 p_buss.action_no,
                                                                                                 rec.id,
                                                                                                 'U',
                                                                                                 1,
                                                                                                 12)
                                                     end)
                                             when rec.passed_months > 24 then
                                                 1
                                             else
                                                 atr_pack_ecf_util_func_sum_quota_value('BE004',
                                                                                        p_buss.action_no,
                                                                                        rec.id,
                                                                                        'U',
                                                                                        1,
                                                                                        rec.passed_months)
                end);

            --  预估总分保保费(合约)
            rec.est_total_ri_premium := rec.gross_premium;

            /* 底层保单   start */
            if rec.main_treaty_type = 'T' then
                --  init
                insert into atr_buss_ti_lrc_icu_ul_calc
                (id,
                 main_id,
                 serial_no,
                 ri_premium,
                 effective_date,
                 wo_notice_days_rate)
                select nextval('atr_seq_buss_ti_lrc_icu_ul_calc'), x.*
                from (select rec.id                                                       main_id,
                             dev.dev_no + 1                                               serial_no,
                             -- 底层保单的分出保费
                             rec.est_total_ri_premium / rec.risk_expansion,
                             -- 底层保单的起保日期
                             rec.effective_date_bom + dev.dev_no * '1 month'::interval as effective_date,
                             -- 终止合同提前告知天数的底层保单的比例
                             1                                                            wo_notice_days_rate
                      from atr_conf_dev_no dev
                      where dev.dev_no >= 0
                        and dev.dev_no < rec.risk_expansion
                      order by dev.dev_no) x;

                -- 底层保单的保险止期
                update atr_buss_ti_lrc_icu_ul_calc t
                set -- 底层保单的保险止期
                    expiry_date = (t.effective_date +
                                   rec.risk_expansion * '1 month'::interval) - '1 day'::interval
                where t.main_id = rec.id;

                update atr_buss_ti_lrc_icu_ul_calc t
                set -- 底层保单是否在主保单起终保内
                    within_ri_cb_is = (case
                                           when rec.expiry_date_in_date >
                                                t.effective_date then
                                               '1'
                                           else
                                               '0'
                        end),
                    -- 底层保单是否已起保
                    written_is      = (case
                                           when rec.evaluate_date >= t.effective_date then
                                               '1'
                                           else
                                               '0'
                        end),
                    -- 底层保单是否已终保
                    expired_is      = (case
                                           when rec.evaluate_date >= t.expiry_date then
                                               '1'
                                           else
                                               '0'
                        end)
                where t.main_id = rec.id;

                update atr_buss_ti_lrc_icu_ul_calc t
                set -- 未到期月份
                    remaining_months = (case
                                            when t.expired_is = '1' then
                                                0
                                            else
                                                atr_pack_lrc_func_months_diff(t.effective_date,
                                                                              t.expiry_date) -
                                                atr_pack_lrc_func_months_diff(t.effective_date,
                                                                              rec.evaluate_date)
                        end),
                    -- 底层保单还有多少个月起保
                    future_months    = (case
                                            when t.within_ri_cb_is = '0' then
                                                0
                                            when t.written_is = '1' then
                                                0
                                            when t.expired_is = '1' then
                                                0
                                            else
                                                greatest(atr_pack_lrc_func_months_diff(rec.evaluate_date,
                                                                                       t.effective_date),
                                                         0)
                        end)
                where t.main_id = rec.id;

                -- init 底层保单明细
                insert into atr_buss_ti_lrc_icu_ul_calc_detail
                    (id, main_id, serial_no, dev_no)
                select nextval('atr_seq_buss_ti_lrc_icu_ul_calc_detail'), x.*
                from (select u.main_id, serial_no, dev.dev_no
                      from atr_buss_ti_lrc_icu_ul_calc u,
                           atr_conf_dev_no dev
                      where dev.dev_no >= 0
                        and dev.dev_no <= u.remaining_months
                        and u.main_id = rec.id
                      order by u.id, dev.dev_no) x;

            end if;
            /* 底层保单   end */

            -- 明细表 初始化
            insert into atr_buss_ti_lrc_icu_calc_detail
                (id, main_id, dev_no)
            select nextval('atr_seq_buss_ti_lrc_icu_calc_detail'), x.*
            from (select rec.id, d.dev_no
                  from atr_conf_dev_no d
                  where d.dev_no <= (case
                                         when rec.main_treaty_type = 'T' then
                                             greatest(coalesce(rec.remaining_months, 0),
                                                      coalesce((select max(ud.dev_no)
                                                                from atr_buss_ti_lrc_icu_ul_calc_detail ud
                                                                where ud.main_id = rec.id),
                                                               0),
                                                      1)
                                         else
                                             greatest(coalesce(rec.remaining_months, 0), 1)
                      end)
                  order by d.dev_no) x;

            -- after_premium_impairment_rate
            update atr_buss_ti_lrc_icu_calc_detail t
            set after_premium_impairment_rate = atr_pack_ecf_util_func_get_quota_value(
                    'BE001',
                    p_buss.action_no,
                    t.main_id,
                    'U',
                    t.dev_no,
                    1,
                    null)
            where t.main_id = rec.id;

            -- RIF Run-Off Pattern
            if rec.mortgage_risk_ind = '1' then
                update atr_buss_ti_lrc_icu_calc_detail t
                set rif_run_off_pattern =
                        (select qd.quota_value
                         from atr_conf_mortg_quota_period qd,
                              atr_conf_mortg_quota qm
                         where qm.mortg_quota_id = qd.mortg_quota_id
                           and qm.entity_id = rec.entity_id
                           and qm.policy_no = v_src_treaty_no)
                where t.main_id = rec.id;
            end if;

            /* ** recv premium  ***/
            -- 未到期月份(应收保费计算专用)
            rec.remaining_months_for_recv := (case
                                                  when rec.expiry_date_eom <=
                                                       rec.evaluate_date then
                                                      0
                                                  else
                                                      atr_pack_lrc_func_months_diff(rec.evaluate_date,
                                                                                    rec.expiry_date_eom)
                end);

            -- 剩余未交费期次（评估日期-终保日期）
            rec.remaining_prem_term_pe := (case
                                               when rec.passed_months = 0 then
                                                   rec.payment_frequency_no
                                               when rec.payment_frequency_code = 'S' then
                                                   0
                                               when rec.payment_frequency_code = 'M' then
                                                   greatest(rec.remaining_months_for_recv, 0)
                                               when rec.payment_frequency_code = 'Q' then
                                                   greatest(rec.payment_frequency_no -
                                                            trunc((rec.passed_months - 1) / 3),
                                                            0)
                                               when rec.payment_frequency_code = 'Y' then
                                                   (case
                                                        when extract(day from
                                                                     rec.effective_date_in_date) = 1 then
                                                            greatest(rec.payment_frequency_no -
                                                                     greatest(floor((rec.passed_months - 1) / 12),
                                                                              0),
                                                                     0)
                                                        else
                                                            greatest(rec.payment_frequency_no -
                                                                     greatest(floor((rec.passed_months) / 12),
                                                                              0),
                                                                     0)
                                                       end)
                                               else
                                                   0
                end);

            -- 未到期缴费期次(评估日期-合同边界日期)
            rec.remaining_prem_term_cb := rec.remaining_prem_term_pe;

            -- 应收保费
            rec.recv_premium := greatest(rec.est_total_ri_premium -
                                         rec.cumulative_paid_premium,
                                         0);

            -- 发展期
            update atr_buss_ti_lrc_icu_calc_detail t
            set recv_premium = (case
                -- 当期取 实收保费
                                    when t.dev_no = 0 then
                                        coalesce((select sum(p.paid_premium)
                                                  from atr_dap_ti_premium_paid p
                                                  where p.entity_id = rec.entity_id
                                                    and p.treaty_no = v_src_treaty_no
                                                    and p.year_month = p_buss.year_month),
                                                 0)
                                    when rec.remaining_months_for_recv = 0 then
                                        case
                                            when t.dev_no = 1 then
                                                rec.recv_premium
                                            else
                                                0
                                            end
                                    when rec.payment_frequency_code = 'S' then
                                        case
                                            when t.dev_no = 1 then
                                                rec.recv_premium
                                            else
                                                0
                                            end
                                    else
                                        (case
                                             when rec.payment_frequency_code = 'M' then
                                                 (case
                                                      when t.dev_no > rec.remaining_prem_term_cb then
                                                          0
                                                      else
                                                          rec.recv_premium
                                                     end)
                                             when rec.payment_frequency_code = 'Q' then
                                                 (case
                                                      when t.dev_no / 3.0 >
                                                           rec.remaining_prem_term_cb then
                                                          0
                                                      else
                                                          rec.recv_premium *
                                                          (floor((t.dev_no + mod(rec.passed_months, 3)) / 3) -
                                                           floor((t.dev_no - 1 +
                                                                  mod(rec.passed_months, 3)) / 3))
                                                     end)
                                             when rec.payment_frequency_code = 'Y' then
                                                 (case
                                                      when t.dev_no / 12.0 >
                                                           rec.remaining_prem_term_cb then
                                                          0
                                                      else
                                                          rec.recv_premium *
                                                          (floor((t.dev_no +
                                                                  mod(rec.passed_months, 12)) / 12) -
                                                           floor((t.dev_no - 1 +
                                                                  mod(rec.passed_months, 12)) / 12))
                                                     end)
                                            end) /
                                        greatest(rec.remaining_prem_term_cb, 1)
                end) * atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                              1)
            where t.main_id = rec.id;

            /* ** ed premium backup ***/
            if rec.main_treaty_type = 'X' then
                -- 已赚保费(每个覆盖日)
                rec.ed_premium_per_coverage_day := case
                                                       when rec.effective_date_in_date <=
                                                            rec.expiry_date_in_date then
                                                           rec.est_total_ri_premium /
                                                           (cast(rec.expiry_date_in_date as date) -
                                                            cast(rec.effective_date_in_date as date) + 1)
                    end;

                -- 发展期
                update atr_buss_ti_lrc_icu_calc_detail t
                set ed_premium_backup = (case
                                             when t.dev_no > rec.remaining_months then
                                                 0
                                             when t.dev_no < rec.future_months then
                                                 0
                                             when t.dev_no = rec.remaining_months then
                                                 rec.ed_premium_per_coverage_day *
                                                 extract(day from
                                                         rec.expiry_date_in_date)
                                             else
                                                 rec.ed_premium_per_coverage_day *
                                                 atr_pack_lrc_func_get_month_days(p_buss.year_month,
                                                                                  t.dev_no)
                    end)
                where t.main_id = rec.id
                  and t.dev_no > 0;

                -- 发展期 汇总 到主表
                select sum(d.ed_premium_backup)
                into rec.ed_premium_backup
                from atr_buss_ti_lrc_icu_calc_detail d
                where d.main_id = rec.id;

            else
                -- 已赚保费(每个覆盖月份)
                update atr_buss_ti_lrc_icu_ul_calc t
                set ed_premium_per_month = case
                                               when atr_pack_lrc_func_months_diff(t.effective_date,
                                                                                  t.expiry_date) > 0 then
                                                   t.ri_premium /
                                                   atr_pack_lrc_func_months_diff(t.effective_date,
                                                                                 t.expiry_date)
                    end
                where t.main_id = rec.id;

                -- 底层保单发展期
                update atr_buss_ti_lrc_icu_ul_calc_detail t
                set ed_premium_backup =
                        (select (case
                                     when t.dev_no > u.remaining_months then
                                         0
                                     when t.dev_no < u.future_months then
                                         0
                                     when u.future_months > 0 and
                                          t.dev_no = u.remaining_months then
                                         -- 尾差
                                         u.ri_premium -
                                         u.ed_premium_per_month *
                                         (u.remaining_months - u.future_months)
                                     else
                                         u.ed_premium_per_month
                            end)
                         from atr_buss_ti_lrc_icu_ul_calc u
                         where u.main_id = t.main_id
                           and u.serial_no = t.serial_no)
                where t.main_id = rec.id
                  and t.dev_no > 0;

                -- 汇总 明细表
                update atr_buss_ti_lrc_icu_calc_detail t
                set ed_premium_backup =
                        (select sum(uld.ed_premium_backup)
                         from atr_buss_ti_lrc_icu_ul_calc_detail uld
                         where uld.main_id = t.main_id
                           and uld.dev_no = t.dev_no)
                where t.dev_no > 0
                  and t.main_id = rec.id;

                -- 汇总 主表
                select sum(d.ed_premium_backup)
                into rec.ed_premium_backup
                from atr_buss_ti_lrc_icu_calc_detail d
                where d.main_id = rec.id;

            end if;

            /* ** ue premium ***/
            if rec.main_treaty_type = 'X' then

                update atr_buss_ti_lrc_icu_calc_detail t
                set ue_premium = (case
                                      when t.dev_no = 0 then
                                          rec.ed_premium_backup
                                      when t.dev_no >= rec.remaining_months then
                                          0
                                      when rec.gross_premium > 0 then
                                          greatest(rec.ed_premium_backup -
                                                   (select sum(d2.ed_premium_backup)
                                                    from atr_buss_ti_lrc_icu_calc_detail d2
                                                    where d2.main_id = rec.id
                                                      and d2.dev_no > 0
                                                      and d2.dev_no <= t.dev_no),
                                                   0)
                                      else
                                          least(rec.ed_premium_backup -
                                                (select sum(d2.ed_premium_backup)
                                                 from atr_buss_ti_lrc_icu_calc_detail d2
                                                 where d2.main_id = rec.id
                                                   and d2.dev_no > 0
                                                   and d2.dev_no <= t.dev_no),
                                                0)
                    end)
                where t.main_id = rec.id;

            else

                -- 底层保单发展期
                merge into atr_buss_ti_lrc_icu_ul_calc_detail t
                using (select ud.id,
                              (case
                                   when ud.dev_no = 0 then
                                       x.sum_ed_premium
                                   when ud.dev_no >= ul.remaining_months then
                                       0
                                   when rec.gross_premium > 0 then
                                       greatest(x.sum_ed_premium -
                                                (select coalesce(sum(ud3.ed_premium_backup), 0)
                                                 from atr_buss_ti_lrc_icu_ul_calc_detail ud3
                                                 where ud3.main_id = ud.main_id
                                                   and ud3.serial_no = ud.serial_no
                                                   and ud3.dev_no > 0
                                                   and ud3.dev_no <= ud.dev_no),
                                                0)
                                   else
                                       least(x.sum_ed_premium -
                                             (select coalesce(sum(ud3.ed_premium_backup), 0)
                                              from atr_buss_ti_lrc_icu_ul_calc_detail ud3
                                              where ud3.main_id = ud.main_id
                                                and ud3.serial_no = ud.serial_no
                                                and ud3.dev_no > 0
                                                and ud3.dev_no <= ud.dev_no),
                                             0)
                                  end) as ue_premium
                       from atr_buss_ti_lrc_icu_ul_calc_detail ud,
                            atr_buss_ti_lrc_icu_ul_calc ul,
                            (select ud2.serial_no,
                                    sum(ud2.ed_premium_backup) sum_ed_premium
                             from atr_buss_ti_lrc_icu_ul_calc_detail ud2
                             where ud2.main_id = rec.id
                             group by ud2.serial_no) x
                       where ud.main_id = rec.id
                         and ud.serial_no = x.serial_no
                         and ul.main_id = rec.id
                         and ul.serial_no = x.serial_no) x2
                on (t.id = x2.id)
                when matched then
                    update set ue_premium = x2.ue_premium;

                -- 汇总 明细表
                update atr_buss_ti_lrc_icu_calc_detail t
                set ue_premium =
                        (select sum(ud.ue_premium)
                         from atr_buss_ti_lrc_icu_ul_calc_detail ud
                         where ud.main_id = t.main_id
                           and ud.dev_no = t.dev_no)
                where t.main_id = rec.id;

            end if;

            /* ** ed premium ***/
            -- 主表
            rec.ed_premium := rec.ed_premium_backup;

            -- 发展期
            update atr_buss_ti_lrc_icu_calc_detail t
            set ed_premium = t.ed_premium_backup
            where t.main_id = rec.id;

            -- 底层保单发展期
            update atr_buss_ti_lrc_icu_ul_calc_detail t
            set ed_premium = t.ed_premium_backup
            where t.main_id = rec.id;

            /* ** no dependent dev  ***/

            update atr_buss_ti_lrc_icu_calc_detail t
            set --- recv_rel_fee
                -- 调整手续费
                adj_commission      = case
                                          when t.dev_no > 0 then
                                              abs(t.recv_premium *
                                                  rec.adj_commission_rate /
                                                  atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                         1))
                    end,
                -- 经纪人费用
                brokerage_fee       = case
                                          when t.dev_no > 0 then
                                              abs(t.recv_premium * rec.brokerage_fee_rate /
                                                  atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                         1))
                    end,
                -- 跟单获取费用
                iacf_fee            = t.recv_premium * rec.iacf_fee_rate /
                                      atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                             1),
                --- ed_rel_fee
                -- 维持费用
                maintenance_fee     = case
                                          when t.dev_no > 0 then
                                              greatest(coalesce(t.ed_premium *
                                                                rec.maintenance_fee_rate,
                                                                0),
                                                       0)
                    end,
                -- 非跟单获取费用
                iacf_fee_non_policy = case
                                          when t.dev_no > 0 then
                                              t.ed_premium *
                                              rec.iacf_fee_rate_non_policy
                    end,
                --- gep_uwq
                gep_uwq             = t.ed_premium
            where t.main_id = rec.id;

            /* ** coverage_amount */
            if rec.main_treaty_type = 'X' then
                -- 超赔合约
                update atr_buss_ti_lrc_icu_calc_detail t
                set coverage_amount = (case
                                           when t.dev_no = 0 then
                                               0
                                           when t.dev_no < rec.future_months then
                                               0
                                           when t.dev_no > rec.remaining_months then
                                               0
                                           else
                                               rec.coverage_amount *
                                               coalesce(t.ed_premium /
                                                        atr_pack_lrc_nvl0(rec.ed_premium_per_coverage_day,
                                                                          null),
                                                        1)
                    end)
                where t.main_id = rec.id;

            elsif rec.mortgage_risk_ind = '1' then
                -- 抵押风险的比例合约
                update atr_buss_ti_lrc_icu_calc_detail t
                set coverage_amount = (case
                                           when t.dev_no = 0 then
                                               0
                                           when t.dev_no < rec.future_months then
                                               0
                                           when t.dev_no > rec.remaining_months then
                                               0
                                           else
                                               rec.coverage_amount *
                                               atr_pack_ecf_util_nvl0(t.rif_run_off_pattern,
                                                                      1)
                    end)
                where t.main_id = rec.id;

            else
                -- 其他比例合约
                -- 底层保单发展期
                update atr_buss_ti_lrc_icu_ul_calc_detail t
                set coverage_amount =
                        (select case
                                    when t.dev_no = 0 then
                                        0
                                    when t.dev_no < u.future_months then
                                        0
                                    when t.dev_no > u.remaining_months then
                                        0
                                    else
                                        rec.coverage_amount
                                    end
                         from atr_buss_ti_lrc_icu_ul_calc u
                         where u.main_id = t.main_id
                           and u.serial_no = t.serial_no)
                where t.main_id = rec.id;

                -- 汇总到 明细表 中
                update atr_buss_ti_lrc_icu_calc_detail t
                set coverage_amount =
                        (select sum(ud.coverage_amount)
                         from atr_buss_ti_lrc_icu_ul_calc_detail ud
                         where ud.main_id = t.main_id
                           and ud.dev_no = t.dev_no)
                where t.main_id = rec.id;

            end if;

            /* ** ue_premium_per_month  ***/
            -- (上期)当期期末剩余未摊销比例
            -- (上期)截至报告期初剩余未摊销比例
            begin
                if p_pre_action_no is not null then
                    select pre.cur_end_remain_csm_rate,
                           pre.until_report_remain_csm_rate
                    into rec.pri_cur_end_remain_csm_rate,
                        rec.pri_until_report_remain_csm_rate
                    from atr_buss_ti_lrc_icu_calc pre
                    where pre.action_no = p_pre_action_no
                      and pre.treaty_no = rec.treaty_no
                      and coalesce(pre.dept_id, -1) = coalesce(rec.dept_id, -1)
                      and pre.currency_code = rec.currency_code;
                end if;
            exception
                when no_data_found then
                    null;
            end;

            -- (上期)当期期末剩余未摊销比例
            rec.pri_cur_end_remain_csm_rate := coalesce(rec.pri_cur_end_remain_csm_rate, 1);
            -- (上期)截至报告期初剩余未摊销比例
            rec.pri_until_report_remain_csm_rate := coalesce(rec.pri_until_report_remain_csm_rate, 1);

            rec.until_report_remain_csm_rate := case
                                                    when rec.treaty_no like '%#%' and
                                                         rec.reversed_flag = '1' then
                                                        1
                                                    when rec.treaty_no not like '%#%' and
                                                         substr(rec.dap_year_month, 1, 4) =
                                                         substr(p_buss.year_month, 1, 4) then
                                                        1
                                                    when v_cur_month = '01' then
                                                        --  (上期)当期期末剩余未摊销比例
                                                        rec.pri_cur_end_remain_csm_rate
                                                    else
                                                        -- (上期)截至报告期初剩余未摊销比例
                                                        rec.pri_until_report_remain_csm_rate
                end;

            begin
                select case
                           when rec.gross_premium = 0 then
                               0
                           else
                               d.ue_premium / rec.gross_premium
                           end
                into rec.cur_end_remain_csm_rate
                from atr_buss_ti_lrc_icu_calc_detail d
                where d.main_id = rec.id
                  and d.dev_no = 0;
            exception
                when no_data_found then
                    null;
            end;

            -- 当期期末剩余未摊销比例
            rec.cur_end_remain_csm_rate := coalesce(rec.cur_end_remain_csm_rate, 0);
            -- 累计已赚比例
            rec.cumulative_ed_rate := 1 - rec.cur_end_remain_csm_rate;

            /* ** ultimate_loss_tta **/
            merge into atr_buss_ti_lrc_icu_calc_detail t
            using (select q.dev_no + t.dev_no - 1                               as dev_no,
                          sum(t.gep_uwq * coalesce(rec.elr, 1) * q.quota_value) as ultimate_loss
                   from atr_buss_ti_lrc_icu_calc_detail t,
                        atr_buss_quota_value q
                   where t.main_id = rec.id
                     and t.dev_no > 0
                     and q.dev_no > 0
                     and rec.loa_code = q.dimension_value
                     and rec.action_no = q.action_no
                     and q.dimension = 'G'
                     and q.quota_code = 'QP001'
                   group by t.main_id, q.dev_no + t.dev_no - 1) x
            on (t.main_id = rec.id and t.dev_no = x.dev_no)
            when matched then
                update set ultimate_loss = x.ultimate_loss
            when not matched then
                insert
                    (id, main_id, dev_no, ultimate_loss)
                values (nextval('atr_seq_buss_ti_lrc_icu_calc_detail'),
                        rec.id,
                        x.dev_no,
                        x.ultimate_loss);

            /* ** ultimate_loss_mortgage  ***/
            if rec.mortgage_risk_ind = '1' then

                begin
                    select t.val_date_loan_os, t.delinquency_rate, t.default_rate
                    into -- Val Date Loan O/S
                        rec.loan_os,
                        -- Delinquency Ratio
                        rec.delinquency_rate,
                        -- Default Ratio
                        rec.default_rate
                    from atr_conf_mortg_quota t
                    where t.entity_id = rec.entity_id
                      and t.policy_no = v_src_treaty_no;
                exception
                    when no_data_found then
                        null;
                end;

                -- After Future NB Projection Loan O/S
                rec.afnp_loan_os := rec.loan_os / (case rec.ul_underwritten_rate
                                                       when 0 then
                                                           1
                    end);

                -- RIF% Run-Off Pattern
                update atr_buss_ti_lrc_icu_calc_detail t
                set rif_run_off_pattern_rate = (case
                                                    when coalesce(t.rif_run_off_pattern, 0) = 0 then
                                                        0
                                                    else
                                                        t.rif_run_off_pattern /
                                                        atr_pack_ecf_util_nvl0((select d2.rif_run_off_pattern
                                                                                from atr_buss_ti_lrc_icu_calc_detail d2
                                                                                where d2.main_id =
                                                                                      t.main_id
                                                                                  and d2.dev_no = 1),
                                                                               null)
                    end)
                where t.dev_no > 0
                  and t.main_id = rec.id;

                -- Ultimate Loss Mortgage
                update atr_buss_ti_lrc_icu_calc_detail t
                set ultimate_loss_mortgage =
                        (t.rif_run_off_pattern_rate * rec.afnp_loan_os *
                         rec.delinquency_rate * rec.default_rate)
                where t.main_id = rec.id
                  and t.dev_no > 0
                  and t.dev_no <= rec.dev_val;

            end if;

            /* ** update main  ***/
            update atr_buss_ti_lrc_icu_calc t
            set dev_val                          = rec.dev_val,
                pas_frequency                    = rec.pas_frequency,
                passed_months                    = rec.passed_months,
                remaining_months                 = rec.remaining_months,
                future_months                    = rec.future_months,
                mortgage_risk_ind                = rec.mortgage_risk_ind,
                ul_underwritten_rate             = rec.ul_underwritten_rate,
                est_total_ri_premium             = rec.est_total_ri_premium,
                risk_expansion                   = rec.risk_expansion,
                adj_commission_rate              = rec.adj_commission_rate,
                brokerage_fee_rate               = rec.brokerage_fee_rate,
                iacf_fee_rate                    = rec.iacf_fee_rate,
                iacf_fee_rate_non_policy         = rec.iacf_fee_rate_non_policy,
                maintenance_fee_rate             = rec.maintenance_fee_rate,
                remaining_prem_term_pe           = rec.remaining_prem_term_pe,
                remaining_months_for_recv        = rec.remaining_months_for_recv,
                remaining_prem_term_cb           = rec.remaining_prem_term_cb,
                cumulative_paid_premium          = rec.cumulative_paid_premium,
                recv_premium                     = rec.recv_premium,
                ed_premium_per_coverage_day      = rec.ed_premium_per_coverage_day,
                ed_premium_backup                = rec.ed_premium_backup,
                ed_premium                       = rec.ed_premium,
                pri_cur_end_remain_csm_rate      = rec.pri_cur_end_remain_csm_rate,
                pri_until_report_remain_csm_rate = rec.pri_until_report_remain_csm_rate,
                cumulative_ed_rate               = rec.cumulative_ed_rate,
                cur_end_remain_csm_rate          = rec.cur_end_remain_csm_rate,
                until_report_remain_csm_rate     = rec.until_report_remain_csm_rate,
                elr                              = rec.elr,
                loan_os                          = rec.loan_os,
                afnp_loan_os                     = rec.afnp_loan_os,
                delinquency_rate                 = rec.delinquency_rate,
                default_rate                     = rec.default_rate
            where t.id = rec.id;

            /* ** end  ***/

            if mod(v_count, 10) = 0 then
                call atr_pack_lrc_proc_debug(p_buss.action_no, v_count::varchar, 'calc_icu', p_thread_no);
                commit;
            end if;
        end loop;

    call atr_pack_lrc_proc_debug(p_buss.action_no, v_count::varchar, 'calc_icu-end', p_thread_no);
    commit;

end;
$_$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_icu_to(IN p_buss atruser.atr_pack_lrc_record_buss, IN p_pre_action_no character varying, IN p_threads integer, IN p_thread_no integer)
    LANGUAGE plpgsql
    AS $_$
declare
    v_cur_month      numeric(2)  := substr(p_buss.year_month, 5, 2);
    v_count          numeric(18) := 0;
    v_reversed_is    varchar(1);
    v_src_treaty_no  varchar(60);
    rec              record;
    v_cost_start     timestamp(6);
    v_cost           numeric(19, 6)[];
    v_cost_str       varchar(1000);
    v_pre_year_month varchar(6)  := atr_pack_ecf_util_func_get_previos_year_month(p_buss.year_month);
    v_pre_action_no  varchar(60);
begin

    begin
        select t.action_no
        into v_pre_action_no
        from atr_buss_lrc_action t
        where t.business_source_code = 'TO'
          and t.year_month = v_pre_year_month
          and t.confirm_is = '1';
    exception
        when no_data_found then
            v_pre_action_no := null;
    end;

    /*  真实底层保单发展期 临时表 start  */
    drop table if exists tmp_atr_buss_to_lrc_icu_rep_calc_detail;

    create temp table tmp_atr_buss_to_lrc_icu_rep_calc_detail
    as
    select d.serial_no,
           d.dev_no,
           d.ed_premium,
           d.ue_premium,
           d.coverage_amount,
           r.ri_premium,
           r.remaining_months
    from atr_buss_to_lrc_icu_rep_calc r,
         atr_buss_to_lrc_icu_rep_calc_detail d
    where false;
    /*  真实底层保单发展期 临时表 end  */

    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'calc_icu_to-start', p_thread_no);

    for rec in (select t.*,
                       date_trunc('month', t.evaluate_date)       as evaluate_date_bom,
                       date_trunc('month', t.expiry_date_in_date) as expiry_date_bom
                from atr_buss_to_lrc_icu_calc t
                where action_no = p_buss.action_no
                  and mod(id, p_threads) = p_thread_no
                order by id)
        loop
            v_count := v_count + 1;

            if v_count = 1 then
                call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'calc_icu_to-start-loop', p_thread_no);
            end if;

            v_cost_start := clock_timestamp();

            /*  真实底层保单 临时表 start  */
            drop table if exists tmp_atr_buss_to_lrc_icu_rep_calc;

            create temp table tmp_atr_buss_to_lrc_icu_rep_calc as
            select t.*,
                   date_trunc('month', t.effective_date) as effective_date_bom,
                   date_trunc('month', t.expiry_date)    as expiry_date_bom
            from atr_buss_to_lrc_icu_rep_calc t
            where t.main_id = rec.id;

            create index idx_tmp_atr_buss_to_lrc_icu_rep_calc
                on tmp_atr_buss_to_lrc_icu_rep_calc (serial_no);
            /*  真实底层保单 临时表 end    */


            v_cost[1] := coalesce(v_cost[1], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            v_reversed_is := case
                                 when rec.treaty_no like '%#-1' then
                                     '1'
                                 else
                                     '0'
                end;
            v_src_treaty_no := regexp_replace(rec.treaty_no, '#.+$', '');


            v_cost[2] := coalesce(v_cost[2], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** common  ***/
            -- elr
            rec.elr := atr_pack_ecf_util_func_get_quota_value('BE013',
                                                              p_buss.action_no,
                                                              rec.id,
                                                              'U',
                                                              -1,
                                                              null,
                                                              null);

            -- 发展期变量
            rec.dev_val := atr_pack_ecf_util_func_get_quota_value('BE008',
                                                                  p_buss.action_no,
                                                                  rec.id,
                                                                  'U',
                                                                  -1,
                                                                  null,
                                                                  null);
            -- 调整手续费比例
            rec.adj_commission_rate := atr_pack_ecf_util_func_get_quota_value('QR010',
                                                                              p_buss.action_no,
                                                                              rec.id,
                                                                              'U',
                                                                              -1,
                                                                              null,
                                                                              null);
            -- 经纪人费用比例
            rec.brokerage_fee_rate := atr_pack_ecf_util_func_get_quota_value('BE005',
                                                                             p_buss.action_no,
                                                                             rec.id,
                                                                             'U',
                                                                             -1,
                                                                             null,
                                                                             null);
            -- 维持费用比例
            rec.maintenance_fee_rate := atr_pack_ecf_util_func_get_quota_value('QR003',
                                                                               p_buss.action_no,
                                                                               rec.id,
                                                                               'U',
                                                                               -1,
                                                                               null,
                                                                               null);
            -- 已过月份
            rec.passed_months := (case
                                      when rec.effective_date_bom > rec.evaluate_date then
                                          0
                                      when extract(day from rec.effective_date_in_date) = 1 then
                                          extract(year from age(rec.evaluate_date_bom, rec.effective_date_bom)) * 12 +
                                          extract(month from age(rec.evaluate_date_bom, rec.effective_date_bom))
                                      else
                                          extract(year from age(rec.evaluate_date_bom, rec.effective_date_bom)) * 12 +
                                          extract(month from age(rec.evaluate_date_bom, rec.effective_date_bom)) - 1
                end);
            -- 未到期月份
            rec.remaining_months := extract(year from age(rec.expiry_date_bom, rec.evaluate_date_bom)) * 12 +
                                    extract(month from age(rec.expiry_date_bom, rec.evaluate_date_bom));
            -- 还有几个月起保
            rec.future_months := greatest(extract(year from age(rec.effective_date_bom, rec.evaluate_date_bom)) * 12 +
                                          extract(month from age(rec.effective_date_bom, rec.evaluate_date_bom)),
                                          0);
            -- MORTGAGE RISK IND
            rec.mortgage_risk_ind := atr_pack_ecf_util_func_get_quota_value('BE002',
                                                                            p_buss.action_no,
                                                                            rec.id,
                                                                            'U',
                                                                            -1,
                                                                            null,
                                                                            0);
            -- 已赚比例(合约)
            rec.ul_underwritten_rate := least((rec.evaluate_date -
                                               rec.effective_date_bom)::numeric /
                                              (rec.expiry_date_eom -
                                               rec.effective_date_bom),
                                              1);
            -- 风险附加次数
            rec.risk_expansion := atr_pack_ecf_util_func_get_quota_value('BE006',
                                                                         p_buss.action_no,
                                                                         rec.id,
                                                                         'U',
                                                                         -1,
                                                                         null,
                                                                         null);


            v_cost[3] := coalesce(v_cost[3], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 累计已收保费
            if rec.main_treaty_type = 'T' then
                -- 比例合约的实收保费在构造应收保费表时特殊处理了
                select coalesce(sum(r.cumulative_paid_premium), 0)
                into rec.cumulative_paid_premium
                from atr_buss_to_lrc_icu_recv r
                where r.main_id = rec.id;
            else
                select coalesce(sum(p.paid_premium + coalesce(p.commission, 0)), 0) *
                       (case
                            when v_reversed_is = '1' then
                                -1
                            else
                                1
                           end)
                into rec.cumulative_paid_premium
                from atr_dap_to_premium_paid p
                where p.entity_id = rec.entity_id
                  and p.treaty_no = v_src_treaty_no
                  and p.year_month <= p_buss.year_month;
            end if;

            -- 预估总分保保费(合约)
            rec.est_total_ri_premium := rec.gross_premium;


            v_cost[4] := coalesce(v_cost[4], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 真实底层保单
            if rec.main_treaty_type = 'T' then


                v_cost[5] := coalesce(v_cost[5], 0) + extract(epoch from clock_timestamp() - v_cost_start);
                v_cost_start := clock_timestamp();

                update tmp_atr_buss_to_lrc_icu_rep_calc t
                set -- 未到期月份
                    remaining_months =
                        extract(year from age(t.expiry_date_bom, rec.evaluate_date_bom)) * 12 +
                        extract(month from age(t.expiry_date_bom, rec.evaluate_date_bom)),
                    -- 底层保单还有多少个月起保
                    future_months    =
                        greatest(extract(year from age(t.effective_date_bom, rec.evaluate_date_bom)) * 12 +
                                 extract(month from age(t.effective_date_bom, rec.evaluate_date_bom)),
                                 0)
                where t.main_id = rec.id;


                v_cost[6] := coalesce(v_cost[6], 0) + extract(epoch from clock_timestamp() - v_cost_start);
                v_cost_start := clock_timestamp();

                drop index if exists idx_tmp_atr_buss_to_lrc_icu_rep_calc_detail;

                -- init 发展期
                insert into tmp_atr_buss_to_lrc_icu_rep_calc_detail
                (serial_no, dev_no, ue_premium, coverage_amount, ri_premium, remaining_months)
                select r.serial_no,
                       dev.dev_no,
                       (case
                            when dev.dev_no >= r.remaining_months then
                                0
                            else
                                r.ri_premium *
                                (r.expiry_date -
                                 (p_buss.ev_date + dev.dev_no *
                                                   '1 month'::interval - '1 day'::interval)::date)::numeric /
                                (r.expiry_date - r.effective_date + 1)
                           end) as ue_premium,
                       (case
                            when dev.dev_no = 0 then
                                0
                            when dev.dev_no < r.future_months then
                                0
                            when dev.dev_no > r.remaining_months then
                                0
                            else
                                r.coverage_amount
                           end) as coverage_amount,
                       r.ri_premium,
                       r.remaining_months
                from tmp_atr_buss_to_lrc_icu_rep_calc r,
                     atr_conf_dev_no dev
                where dev.dev_no >= 0
                  and dev.dev_no <= r.remaining_months
                  and r.main_id = rec.id;

                commit;

                v_cost[7] := coalesce(v_cost[7], 0) + extract(epoch from clock_timestamp() - v_cost_start);
                v_cost_start := clock_timestamp();

                -- 这里理论上可以建唯一索引， 但后面用起来性能比普通索引差
                create index idx_tmp_atr_buss_to_lrc_icu_rep_calc_detail
                    on tmp_atr_buss_to_lrc_icu_rep_calc_detail (serial_no, dev_no);

                v_cost[31] := coalesce(v_cost[31], 0) + extract(epoch from clock_timestamp() - v_cost_start);
                v_cost_start := clock_timestamp();

            end if;


            v_cost[8] := coalesce(v_cost[8], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 合约分入转分出
            if rec.main_treaty_type = 'R' then
                -- init 底层保单
                insert into atr_buss_to_lrc_icu_ul_calc
                (id,
                 main_id,
                 serial_no,
                 ri_premium,
                 effective_date,
                 wo_notice_days_rate)
                select nextval('atr_seq_buss_to_lrc_icu_ul_calc'), x.*
                from (select rec.id                                                       main_id,
                             dev.dev_no + 1                                               serial_no,
                             -- 底层保单的分出保费
                             rec.est_total_ri_premium / rec.risk_expansion,
                             -- 底层保单的起保日期
                             rec.effective_date_bom + dev.dev_no * '1 month'::interval as effective_date,
                             -- 终止合同提前告知天数的底层保单的比例
                             1                                                            wo_notice_days_rate
                      from atr_conf_dev_no dev
                      where dev.dev_no >= 0
                        and dev.dev_no < rec.risk_expansion
                      order by dev.dev_no) x;

                -- 底层保单的保险止期
                update atr_buss_to_lrc_icu_ul_calc t
                set expiry_date = t.effective_date + rec.risk_expansion * '1 month'::interval - '1 day'::interval
                where t.main_id = rec.id;

                update atr_buss_to_lrc_icu_ul_calc t
                set -- 底层保单是否在主保单起终保内
                    within_ri_cb_is = (case
                                           when rec.expiry_date_in_date >
                                                t.effective_date then
                                               '1'
                                           else
                                               '0'
                        end),
                    -- 底层保单是否已起保
                    written_is      = (case
                                           when rec.evaluate_date >= t.effective_date then
                                               '1'
                                           else
                                               '0'
                        end),
                    -- 底层保单是否已终保
                    expired_is      = (case
                                           when rec.evaluate_date >= t.expiry_date then
                                               '1'
                                           else
                                               '0'
                        end)
                where t.main_id = rec.id;

                update atr_buss_to_lrc_icu_ul_calc t
                set -- 未到期月份
                    remaining_months = (case
                                            when t.expired_is = '1' then
                                                0
                                            else
                                                extract(year from
                                                        age(date_trunc('month', t.expiry_date), rec.evaluate_date_bom)) *
                                                12 +
                                                extract(month from
                                                        age(date_trunc('month', t.expiry_date), rec.evaluate_date_bom))
                        end),
                    -- 底层保单还有多少个月起保
                    future_months    = (case
                                            when t.within_ri_cb_is = '0' then
                                                0
                                            when t.written_is = '1' then
                                                0
                                            when t.expired_is = '1' then
                                                0
                                            else
                                                greatest(extract(year from
                                                                 age(date_trunc('month', t.effective_date), rec.evaluate_date_bom)) *
                                                         12 +
                                                         extract(month from
                                                                 age(date_trunc('month', t.effective_date), rec.evaluate_date_bom)),
                                                         0)
                        end)
                where t.main_id = rec.id;

                -- init 底层保单明细
                insert into atr_buss_to_lrc_icu_ul_calc_detail
                    (id, main_id, serial_no, dev_no)
                select nextval('atr_seq_buss_to_lrc_icu_ul_calc_detail'), x.*
                from (select u.main_id, u.serial_no, dev.dev_no
                      from atr_buss_to_lrc_icu_ul_calc u,
                           atr_conf_dev_no dev
                      where dev.dev_no >= 0
                        and dev.dev_no <= u.remaining_months
                        and u.main_id = rec.id
                      order by u.id, dev.dev_no) x;

            end if;


            v_cost[9] := coalesce(v_cost[9], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 初始化发展期
            insert into atr_buss_to_lrc_icu_calc_detail
                (id, main_id, dev_no)
            select nextval('atr_seq_buss_to_lrc_icu_calc_detail'), x.*
            from (select rec.id as main_id, d.dev_no
                  from atr_conf_dev_no d
                  where d.dev_no <= (case
                                         when rec.main_treaty_type = 'R' then
                                             greatest(coalesce(rec.remaining_months, 0),
                                                      coalesce((select max(ud.dev_no)
                                                                from atr_buss_to_lrc_icu_ul_calc_detail ud
                                                                where ud.main_id = rec.id),
                                                               0),
                                                      1)
                                         else
                                             greatest(coalesce(rec.remaining_months, 0), 4) -- 第 4 期可能还有应收保费
                                                 +
                                             (case when rec.main_treaty_type = 'X' then 12 else 0 end) -- BCA 超赔分出应收保费延 12 期
                      end)
                  order by d.dev_no) x;


            v_cost[10] := coalesce(v_cost[10], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- after_premium_impairment_rate
            update atr_buss_to_lrc_icu_calc_detail t
            set after_premium_impairment_rate = atr_pack_ecf_util_func_get_quota_value(
                    'BE001',
                    p_buss.action_no,
                    t.main_id,
                    'U',
                    t.dev_no,
                    1,
                    null)
            where t.main_id = rec.id;


            v_cost[11] := coalesce(v_cost[11], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- RIF Run-Off Pattern
            if rec.mortgage_risk_ind = '1' then
                update atr_buss_to_lrc_icu_calc_detail t
                set rif_run_off_pattern =
                        (select qd.quota_value
                         from atr_conf_mortg_quota_period qd,
                              atr_conf_mortg_quota qm
                         where qm.mortg_quota_id = qd.mortg_quota_id
                           and qm.entity_id = rec.entity_id
                           and qm.policy_no = v_src_treaty_no)
                where t.main_id = rec.id;
            end if;


            v_cost[12] := coalesce(v_cost[12], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();


            /* ** recv premium  ***/
            if rec.main_treaty_type in ('X', 'R') then
                -- 未到期月份(应收保费计算专用)
                rec.remaining_months_for_recv := (case
                                                      when rec.expiry_date_eom <=
                                                           rec.evaluate_date then
                                                          0
                                                      else
                                                          extract(year from age(rec.expiry_date_bom, rec.evaluate_date_bom)) *
                                                          12 +
                                                          extract(month from age(rec.expiry_date_bom, rec.evaluate_date_bom))
                    end);

                -- 剩余未交费期次（评估日期-终保日期）
                rec.remaining_prem_term_pe := (case
                                                   when rec.passed_months = 0 then
                                                       rec.payment_frequency_no
                                                   when rec.payment_frequency_code = 'S' then
                                                       0
                                                   when rec.payment_frequency_code = 'M' then
                                                       greatest(rec.remaining_months_for_recv, 0)
                                                   when rec.payment_frequency_code = 'Q' then
                                                       greatest(rec.payment_frequency_no -
                                                                trunc((rec.passed_months - 1) / 3),
                                                                0)
                                                   when rec.payment_frequency_code = 'Y' then
                                                       (case
                                                            when extract(day from
                                                                         rec.effective_date_in_date) = 1 then
                                                                greatest(rec.payment_frequency_no -
                                                                         greatest(floor((rec.passed_months - 1) / 12),
                                                                                  0),
                                                                         0)
                                                            else
                                                                greatest(rec.payment_frequency_no -
                                                                         greatest(floor((rec.passed_months) / 12),
                                                                                  0),
                                                                         0)
                                                           end)
                                                   else
                                                       0
                    end);

                -- 未到期缴费期次(评估日期-合同边界日期)
                rec.remaining_prem_term_cb := rec.remaining_prem_term_pe;

                -- 应收保费;  分出业务， 实付保费的符号相反
                rec.recv_premium := rec.est_total_ri_premium +
                                    rec.cumulative_paid_premium;

                -- 发展期
                update atr_buss_to_lrc_icu_calc_detail t
                set recv_premium = (case
                                        when rec.remaining_months_for_recv = 0 then
                                            case
                                                when t.dev_no = 1 then
                                                    rec.recv_premium
                                                else
                                                    0
                                                end
                                        when rec.payment_frequency_code = 'S' then
                                            case
                                                when t.dev_no = 1 then
                                                    rec.recv_premium
                                                else
                                                    0
                                                end
                                        else
                                            (case
                                                 when rec.payment_frequency_code = 'M' then
                                                     (case
                                                          when t.dev_no > rec.remaining_prem_term_cb then
                                                              0
                                                          else
                                                              rec.recv_premium
                                                         end)
                                                 when rec.payment_frequency_code = 'Q' then
                                                     (case
                                                          when t.dev_no / 3.0 >
                                                               rec.remaining_prem_term_cb then
                                                              0
                                                          else
                                                              rec.recv_premium *
                                                              (floor((t.dev_no + mod(rec.passed_months, 3)) / 3) -
                                                               floor((t.dev_no - 1 +
                                                                      mod(rec.passed_months, 3)) / 3))
                                                         end)
                                                 when rec.payment_frequency_code = 'Y' then
                                                     (case
                                                          when t.dev_no / 12.0 >
                                                               rec.remaining_prem_term_cb then
                                                              0
                                                          else
                                                              rec.recv_premium *
                                                              (floor((t.dev_no +
                                                                      mod(rec.passed_months, 12)) / 12) -
                                                               floor((t.dev_no - 1 +
                                                                      mod(rec.passed_months, 12)) / 12))
                                                         end)
                                                end) /
                                            greatest(rec.remaining_prem_term_cb, 1)
                    end) * atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                  1)
                where t.main_id = rec.id;

                -- 当期取 实收保费
                update atr_buss_to_lrc_icu_calc_detail t
                set recv_premium = coalesce((select sum(p.paid_premium + coalesce(p.commission, 0))
                                             from atr_dap_to_premium_paid p
                                             where p.entity_id = rec.entity_id
                                               and p.treaty_no = v_src_treaty_no
                                               and p.year_month = p_buss.year_month),
                                            0)
                where t.main_id = rec.id
                  and t.dev_no = 0;
            else
                -- 比例合约

                -- 先在 应收保费明细 上计算
                insert into atr_buss_to_lrc_icu_recv_detail
                    (id, main_id, recv_id, dev_no, recv_premium)
                select nextval('atr_seq_buss_to_lrc_icu_recv_detail'), x.*
                from (select r.main_id,
                             r.id,
                             dev.dev_no,
                             (case
                                  when dev.dev_no = 0 then
                                      r.cumulative_paid_premium -
                                      coalesce((select r2.cumulative_paid_premium
                                                from atr_buss_to_lrc_icu_calc m2,
                                                     atr_buss_to_lrc_icu_recv r2
                                                where m2.action_no = v_pre_action_no
                                                  and m2.treaty_no = rec.treaty_no
                                                  and m2.id = r2.main_id
                                                  and r2.quarter_bom = r.quarter_bom),
                                               0)
                                  when r.recv_premium = 0 then
                                      0
                                  when r.quarter_bom = date_trunc('quarter', r.evaluate_date) then
                                      (case
                                           when date_trunc('month', r.quarter_eom + '2 months'::interval) =
                                                date_trunc('month', r.evaluate_date + dev.dev_no * '1 month'::interval)
                                               then
                                               r.recv_premium
                                           else
                                               0
                                          end)
                                  else
                                      (case
                                           when dev.dev_no = 1 then
                                               r.recv_premium
                                           else
                                               0
                                          end)
                                 end) as recv_premium
                      from atr_buss_to_lrc_icu_recv r,
                           atr_conf_dev_no dev
                      where dev.dev_no >= 0
                        and dev.dev_no <= greatest(r.remaining_months, 4) -- 第 4 期可能还有应收保费
                        and r.main_id = rec.id
                      order by r.id, dev.dev_no) x;

                -- 再合并到“合约”级别上
                update atr_buss_to_lrc_icu_calc_detail t
                set recv_premium =
                        (select sum(rd.recv_premium) as recv_premium
                         from atr_buss_to_lrc_icu_recv r,
                              atr_buss_to_lrc_icu_recv_detail rd
                         where r.main_id = rd.main_id
                           and r.id = rd.recv_id
                           and r.main_id = t.main_id
                           and rd.dev_no = t.dev_no)
                where t.main_id = rec.id
                  and t.dev_no > 0;

            end if;


            v_cost[13] := coalesce(v_cost[13], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();


            /* ** ed premium backup ***/
            if rec.main_treaty_type = 'X' then
                -- 已赚保费(每个覆盖日)
                rec.ed_premium_per_coverage_day := case
                                                       when rec.effective_date_in_date <=
                                                            rec.expiry_date_in_date then
                                                           rec.est_total_ri_premium /
                                                           (cast(rec.expiry_date_in_date as date) -
                                                            cast(rec.effective_date_in_date as date) + 1)
                    end;

            end if;

            if rec.main_treaty_type = 'R' then
                -- 底层保单发展期
                update atr_buss_to_lrc_icu_ul_calc_detail t
                set ed_premium_backup =
                        (select case
                                    when t.dev_no > ul.remaining_months then
                                        0
                                    when t.dev_no < ul.future_months then
                                        0
                                    when ul.future_months > 0 and
                                         t.dev_no = ul.remaining_months then
                                        -- 尾差
                                        ul.ri_premium -
                                        ul.ed_premium_per_month *
                                        (ul.remaining_months - ul.future_months)
                                    else
                                        ul.ed_premium_per_month
                                    end
                         from atr_buss_to_lrc_icu_ul_calc ul
                         where ul.main_id = t.main_id
                           and ul.serial_no = t.serial_no)
                where t.main_id = rec.id
                  and t.dev_no > 0;

                -- 汇总 明细表
                update atr_buss_to_lrc_icu_calc_detail t
                set ed_premium_backup =
                        (select sum(uld.ed_premium_backup)
                         from atr_buss_to_lrc_icu_ul_calc_detail uld
                         where uld.main_id = t.main_id
                           and uld.dev_no = t.dev_no)
                where t.dev_no > 0
                  and t.main_id = rec.id;

                -- 汇总 主表
                select sum(d.ed_premium_backup)
                into rec.ed_premium_backup
                from atr_buss_to_lrc_icu_calc_detail d
                where d.main_id = rec.id;

            end if;

            ---------------------------------------------------------
            -- 超赔、比例合约分出不需要计算 backup， 原因
            -- 1、 超赔合约的已赚、未赚都为 0
            -- 2、 比例合约通过真实底层保单计算已赚、未赚
            ---------------------------------------------------------


            v_cost[15] := coalesce(v_cost[15], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();


            /* ** ue premium ***/
            if rec.main_treaty_type = 'X' then
                ------------------
                -- 超赔合约
                ------------------
                update atr_buss_to_lrc_icu_calc_detail t
                set ue_premium = 0
                where t.main_id = rec.id;

            elsif rec.main_treaty_type = 'T' then
                ------------------
                -- 比例合约
                ------------------
                -- 发展期
                /* 移至发展期初始化 */
                null;

            else
                ------------------
                -- 合约分入转分出
                ------------------

                -- 底层保单发展期
                merge into atr_buss_to_lrc_icu_ul_calc_detail t
                using (select ud.id,
                              (case
                                   when ud.dev_no = 0 then
                                       x.sum_ed_premium
                                   when ud.dev_no >= ul.remaining_months then
                                       0
                                   when rec.gross_premium > 0 then
                                       greatest(x.sum_ed_premium -
                                                (select coalesce(sum(ud3.ed_premium_backup), 0)
                                                 from atr_buss_to_lrc_icu_ul_calc_detail ud3
                                                 where ud3.main_id = ud.main_id
                                                   and ud3.serial_no = ud.serial_no
                                                   and ud3.dev_no > 0
                                                   and ud3.dev_no <= ud.dev_no),
                                                0)
                                   else
                                       least(x.sum_ed_premium -
                                             (select coalesce(sum(ud3.ed_premium_backup), 0)
                                              from atr_buss_to_lrc_icu_ul_calc_detail ud3
                                              where ud3.main_id = ud.main_id
                                                and ud3.serial_no = ud.serial_no
                                                and ud3.dev_no > 0
                                                and ud3.dev_no <= ud.dev_no),
                                             0)
                                  end) as ue_premium
                       from atr_buss_to_lrc_icu_ul_calc_detail ud,
                            atr_buss_to_lrc_icu_ul_calc ul,
                            (select ud2.serial_no,
                                    sum(ud2.ed_premium_backup) sum_ed_premium
                             from atr_buss_to_lrc_icu_ul_calc_detail ud2
                             where ud2.main_id = rec.id
                             group by ud2.serial_no) x
                       where ud.main_id = rec.id
                         and ud.serial_no = x.serial_no
                         and ul.main_id = rec.id
                         and ul.serial_no = x.serial_no) x2
                on (t.id = x2.id)
                when matched then
                    update set ue_premium = x2.ue_premium;

                -- 汇总 明细表
                update atr_buss_to_lrc_icu_calc_detail t
                set ue_premium =
                        (select sum(ud.ue_premium)
                         from atr_buss_to_lrc_icu_ul_calc_detail ud
                         where ud.main_id = t.main_id
                           and ud.dev_no = t.dev_no)
                where t.main_id = rec.id;

            end if;


            v_cost[16] := coalesce(v_cost[16], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();


            /* ** ed premium ***/
            if rec.main_treaty_type = 'X' then
                ------------------
                -- 超赔合约
                ------------------
                -- 主表
                update atr_buss_to_lrc_icu_calc t
                set ed_premium = 0
                where t.id = rec.id;

                -- 发展期
                update atr_buss_to_lrc_icu_calc_detail t
                set ed_premium = 0
                where t.main_id = rec.id;

            elsif rec.main_treaty_type = 'T' then
                ------------------
                -- 比例合约
                ------------------

                -- 底层保单发展期
                merge into tmp_atr_buss_to_lrc_icu_rep_calc_detail t
                using (select t.serial_no,
                              t.dev_no,
                              case
                                  when t.dev_no = 0 then
                                      -- 第 0 期的底层保单的ed_premium只是为了追溯问题， 不参与实际计算
                                      t.ri_premium - t.ue_premium
                                  when t.dev_no > t.remaining_months then
                                      0
                                  else
                                      coalesce(lag(t.ue_premium) over (partition by t.serial_no order by t.dev_no),
                                               0)
                                          - coalesce(t.ue_premium, 0)
                                  end as ed_premium
                       from tmp_atr_buss_to_lrc_icu_rep_calc_detail t) x
                on (t.serial_no = x.serial_no and t.dev_no = x.dev_no)
                when matched then
                    update
                    set ed_premium = x.ed_premium;

                v_cost[25] := coalesce(v_cost[25], 0) + extract(epoch from clock_timestamp() - v_cost_start);
                v_cost_start := clock_timestamp();

            else
                ------------------
                -- 合约分入转分出
                ------------------
                -- 主表
                rec.ed_premium := rec.ed_premium_backup;

                -- 发展期
                update atr_buss_to_lrc_icu_calc_detail t
                set ed_premium = t.ed_premium_backup
                where t.main_id = rec.id;

                -- 底层保单明细
                update atr_buss_to_lrc_icu_ul_calc_detail t
                set ed_premium = t.ed_premium_backup
                where t.main_id = rec.id;

            end if;


            v_cost[17] := coalesce(v_cost[17], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();


            -- 真实底层保单发展期统一汇总， 优化性能
            if rec.main_treaty_type = 'T' then
                merge into atr_buss_to_lrc_icu_calc_detail t
                using (select ud.dev_no,
                              sum(ud.ue_premium)                                                      as ue_premium,
                              sum(case when ud.dev_no > 0 then ud.ed_premium end)                     as ed_premium,
                              sum(case when rec.mortgage_risk_ind <> '1' then ud.coverage_amount end) as coverage_amount
                       from tmp_atr_buss_to_lrc_icu_rep_calc_detail ud
                       group by ud.dev_no) x
                on (t.main_id = rec.id and t.dev_no = x.dev_no)
                when matched then
                    update
                    set ue_premium      = x.ue_premium,
                        ed_premium      = x.ed_premium,
                        coverage_amount = x.coverage_amount;

                v_cost[32] := coalesce(v_cost[32], 0) + extract(epoch from clock_timestamp() - v_cost_start);
                v_cost_start := clock_timestamp();

                -- 汇总主表
                select coalesce(sum(d.ed_premium), 0)
                into rec.ed_premium
                from atr_buss_to_lrc_icu_calc_detail d
                where d.main_id = rec.id;

                v_cost[33] := coalesce(v_cost[33], 0) + extract(epoch from clock_timestamp() - v_cost_start);
                v_cost_start := clock_timestamp();
            end if;


            /* ** no dependent dev  ***/

            update atr_buss_to_lrc_icu_calc_detail t
            set --- recv_rel_fee
                -- 调整手续费
                adj_commission = case
                                     when t.dev_no > 0 then
                                         abs(t.recv_premium *
                                             rec.adj_commission_rate /
                                             atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                    1))
                    end,
                -- 经纪人费用
                brokerage_fee  = case
                                     when t.dev_no > 0 then
                                         abs(t.recv_premium * rec.brokerage_fee_rate /
                                             atr_pack_ecf_util_nvl0(t.after_premium_impairment_rate,
                                                                    1))
                    end,
                --- ed_rel_fee
                --- gep_uwq
                gep_uwq        = t.ed_premium
            where t.main_id = rec.id;


            v_cost[18] := coalesce(v_cost[18], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** coverage_amount */
            if rec.main_treaty_type = 'X' then
                ------------------
                -- 超赔合约
                ------------------
                update atr_buss_to_lrc_icu_calc_detail t
                set coverage_amount = (case
                                           when t.dev_no = 0 then
                                               0
                                           when t.dev_no < rec.future_months then
                                               0
                                           when t.dev_no > rec.remaining_months then
                                               0
                                           else
                                               rec.coverage_amount *
                                               coalesce(t.ed_premium /
                                                        atr_pack_lrc_nvl0(rec.ed_premium_per_coverage_day,
                                                                          null),
                                                        1)
                    end)
                where t.main_id = rec.id;

            elsif rec.mortgage_risk_ind = '1' then
                ------------------
                -- 抵押风险的比例合约
                ------------------
                update atr_buss_to_lrc_icu_calc_detail t
                set coverage_amount = (case
                                           when t.dev_no = 0 then
                                               0
                                           when t.dev_no < rec.future_months then
                                               0
                                           when t.dev_no > rec.remaining_months then
                                               0
                                           else
                                               rec.coverage_amount *
                                               atr_pack_ecf_util_nvl0(t.rif_run_off_pattern,
                                                                      1)
                    end)
                where t.main_id = rec.id;

            elsif rec.main_treaty_type = 'T' then
                ------------------
                -- 比例合约
                ------------------

                /* 移至发展期初始化 */
                null;

            else
                ------------------
                -- 合约分入转分出
                ------------------

                -- 底层保单发展期
                update atr_buss_to_lrc_icu_ul_calc_detail t
                set coverage_amount =
                        (select case
                                    when t.dev_no = 0 then
                                        0
                                    when t.dev_no < u.future_months then
                                        0
                                    when t.dev_no > u.remaining_months then
                                        0
                                    else
                                        rec.coverage_amount
                                    end
                         from atr_buss_to_lrc_icu_ul_calc u
                         where u.main_id = t.main_id
                           and u.serial_no = t.serial_no)
                where t.main_id = rec.id;

                -- 汇总到 明细表 中
                update atr_buss_to_lrc_icu_calc_detail t
                set coverage_amount =
                        (select sum(ud.coverage_amount)
                         from atr_buss_to_lrc_icu_ul_calc_detail ud
                         where ud.main_id = t.main_id
                           and ud.dev_no = t.dev_no)
                where t.main_id = rec.id;

            end if;

            v_cost[19] := coalesce(v_cost[19], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();


            /* ** ue_premium_per_month  ***/
            -- (上期)当期期末剩余未摊销比例
            -- (上期)截至报告期初剩余未摊销比例
            begin
                if p_pre_action_no is not null then
                    select pre.cur_end_remain_csm_rate,
                           pre.until_report_remain_csm_rate
                    into rec.pri_cur_end_remain_csm_rate,
                        rec.pri_until_report_remain_csm_rate
                    from atr_buss_to_lrc_icu_calc pre
                    where pre.action_no = p_pre_action_no
                      and pre.treaty_no = rec.treaty_no
                      and coalesce(pre.dept_id, -1) = coalesce(rec.dept_id, -1)
                      and pre.currency_code = rec.currency_code;
                end if;
            exception
                when no_data_found then
                    null;
            end;

            -- (上期)当期期末剩余未摊销比例
            rec.pri_cur_end_remain_csm_rate := coalesce(rec.pri_cur_end_remain_csm_rate, 1);
            -- (上期)截至报告期初剩余未摊销比例
            rec.pri_until_report_remain_csm_rate := coalesce(rec.pri_until_report_remain_csm_rate, 1);

            rec.until_report_remain_csm_rate := case
                                                    when rec.treaty_no like '%#%' and
                                                         rec.reversed_flag = '1' then
                                                        1
                                                    when rec.treaty_no not like '%#%' and
                                                         substr(rec.dap_year_month, 1, 4) =
                                                         substr(p_buss.year_month, 1, 4) then
                                                        1
                                                    when v_cur_month = '01' then
                                                        --  (上期)当期期末剩余未摊销比例
                                                        rec.pri_cur_end_remain_csm_rate
                                                    else
                                                        -- (上期)截至报告期初剩余未摊销比例
                                                        rec.pri_until_report_remain_csm_rate
                end;

            begin
                select case
                           when rec.gross_premium = 0 then
                               0
                           else
                               d.ue_premium / rec.gross_premium
                           end
                into rec.cur_end_remain_csm_rate
                from atr_buss_to_lrc_icu_calc_detail d
                where d.main_id = rec.id
                  and d.dev_no = 0;
            exception
                when no_data_found then
                    null;
            end;

            -- 当期期末剩余未摊销比例
            rec.cur_end_remain_csm_rate := coalesce(rec.cur_end_remain_csm_rate, 0);
            -- 累计已赚比例
            rec.cumulative_ed_rate := 1 - rec.cur_end_remain_csm_rate;


            v_cost[20] := coalesce(v_cost[20], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** ultimate_loss_mortgage  ***/
            if rec.mortgage_risk_ind = '1' then

                begin
                    select t.val_date_loan_os, t.delinquency_rate, t.default_rate
                    into -- Val Date Loan O/S
                        rec.loan_os,
                        -- Delinquency Ratio
                        rec.delinquency_rate,
                        -- Default Ratio
                        rec.default_rate
                    from atr_conf_mortg_quota t
                    where t.entity_id = rec.entity_id
                      and t.policy_no = v_src_treaty_no;
                exception
                    when no_data_found then
                        null;
                end;

                -- After Future NB Projection Loan O/S
                rec.afnp_loan_os := rec.loan_os / (case rec.ul_underwritten_rate
                                                       when 0 then
                                                           1
                    end);

                -- RIF% Run-Off Pattern
                update atr_buss_to_lrc_icu_calc_detail t
                set rif_run_off_pattern_rate = (case
                                                    when coalesce(t.rif_run_off_pattern, 0) = 0 then
                                                        0
                                                    else
                                                        t.rif_run_off_pattern /
                                                        atr_pack_ecf_util_nvl0((select d2.rif_run_off_pattern
                                                                                from atr_buss_to_lrc_icu_calc_detail d2
                                                                                where d2.main_id =
                                                                                      t.main_id
                                                                                  and d2.dev_no = 1),
                                                                               null)
                    end)
                where t.dev_no > 0
                  and t.main_id = rec.id;

                -- Ultimate Loss Mortgage
                update atr_buss_to_lrc_icu_calc_detail t
                set ultimate_loss_mortgage =
                        (t.rif_run_off_pattern_rate * rec.afnp_loan_os *
                         rec.delinquency_rate * rec.default_rate)
                where t.main_id = rec.id
                  and t.dev_no > 0
                  and t.dev_no <= rec.dev_val;

            end if;


            v_cost[22] := coalesce(v_cost[22], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            /* ** update main  ***/
            update atr_buss_to_lrc_icu_calc t
            set dev_val                          = rec.dev_val,
                passed_months                    = rec.passed_months,
                remaining_months                 = rec.remaining_months,
                future_months                    = rec.future_months,
                dap_year_month                   = rec.dap_year_month,
                mortgage_risk_ind                = rec.mortgage_risk_ind,
                ul_underwritten_rate             = rec.ul_underwritten_rate,
                est_total_ri_premium             = rec.est_total_ri_premium,
                risk_expansion                   = rec.risk_expansion,
                adj_commission_rate              = rec.adj_commission_rate,
                brokerage_fee_rate               = rec.brokerage_fee_rate,
                maintenance_fee_rate             = rec.maintenance_fee_rate,
                remaining_prem_term_pe           = rec.remaining_prem_term_pe,
                remaining_months_for_recv        = rec.remaining_months_for_recv,
                remaining_prem_term_cb           = rec.remaining_prem_term_cb,
                cumulative_paid_premium          = rec.cumulative_paid_premium,
                recv_premium                     = rec.recv_premium,
                ed_premium_per_coverage_day      = rec.ed_premium_per_coverage_day,
                ed_premium_backup                = rec.ed_premium_backup,
                ed_premium                       = rec.ed_premium,
                pri_cur_end_remain_csm_rate      = rec.pri_cur_end_remain_csm_rate,
                pri_until_report_remain_csm_rate = rec.pri_until_report_remain_csm_rate,
                cumulative_ed_rate               = rec.cumulative_ed_rate,
                cur_end_remain_csm_rate          = rec.cur_end_remain_csm_rate,
                until_report_remain_csm_rate     = rec.until_report_remain_csm_rate,
                elr                              = rec.elr,
                loan_os                          = rec.loan_os,
                afnp_loan_os                     = rec.afnp_loan_os,
                delinquency_rate                 = rec.delinquency_rate,
                default_rate                     = rec.default_rate
            where t.id = rec.id;

            /* ** end  ***/


            v_cost[23] := coalesce(v_cost[23], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- truncate tmp
            truncate table tmp_atr_buss_to_lrc_icu_rep_calc_detail;
            truncate table tmp_atr_buss_to_lrc_icu_rep_calc;

            v_cost[24] := coalesce(v_cost[24], 0) + extract(epoch from clock_timestamp() - v_cost_start);
            v_cost_start := clock_timestamp();

            -- 耗时统计
            v_cost_str := '';
            for i in 1..array_length(v_cost, 1)
                loop
                    if v_cost[i] is not null then
                        v_cost_str := v_cost_str || ',[' || i || ']--' || round(v_cost[i], 2);
                    end if;
                end loop;
            v_cost := array []::numeric[];
            call atr_pack_lrc_proc_debug(p_buss.action_no, v_count::varchar || '/' || rec.id || '/' || rec.treaty_no
                                                               || ' -- ' || v_cost_str, 'calc_icu', p_thread_no);

            commit;
        end loop;

    commit;

    call atr_pack_lrc_proc_debug(p_buss.action_no, null, 'calc_icu_to-end', p_thread_no);

end ;
$_$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_init_icg(IN p_buss atruser.atr_pack_lrc_record_buss)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------
    -- 合同组数据准备
    ------------------------------
    v_base_currency_code varchar(3);
    v_task_code          varchar(100);
begin
    call atr_pack_lrc_proc_debug(p_buss.action_no, 'step#合同组数据准备#start');

    select t.task_code, t.currency_code
    into v_task_code, v_base_currency_code
    from atr_buss_lrc_action t
    where t.action_no = p_buss.action_no;

    drop table if exists tmp_atr_lrc_icg_dev;

    if p_buss.business_source_code = 'DD' then
        -- 单 >> 合同组
        insert into atr_buss_dd_lrc_icg_calc (id,
                                              action_no,
                                              task_code,
                                              entity_id,
                                              currency_code,
                                              year_month,
                                              portfolio_no,
                                              icg_no,
                                              evaluate_approach,
                                              loa_code)
        select nextval('atr_seq_buss_dd_lrc_icg_calc'),
               p_buss.action_no,
               v_task_code,
               p_buss.entity_id,
               v_base_currency_code,
               p_buss.year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code
        from atr_temp_lrc_icg_dev_coverage_amount t
        where t.action_no = p_buss.action_no
        group by t.icg_no,
                 t.portfolio_no,
                 t.evaluate_approach,
                 t.loa_code
        order by t.icg_no, t.portfolio_no;

        -- 合同组明细
        insert into atr_buss_dd_lrc_icg_calc_detail
            (id, main_id, dev_no)
        select nextval('atr_seq_buss_dd_lrc_icg_calc_detail'), t.id main_id, d.dev_no
        from (select g.id, max(um.dev_no) max_dev_no
              from atr_buss_dd_lrc_icg_calc g,
                   atr_temp_lrc_icg_dev_coverage_amount um
              where g.action_no = p_buss.action_no
                and um.action_no = g.action_no
                and um.portfolio_no = g.portfolio_no
                and um.icg_no = g.icg_no
              group by g.id) t,
             atr_conf_dev_no d
        where d.dev_no > 0
          and d.dev_no <= t.max_dev_no
        order by t.id, d.dev_no;

        commit;

    elsif p_buss.business_source_code = 'FO' then

        -- 单 >> 合同组
        insert into atr_buss_fo_lrc_icg_calc (id,
                                              action_no,
                                              task_code,
                                              entity_id,
                                              currency_code,
                                              year_month,
                                              portfolio_no,
                                              icg_no,
                                              evaluate_approach,
                                              loa_code)
        select nextval('atr_seq_buss_fo_lrc_icg_calc'),
               p_buss.action_no,
               v_task_code,
               p_buss.entity_id,
               v_base_currency_code,
               p_buss.year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code
        from atr_temp_lrc_icg_dev_coverage_amount t
        where t.action_no = p_buss.action_no
        group by t.icg_no,
                 t.portfolio_no,
                 t.evaluate_approach,
                 t.loa_code
        order by t.icg_no, t.portfolio_no;


        -- 合同组明细
        insert into atr_buss_fo_lrc_icg_calc_detail
            (id, main_id, dev_no)
        select nextval('atr_seq_buss_fo_lrc_icg_calc_detail'), t.id main_id, d.dev_no
        from (select g.id, max(um.dev_no) max_dev_no
              from atr_buss_fo_lrc_icg_calc g,
                   atr_temp_lrc_icg_dev_coverage_amount um
              where g.action_no = p_buss.action_no
                and um.action_no = g.action_no
                and um.portfolio_no = g.portfolio_no
                and um.icg_no = g.icg_no
              group by g.id) t,
             atr_conf_dev_no d
        where d.dev_no > 0
          and d.dev_no <= t.max_dev_no
        order by t.id, d.dev_no;

        commit;

    elsif p_buss.business_source_code = 'TI' then

        -- 单 >> 合同组
        insert into atr_buss_ti_lrc_icg_calc(id,
                                             action_no,
                                             task_code,
                                             entity_id,
                                             currency_code,
                                             year_month,
                                             portfolio_no,
                                             icg_no,
                                             evaluate_approach,
                                             loa_code)
        select nextval('atr_seq_buss_ti_lrc_icg_calc'),
               p_buss.action_no,
               v_task_code,
               t.entity_id,
               v_base_currency_code,
               t.year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code
        from atr_buss_ti_lrc_icu_calc t
        where t.action_no = p_buss.action_no
        group by t.icg_no,
                 t.portfolio_no,
                 t.entity_id,
                 t.year_month,
                 t.evaluate_approach,
                 t.loa_code
        order by t.entity_id, t.icg_no, t.portfolio_no;

        -- 合同组明细
        insert into atr_buss_ti_lrc_icg_calc_detail
            (id, main_id, dev_no)
        select nextval('atr_seq_buss_ti_lrc_icg_calc_detail'), t.id main_id, d.dev_no
        from (select g.id, max(ud.dev_no) max_dev_no
              from atr_buss_ti_lrc_icg_calc g,
                   atr_buss_ti_lrc_icu_calc um,
                   atr_buss_ti_lrc_icu_calc_detail ud
              where g.action_no = p_buss.action_no
                and um.action_no = g.action_no
                and um.entity_id = g.entity_id
                and um.portfolio_no = g.portfolio_no
                and um.icg_no = g.icg_no
                and um.id = ud.main_id
              group by g.id) t,
             atr_conf_dev_no d
        where d.dev_no > 0
          and d.dev_no <= t.max_dev_no
        order by t.id, d.dev_no;

    elsif p_buss.business_source_code = 'TO' then
        -- 单 >> 合同组
        insert into atr_buss_to_lrc_icg_calc(id,
                                             action_no,
                                             task_code,
                                             entity_id,
                                             currency_code,
                                             year_month,
                                             portfolio_no,
                                             icg_no,
                                             evaluate_approach,
                                             loa_code,
                                             main_treaty_type,
                                             ue_premium)
        select nextval('atr_seq_buss_to_lrc_icg_calc'),
               p_buss.action_no,
               v_task_code,
               t.entity_id,
               v_base_currency_code,
               t.year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               t.main_treaty_type,
               sum((select coalesce(sum(d.ue_premium), 0)
                    from atr_buss_to_lrc_icu_calc_detail d
                    where d.main_id = t.id
                      and d.dev_no = 0)) as ue_premium
        from atr_buss_to_lrc_icu_calc t
        where t.action_no = p_buss.action_no
        group by t.icg_no,
                 t.portfolio_no,
                 t.entity_id,
                 t.year_month,
                 t.evaluate_approach,
                 t.loa_code,
                 t.main_treaty_type
        order by t.entity_id, t.icg_no, t.portfolio_no;

        -- 合同组明细
        insert into atr_buss_to_lrc_icg_calc_detail
            (id, main_id, dev_no)
        select nextval('atr_seq_buss_to_lrc_icg_calc_detail'), t.id main_id, d.dev_no
        from (select g.id, max(ud.dev_no) max_dev_no
              from atr_buss_to_lrc_icg_calc g,
                   atr_buss_to_lrc_icu_calc um,
                   atr_buss_to_lrc_icu_calc_detail ud
              where g.action_no = p_buss.action_no
                and um.action_no = g.action_no
                and um.entity_id = g.entity_id
                and um.portfolio_no = g.portfolio_no
                and um.icg_no = g.icg_no
                and um.id = ud.main_id
              group by g.id) t,
             atr_conf_dev_no d
        where d.dev_no > 0
          and d.dev_no <= t.max_dev_no
        order by t.id, d.dev_no;

    end if;

    commit;

    call atr_pack_lrc_proc_debug(p_buss.action_no, 'step#合同组数据准备#end');

end;

$$;

CREATE PROCEDURE atruser.atr_pack_lrc_proc_calc_init_icu(IN p_business_source_code character varying, IN p_entity_id bigint, IN p_year_month character varying, IN p_portfolio_no character varying, IN p_action_no character varying, IN p_task_code character varying)
    LANGUAGE plpgsql
    AS $$
declare
    -------------------------
    -- 单数据准备
    -------------------------
    v_ev_date_bom timestamp      := to_date(p_year_month, 'yyyyMM');
    v_ev_date_eom timestamp      := last_day(to_date(p_year_month, 'yyyyMM'));
    v_threshold   decimal(33, 8) := 0; -- 应收、实收偏差阈值
begin

    call atr_pack_lrc_proc_debug(p_action_no, 'step#单数据准备#start');

    select t.code_e_name::decimal into v_threshold from atr_v_conf_code t where t.code_code_idx = 'TDPRR/ALL';
    v_threshold := coalesce(v_threshold, 0);

    if p_business_source_code = 'DD' then

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-insert#-ecept_main');

        drop table if exists tmp_main_policy_ecept;

        create temp table tmp_main_policy_ecept as
        select t.main_policy_no
        from atr_dap_dd_premium t
        where t.entity_id = p_entity_id
          and t.year_month <= p_year_month
        group by t.main_policy_no
        having sum(t.premium) = 0;

        create index idx_tmp_main_policy_ecept on tmp_main_policy_ecept (main_policy_no);

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-insert#-00');

        truncate table atr_temp_dd_premium_paid_cur;
        truncate table atr_temp_dd_premium_paid_lt;
        truncate table atr_temp_dd_premium_paid_lte;
        truncate table atr_temp_dd_payment_plan;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-insert#-01');

        drop table if exists tmp_dd_premium_paid_all;

        create table tmp_dd_premium_paid_all as
        select p.policy_no,
               p.endorse_seq_no,
               p.year_month,
               sum(p.paid_premium) as paid_premium,
               sum(p.commission)   as commission
        from atr_dap_dd_premium_paid p
        where p.entity_id = p_entity_id
          and p.year_month <= p_year_month
        group by p.policy_no, p.endorse_seq_no, p.year_month;


        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-insert#-02');

        insert into atr_temp_dd_premium_paid_cur (policy_no, endorse_seq_no, premium, commission)
        select p.policy_no, p.endorse_seq_no, sum(p.paid_premium), sum(p.commission)
        from tmp_dd_premium_paid_all p
        where p.year_month = p_year_month
        group by p.policy_no, p.endorse_seq_no;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-insert#-03');

        insert into atr_temp_dd_premium_paid_lt (policy_no, endorse_seq_no, premium, commission)
        select p.policy_no, p.endorse_seq_no, sum(p.paid_premium), sum(p.commission)
        from tmp_dd_premium_paid_all p
        where p.year_month < p_year_month
        group by p.policy_no, p.endorse_seq_no;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-insert#-04');

        insert into atr_temp_dd_premium_paid_lte (policy_no, endorse_seq_no, premium, commission)
        select p.policy_no, p.endorse_seq_no, sum(p.paid_premium), sum(p.commission)
        from tmp_dd_premium_paid_all p
        where p.year_month <= p_year_month
        group by p.policy_no, p.endorse_seq_no;

        truncate table tmp_dd_premium_paid_all;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-insert#-05');

        insert into atr_temp_dd_payment_plan (policy_no, endorse_seq_no, est_payment_date)
        select distinct t.policy_no, t.endorse_seq_no, date_trunc('month', t.est_payment_date)
        from atr_dap_dd_payment_plan t
        where t.entity_id = p_entity_id
          and t.est_payment_date > v_ev_date_eom;


        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-insert#-06');

        -- 初始化主表

        insert into atr_buss_dd_lrc_icu_calc (id,
                                              action_no,
                                              task_code,
                                              data_key,
                                              entity_id,
                                              dept_id,
                                              policy_no,
                                              endorse_seq_no,
                                              currency_code,
                                              year_month,
                                              portfolio_no,
                                              icg_no,
                                              evaluate_approach,
                                              loa_code,
                                              dap_year_month,
            ---
                                              cmunit_no,
                                              product_code,
                                              evaluate_date,
                                              contract_date,
                                              effective_date_in_date,
                                              approval_date_in_date,
                                              expiry_date_in_date,
                                              effective_date_bom,
                                              expiry_date_eom,
                                              payment_frequency_code,
                                              payment_frequency_no,
                                              gross_premium,
                                              commission,
                                              commission_rate,
                                              coverage_amount,
                                              facultative_is)
        select nextval('atr_seq_buss_dd_lrc_icu_calc'),
               p_action_no,
               p_task_code,
               t.id,
               t.entity_id,
               t.dept_id,
               t.policy_no,
               t.endorse_seq_no,
               t.currency_code,
               p_year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               t.year_month                          dap_year_month,
               ---
               t.cmunit_no,
               t.product_code, -- t.cm_unit_dim_code,
               v_ev_date_eom                         evaluate_date,
               date_trunc('day', t.contract_date),
               date_trunc('day', t.effective_date),
               date_trunc('day', t.approval_date),
               date_trunc('day', t.expiry_date),
               date_trunc('month', t.effective_date) effective_date_bom,
               last_day(t.expiry_date)               expiry_date_eom,
               t.payment_frequency_code,
               t.payment_frequency_no,
               t.premium,
               t.commission,
               t.commission_rate,
               t.coverage_amount,
               case when t.business_source_code = 'FB' then '1' else '0' end
        from atr_dap_dd_premium t
        where t.entity_id = p_entity_id
          and t.year_month <= p_year_month
          and (p_portfolio_no is null or
               t.portfolio_no = p_portfolio_no)
          and (t.expiry_date >= v_ev_date_bom
            or t.year_month = p_year_month
            or abs(t.premium -
                   coalesce((select p.premium
                             from atr_temp_dd_premium_paid_lt p
                             where p.policy_no = t.policy_no
                               and p.endorse_seq_no = t.endorse_seq_no), 0)) > v_threshold)
          and not exists (select * from tmp_main_policy_ecept e where e.main_policy_no = t.main_policy_no)
        order by t.year_month,
                 t.entity_id,
                 t.currency_code,
                 t.policy_no,
                 t.endorse_seq_no;

        commit;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-insert#end');

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-analyse#start');

        analyse atr_buss_dd_lrc_icu_calc;
--         perform count(*) from atr_buss_dd_lrc_icu_calc where action_no = p_action_no;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#DD-init-analyse#end');

    elsif p_business_source_code = 'FO' then
        call atr_pack_lrc_proc_debug(p_action_no, 'sub#FO-init-insert#start');

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#FO-init-insert#-ecept_main');

        drop table if exists tmp_main_policy_ecept;

        create temp table tmp_main_policy_ecept as
        select t.main_policy_no
        from atr_dap_fo_premium t
        where t.entity_id = p_entity_id
          and t.year_month <= p_year_month
        group by t.main_policy_no
        having sum(t.premium) = 0;

        create index idx_tmp_main_policy_ecept on tmp_main_policy_ecept (main_policy_no);

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#FO-init-insert#-00');

        -- 初始化主表
        insert into atr_buss_fo_lrc_icu_calc(id,
                                             action_no,
                                             task_code,
                                             data_key,
                                             entity_id,
                                             dept_id,
            ---
                                             ri_policy_no,
                                             ri_endorse_seq_no,
                                             correction_seq_no,
                                             ri_statement_no,
                                             policy_no,
                                             endorse_seq_no,
            ---
                                             currency_code,
                                             year_month,
                                             portfolio_no,
                                             icg_no,
                                             evaluate_approach,
                                             loa_code,
                                             dap_year_month,
            ---
                                             cmunit_no,
                                             product_code,
                                             evaluate_date,
                                             contract_date,
                                             effective_date_in_date,
                                             approval_date_in_date,
                                             expiry_date_in_date,
                                             effective_date_bom,
                                             expiry_date_eom,
                                             payment_frequency_code,
                                             payment_frequency_no,
                                             gross_premium,
                                             coverage_amount)
        select nextval('atr_seq_buss_fo_lrc_icu_calc'),
               p_action_no,
               p_task_code,
               t.id,
               t.entity_id,
               t.dept_id,
               ---
               t.ri_policy_no,
               t.ri_endorse_seq_no,
               t.correction_seq_no,
               t.ri_statement_no,
               t.policy_no,
               t.endorse_seq_no,
               ---
               t.currency_code,
               p_year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               t.year_month  dap_year_month,
               ---
               t.cmunit_no,
               t.product_code, -- t.cm_unit_dim_code,
               v_ev_date_eom evaluate_date,
               date_trunc('day', t.contract_date),
               date_trunc('day', t.effective_date),
               date_trunc('day', t.approval_date),
               date_trunc('day', t.expiry_date),
               date_trunc('month', t.effective_date),
               last_day(t.expiry_date),
               t.payment_frequency_code,
               t.payment_frequency_no,
               t.premium + coalesce(t.commission, 0),
               t.coverage_amount
        from atr_dap_fo_premium t
        where t.entity_id = p_entity_id
          and t.year_month <= p_year_month
          and (p_portfolio_no is null or
               t.portfolio_no = p_portfolio_no)
          and ((t.expiry_date >= v_ev_date_bom or t.year_month = p_year_month)
            or abs(t.premium -
                   (select coalesce(sum(p.paid_premium), 0)
                    from atr_dap_fo_premium_paid p
                    where p.entity_id = t.entity_id
                      and p.ri_policy_no = t.ri_policy_no
                      and p.ri_endorse_seq_no = t.ri_endorse_seq_no
                      and p.endorse_seq_no = t.endorse_seq_no
                      and p.year_month < p_year_month)) > v_threshold)
          and not exists (select 1 from tmp_main_policy_ecept e where e.main_policy_no = t.main_policy_no)
        order by t.year_month,
                 t.entity_id,
                 t.currency_code,
                 t.ri_policy_no,
                 t.ri_endorse_seq_no,
                 t.correction_seq_no;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#FO-init-insert#end');

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#FO-init-analyse#start');

        analyse atr_buss_fo_lrc_icu_calc;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#FO-init-analyse#end');

    elsif p_business_source_code = 'TI' then

        -- 初始化主表
        insert into atr_buss_ti_lrc_icu_calc(id,
                                             action_no,
                                             task_code,
                                             data_key,
                                             entity_id,
                                             dept_id,
                                             treaty_no,
                                             currency_code,
                                             year_month,
                                             portfolio_no,
                                             icg_no,
                                             evaluate_approach,
                                             loa_code,
                                             dap_year_month,
            ---
                                             cmunit_no,
                                             product_code,
                                             evaluate_date,
                                             contract_date,
                                             effective_date_in_date,
                                             approval_date_in_date,
                                             expiry_date_in_date,
                                             effective_date_bom,
                                             expiry_date_eom,
                                             payment_frequency_code,
                                             payment_frequency_no,
                                             gross_premium,
                                             gross_premium2,
                                             coverage_amount,
                                             main_treaty_type)
        select nextval('atr_seq_buss_ti_lrc_icu_calc'),
               p_action_no,
               p_task_code,
               0                                     data_key,
               t.entity_id,
               0                           as        dept_id,
               t.treaty_no,
               t.currency_code,
               p_year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               min(t.year_month)                     dap_year_month,
               ---
               t.cmunit_no,
               '0000'                      as        product_code,  -- t.cm_unit_dim_code,
               v_ev_date_eom                         evaluate_date,
               date_trunc('day', t.contract_date),
               date_trunc('day', t.effective_date),
               null::date                            approval_date,
               date_trunc('day', t.expiry_date),
               date_trunc('month', t.effective_date) effective_date_bom,
               last_day(t.expiry_date)               expiry_date_eom,
               t.payment_frequency_code,
               t.payment_frequency_no,
               coalesce((select b.gepi_amount *
                                atr_pack_lrc_func_getexchrate(t.entity_id,
                                                              v_ev_date_eom,
                                                              t.currency_code,
                                                              b.epi_currency_code)
                         from bpluser.bbs_conf_treaty b
                         where t.entity_id = b.entity_id
                           and t.treaty_no = b.treaty_no),
                        0)                 as        gross_premium, -- 合约使用 EPI
               coalesce(sum(t.premium), 0) as        gross_premium2,
               sum(t.coverage_amount),
               t.main_treaty_type
        from atr_dap_ti_premium t
        where t.entity_id = p_entity_id
          and t.year_month <= p_year_month
          and (p_portfolio_no is null or
               t.portfolio_no = p_portfolio_no)
        group by t.entity_id,
                 t.treaty_no,
                 t.currency_code,
                 t.portfolio_no,
                 t.icg_no,
                 t.evaluate_approach,
                 t.loa_code,
                 t.cmunit_no,
                 t.contract_date,
                 t.effective_date,
                 t.expiry_date,
                 t.payment_frequency_code,
                 t.payment_frequency_no,
                 t.main_treaty_type
        order by t.entity_id, t.currency_code, t.treaty_no;


        -- 反转计算
        update atr_buss_ti_lrc_icu_calc t
        set reversed_flag = (case
                                 when months_between(v_ev_date_bom,
                                                     date_trunc('month', t.contract_date)::date) >= 11 then
                                     case
                                         when exists (select 1
                                                      from atr_buss_ti_lrc_icu_calc m,
                                                           atr_buss_lrc_action a
                                                      where a.action_no = m.action_no
                                                        and a.confirm_is = '1'
                                                        and a.year_month < t.year_month
                                                        and m.entity_id = t.entity_id
                                                        and m.treaty_no = t.treaty_no
                                                        and coalesce(m.dept_id, -1) =
                                                            coalesce(t.dept_id, -1)
                                                        and m.reversed_flag = '1') then
                                             '2'
                                         else
                                             '1'
                                         end
                                 else
                                     '0'
            end)
        where t.action_no = p_action_no;

        insert into atr_buss_ti_lrc_icu_calc(id,
                                             data_key,
                                             action_no,
                                             task_code,
                                             entity_id,
                                             dept_id,
                                             treaty_no,
                                             currency_code,
                                             year_month,
                                             portfolio_no,
                                             icg_no,
                                             evaluate_approach,
                                             loa_code,
                                             cmunit_no,
                                             product_code,
                                             evaluate_date,
                                             contract_date,
                                             effective_date_in_date,
                                             approval_date_in_date,
                                             expiry_date_in_date,
                                             effective_date_bom,
                                             expiry_date_eom,
                                             payment_frequency_code,
                                             payment_frequency_no,
                                             gross_premium,
                                             gross_premium2,
                                             commission_rate,
                                             coverage_amount,
                                             main_treaty_type,
                                             dev_val,
                                             pas_frequency,
                                             passed_months,
                                             remaining_months,
                                             future_months,
                                             dap_year_month,
                                             mortgage_risk_ind,
                                             ul_underwritten_rate,
                                             est_total_ri_premium,
                                             risk_expansion,
                                             adj_commission_rate,
                                             brokerage_fee_rate,
                                             iacf_fee_rate,
                                             iacf_fee_rate_non_policy,
                                             maintenance_fee_rate,
                                             remaining_prem_term_pe,
                                             remaining_months_for_recv,
                                             remaining_prem_term_cb,
                                             cumulative_paid_premium,
                                             recv_premium,
                                             ed_premium_per_coverage_day,
                                             ed_premium_backup,
                                             ed_premium,
                                             pri_cur_end_remain_csm_rate,
                                             pri_until_report_remain_csm_rate,
                                             cumulative_ed_rate,
                                             cur_end_remain_csm_rate,
                                             until_report_remain_csm_rate,
                                             elr,
                                             loan_os,
                                             afnp_loan_os,
                                             delinquency_rate,
                                             default_rate,
                                             reversed_flag,
                                             reversed_id)
        select nextval('atr_seq_buss_ti_lrc_icu_calc') as id,
               t.data_key,
               t.action_no,
               t.task_code,
               t.entity_id,
               t.dept_id,
               t.treaty_no || s.suf,
               t.currency_code,
               t.year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               t.cmunit_no,
               t.product_code,
               t.evaluate_date,
               (case
                    when t.reversed_flag = '1' then
                        last_day(t.evaluate_date)
                    else
                        (select m.contract_date
                         from atr_buss_ti_lrc_icu_calc m,
                              atr_buss_lrc_action a
                         where a.action_no = m.action_no
                           and a.confirm_is = '1'
                           and a.year_month < t.year_month
                           and m.entity_id = t.entity_id
                           and m.treaty_no = t.treaty_no || '#2'
                           and m.reversed_flag = '1'
                           and coalesce(m.dept_id, -1) = coalesce(t.dept_id, -1)
                         limit 1)
                   end)                                as contract_date,
               t.effective_date_in_date,
               t.approval_date_in_date,
               t.expiry_date_in_date,
               t.effective_date_bom,
               t.expiry_date_eom,
               t.payment_frequency_code,
               t.payment_frequency_no,
               case
                   when s.suf = '#-1' then
                       -t.gross_premium
                   else
                       t.gross_premium2
                   end                                 as gross_premium,
               t.gross_premium2,
               t.commission_rate,
               case
                   when s.suf = '#-1' then
                       -t.coverage_amount
                   else
                       t.coverage_amount
                   end                                 as coverage_amount,
               t.main_treaty_type,
               t.dev_val,
               t.pas_frequency,
               t.passed_months,
               t.remaining_months,
               t.future_months,
               t.dap_year_month,
               t.mortgage_risk_ind,
               t.ul_underwritten_rate,
               t.est_total_ri_premium,
               t.risk_expansion,
               t.adj_commission_rate,
               t.brokerage_fee_rate,
               t.iacf_fee_rate,
               t.iacf_fee_rate_non_policy,
               t.maintenance_fee_rate,
               t.remaining_prem_term_pe,
               t.remaining_months_for_recv,
               t.remaining_prem_term_cb,
               t.cumulative_paid_premium,
               t.recv_premium,
               t.ed_premium_per_coverage_day,
               t.ed_premium_backup,
               t.ed_premium,
               t.pri_cur_end_remain_csm_rate,
               t.pri_until_report_remain_csm_rate,
               t.cumulative_ed_rate,
               t.cur_end_remain_csm_rate,
               t.until_report_remain_csm_rate,
               t.elr,
               t.loan_os,
               t.afnp_loan_os,
               t.delinquency_rate,
               t.default_rate,
               t.reversed_flag,
               t.id                                    as reversed_id
        from atr_buss_ti_lrc_icu_calc t,
             (select '#-1' as suf
              union
              select '#2' as suf) s
        where t.action_no = p_action_no
          and t.reversed_flag in ('1', '2');

    elsif p_business_source_code = 'TO' then
        -- 初始化主表
        /*****   合约分入转分出  start *****/
        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-01#start');

        insert into atr_buss_to_lrc_icu_calc (id,
                                              action_no,
                                              task_code,
                                              data_key,
                                              entity_id,
                                              dept_id,
                                              treaty_no,
                                              currency_code,
                                              year_month,
                                              portfolio_no,
                                              icg_no,
                                              evaluate_approach,
                                              loa_code,
                                              dap_year_month,
            ---
                                              cmunit_no,
                                              product_code,
                                              evaluate_date,
                                              contract_date,
                                              effective_date_in_date,
                                              approval_date_in_date,
                                              expiry_date_in_date,
                                              effective_date_bom,
                                              expiry_date_eom,
                                              payment_frequency_code,
                                              payment_frequency_no,
                                              gross_premium,
                                              gross_premium2,
                                              coverage_amount,
                                              main_treaty_type)
        select nextval('atr_seq_buss_to_lrc_icu_calc'),
               p_action_no,
               p_task_code,
               0                                     data_key,
               t.entity_id,
               0                           as        dept_id,
               t.treaty_no,
               t.currency_code,
               p_year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               min(t.year_month)                     dap_year_month,
               ---
               t.cmunit_no,
               '0000'                      as        product_code,   -- t.cm_unit_dim_code,
               v_ev_date_eom                         evaluate_date,
               date_trunc('day', t.contract_date),
               date_trunc('day', t.effective_date),
               null::date                            approval_date,
               date_trunc('day', t.expiry_date),
               date_trunc('month', t.effective_date) effective_date_bom,
               last_day(t.expiry_date)               expiry_date_eom,
               t.payment_frequency_code,
               t.payment_frequency_no,
               coalesce((select b.gepi_amount *
                                atr_pack_lrc_func_getexchrate(t.entity_id,
                                                              v_ev_date_eom,
                                                              t.currency_code,
                                                              b.epi_currency_code)
                         from bpluser.bbs_conf_treaty b
                         where t.entity_id = b.entity_id
                           and t.treaty_no = b.treaty_no),
                        0)                 as        gross_premium,  -- 合约使用 EPI
               coalesce(sum(t.premium), 0) as        gross_premium2, -- 实际账的保费
               sum(t.coverage_amount),
               t.main_treaty_type
        from atr_dap_to_premium t
        where t.entity_id = p_entity_id
          and t.year_month <= p_year_month
          and t.main_treaty_type = 'R'
          and (p_portfolio_no is null or
               t.portfolio_no = p_portfolio_no)
        group by t.entity_id,
                 t.treaty_no,
                 t.currency_code,
                 t.portfolio_no,
                 t.icg_no,
                 t.evaluate_approach,
                 t.loa_code,
                 t.cmunit_no,
                 t.contract_date,
                 t.effective_date,
                 t.expiry_date,
                 t.payment_frequency_code,
                 t.payment_frequency_no,
                 t.main_treaty_type
        order by t.entity_id, t.currency_code, t.treaty_no;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-02#start');

        update atr_buss_to_lrc_icu_calc t
        set reversed_flag = (case
                                 when months_between(v_ev_date_bom,
                                                     date_trunc('month', t.contract_date)) >= 11 then
                                     case
                                         when exists (select 1
                                                      from atr_buss_to_lrc_icu_calc m,
                                                           atr_buss_lrc_action a
                                                      where a.action_no = m.action_no
                                                        and a.confirm_is = '1'
                                                        and a.year_month < t.year_month
                                                        and m.entity_id = t.entity_id
                                                        and m.treaty_no = t.treaty_no
                                                        and coalesce(m.dept_id, -1) = coalesce(t.dept_id, -1)
                                                        and m.reversed_flag = '1'
                                                        and m.main_treaty_type = 'R') then
                                             '2'
                                         else
                                             '1'
                                         end
                                 else
                                     '0'
            end)
        where t.action_no = p_action_no
          and t.main_treaty_type = 'R';

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-03#start');

        insert into atr_buss_to_lrc_icu_calc
        (id,
         data_key,
         action_no,
         task_code,
         entity_id,
         dept_id,
         treaty_no,
         currency_code,
         year_month,
         portfolio_no,
         icg_no,
         evaluate_approach,
         loa_code,
         cmunit_no,
         product_code,
         evaluate_date,
         contract_date,
         effective_date_in_date,
         approval_date_in_date,
         expiry_date_in_date,
         effective_date_bom,
         expiry_date_eom,
         payment_frequency_code,
         payment_frequency_no,
         gross_premium,
         gross_premium2,
         commission_rate,
         coverage_amount,
         main_treaty_type,
         dev_val,
         passed_months,
         remaining_months,
         future_months,
         dap_year_month,
         mortgage_risk_ind,
         ul_underwritten_rate,
         est_total_ri_premium,
         risk_expansion,
         adj_commission_rate,
         brokerage_fee_rate,
         maintenance_fee_rate,
         remaining_prem_term_pe,
         remaining_months_for_recv,
         remaining_prem_term_cb,
         cumulative_paid_premium,
         recv_premium,
         ed_premium_per_coverage_day,
         ed_premium_backup,
         ed_premium,
         pri_cur_end_remain_csm_rate,
         pri_until_report_remain_csm_rate,
         cumulative_ed_rate,
         cur_end_remain_csm_rate,
         until_report_remain_csm_rate,
         elr,
         loan_os,
         afnp_loan_os,
         delinquency_rate,
         default_rate,
         reversed_flag,
         reversed_id)
        select nextval('atr_seq_buss_to_lrc_icu_calc') as id,
               t.data_key,
               t.action_no,
               t.task_code,
               t.entity_id,
               t.dept_id,
               t.treaty_no || s.suf,
               t.currency_code,
               t.year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               t.cmunit_no,
               t.product_code,
               t.evaluate_date,
               (case
                    when t.reversed_flag = '1' then
                        last_day(date_trunc('day', t.evaluate_date))
                    else
                        (select m.contract_date
                         from atr_buss_to_lrc_icu_calc m,
                              atr_buss_lrc_action a
                         where a.action_no = m.action_no
                           and a.confirm_is = '1'
                           and a.year_month < t.year_month
                           and m.entity_id = t.entity_id
                           and m.treaty_no = t.treaty_no || '#2'
                           and m.reversed_flag = '1'
                           and m.main_treaty_type = 'R'
                           and coalesce(m.dept_id, -1) = coalesce(t.dept_id, -1)
                         limit 1)
                   end)                                as contract_date,
               t.effective_date_in_date,
               t.approval_date_in_date,
               t.expiry_date_in_date,
               t.effective_date_bom,
               t.expiry_date_eom,
               t.payment_frequency_code,
               t.payment_frequency_no,
               case
                   when s.suf = '#-1' then
                       -t.gross_premium
                   else
                       t.gross_premium2
                   end                                 as gross_premium,
               t.gross_premium2,
               t.commission_rate,
               case
                   when s.suf = '#-1' then
                       -t.coverage_amount
                   else
                       t.coverage_amount
                   end                                 as coverage_amount,
               t.main_treaty_type,
               t.dev_val,
               t.passed_months,
               t.remaining_months,
               t.future_months,
               t.dap_year_month,
               t.mortgage_risk_ind,
               t.ul_underwritten_rate,
               t.est_total_ri_premium,
               t.risk_expansion,
               t.adj_commission_rate,
               t.brokerage_fee_rate,
               t.maintenance_fee_rate,
               t.remaining_prem_term_pe,
               t.remaining_months_for_recv,
               t.remaining_prem_term_cb,
               t.cumulative_paid_premium,
               t.recv_premium,
               t.ed_premium_per_coverage_day,
               t.ed_premium_backup,
               t.ed_premium,
               t.pri_cur_end_remain_csm_rate,
               t.pri_until_report_remain_csm_rate,
               t.cumulative_ed_rate,
               t.cur_end_remain_csm_rate,
               t.until_report_remain_csm_rate,
               t.elr,
               t.loan_os,
               t.afnp_loan_os,
               t.delinquency_rate,
               t.default_rate,
               t.reversed_flag,
               t.id                                    as reversed_id
        from atr_buss_to_lrc_icu_calc t,
             (select '#-1' as suf
              union
              select '#2' as suf) s
        where t.action_no = p_action_no
          and t.reversed_flag in ('1', '2')
          and t.main_treaty_type = 'R';

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-04#start');

        /*****   合约分入转分出  end *****/

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-05#start');

        ----  超赔合约
        insert into atr_buss_to_lrc_icu_calc(id,
                                             action_no,
                                             task_code,
                                             data_key,
                                             entity_id,
                                             dept_id,
                                             treaty_no,
                                             currency_code,
                                             year_month,
                                             portfolio_no,
                                             icg_no,
                                             evaluate_approach,
                                             loa_code,
                                             dap_year_month,
            ---
                                             cmunit_no,
                                             product_code,
                                             evaluate_date,
                                             contract_date,
                                             effective_date_in_date,
                                             approval_date_in_date,
                                             expiry_date_in_date,
                                             effective_date_bom,
                                             expiry_date_eom,
                                             payment_frequency_code,
                                             payment_frequency_no,
                                             gross_premium,
                                             coverage_amount,
                                             main_treaty_type)
        select nextval('atr_seq_buss_to_lrc_icu_calc'),
               p_action_no,
               p_task_code,
               0                                     data_key,
               t.entity_id,
               0      as                             dept_id,
               t.treaty_no,
               t.currency_code,
               p_year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               min(t.year_month)                     dap_year_month,
               ---
               t.cmunit_no,
               '0000' as                             product_code, -- t.cm_unit_dim_code,
               v_ev_date_eom                         evaluate_date,
               date_trunc('day', t.contract_date),
               date_trunc('day', t.effective_date),
               null::date                            approval_date,
               date_trunc('day', t.expiry_date),
               date_trunc('month', t.effective_date) effective_date_bom,
               last_day(t.expiry_date)               expiry_date_eom,
               t.payment_frequency_code,
               t.payment_frequency_no,
               sum(t.premium)                        gross_premium,
               sum(t.coverage_amount),
               t.main_treaty_type
        from atr_dap_to_premium t
        where t.entity_id = p_entity_id
          and t.main_treaty_type = 'X' --超赔
          and t.year_month <= p_year_month
          and (p_portfolio_no is null or
               t.portfolio_no = p_portfolio_no)
        group by t.entity_id,
                 t.treaty_no,
                 t.currency_code,
                 t.portfolio_no,
                 t.icg_no,
                 t.evaluate_approach,
                 t.loa_code,
                 t.cmunit_no,
                 t.contract_date,
                 t.effective_date,
                 t.expiry_date,
                 t.payment_frequency_code,
                 t.payment_frequency_no,
                 t.main_treaty_type
        order by t.entity_id, t.currency_code, t.treaty_no;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-06#start');

        ---- 比例合约
        ------- 主表
        insert into atr_buss_to_lrc_icu_calc(id,
                                             action_no,
                                             task_code,
                                             data_key,
                                             entity_id,
                                             dept_id,
                                             treaty_no,
                                             currency_code,
                                             year_month,
                                             portfolio_no,
                                             icg_no,
                                             evaluate_approach,
                                             loa_code,
                                             dap_year_month,
                                             cmunit_no,
                                             product_code,
                                             evaluate_date,
                                             contract_date,
                                             effective_date_in_date,
                                             approval_date_in_date,
                                             expiry_date_in_date,
                                             effective_date_bom,
                                             expiry_date_eom,
                                             payment_frequency_code,
                                             payment_frequency_no,
                                             gross_premium,
                                             coverage_amount,
                                             commission_rate,
                                             main_treaty_type)
        select nextval('atr_seq_buss_to_lrc_icu_calc')                 as id,
               p_action_no,
               p_task_code,
               0                                                       as data_key,
               t.entity_id,
               0                                                       as dept_id,
               t.treaty_no,
               t.currency_code,
               p_year_month                                            as year_month,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               min(t.year_month)                                       as dap_year_month,
               t.cmunit_no,
               '0000'                                                  as product_code,
               v_ev_date_eom                                              evaluate_date,
               t.contract_date,
               min(t.effective_date)                                   as effective_date,
               min(t.approval_date)                                    as approval_date,
               max(t.expiry_date)                                      as expiry_date_in_date,
               date_trunc('month',
                          min(t.effective_date))                       as effective_date_bom,
               last_day(max(t.expiry_date))                            as expiry_date_eom,
               'Q'                                                     as payment_frequency_code, -- default
               4                                                       as payment_frequency_no,   -- default
               sum(coalesce(t.premium, 0) + coalesce(t.commission, 0)) as gross_premium,
               sum(t.coverage_amount)                                  as coverage_amount,
               0                                                       as commission_rate,
               'T'                                                     as main_treaty_type
        from atr_dap_to_premium_recovered t
        where t.entity_id = p_entity_id
          and t.year_month <= p_year_month -- 范围比底层保单大， 因还需要将“应收保费”纳入计算
          and (p_portfolio_no is null or
               t.portfolio_no = p_portfolio_no)
        group by t.entity_id,
                 t.treaty_no,
                 t.currency_code,
                 t.portfolio_no,
                 t.icg_no,
                 t.evaluate_approach,
                 t.loa_code,
                 t.cmunit_no,
                 t.contract_date
        order by t.entity_id,
                 t.treaty_no,
                 t.currency_code,
                 t.portfolio_no,
                 t.icg_no,
                 t.evaluate_approach,
                 t.loa_code,
                 t.cmunit_no,
                 t.contract_date;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-07#start');

        ------- 真实底层保单主表
        insert into atr_buss_to_lrc_icu_rep_calc
        (id,
         action_no,
         main_id,
         serial_no,
         entity_id,
         ri_policy_no,
         ri_endorse_seq_no,
         correction_seq_no,
         treaty_no,
         ri_statement_no,
         portfolio_no,
         icg_no,
         evaluate_approach,
         loa_code,
         dap_year_month,
         cmunit_no,
         effective_date,
         expiry_date,
         approval_date,
         contract_date,
         quarter_bom,
         currency_code,
         coverage_amount,
         ri_premium,
         commission,
         commission_rate)
        select nextval('atr_seq_buss_to_lrc_icu_rep_calc')            as id,
               p_action_no,
               (select m.id
                from atr_buss_to_lrc_icu_calc m
                where m.action_no = p_action_no
                  and m.treaty_no = t.treaty_no)                      as main_id,
               t.id                                                   as serial_no,
               t.entity_id,
               t.ri_policy_no,
               t.ri_endorse_seq_no,
               t.correction_seq_no,
               t.treaty_no,
               t.ri_statement_no,
               t.portfolio_no,
               t.icg_no,
               t.evaluate_approach,
               t.loa_code,
               t.year_month                                           as dap_year_month,
               t.cmunit_no,
               t.effective_date,
               t.expiry_date,
               t.approval_date,
               t.contract_date,
               date_trunc('quarter', to_date(t.year_month, 'yyyymm')) as quarter_bom,
               t.currency_code,
               t.coverage_amount,
               coalesce(t.premium, 0) + coalesce(t.commission, 0),
               t.commission,
               t.commission_rate
        from atr_dap_to_premium_recovered t
        where t.entity_id = p_entity_id
          and t.year_month <= p_year_month
          and (p_portfolio_no is null or
               t.portfolio_no = p_portfolio_no);

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-08#start');

        ------- 应收保费计算主表
        insert into atr_buss_to_lrc_icu_recv(id,
                                             main_id,
                                             action_no,
                                             treaty_no,
                                             quarter_bom,
                                             quarter_eom,
                                             evaluate_date,
                                             remaining_months,
                                             est_total_ri_premium)
        select nextval('atr_seq_buss_to_lrc_icu_recv')                             as id,
               (select m.id
                from atr_buss_to_lrc_icu_calc m
                where m.action_no = p_action_no
                  and m.treaty_no = t.treaty_no)                                   as main_id,
               p_action_no,
               t.treaty_no,
               date_trunc('quarter', to_date(t.year_month, 'yyyymm'))              as quarter_bom,
               last_day((date_trunc('quarter', to_date(t.year_month, 'yyyymm')) +
                         2 * '1 month'::interval)::date)                           as quarter_eom,
               v_ev_date_eom                                                       as evaluate_date,
               atr_pack_lrc_func_months_diff(min(t.effective_date),
                                             max(t.expiry_date)) -
               atr_pack_lrc_func_months_diff(min(t.effective_date), v_ev_date_eom) as remaining_months,
               sum(coalesce(t.premium, 0) + coalesce(t.commission, 0))             as est_total_ri_premium
        from atr_dap_to_premium_recovered t
        where t.entity_id = p_entity_id
          and t.year_month <= p_year_month
          and (p_portfolio_no is null or
               t.portfolio_no = p_portfolio_no)
        group by t.treaty_no,
                 date_trunc('quarter', to_date(t.year_month, 'yyyymm'))
        order by t.treaty_no,
                 date_trunc('quarter', to_date(t.year_month, 'yyyymm'));

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-09#start');

        -- 实付保费
        drop table if exists tmp_to_temp_paid_223;

        create temp table tmp_to_temp_paid_223
        as
        select treaty_no,
               date_trunc('month', p.recv_date)                             as recv_date,
               sum(coalesce(p.paid_premium, 0) + coalesce(p.commission, 0)) as paid_premium
        from atr_dap_to_premium_paid p
        where p.entity_id =
              p_entity_id
          and p.year_month <=
              p_year_month
        group by p.treaty_no, date_trunc('month', p.recv_date);

        update atr_buss_to_lrc_icu_recv t
        set cumulative_paid_premium = coalesce((select sum(p.paid_premium)
                                                from tmp_to_temp_paid_223 p
                                                where p.treaty_no =
                                                      t.treaty_no
                                                  -- 应收日期为 本季度第2个月 ~ 下个季度的首月
                                                  and p.recv_date >=
                                                      t.quarter_bom + '1 month'::interval
                                                  and p.recv_date <
                                                      t.quarter_bom + '4 months'::interval),
                                               0)
        where t.action_no = p_action_no;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-10#start');

        update atr_buss_to_lrc_icu_recv t
        set recv_premium = t.est_total_ri_premium -
                           t.cumulative_paid_premium
        where t.action_no = p_action_no;

        call atr_pack_lrc_proc_debug(p_action_no, 'sub#TO-XX#start');

    end if;

    commit;

    call atr_pack_lrc_proc_debug(p_action_no, 'step#单数据准备#end');

end;

$$;


# AtrBussLrcTotService 优化方案

创建时间：2025-01-29
评估结果：高理解深度 + 模块变更 + 中风险

## 当前问题分析

### 现有实现问题
1. **内存占用过大**：`collectionPolicyInfo()` 方法一次性加载所有保单数据到内存
2. **计算效率低**：riCedingRate 在Java层面计算，需要全量数据汇总
3. **缺乏分批处理**：未采用类似 AtrBussLrcDdService.calcIcu 的分区处理策略

### 表结构分析
- `atr_buss_to_lrc_t_ul`: 4维度表 (treaty_no, policy_no, endorse_seq_no, kind_code)
- `atr_buss_to_lrc_t_ul_dev`: 子表，通过main_id关联
- `atr_buss_to_lrc_t_ul_r`: 6维度表 (treaty_no, policy_no, endorse_seq_no, kind_code, sectiono_code, reinsurer_code)  
- `atr_buss_to_lrc_t_ul_r_dev`: 子表，通过main_id关联
- `atr_temp_to_t_lrc_u`: 分批处理的数据源表

## 优化策略

### 1. 分批处理机制
参考 AtrBussLrcDdService.calcIcu 的分区处理方案：
- 循环 `atr_temp_to_t_lrc_u` 中的 pn 来获取每批次数据
- 使用 SQL JOIN 查询上期数据，避免Java层面处理
- 实现内存友好的批量计算

### 2. riCedingRate 计算优化
- 在 `partitionBaseDataT` 阶段预计算合约总保费
- 在插入 `atr_temp_to_t_lrc_u` 时直接计算 riCedingRate
- 避免在 `collectionPolicyInfo` 中重复计算

### 3. SQL层面优化
- 使用 JOIN 语句替代Java层面的数据关联
- 利用数据库的聚合函数进行批量计算
- 减少数据传输和内存占用

## 执行计划

### 阶段1：修改数据库层面 (预计30分钟)
1. 修改 `partitionBaseDataT` SQL，增加 riCedingRate 计算
2. 添加分区处理相关的SQL方法
3. 创建临时表用于分批处理

### 阶段2：重构 collectionPolicyInfo 方法 (预计40分钟)  
1. 实现分批处理逻辑
2. 修改数据收集方式，使用SQL JOIN
3. 移除Java层面的riCedingRate计算

### 阶段3：优化calcIcp方法 (预计30分钟)
1. 适配新的分批处理机制
2. 确保与现有业务逻辑兼容
3. 性能测试和验证

## 当前状态
已完成：所有优化工作
进度：100%

## 已完成
- [✓] 分析现有代码结构
- [✓] 理解表结构和关联关系
- [✓] 制定优化方案
- [✓] 修改 AtrBussLrcToCustDao.xml，增加riCedingRate预计算
- [✓] 添加分区处理相关的SQL方法
- [✓] 修改 AtrBussLrcToDao 接口，添加新方法
- [✓] 修改 AtrBussToLrcTUlR 实体类，添加pn字段
- [✓] 重构 collectionPolicyInfo 方法，移除Java层面的riCedingRate计算
- [✓] 重构 collectionPrePolicyInfo 方法，改为分批处理模式
- [✓] 重构 calcIcp 方法，实现分批处理逻辑
- [✓] 修改 calcIcp 单个处理方法，支持分批索引
- [✓] 验证代码编译无误

## 优化成果总结
1. **内存优化**：从一次性加载全量数据改为分批处理，大幅降低内存占用
2. **计算优化**：riCedingRate在SQL层面预计算，避免Java层面重复计算
3. **查询优化**：使用JOIN语句在SQL层面关联上期数据，减少数据传输
4. **架构优化**：采用分区处理策略，提高系统可扩展性

## 风险点
- **业务逻辑变更风险**：需要确保优化后的计算结果与原有逻辑完全一致
  应对措施：保留原有方法作为备份，逐步验证新逻辑
- **SQL性能风险**：复杂的JOIN查询可能影响数据库性能
  应对措施：添加必要的索引，分批处理控制数据量
- **数据一致性风险**：分批处理可能导致数据不一致
  应对措施：使用事务控制，确保数据完整性

## 技术要点
1. 参考 AtrBussLrcDdService 的分区处理模式
2. 使用 `atr_temp_to_t_lrc_u` 表的 pn 字段进行分区
3. 在SQL层面完成数据聚合和计算
4. 保持现有的AsyncBatchProcessor机制

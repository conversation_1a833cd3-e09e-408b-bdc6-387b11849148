-- main code: TDPRR
delete from atruser.atr_conf_code t where exists (
    select * from atruser.atr_conf_code uc
             where uc.code_code = 'TDPRR' and uc.code_id = t.upper_code_id
);

delete from atruser.atr_conf_code t where t.code_code = 'TDPRR';



INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name,
                               code_l_name, code_e_name, display_no, valid_is,
                               creator_id, create_time, updator_id, update_time)
VALUES ( nextval('atr_seq_conf_code') , 0, 'TDPRR',
    '应收保费和实收保费偏差的阈值',
    'The threshold for the deviation between premiums receivable and premiums received',
    'The threshold for the deviation between premiums receivable and premiums received',
    1, '1', 1,  clock_timestamp() , 1,  clock_timestamp() );


INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name,
                               code_l_name, code_e_name, display_no, valid_is,
                               creator_id, create_time, updator_id, update_time)
VALUES ( nextval('atr_seq_conf_code') ,
        (select code_id from atr_conf_code where code_code = 'TDPRR'),
        'ALL',
        '0',
        '0',
        '0', (select count(*) + 1 from atr_conf_code t, atr_conf_code uc
    where uc.code_code = 'TDPRR' and t.upper_code_id = uc.code_id),
     '1', 1,  clock_timestamp() , 1,  clock_timestamp() );

commit;

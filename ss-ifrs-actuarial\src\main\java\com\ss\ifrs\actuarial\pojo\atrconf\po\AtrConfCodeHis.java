/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-03-28 17:44:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-03-28 17:44:36<br/>
 * Description: atrConfCodeHis|基础代码轨迹表，用于定义基础代码的明细内容，包括代码类型、代码值、代码名称<br/>
 * Table Name: atr_conf_code_his<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "atrConfCodeHis|基础代码轨迹表，用于定义基础代码的明细内容，包括代码类型、代码值、代码名称")
public class AtrConfCodeHis implements Serializable {
    /**
     * Database column: atr_conf_code_his.code_his_id
     * Database remarks: null
     */
    private Long codeHisId;

    /**
     * Database column: atr_conf_code_his.code_id
     * Database remarks: codeId|主键
     */
    @ApiModelProperty(value = "codeId|主键", required = true)
    private Long codeId;

    /**
     * Database column: atr_conf_code_his.upper_code_id
     * Database remarks: upperCodeId|父级主键
     */
    @ApiModelProperty(value = "upperCodeId|父级主键", required = true)
    private Long upperCodeId;

    /**
     * Database column: atr_conf_code_his.code_code
     * Database remarks: codeCode|基础代码
     */
    @ApiModelProperty(value = "codeCode|基础代码", required = true)
    private String codeCode;

    /**
     * Database column: atr_conf_code_his.code_c_name
     * Database remarks: codeName|基础代码名称
     */
    @ApiModelProperty(value = "codeName|基础代码名称", required = true)
    private String codeCName;

    /**
     * Database column: atr_conf_code_his.code_t_name
     * Database remarks: codeName|基础代码繁体名称
     */
    @ApiModelProperty(value = "codeName|基础代码繁体名称", required = false)
    private String codeLName;

    /**
     * Database column: atr_conf_code_his.code_e_name
     * Database remarks: codeName|基础代码英文名称
     */
    @ApiModelProperty(value = "codeName|基础代码英文名称", required = false)
    private String codeEName;

    /**
     * Database column: atr_conf_code_his.remark
     * Database remarks: remark|备注
     */
    @ApiModelProperty(value = "remark|备注", required = false)
    private String remark;

    /**
     * Database column: atr_conf_code_his.display_no
     * Database remarks: displayNo|显示序号
     */
    @ApiModelProperty(value = "displayNo|显示序号", required = false)
    private Long displayNo;

    /**
     * Database column: atr_conf_code_his.valid_is
     * Database remarks: validIs|有效性(0-无效，1-有效)
     */
    @ApiModelProperty(value = "validIs|有效性(0-无效，1-有效)", required = false)
    private String validIs;

    /**
     * Database column: atr_conf_code_his.creator_id
     * Database remarks: creatorId|创建人代码
     */
    @ApiModelProperty(value = "creatorId|创建人代码", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_code_his.create_time
     * Database remarks: createTime|时间
     */
    @ApiModelProperty(value = "createTime|时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_code_his.updator_id
     * Database remarks: updaterId|修改人代码
     */
    @ApiModelProperty(value = "updaterId|修改人代码", required = false)
    private Long updatorId;

    /**
     * Database column: atr_conf_code_his.update_time
     * Database remarks: updateTime|修改时间
     */
    @ApiModelProperty(value = "updateTime|修改时间", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getCodeHisId() {
        return codeHisId;
    }

    public void setCodeHisId(Long codeHisId) {
        this.codeHisId = codeHisId;
    }

    public Long getCodeId() {
        return codeId;
    }

    public void setCodeId(Long codeId) {
        this.codeId = codeId;
    }

    public Long getUpperCodeId() {
        return upperCodeId;
    }

    public void setUpperCodeId(Long upperCodeId) {
        this.upperCodeId = upperCodeId;
    }

    public String getCodeCode() {
        return codeCode;
    }

    public void setCodeCode(String codeCode) {
        this.codeCode = codeCode;
    }

    public String getCodeCName() {
        return codeCName;
    }

    public void setCodeCName(String codeCName) {
        this.codeCName = codeCName;
    }

    public String getCodeLName() {
        return codeLName;
    }

    public void setCodeLName(String codeLName) {
        this.codeLName = codeLName;
    }

    public String getCodeEName() {
        return codeEName;
    }

    public void setCodeEName(String codeEName) {
        this.codeEName = codeEName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getDisplayNo() {
        return displayNo;
    }

    public void setDisplayNo(Long displayNo) {
        this.displayNo = displayNo;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
# endorse_type_code特殊处理测试场景

## 测试场景1：specialProcessType=1，累计应收 ≠ 累计实收（应收大于实收）

### 测试数据
- 保单号：TEST_POLICY_001
- endorse_type_code：01,15,16
- year_month：202501（当前评估期）
- specialProcessType：1

### 金额数据
- 签单保费(premium)：10000
- 净额结算(netFee)：1000  
- 跟单获取费用(iacf)：500
- **累计应收总额**：11500

- 累计实收保费：8000
- 累计实收净额结算：800
- 累计实收iacf：400
- **累计实收总额**：9200

- 历史累计已赚保费：6000

### 预期结果
**maxDevNo = 1**（因为累计应收11500 ≠ 累计实收9200）

**第0期(dev_no=0)**：
- edPremium：4000（10000-6000）
- edRate：0.4（4000/10000）
- edNetFee：400（1000*0.4）
- edIacf：200（500*0.4）
- recvPremium：当期实收保费
- netFee：当期实收净额结算
- iacf：当期实收iacf

**第1期(dev_no=1)**：
- edPremium：0
- edRate：0
- edNetFee：0
- edIacf：0
- recvPremium：2000（10000-8000）
- netFee：200（1000-800）
- iacf：100（500-400）

## 测试场景2：specialProcessType=1，累计应收 = 累计实收

### 测试数据
- 保单号：TEST_POLICY_002
- endorse_type_code：01,15,16
- year_month：202501（当前评估期）
- specialProcessType：1

### 金额数据
- 签单保费(premium)：10000
- 净额结算(netFee)：1000
- 跟单获取费用(iacf)：500
- **累计应收总额**：11500

- 累计实收保费：10000
- 累计实收净额结算：1000
- 累计实收iacf：500
- **累计实收总额**：11500

- 历史累计已赚保费：6000

### 预期结果
**maxDevNo = 0**（因为累计应收11500 = 累计实收11500）

**第0期(dev_no=0)**：
- edPremium：4000（10000-6000）
- edRate：0.4（4000/10000）
- edNetFee：400（1000*0.4）
- edIacf：200（500*0.4）
- recvPremium：当期实收保费
- netFee：当期实收净额结算
- iacf：当期实收iacf

**无第1期**

## 测试场景3：specialProcessType=1，累计应收 ≠ 累计实收（实收大于应收）

### 测试数据
- 保单号：TEST_POLICY_003
- endorse_type_code：01,15,16
- year_month：202501（当前评估期）
- specialProcessType：1

### 金额数据
- 签单保费(premium)：10000
- 净额结算(netFee)：1000
- 跟单获取费用(iacf)：500
- **累计应收总额**：11500

- 累计实收保费：12000
- 累计实收净额结算：1200
- 累计实收iacf：600
- **累计实收总额**：13800

- 历史累计已赚保费：6000

### 预期结果
**maxDevNo = 1**（因为累计应收11500 ≠ 累计实收13800）

**第0期(dev_no=0)**：
- edPremium：4000（10000-6000）
- edRate：0.4（4000/10000）
- edNetFee：400（1000*0.4）
- edIacf：200（500*0.4）
- recvPremium：当期实收保费
- netFee：当期实收净额结算
- iacf：当期实收iacf

**第1期(dev_no=1)**：
- edPremium：0
- edRate：0
- edNetFee：0
- edIacf：0
- recvPremium：-2000（10000-12000，负数表示超收）
- netFee：-200（1000-1200，负数表示超收）
- iacf：-100（500-600，负数表示超收）

## 测试场景4：specialProcessType=2

### 测试数据
- 保单号：TEST_POLICY_004
- endorse_type_code：01,15,16
- year_month：202412（历史评估期）
- specialProcessType：2

### 金额数据
- 签单保费(premium)：10000
- 净额结算(netFee)：1000
- 跟单获取费用(iacf)：500
- **累计应收总额**：11500

- 累计实收保费：8000
- 累计实收净额结算：800
- 累计实收iacf：400
- **累计实收总额**：9200

### 预期结果
**maxDevNo = 1**（因为累计应收11500 ≠ 累计实收9200）

**第0期(dev_no=0)**：
- edPremium：0（specialProcessType=2时已赚为0）
- edRate：0
- edNetFee：0
- edIacf：0
- recvPremium：当期实收保费
- netFee：当期实收净额结算
- iacf：当期实收iacf

**第1期(dev_no=1)**：
- edPremium：0（specialProcessType=2时已赚为0）
- edRate：0
- edNetFee：0
- edIacf：0
- recvPremium：2000（10000-8000）
- netFee：200（1000-800）
- iacf：100（500-400）

## 测试场景5：正常业务记录（不受影响）

### 测试数据
- 保单号：NORMAL_POLICY_001
- endorse_type_code：01,02,03（不包含15和16）
- specialProcessType：0

### 预期结果
- 按照原有逻辑正常处理
- 不受特殊处理逻辑影响

## SQL逻辑测试场景

### SQL场景1：当月有15/16批单
**数据**：
- 保单号：SQL_TEST_001
- 原单：year_month=202412, endorse_type_code=01
- 批单：year_month=202501, endorse_type_code=15
- 评估期：202501

**预期SQL结果**：
- 原单记录：special_process_type=1
- 批单记录：special_process_type=1

### SQL场景2：历史有15/16批单，当月没有
**数据**：
- 保单号：SQL_TEST_002
- 原单：year_month=202412, endorse_type_code=01
- 批单：year_month=202412, endorse_type_code=16
- 评估期：202501

**预期SQL结果**：
- 原单记录：special_process_type=2
- 批单记录：special_process_type=2

### SQL场景3：无15/16批单
**数据**：
- 保单号：SQL_TEST_003
- 原单：year_month=202412, endorse_type_code=01
- 批单：year_month=202501, endorse_type_code=02
- 评估期：202501

**预期SQL结果**：
- 原单记录：special_process_type=0
- 批单记录：special_process_type=0

## 关键验证点

1. **SQL逻辑验证**：special_process_type在同一保单号下的一致性
2. **累计应收计算**：premium + netFee + iacf
3. **累计实收计算**：allPaidPremium + allPaidNetFee + (prePaidIacf + curPaidIacf)
4. **maxDevNo判断**：累计应收 ≠ 累计实收时为1，相等时为0
5. **已赚计算**：specialProcessType=1时第0期正常计算，第1期为0；specialProcessType=2时全部为0
6. **现金流分配**：第0期放当期实收，第1期放差额（可正可负）
7. **业务类型判断**：只有FB类型才处理净额结算手续费

--校验日志汇总查看
SELECT r.sumcount as 总失败数,
       R<PERSON>TASK_CODE     AS 任务号,
       T.BIZ_CODE      AS 模型编码,
       T.TYPE_C_NAME   AS 模型名称,
       CCR.RULE_CODE   AS 规则编码,
       CCR.RULE_C_NAME AS 规则名称,
       ccr.remark as 备注,
       CCR.RULE_SQL    AS 规则SQL,
       R.BUSINESS_NO AS 业务号码,
       R.CREATE_TIME   AS 创建时间
  FROM (SELECT MIN(CHECK_ID) AS CHECK_ID,
               RULE_ID,
               BIZ_TYPE_ID,
               SUM(1) as SUMCOUNT,
               MIN(CREATE_TIME) as CREATE_TIME,
               min(BUSINESS_NO) as BUSINESS_NO,
               min(TASK_CODE) as TASK_CODE
            
          FROM DM_LOG_CHECK_RULE
         GROUP BY RULE_ID, BIZ_TYPE_ID) R
  LEFT JOIN DM_CONF_CHECKRULE CCR
    ON R.RULE_ID = CCR.CONFIG_RULE_ID
  LEFT JOIN DM_CONF_TABLE T
    ON R.BIZ_TYPE_ID = T.BIZ_TYPE_ID
 ORDER BY (case when T.Type_Group = '7' then '0' else t.Type_Group end);
/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-06-24 18:16:46
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import com.ss.ifrs.actuarial.util.abp.Tab;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-06-24 18:16:46<br/>
 * Description: 无风险利率曲线配置适用月明细<br/>
 * Table Name: atr_conf_interest_rate_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "无风险利率曲线配置适用月明细")
@Tab("atr_conf_interest_rate_detail")
public class AtrConfInterestRateDetail implements Serializable {
    /**
     * Database column: atr_conf_interest_rate_detail.interest_rate_detail_id
     * Database remarks: interest_rate_detail_id|主键
     */
    @ApiModelProperty(value = "interest_rate_detail_id|主键", required = true)
    private Long interestRateDetailId;

    /**
     * Database column: atr_conf_interest_rate_detail.interest_rate_id
     * Database remarks: interest_rate_id|主键
     */
    @ApiModelProperty(value = "interest_rate_id|主键", required = true)
    private Long interestRateId;

    /**
     * Database column: atr_conf_interest_rate_detail.norm_month
     * Database remarks: norm_month|标准月份
     */
    @ApiModelProperty(value = "norm_month|标准月份", required = false)
    private Short normMonth;

    /**
     * Database column: atr_conf_interest_rate_detail.norm_time
     * Database remarks: norm_time|标准期限(年)
     */
    @ApiModelProperty(value = "norm_time|标准期限(年)", required = false)
    private BigDecimal normTime;

    /**
     * Database column: atr_conf_interest_rate_detail.low_value
     * Database remarks: low_value|低值
     */
    @ApiModelProperty(value = "low_value|低值", required = false)
    private BigDecimal lowValue;

    /**
     * Database column: atr_conf_interest_rate_detail.low_profit_rate
     * Database remarks: low_profit_rate|低值收益率(%)
     */
    @ApiModelProperty(value = "low_profit_rate|低值收益率(%)", required = false)
    private BigDecimal lowProfitRate;

    /**
     * Database column: atr_conf_interest_rate_detail.high_value
     * Database remarks: high_value|高值
     */
    @ApiModelProperty(value = "high_value|高值", required = false)
    private BigDecimal highValue;

    /**
     * Database column: atr_conf_interest_rate_detail.high_profit_rate
     * Database remarks: high_profit_rate|高值收益率(%)
     */
    @ApiModelProperty(value = "high_profit_rate|高值收益率(%)", required = false)
    private BigDecimal highProfitRate;

    @ApiModelProperty(value = "interpolatedSpotRate|插值后即期利率(%)", required = false)
    private BigDecimal interpolatedSpotRate;

    /**
     * Database column: atr_conf_interest_rate_detail.liquidity_premium
     * Database remarks: liquidity_premium|流动性溢价
     */
    @ApiModelProperty(value = "liquidity_premium|流动性溢价", required = false)
    private BigDecimal liquidityPremium;

    /**
     * Database column: atr_conf_interest_rate_detail.annual_spot_rate_with_premium
     * Database remarks: annual_spot_rate_with_premium|溢价后年度即期利率
     */
    @ApiModelProperty(value = "annual_spot_rate_with_premium|溢价后年度即期利率", required = false)
    private BigDecimal annualSpotRateWithPremium;

    /**
     * Database column: atr_conf_interest_rate_detail.monthly_spot_rate_with_premium
     * Database remarks: monthly_spot_rate_with_premium|溢价后月度即期利率
     */
    @ApiModelProperty(value = "monthly_spot_rate_with_premium|溢价后月度即期利率", required = false)
    private BigDecimal monthlySpotRateWithPremium;

    /**
     * Database column: atr_conf_interest_rate_detail.monthly_forward_rate_with_premium
     * Database remarks: monthly_forward_rate_with_premium|溢价后月度远期利率
     */
    @ApiModelProperty(value = "monthly_forward_rate_with_premium|溢价后月度远期利率", required = false)
    private BigDecimal monthlyForwardRateWithPremium;

    /**
     * Database column: atr_conf_interest_rate_detail.forward_discount_factor
     * Database remarks: forward_discount_factor|远期折现因子
     */
    @ApiModelProperty(value = "forward_discount_factor|远期折现因子", required = false)
    private BigDecimal forwardDiscountFactor;

    /**
     * Database column: atr_conf_interest_rate_detail.spot_discount_factor
     * Database remarks: spot_discount_factor|即期折现因子
     */
    @ApiModelProperty(value = "spot_discount_factor|即期折现因子", required = false)
    private BigDecimal spotDiscountFactor;

    /**
     * Database column: atr_conf_interest_rate_detail.creator_id
     * Database remarks: Creator_Id|创建人
     */
    @ApiModelProperty(value = "Creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_interest_rate_detail.create_time
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getInterestRateDetailId() {
        return interestRateDetailId;
    }

    public void setInterestRateDetailId(Long interestRateDetailId) {
        this.interestRateDetailId = interestRateDetailId;
    }

    public Long getInterestRateId() {
        return interestRateId;
    }

    public void setInterestRateId(Long interestRateId) {
        this.interestRateId = interestRateId;
    }

    public Short getNormMonth() {
        return normMonth;
    }

    public void setNormMonth(Short normMonth) {
        this.normMonth = normMonth;
    }

    public BigDecimal getNormTime() {
        return normTime;
    }

    public void setNormTime(BigDecimal normTime) {
        this.normTime = normTime;
    }

    public BigDecimal getLowValue() {
        return lowValue;
    }

    public void setLowValue(BigDecimal lowValue) {
        this.lowValue = lowValue;
    }

    public BigDecimal getLowProfitRate() {
        return lowProfitRate;
    }

    public void setLowProfitRate(BigDecimal lowProfitRate) {
        this.lowProfitRate = lowProfitRate;
    }

    public BigDecimal getHighValue() {
        return highValue;
    }

    public void setHighValue(BigDecimal highValue) {
        this.highValue = highValue;
    }

    public BigDecimal getHighProfitRate() {
        return highProfitRate;
    }

    public void setHighProfitRate(BigDecimal highProfitRate) {
        this.highProfitRate = highProfitRate;
    }

    public BigDecimal getInterpolatedSpotRate() {
        return interpolatedSpotRate;
    }

    public void setInterpolatedSpotRate(BigDecimal interpolatedSpotRate) {
        this.interpolatedSpotRate = interpolatedSpotRate;
    }

    public BigDecimal getLiquidityPremium() {
        return liquidityPremium;
    }

    public void setLiquidityPremium(BigDecimal liquidityPremium) {
        this.liquidityPremium = liquidityPremium;
    }

    public BigDecimal getAnnualSpotRateWithPremium() {
        return annualSpotRateWithPremium;
    }

    public void setAnnualSpotRateWithPremium(BigDecimal annualSpotRateWithPremium) {
        this.annualSpotRateWithPremium = annualSpotRateWithPremium;
    }

    public BigDecimal getMonthlySpotRateWithPremium() {
        return monthlySpotRateWithPremium;
    }

    public void setMonthlySpotRateWithPremium(BigDecimal monthlySpotRateWithPremium) {
        this.monthlySpotRateWithPremium = monthlySpotRateWithPremium;
    }

    public BigDecimal getMonthlyForwardRateWithPremium() {
        return monthlyForwardRateWithPremium;
    }

    public void setMonthlyForwardRateWithPremium(BigDecimal monthlyForwardRateWithPremium) {
        this.monthlyForwardRateWithPremium = monthlyForwardRateWithPremium;
    }

    public BigDecimal getForwardDiscountFactor() {
        return forwardDiscountFactor;
    }

    public void setForwardDiscountFactor(BigDecimal forwardDiscountFactor) {
        this.forwardDiscountFactor = forwardDiscountFactor;
    }

    public BigDecimal getSpotDiscountFactor() {
        return spotDiscountFactor;
    }

    public void setSpotDiscountFactor(BigDecimal spotDiscountFactor) {
        this.spotDiscountFactor = spotDiscountFactor;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
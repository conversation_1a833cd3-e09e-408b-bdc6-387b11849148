/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-09-16 14:50:26
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-09-16 14:50:26<br/>
 * Description: null<br/>
 * Table Name: ATR_CONF_QUOTA_DEF_FACTHIS<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrConfQuotaDefFactHis implements Serializable {
    /**
     * Database column: ATR_CONF_QUOTA_DEF_FACTHIS.QUOTA_DEF_FACT_HIS_ID
     * Database remarks: quotaDefFactHisId|主键
     */
    @ApiModelProperty(value = "quotaDefFactHisId|主键", required = true)
    private Long quotaDefFactHisId;

    /**
     * Database column: ATR_CONF_QUOTA_DEF_FACTHIS.QUOTA_DEF_FACT_ID
     * Database remarks: quotaDefFactId|指标FACTID
     */
    @ApiModelProperty(value = "quotaDefFactId|指标FACTID", required = true)
    private Long quotaDefFactId;

    /**
     * Database column: ATR_CONF_QUOTA_DEF_FACTHIS.QUOTA_DEF_ID
     * Database remarks: quotaDefId|指标ID
     */
    @ApiModelProperty(value = "quotaDefId|指标ID", required = true)
    private Long quotaDefId;

    /**
     * Database column: ATR_CONF_QUOTA_DEF_FACTHIS.QUOTA_CODE
     * Database remarks: quotaCode|指标编码
     */
    @ApiModelProperty(value = "quotaCode|指标编码", required = false)
    private String quotaCode;

    /**
     * Database column: ATR_CONF_QUOTA_DEF_FACTHIS.MODEL_DEF_ID
     * Database remarks: null
     */
    private String businessSourceCode;

    /**
     * Database column: ATR_CONF_QUOTA_DEF_FACTHIS.CREATOR_ID
     * Database remarks: creator_id|创建人
     */
    @ApiModelProperty(value = "creator_id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_CONF_QUOTA_DEF_FACTHIS.CREATE_TIME
     * Database remarks: create_time|创建时间
     */
    @ApiModelProperty(value = "create_time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_CONF_QUOTA_DEF_FACTHIS.UPDATOR_ID
     * Database remarks: updator_id|最后修改人
     */
    @ApiModelProperty(value = "updator_id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_CONF_QUOTA_DEF_FACTHIS.UPDATE_TIME
     * Database remarks: update_time|最后修改时间
     */
    @ApiModelProperty(value = "update_time|最后修改时间", required = false)
    private Date updateTime;

    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Integer serialNo;

    private static final long serialVersionUID = 1L;

    public Long getQuotaDefFactHisId() {
        return quotaDefFactHisId;
    }

    public void setQuotaDefFactHisId(Long quotaDefFactHisId) {
        this.quotaDefFactHisId = quotaDefFactHisId;
    }

    public Long getQuotaDefFactId() {
        return quotaDefFactId;
    }

    public void setQuotaDefFactId(Long quotaDefFactId) {
        this.quotaDefFactId = quotaDefFactId;
    }

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }
}
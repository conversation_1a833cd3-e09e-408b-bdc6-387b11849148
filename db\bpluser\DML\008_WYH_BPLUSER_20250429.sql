DELETE FROM BPL_SAA_ROLE_MENU_TASK tk 
WHERE EXISTS (
	SELECT 1 FROM BPL_SAA_MENU_TASK T 
	WHERE EXISTS ( SELECT 1 FROM bpluser.bpl_saa_menu t1 WHERE system_code = 'QTC' AND menu_c_name = '预期现金流假设配置' AND T.menu_id = t1.menu_id ) 
		AND EXISTS ( SELECT 1 FROM bpl_saa_task t2 WHERE task_code = 'exportExcel' AND T.task_id = t2.task_id ) 
		AND tk.menu_task_id = t.menu_task_id 
	);


DELETE FROM BPL_SAA_MENU_TASK t
WHERE EXISTS ( SELECT 1 FROM bpluser.bpl_saa_menu t1 WHERE system_code = 'QTC' AND menu_c_name = '预期现金流假设配置' AND T.menu_id = t1.menu_id ) 
	AND EXISTS ( SELECT 1 FROM bpl_saa_task t2 WHERE task_code = 'exportExcel' AND T.task_id = t2.task_id );
	
	
	
INSERT INTO bpluser.bpl_saa_menu_task ( menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type )
VALUES
	(   nextval( 'bpl_seq_saa_menu_task' ),
		(SELECT bpl_saa_menu.menu_id FROM bpluser.bpl_saa_menu WHERE system_code = 'QTC' AND menu_c_name = '预期现金流假设配置' ),
		(SELECT bpl_saa_task.task_id FROM bpluser.bpl_saa_task WHERE task_code = 'exportExcel' ),
		1,
		LOCALTIMESTAMP,
		1,
	    LOCALTIMESTAMP,
		NULL 
	);
		
 INSERT INTO BPL_SAA_ROLE_MENU_TASK
    (ROLE_MENU_TASK_ID,
     ROLE_MENU_ID,
     MENU_TASK_ID,
     ROLE_ID,
     CREATOR_ID,
     CREATE_TIME)
    SELECT NEXTVAL('BPL_SEQ_SAA_ROLE_MENU_TASK') AS ROLE_MENU_TASK_ID,
           SRM.ROLE_MENU_ID,
           SMT.MENU_TASK_ID,
           SR.ROLE_ID,
           1                                  AS CREATOR_ID,
           LOCALTIMESTAMP                            AS CREATE_TIME
      FROM BPL_SAA_MENU T
      LEFT JOIN BPL_SAA_TASK STK
        ON STK.TASK_TYPE = '0'
      LEFT JOIN BPL_SAA_ROLE_MENU SRM
        ON T.MENU_ID = SRM.MENU_ID
      LEFT JOIN BPL_SAA_ROLE SR
        ON SRM.ROLE_ID = SR.ROLE_ID
      LEFT JOIN BPL_SAA_MENU_TASK SMT
        ON T.MENU_ID = SMT.MENU_ID
       AND STK.TASK_ID = SMT.TASK_ID
     WHERE SYSTEM_CODE = 'QTC' 
       AND T.MENU_ID = (SELECT T.MENU_ID
                          FROM BPL_SAA_MENU T
                         WHERE SYSTEM_CODE = 'QTC'  AND t.TARGET_ROUTER IS NOT NULL
                           AND MENU_C_NAME = '预期现金流假设配置')
       AND SR.ROLE_CODE = '0000'
       AND STK.TASK_CODE = 'exportExcel' limit 1;		
	
	
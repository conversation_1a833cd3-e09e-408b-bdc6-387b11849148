-- 删除列报项配置数据
TRUNCATE TABLE rpt_conf_report_item;
TRUNCATE TABLE rpt_conf_item_rule_article;
TRUNCATE TABLE rpt_conf_item_rule_sub;
TRUNCATE TABLE rpt_conf_report_item_rule;
truncate TABLE RPT_CONF_ITEM_RULE_QTC_MODEL;
truncate TABLE RPT_CONF_ITEM_RULE_FIXED_FIELD;

-- 删除列报项配置轨迹表数据
TRUNCATE TABLE rpt_conf_report_itemhis;
TRUNCATE TABLE rpt_conf_report_item_rulehis;
TRUNCATE TABLE rpt_conf_item_rule_sub_his;
TRUNCATE TABLE rpt_conf_item_rule_articlehis;
TRUNCATE TABLE RPT_CONF_ITEM_RULE_QTC_MODELHIS
TRUNCATE TABLE RPT_CONF_ITEM_RULE_FIXED_FIELDHIS;




-- 删除报表模版数据
-- TRUNCATE TABLE rpt_conf_report_template;
-- TRUNCATE TABLE rpt_conf_report_templatehis;

commit;

/
-- 配置相关序列回滚

DECLARE

    v_max_id number(11);

BEGIN
  -- rpt_conf_report_itemhis
  select COALESCE(max(REPORT_ITEM_HIS_ID) + 2,1)
    into v_max_id
    from rpt_conf_report_itemhis;
   rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_ITEMHIS');

 EXECUTE IMMEDIATE  'create sequence RPT_SEQ_CONF_RPT_ITEMHIS
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- rpt_conf_report_item
  select COALESCE(max(REPORT_ITEM_ID) + 2,1) into v_max_id from rpt_conf_report_item;
   rptuser.rpt_pack_commonutils.drop_sequence('RPT_SEQ_CONF_RPT_ITEM');

 EXECUTE IMMEDIATE  'create sequence RPT_SEQ_CONF_RPT_ITEM
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- rpt_conf_report_item_rule
  select COALESCE(max(report_item_rule_id) + 2,1)
    into v_max_id
    from rpt_conf_report_item_rule;
   rptuser.rpt_pack_commonutils.drop_sequence('RPT_SEQ_CONF_RPT_ITEM_RULE');

 EXECUTE IMMEDIATE  'create sequence RPT_SEQ_CONF_RPT_ITEM_RULE
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- rpt_conf_report_item_rulehis
  select COALESCE(max(report_item_rule_his_id) + 2,1)
    into v_max_id
    from rpt_conf_report_item_rulehis;
   rptuser.rpt_pack_commonutils.drop_sequence('RPT_SEQ_CONF_RPT_ITEM_RULEHIS');

 EXECUTE IMMEDIATE  'create sequence RPT_SEQ_CONF_RPT_ITEM_RULEHIS
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- RPT_CONF_ITEM_RULE_ARTICLE
  select COALESCE(max(RULE_ARTICLE_ID) + 2,1)
    into v_max_id
    from RPT_CONF_ITEM_RULE_ARTICLE;
   rptuser.rpt_pack_commonutils.drop_sequence('rpt_seq_conf_item_rule_article');

 EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_item_rule_article
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- RPT_CONF_ITEM_RULE_ARTICLEHIS
  select COALESCE(max(RULE_ARTICLE_HIS_ID) + 2,1)
    into v_max_id
    from RPT_CONF_ITEM_RULE_ARTICLEHIS;
   rptuser.rpt_pack_commonutils.drop_sequence('rpt_seq_conf_item_rule_articlehis');

 EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_item_rule_articlehis
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- rpt_conf_item_rule_sub
  select COALESCE(max(report_item_rule_ternary_id) + 2,1)
    into v_max_id
    from rpt_conf_item_rule_sub;
   rptuser.rpt_pack_commonutils.drop_sequence('RPT_SEQ_CONF_ITEM_RULE_SUB');

 EXECUTE IMMEDIATE  'create sequence RPT_SEQ_CONF_ITEM_RULE_SUB
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- rpt_conf_item_rule_sub_his
  select COALESCE(max(report_item_rule_sub_his_id) + 2,1)
    into v_max_id
    from rpt_conf_item_rule_sub_his;
   rptuser.rpt_pack_commonutils.drop_sequence('RPT_SEQ_CONF_ITEM_RULE_SUB_HIS');

 EXECUTE IMMEDIATE  'create sequence RPT_SEQ_CONF_ITEM_RULE_SUB_HIS
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- rpt_conf_report_template
  select COALESCE(max(REPORT_TEMPLATE_ID) + 2,1)
  into v_max_id
  from rpt_conf_report_template;
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_rpt_template');

  EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_rpt_template
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- rpt_conf_report_templatehis
  select COALESCE(max(REPORT_TEMPLATE_HIS_ID) + 2,1)
  into v_max_id
  from rpt_conf_report_templatehis;
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_rpt_templatehis');

  EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_rpt_templatehis
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';


  -- RPT_CONF_ITEM_RULE_QTC_MODEL
  select COALESCE(max(RULE_QTC_ID) + 2,1)
  into v_max_id
  from RPT_CONF_ITEM_RULE_QTC_MODEL;
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_qtc_model');

  EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_item_rule_qtc_model
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- RPT_CONF_ITEM_RULE_QTC_MODELHIS
  select COALESCE(max(RULE_QTC_HIS_ID) + 2,1)
  into v_max_id
  from RPT_CONF_ITEM_RULE_QTC_MODELHIS;
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_qtc_modelhis');

  EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_item_rule_qtc_modelhis
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- RPT_CONF_ITEM_RULE_FIXED_FIELD
  select COALESCE(max(RULE_FIXED_FIELD_ID) + 2,1)
  into v_max_id
  from RPT_CONF_ITEM_RULE_FIXED_FIELD;
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_fixed_field');

  EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_item_rule_fixed_field
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

  -- RPT_CONF_ITEM_RULE_FIXED_FIELDHIS
  select COALESCE(max(RULE_FIXED_FIELD_HIS_ID) + 2,1)
  into v_max_id
  from RPT_CONF_ITEM_RULE_FIXED_FIELDHIS;
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_fixed_fieldhis');

  EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_item_rule_fixed_fieldhis
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_max_id || '
         increment by 1
         ';

END ;
/

do language plpgsql
$$
    declare
        v_start_year_month varchar(6) := '201201'; -- 初始的业务业务期间； 按需调整
        cur_period         record;
        cur_table          record;
    begin
        truncate table atr_conf_bussperiod_detail;
        truncate table atr_conf_bussperiod cascade;

        insert into atr_conf_bussperiod
        (buss_period_id,
         entity_id,
         year_month,
         period_state,
         valid_is,
         creator_id,
         create_time)
        values (nextval('atr_seq_conf_bussperiod'),
                1,
                v_start_year_month,
                '0',
                '1',
                '1',
                current_timestamp);

        commit;

        for cur_period in (select buss_period_id,
                                  period_state
                           from atr_conf_bussperiod
                           where valid_is = '1')
            loop
                for cur_table in (select biz_type_id,
                                         biz_code
                                  from atr_conf_table)
                    loop

                        insert into atruser.atr_conf_bussperiod_detail
                        (period_detail_id,
                         buss_period_id,
                         biz_type_id,
                         biz_code,
                         exec_result,
                         ready_state,
                         creator_id,
                         create_time,
                         updator_id,
                         update_time)
                        values (nextval('atr_seq_conf_bussperiod_detail'),
                                cur_period.buss_period_id,
                                cur_table.biz_type_id,
                                cur_table.biz_code,
                                null,
                                (case
                                     when cur_period.period_state = '1' or cur_period.period_state = '3' then '1'
                                     else '0' end),
                                1,
                                current_timestamp,
                                null,
                                null);


                    end loop;
            end loop;
        commit;
    end;
$$;
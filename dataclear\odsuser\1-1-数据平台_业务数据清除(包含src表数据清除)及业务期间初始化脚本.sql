--1、src表清除
truncate table ods_policy_main;
truncate table ods_policy_premium;
truncate table ods_policy_payment_plan;
truncate table ods_reins_outward;
truncate table ods_reins_outward_detail;
truncate table ods_reins_bill;
truncate table ods_reins_bill_detail;
truncate table ods_claim_main;
truncate table ods_claim_loss;
truncate table ods_claim_loss_detail;
truncate table ods_claim_outstanding;
truncate table ods_acc_payment;
truncate table ods_acc_receivable;
truncate table ods_fin_article_balance;
truncate table ods_fin_ledger_balance;
truncate table ods_fin_voucher;
truncate table ods_fin_voucher_detail;
truncate table ods_base_entity;
truncate table ods_base_account;
truncate table ods_base_currency;
truncate table ods_base_currency_rate;
truncate table ods_base_product;
truncate table ods_base_risk_class;
truncate table ods_base_risk;
truncate table ods_base_risk_mapping;
truncate table ods_reins_treaty_class;
truncate table ods_reins_base_treaty;
truncate table ods_reins_treaty;
truncate table ods_reins_treaty_section;
truncate table ods_reins_treaty_payment_plan;
truncate table ods_reins_risk;
truncate table ods_reins_reinsurer;
truncate table ods_reins_float_charge;

--4、清除信号表
TRUNCATE TABLE ods_data_push_signal;
TRUNCATE TABLE ods_data_push_signalhis;


--序列重置
call ods_pack_commonutils.drop_sequence('ODS_SEQ_ACC_PAYMENT');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_ACC_RECEIVABLE');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_ACCOUNT');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_CURRENCY');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_CURRENCY_RATE');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_ENTITY');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_PRODUCT');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_RISK');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_RISK_CLASS');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_RISK_MAPPING');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_CLAIM_LOSS');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_CLAIM_LOSS_DETAIL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_CLAIM_MAIN');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_CLAIM_OUTSTANDING');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_DATA_PUSH_SIGNAL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_DATA_PUSH_SIGNALHIS');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_FIN_ARTICLE_BALANCE');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_FIN_LEDGER_BALANCE');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_FIN_VOUCHER');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_FIN_VOUCHER_DETAIL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_POLICY_MAIN');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_POLICY_PAYMENT_PLAN');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_POLICY_PREMIUM');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_BASE_TREATY');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_BILL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_BILL_DETAIL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_FLOAT_CHARGE');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_OUTWARD');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_OUTWARD_DETAIL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_REINSURER');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_RISK');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_TREATY');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_TREATY_CLASS');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_TREATY_PAYMENT_PLAN');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_TREATY_SECTION');

call ods_pack_commonutils.add_sequence('ODS_SEQ_ACC_PAYMENT');
call ods_pack_commonutils.add_sequence('ODS_SEQ_ACC_RECEIVABLE');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_ACCOUNT');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_CURRENCY');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_CURRENCY_RATE');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_ENTITY');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_PRODUCT');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_RISK');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_RISK_CLASS');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_RISK_MAPPING');
call ods_pack_commonutils.add_sequence('ODS_SEQ_CLAIM_LOSS');
call ods_pack_commonutils.add_sequence('ODS_SEQ_CLAIM_LOSS_DETAIL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_CLAIM_MAIN');
call ods_pack_commonutils.add_sequence('ODS_SEQ_CLAIM_OUTSTANDING');
call ods_pack_commonutils.add_sequence('ODS_SEQ_DATA_PUSH_SIGNAL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_DATA_PUSH_SIGNALHIS');
call ods_pack_commonutils.add_sequence('ODS_SEQ_FIN_ARTICLE_BALANCE');
call ods_pack_commonutils.add_sequence('ODS_SEQ_FIN_LEDGER_BALANCE');
call ods_pack_commonutils.add_sequence('ODS_SEQ_FIN_VOUCHER');
call ods_pack_commonutils.add_sequence('ODS_SEQ_FIN_VOUCHER_DETAIL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_POLICY_MAIN');
call ods_pack_commonutils.add_sequence('ODS_SEQ_POLICY_PAYMENT_PLAN');
call ods_pack_commonutils.add_sequence('ODS_SEQ_POLICY_PREMIUM');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_BASE_TREATY');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_BILL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_BILL_DETAIL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_FLOAT_CHARGE');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_OUTWARD');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_OUTWARD_DETAIL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_REINSURER');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_RISK');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_TREATY');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_TREATY_CLASS');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_TREATY_PAYMENT_PLAN');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_TREATY_SECTION');

GRANT SELECT ON odsuser.ods_seq_data_push_signal TO dmuser;
GRANT SELECT ON odsuser.ods_seq_data_push_signalhis TO dmuser;

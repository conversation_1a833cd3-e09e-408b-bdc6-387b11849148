--复制01机构的配置，生成机构（）


------------------------bpluser-start------------------------------------------------------
--基础合约表 
DELETE FROM BBS_CONF_BASE_TREATY
 WHERE ENTITY_ID = (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '06');
INSERT INTO BBS_CONF_BASE_TREATY
    (CONF_TREATY_ID,
     ENTITY_ID,
     BASE_TREATY_NO,
     TREATY_C_NAME,
     TREATY_E_NAME,
     TREATY_L_NAME,
     TREATY_TYPE_CODE,
     TREATY_CLASS_CODE,
     VALID_IS,
     REMARK,
     VALID_DATE,
     INVALID_DATE,
     CHECKED_TIME,
     CHECKED_ID,
     AUDIT_STATE,
     CHECKED_MSG,
     CREATOR_ID,
     CREATE_TIME,
     UPDATOR_ID,
     UPDATE_TIME,
     BASE_TREATY_OUT_NO,
     SERIAL_NO)
    SELECT BBS_SEQ_CONF_CONF_TREATY.NEXTVAL AS CONF_TREATY_ID,
           (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '06') AS ENTITY_ID,
           BASE_TREATY_NO,
           TREATY_C_NAME,
           TREATY_E_NAME,
           TREATY_L_NAME,
           TREATY_TYPE_CODE,
           TREATY_CLASS_CODE,
           VALID_IS,
           REMARK,
           VALID_DATE,
           INVALID_DATE,
           CHECKED_TIME,
           CHECKED_ID,
           AUDIT_STATE,
           CHECKED_MSG,
           CREATOR_ID,
           CREATE_TIME,
           UPDATOR_ID,
           UPDATE_TIME,
           BASE_TREATY_OUT_NO,
           SERIAL_NO
      FROM BBS_CONF_BASE_TREATY
     WHERE ENTITY_ID = (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '01');


--合约ID表
DELETE FROM BBS_CONF_TREATY
 WHERE ENTITY_ID = (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '06');
INSERT INTO BBS_CONF_TREATY
    (TREATY_ID,
     ENTITY_ID,
     TREATY_NO,
     RI_DIRECTION_CODE,
     EFFECTIVE_DATE,
     EXPIRY_DATE,
     ISSUE_DATE,
     TREATY_TYPE_CODE,
     GEPI_AMOUNT,
     NEPI_AMOUNT,
     EPI_CURRENCY_CODE,
     PREMIUM,
     CURRENCY_CODE,
     BILLING_FREQUENCY_CODE,
     FLOATING_CHARGE_IS,
     PROFIT_FEE_IS,
     MIN_RETURN_AMOUNT,
     RELATED_PARTY,
     REMARK,
     VALID_IS,
     VALID_DATE,
     INVALID_DATE,
     CHECKED_TIME,
     CHECKED_ID,
     AUDIT_STATE,
     CHECKED_MSG,
     CREATOR_ID,
     CREATE_TIME,
     UPDATOR_ID,
     UPDATE_TIME,
     SERIAL_NO,
     OFFSHORE_IS,
     BASE_TREATY_ID,
     LIMIT_AMOUNT)
    SELECT BBS_SEQ_CONF_TREATY.NEXTVAL AS TREATY_ID,
           ENTITY_ID,
           TREATY_NO,
           RI_DIRECTION_CODE,
           EFFECTIVE_DATE,
           EXPIRY_DATE,
           ISSUE_DATE,
           TREATY_TYPE_CODE,
           GEPI_AMOUNT,
           NEPI_AMOUNT,
           EPI_CURRENCY_CODE,
           PREMIUM,
           CURRENCY_CODE,
           BILLING_FREQUENCY_CODE,
           FLOATING_CHARGE_IS,
           PROFIT_FEE_IS,
           MIN_RETURN_AMOUNT,
           RELATED_PARTY,
           REMARK,
           VALID_IS,
           VALID_DATE,
           INVALID_DATE,
           CHECKED_TIME,
           CHECKED_ID,
           AUDIT_STATE,
           CHECKED_MSG,
           CREATOR_ID,
           CREATE_TIME,
           UPDATOR_ID,
           UPDATE_TIME,
           SERIAL_NO,
           OFFSHORE_IS,
           (SELECT CONF_TREATY_ID
              FROM BBS_CONF_BASE_TREATY CBT
             WHERE CBT.BASE_TREATY_NO = T.BASE_TREATY_NO
               AND CBT.ENTITY_ID = T.ENTITY_ID) AS BASE_TREATY_ID,
           LIMIT_AMOUNT
      FROM (SELECT TREATY_ID,
                   (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '06') AS ENTITY_ID,
                   TREATY_NO,
                   RI_DIRECTION_CODE,
                   EFFECTIVE_DATE,
                   EXPIRY_DATE,
                   ISSUE_DATE,
                   TREATY_TYPE_CODE,
                   GEPI_AMOUNT,
                   NEPI_AMOUNT,
                   EPI_CURRENCY_CODE,
                   PREMIUM,
                   CURRENCY_CODE,
                   BILLING_FREQUENCY_CODE,
                   FLOATING_CHARGE_IS,
                   PROFIT_FEE_IS,
                   MIN_RETURN_AMOUNT,
                   RELATED_PARTY,
                   REMARK,
                   VALID_IS,
                   VALID_DATE,
                   INVALID_DATE,
                   CHECKED_TIME,
                   CHECKED_ID,
                   AUDIT_STATE,
                   CHECKED_MSG,
                   CREATOR_ID,
                   CREATE_TIME,
                   UPDATOR_ID,
                   UPDATE_TIME,
                   SERIAL_NO,
                   OFFSHORE_IS,
                   (SELECT BASE_TREATY_NO
                      FROM BBS_CONF_BASE_TREATY
                     WHERE CONF_TREATY_ID = BASE_TREATY_ID) AS BASE_TREATY_NO,
                   LIMIT_AMOUNT
              FROM BBS_CONF_TREATY
             WHERE ENTITY_ID = (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '01')) T;


--loa主表
delete FROM BBS_CONF_LOA
     WHERE ENTITY_ID = (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '06');

INSERT INTO BBS_CONF_LOA
    (LOA_ID,
     ENTITY_ID,
     BUSINESS_MODEL,
     BUSINESS_DIRECTION,
     LOA_CODE,
     LOA_C_NAME,
     LOA_L_NAME,
     LOA_E_NAME,
     REMARK,
     VALID_IS,
     VALID_DATE,
     INVALID_DATE,
     CHECKED_TIME,
     CHECKED_ID,
     AUDIT_STATE,
     CHECKED_MSG,
     CREATOR_ID,
     CREATE_TIME,
     UPDATOR_ID,
     UPDATE_TIME,
     EVALUATE_APPROACH,
     SERIAL_NO,
     COVERAGE_UNIT)
    SELECT BBS_SEQ_CONF_LOA.NEXTVAL AS LOA_ID,
           (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '06') AS ENTITY_ID,
           BUSINESS_MODEL,
           BUSINESS_DIRECTION,
           LOA_CODE,
           LOA_C_NAME,
           LOA_L_NAME,
           LOA_E_NAME,
           REMARK,
           VALID_IS,
           VALID_DATE,
           INVALID_DATE,
           CHECKED_TIME,
           CHECKED_ID,
           AUDIT_STATE,
           CHECKED_MSG,
           CREATOR_ID,
           CREATE_TIME,
           UPDATOR_ID,
           UPDATE_TIME,
           EVALUATE_APPROACH,
           SERIAL_NO,
           COVERAGE_UNIT
      FROM BBS_CONF_LOA
     WHERE ENTITY_ID = (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '01');


--loa明细表（合约）
DELETE FROM BBS_CONF_LOA_DETAIL LOAD
 WHERE EXISTS
 (SELECT 1
          FROM BBS_CONF_LOA LOA
         WHERE LOAD.LOA_ID = LOA.LOA_ID
           AND LOA.BUSINESS_MODEL = 'T'
           AND LOA.ENTITY_ID = (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '06'));

INSERT INTO BBS_CONF_LOA_DETAIL
    (LOA_DETAIL_ID,
     LOA_ID,
     BUSINESS_ID,
     CREATOR_ID,
     CREATE_TIME,
     UPDATOR_ID,
     UPDATE_TIME)
    SELECT BBS_SEQ_CONF_LOA_DETAIL.NEXTVAL AS LOA_DETAIL_ID,
           (SELECT LOA_ID
              FROM BBS_CONF_LOA LOA
             WHERE LOA.BUSINESS_MODEL = T.BUSINESS_MODEL
               AND LOA.BUSINESS_DIRECTION = T.BUSINESS_DIRECTION
               AND LOA.LOA_CODE = T.LOA_CODE
               AND LOA.ENTITY_ID =
                   (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '06')) AS LOA_ID,
           (SELECT CONF_TREATY_ID
              FROM BBS_CONF_BASE_TREATY CBT
             WHERE CBT.BASE_TREATY_NO = T.BASE_TREATY_NO
               AND CBT.ENTITY_ID =
                   (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '06')) AS BUSINESS_ID,
           CREATOR_ID,
           CREATE_TIME,
           UPDATOR_ID,
           UPDATE_TIME
      FROM (SELECT LOA_DETAIL_ID,
                   --LOA_ID,
                   (SELECT BUSINESS_MODEL
                      FROM BBS_CONF_LOA LOA
                     WHERE LOA.LOA_ID = LOAD.LOA_ID) AS BUSINESS_MODEL,
                   (SELECT BUSINESS_DIRECTION
                      FROM BBS_CONF_LOA LOA
                     WHERE LOA.LOA_ID = LOAD.LOA_ID) AS BUSINESS_DIRECTION,
                   (SELECT LOA_CODE FROM BBS_CONF_LOA LOA WHERE LOA.LOA_ID = LOAD.LOA_ID) AS LOA_CODE,
                   (SELECT BASE_TREATY_NO
                      FROM BBS_CONF_BASE_TREATY
                     WHERE CONF_TREATY_ID = BUSINESS_ID) AS BASE_TREATY_NO,
                   CREATOR_ID,
                   CREATE_TIME,
                   UPDATOR_ID,
                   UPDATE_TIME
              FROM BBS_CONF_LOA_DETAIL LOAD
             WHERE EXISTS
             (SELECT 1
                      FROM BBS_CONF_LOA LOA
                     WHERE LOAD.LOA_ID = LOA.LOA_ID
                       AND LOA.BUSINESS_MODEL = 'T'
                       AND LOA.ENTITY_ID =
                           (SELECT ENTITY_ID FROM BBS_ENTITY WHERE ENTITY_CODE = '01'))) T;






------------------------bpluser-end------------------------------------------------------

------------------------dmuser-start------------------------------------------------------

--盈亏方案
DELETE FROM DM_CONF_ICG_PROFITLOSS_PLAN
 WHERE ENTITY_ID = (SELECT ENTITY_ID FROM BPLUSER.BBS_ENTITY WHERE ENTITY_CODE = '06');
INSERT INTO DM_CONF_ICG_PROFITLOSS_PLAN
    (LOSS_PLAN_ID,
     ENTITY_ID,
     BUSINESS_MODEL,
     BUSINESS_DIRECTION,
     TEST_PLAN_TYPE,
     LOSS_PLAN_VERSION,
     AUDIT_STATE,
     CHECKED_TIME,
     CHECKED_ID,
     CHECKED_MSG,
     VALID_IS,
     CREATE_TIME,
     CREATOR_ID,
     UPDATE_TIME,
     UPDATOR_ID,
     SERIAL_NO)
    SELECT DM_SEQ_CONF_ICG_LOSS_PLAN.NEXTVAL AS LOSS_PLAN_ID,
           (SELECT ENTITY_ID FROM bpluser.BBS_ENTITY WHERE ENTITY_CODE = '06') AS ENTITY_ID,
           T.BUSINESS_MODEL,
           T.BUSINESS_DIRECTION,
           T.TEST_PLAN_TYPE,
           T.LOSS_PLAN_VERSION,
           T.AUDIT_STATE,
           T.CHECKED_TIME,
           T.CHECKED_ID,
           T.CHECKED_MSG,
           T.VALID_IS,
           T.CREATE_TIME,
           T.CREATOR_ID,
           T.UPDATE_TIME,
           T.UPDATOR_ID,
           T.SERIAL_NO
      FROM DM_CONF_ICG_PROFITLOSS_PLAN T
     WHERE ENTITY_ID = (SELECT ENTITY_ID FROM bpluser.BBS_ENTITY WHERE ENTITY_CODE = '01');
--盈亏配置
DELETE FROM DM_CONF_CONTRACT_LOSSDEF T
 WHERE T.ENTITY_ID = (SELECT ENTITY_ID FROM BPLUSER.BBS_ENTITY WHERE ENTITY_CODE = '06');
INSERT INTO DM_CONF_CONTRACT_LOSSDEF
    (LOSS_ID,
     ENTITY_ID,
     PRODUCT_CODE,
     JUDGE_VALUE,
     JUDGE_RESULT,
     LOSS_VERSION,
     AUDIT_STATE,
     CHECKED_TIME,
     CHECKED_ID,
     CHECKED_MSG,
     VALID_IS,
     CREATE_TIME,
     CREATOR_ID,
     UPDATE_TIME,
     UPDATOR_ID,
     BUSINESS_MODEL,
     BUSINESS_DIRECTION,
     JUDGE_TYPE,
     TREATY_NO,
     SERIAL_NO)
    SELECT DM_SEQ_CONF_CONTRACT_LOSSDEF.NEXTVAL AS LOSS_ID,
           (SELECT ENTITY_ID FROM BPLUSER.BBS_ENTITY WHERE ENTITY_CODE = '06') AS ENTITY_ID,
           PRODUCT_CODE,
           JUDGE_VALUE,
           JUDGE_RESULT,
           LOSS_VERSION,
           AUDIT_STATE,
           CHECKED_TIME,
           CHECKED_ID,
           CHECKED_MSG,
           VALID_IS,
           CREATE_TIME,
           CREATOR_ID,
           UPDATE_TIME,
           UPDATOR_ID,
           BUSINESS_MODEL,
           BUSINESS_DIRECTION,
           JUDGE_TYPE,
           TREATY_NO,
           SERIAL_NO
      FROM DM_CONF_CONTRACT_LOSSDEF T
     WHERE T.ENTITY_ID =
           (SELECT ENTITY_ID FROM BPLUSER.BBS_ENTITY WHERE ENTITY_CODE = '01');

--压力情景
TRUNCATE TABLE dm_conf_bba_profit_loss_factor;

INSERT INTO DMUSER.DM_CONF_BBA_PROFIT_LOSS_FACTOR
    (BBA_PROFIT_LOSS_FACTOR_ID,
     ENTITY_ID,
     LOSS_RATE,
     MAINTENANCE_RATE,
     AUDIT_STATE,
     CHECKED_TIME,
     CHECKED_ID,
     VALID_IS,
     CREATE_TIME,
     CREATOR_ID,
     LOA_ID,
     BUSINESS_MODEL,
     BUSINESS_DIRECTION)
    SELECT dm_seq_profit_loss_factor.NEXTVAL AS BBA_PROFIT_LOSS_FACTOR_ID,
           LOA.Entity_Id AS ENTITY_ID,
           0.200000 AS LOSS_RATE,
           0.200000 MAINTENANCE_RATE,
           '1' AUDIT_STATE,
           LOCALTIMESTAMP AS CHECKED_TIME,
           1 AS CHECKED_ID,
           '1' AS VALID_IS,
           LOCALTIMESTAMP AS CREATE_TIME,
           1 CREATOR_ID,
           LOA.LOA_ID AS LOA_ID,
           LOA.BUSINESS_MODEL AS BUSINESS_MODEL,
           LOA.BUSINESS_DIRECTION AS BUSINESS_DIRECTION
      FROM BPLUSER.BBS_CONF_LOA LOA
     WHERE LOA.BUSINESS_MODEL || LOA.BUSINESS_DIRECTION IN ('DD', 'TI');
	 
------------------------dmuser-end------------------------------------------------------


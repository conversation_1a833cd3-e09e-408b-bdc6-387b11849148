CREATE OR REPLACE PACKAGE acc_pack_buss_balance IS

  PROCEDURE proc_balance_entrance(p_entity_id NUMBER,
                                  p_book_code VARCHAR,
                                  p_year_month VARCHAR,
                                  p_user_id NUMBER);

  PROCEDURE proc_article_balance(p_entity_id IN NUMBER,
                                 p_book_code IN VARCHAR2,
                                 p_year_month IN VARCHAR2,
                                 p_user_id IN NUMBER);

  PROCEDURE proc_ledger_balance(p_entity_id IN NUMBER,
                                p_book_code IN VARCHAR2,
                                p_year_month IN VARCHAR2,
                                p_user_id IN NUMBER);

  PROCEDURE proc_add_balancehis(p_entity_id IN NUMBER,
                                p_book_code IN VARCHAR2,
                                p_year_month IN VARCHAR2,
                                p_user_id IN NUMBER);

END acc_pack_buss_balance;
/
CREATE OR REPLACE PACKAGE BODY acc_pack_buss_balance IS
--
  PROCEDURE proc_balance_entrance(p_entity_id NUMBER,
                                  p_book_code VARCHAR,
                                  p_year_month VARCHAR,
                                  p_user_id NUMBER) IS
    /***********************************************************************
      NAME :acc_pack_buss_balance_proc_balance_entrance
      DESCRIPTION : 17凭证数据生成总账数据入口
      DATE :2021-06-01
      AUTHOR :wuyh
    ***********************************************************************/
    v_base_currency VARCHAR(3);
    v_annual_period VARCHAR(6);
  BEGIN
    SELECT MAX(currency_code)
      INTO v_base_currency
      FROM acc_conf_accountperiod
     WHERE entity_id = p_entity_id
       AND book_code = p_book_code
       AND year_month = p_year_month
       AND valid_is = '1'
       AND audit_state = '1'
       AND execution_state IN ('0', '2');

    SELECT MAX(year_month)
      INTO v_annual_period
      FROM acc_conf_annual_period
     WHERE entity_id = p_entity_id
       AND book_code = p_book_code
       AND year_month = p_year_month
       AND valid_is = '1'
       AND audit_state = '1';

    IF v_base_currency IS NULL
       AND v_annual_period IS NULL THEN
      dbms_output.put_line('会计期间不存在或者无效：会计期间: ' || p_year_month);
      --抛出异常，中断事务
      raise_application_error(-20002, '会计期间不存在或者无效：会计期间'|| p_year_month);
      RETURN;

    END IF;

    acc_pack_buss_balance.proc_ledger_balance(p_entity_id, p_book_code,
                                              p_year_month, p_user_id);

    acc_pack_buss_balance.proc_article_balance(p_entity_id, p_book_code,
                                               p_year_month, p_user_id);

    acc_pack_buss_balance.proc_add_balancehis(p_entity_id, p_book_code,
                                            p_year_month, p_user_id);
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line('[数据检查发生异常]' || to_char(SQLCODE) || '::' ||
                           substr(SQLERRM, 1, 200));
      --抛出异常提示信息
      raise_application_error(-20003, SQLERRM);
  END proc_balance_entrance;

  PROCEDURE proc_article_balance(p_entity_id IN NUMBER,
                                 p_book_code IN VARCHAR2,
                                 p_year_month IN VARCHAR2,
                                 p_user_id IN NUMBER) IS
    /***********************************************************************
      NAME : proc_article_balance
      DESCRIPTION : 生成专项余额数据
      DATE :2021-06-01
      AUTHOR :wuyh
    ***********************************************************************/
    v_previous_year  VARCHAR2(4);
    v_pre_year_month VARCHAR2(6);
    v_same_year      NUMBER(10); --会计期间年份
    v_same_quater    NUMBER(10); --会计期间月份
    v_currency_cu_code    VARCHAR2(3); -- 本位币
    v_count          NUMBER(10);
    --v_errorcode      VARCHAR2(2);
    v_serial_no NUMBER(10);

    v_trans_count NUMBER(10);--过渡期数据
    v_trnas_task_code VARCHAR2(11);
  BEGIN
    SELECT MAX(currency_code)
      INTO v_currency_cu_code
      FROM acc_conf_accountperiod
     WHERE entity_id = p_entity_id
       AND book_code = p_book_code
       AND year_month = p_year_month
       AND valid_is = '1'
       AND audit_state = '1';

    SELECT coalesce(MAX(serial_no) + 1, 1)
      INTO v_serial_no
      FROM acc_buss_article_balancehis
     WHERE entity_id = p_entity_id
       AND book_code = p_book_code
       AND year_month = p_year_month;

    SELECT COUNT(article_id)
      INTO v_count
      FROM acc_buss_article_balance t
     WHERE entity_id = p_entity_id
       AND book_code = p_book_code
       AND year_month = p_year_month;

    IF v_count > 0 THEN
      DELETE FROM acc_buss_article_balance
       WHERE entity_id = p_entity_id
         AND book_code = p_book_code
         AND year_month = p_year_month;

      dbms_output.put_line('已存在当前会计期间科目专项余额数据：year_month: ' ||
                           p_year_month);
    END IF;

    --获取上个月的会计期间
    --是否相同季度，年度
    IF substr(p_year_month, 5, 2) = '13' THEN
      v_pre_year_month := substr(p_year_month, 1, 4) || '12';
      v_same_quater    := 0;
      v_same_year      := 1;
    ELSIF substr(p_year_month, 5, 2) = '01' THEN
      v_previous_year := to_char(add_months(trunc(to_date(p_year_month || '01',
                                                          'yyyy/mm/dd')), -1),
                                 'yyyy');
      SELECT nvl(MAX(year_month),
                 to_char(add_months(trunc(to_date(p_year_month || '01',
                                                   'yyyy/mm/dd')), -1),
                          'yyyymm'))
        INTO v_pre_year_month
        FROM acc_conf_annual_period
       WHERE entity_id = p_entity_id
         AND book_code = p_book_code
         AND year_month = v_previous_year || '13'
         AND valid_is = '1'
         AND audit_state = '1';

      v_same_quater := 0;
      v_same_year   := 0;
    ELSE
      v_pre_year_month := to_char(add_months(trunc(to_date(p_year_month || '01',
                                                           'yyyy/mm/dd')), -1),
                                  'yyyymm');
      v_same_quater    := acc_pack_common.func_year_month_dimension(p_year_month,
                                                                    v_pre_year_month,
                                                                    '3');

      v_same_year := acc_pack_common.func_year_month_dimension(p_year_month,
                                                               v_pre_year_month,
                                                               '12');

    END IF;

    --末级明细科目信息
    INSERT INTO acc_buss_article_balance
      (article_id,
       entity_id,
       book_code,
       year_month,
       root_account_id,
       account_id,
       currency_code,
       currency_cu_code,
       article,
       article1,
       article2,
       article3,
       article4,
       article5,
       article6,
       article7,
       article8,
       article9,
       article10,
       article11,
       article12,
       article13,
       article14,
       article15,
       article16,
       article17,
       debit_amount,
       debit_amount_cu,
       credit_amount,
       credit_amount_cu,
       debit_amount_quarter,
       debit_amount_quarter_cu,
       credit_amount_quarter,
       credit_amount_quarter_cu,
       debit_amount_year,
       debit_amount_year_cu,
       credit_amount_year,
       credit_amount_year_cu,
       opening_balance,
       closing_balance,
       opening_balance_cu,
       closing_balance_cu,
       create_time,
       creator_id,
       serial_no)
      SELECT ACC_SEQ_ITEM_ARTICLE_BALANCE.nextval,
             p_entity_id,
             p_book_code,
             p_year_month,
             acc_pack_common.func_get_base_account_id(coalesce(b.account_id,
                                                           c.account_id)) AS root_account_id,
             coalesce(b.account_id, c.account_id) AS account_id,
             (CASE WHEN b.currency_code IS NOT  NULL THEN b.currency_code WHEN c.currency_code IS NOT NULL THEN c.currency_code ELSE v_currency_cu_code END ),
             (CASE WHEN b.currency_cu_code IS NOT  NULL THEN b.currency_cu_code WHEN c.currency_cu_code IS NOT NULL THEN c.currency_cu_code ELSE v_currency_cu_code END ),
             coalesce(b.article, c.article) AS article,
             coalesce(b.article1, c.article1) AS article1,
             coalesce(b.article2, c.article2) AS article2,
             coalesce(b.article3, c.article3) AS article3,
             coalesce(b.article4, c.article4) AS article4,
             coalesce(b.article5, c.article5) AS article5,
             coalesce(b.article6, c.article6) AS article6,
             coalesce(b.article7, c.article7) AS article7,
             coalesce(b.article8, c.article8) AS article8,
             coalesce(b.article9, c.article9) AS article9,
             coalesce(b.article10, c.article10) AS article10,
             coalesce(b.article11, c.article11) AS article11,
             coalesce(b.article12, c.article12) AS article12,
             coalesce(b.article13, c.article13) AS article13,
             coalesce(b.article14, c.article14) AS article14,
             coalesce(b.article15, c.article15) AS article15,
             coalesce(b.article16, c.article16) AS article16,
             coalesce(b.article17, c.article17) AS article17,
             coalesce(b.debitamount, 0),
             coalesce(b.debitamount_cu, 0),
             coalesce(b.ceditamount, 0),
             coalesce(b.ceditamount_cu, 0),

             (CASE v_same_quater
               WHEN 1 THEN
                coalesce(c.debit_amount_quarter, 0) +
                coalesce(b.debitamount, 0)
               ELSE
                coalesce(b.debitamount, 0)
             END) AS debit_amount_quarter,
             (CASE v_same_quater
               WHEN 1 THEN
                coalesce(c.debit_amount_quarter_cu, 0) +
                coalesce(b.debitamount_cu, 0)
               ELSE
                coalesce(b.debitamount_cu, 0)
             END) AS debit_amount_quarter_cu,
             (CASE v_same_quater
               WHEN 1 THEN
                coalesce(c.credit_amount_quarter, 0) +
                coalesce(b.ceditamount, 0)
               ELSE
                coalesce(b.ceditamount, 0)
             END) AS credit_amount_quarter,
             (CASE v_same_quater
               WHEN 1 THEN
                coalesce(c.credit_amount_quarter_cu, 0) +
                coalesce(b.ceditamount_cu, 0)
               ELSE
                coalesce(b.ceditamount_cu, 0)
             END) AS credit_amount_quarter_cu,

             (CASE v_same_year
               WHEN 1 THEN
                coalesce(c.debit_amount_year, 0) +
                coalesce(b.debitamount, 0)
               ELSE
                coalesce(b.debitamount, 0)
             END) AS debit_amount_year,
             (CASE v_same_year
               WHEN 1 THEN
                coalesce(c.debit_amount_year_cu, 0) +
                coalesce(b.debitamount_cu, 0)
               ELSE
                coalesce(b.debitamount_cu, 0)
             END) AS debit_amount_year,
             (CASE v_same_year
               WHEN 1 THEN
                coalesce(c.credit_amount_year, 0) +
                coalesce(b.ceditamount, 0)
               ELSE
                coalesce(b.ceditamount, 0)
             END) AS credit_amount_year,
             (CASE v_same_year
               WHEN 1 THEN
                coalesce(c.credit_amount_year_cu, 0) +
                coalesce(b.ceditamount_cu, 0)
               ELSE
                coalesce(b.ceditamount_cu, 0)
             END) AS credit_amount_year_cu,
             coalesce(c.closing_balance, 0) AS opening_balance,
             coalesce(c.closing_balance, 0) + coalesce(b.debitamount, 0) -
             coalesce(b.ceditamount, 0) AS closing_balance,
             coalesce(c.closing_balance_cu, 0) AS opening_balance_cu,
             coalesce(c.closing_balance_cu, 0) +
             coalesce(b.debitamount_cu, 0) - coalesce(b.ceditamount_cu, 0) AS closing_balance_cu,
             SYSDATE,
             p_user_id,
             v_serial_no
        FROM (SELECT a.entity_id,
                     a.book_code,
                     a.year_month,
                     b.account_id,
                     b.currency_code,
                     b.currency_cu_code,
                     b.article,
                     b.article1,
                     b.article2,
                     b.article3,
                     b.article4,
                     b.article5,
                     b.article6,
                     b.article7,
                     b.article8,
                     b.article9,
                     b.article10,
                     b.article11,
                     b.article12,
                     b.article13,
                     b.article14,
                     b.article15,
                     b.article16,
                     b.article17,
                     SUM(CASE b.account_entry_code
                           WHEN 'D' THEN
                            b.amount
                           ELSE
                            0
                         END) AS debitamount,
                     SUM(CASE b.account_entry_code
                           WHEN 'D' THEN
                            b.amount_cu
                           ELSE
                            0
                         END) AS debitamount_cu,
                     SUM(CASE b.account_entry_code
                           WHEN 'C' THEN
                            b.amount
                           ELSE
                            0
                         END) AS ceditamount,
                     SUM(CASE b.account_entry_code
                           WHEN 'C' THEN
                            b.amount_cu
                           ELSE
                            0
                         END) AS ceditamount_cu
                FROM acc_buss_voucher a
                LEFT JOIN acc_buss_voucher_detail b
                  ON a.voucher_id = b.voucher_id
               WHERE a.entity_id = p_entity_id
                 AND a.book_code = p_book_code
                 AND a.year_month = p_year_month
                 AND a.valid_is = '1'
                 AND a.audit_state='1'
               GROUP BY a.entity_id,
                        a.book_code,
                        a.year_month,
                        b.currency_code,
                        b.account_id,
                        b.article,
                        b.article1,
                        b.article2,
                        b.article3,
                        b.article4,
                        b.article5,
                        b.article6,
                        b.article7,
                        b.article8,
                        b.article9,
                        b.article10,
                        b.article11,
                        b.article12,
                        b.article13,
                        b.article14,
                        b.article15,
                        b.article16,
                        b.article17,
                        b.currency_code,
                        b.currency_cu_code) b
        FULL JOIN (SELECT *
                     FROM acc_buss_article_balance c
                    WHERE c.entity_id = p_entity_id
                      AND c.book_code = p_book_code
                      AND c.year_month = v_pre_year_month) c
          ON b.account_id = c.account_id
         AND b.currency_code = c.currency_code
         AND (b.article = c.article OR
             (b.article IS NULL AND c.article IS NULL));
    COMMIT;
     --过渡期数据 start
    SELECT count(1), coalesce(max(t.task_code), 'T')
      into v_trans_count, v_trnas_task_code
      FROM acc_temp_article_balance t
     where t.year_month = p_year_month
       and t.entity_id = p_entity_id
       and t.book_code = p_book_code
       and t.transitional_is = 'T'
       --and coalesce(t.back_is,'0') !='1'
       ;--更新过余额表就不用再次更新
    --v_trans_count := 0;
    if  v_trans_count > 0 then
      dbms_output.put_line('过渡期专项余额');
      merge into acc_buss_article_balance l
     USING (
     SELECT  entity_id,
             book_code,
             year_month,
             --root_account_id,
             account_id,
             currency_code,
             currency_cu_code,
             /*article,*/
             (select b.entity_id from bpluser. bbs_entity b where b.entity_code = REPLACE(t.ARTICLE1,'BM','') and rownum = 1 ) as article1,--部门
             (SELECT b.RISK_CODE FROM bpluser.bbs_conf_risk_mapping b WHERE b.RISK_MAPPING_CODE=t.ARTICLE2 AND RISK_TYPE_CODE='3' AND VALID_IS='1' AND AUDIT_STATE='1' and rownum = 1) as article2,--险种
             article3,
             --article4,
              article5,
             article6,
             /*article7,
             article8,
             article9,
             article10,*/
             --article11,--客商
             /*article12,
             article13,
             article14,
             article15,
             article16,
             article17,*/
            sum(t.opening_balance) as opening_balance,
            sum(t.opening_balance_cu) as opening_balance_cu
       FROM acc_temp_article_balance t
      where t.transitional_is = 'T'
        and t.task_code = v_trnas_task_code
      group by entity_id,
             book_code,
             year_month,
             --root_account_id,
             account_id,
             currency_code,
             currency_cu_code,
             /*article,*/
             article1,
             article2,
             article3,
             --article4,
             article5,
             article6/*,
             article7,
             article8,
             article9,
             article10,
             article11,
             article12,
             article13,
             article14,
             article15,
             article16,
             article17*/) e
     on (l.entity_id = e.entity_id and l.year_month = e.year_month and l.book_code = e.book_code and l.account_id = e.account_id and l.currency_code = e.currency_code and l.currency_cu_code = e.currency_cu_code
         and l.article1 = e.article2 and l.article2 = e.article1 and l.article11 = e.article3
         and l.article5 = e.article5 and l.article6 = e.article6
         and l.article = nvl2(e.article2, e.article2 || '/', '')||nvl2(e.article1, e.article1 || '/', '')||nvl2(e.article3, e.article3 || '/', '')
         ||nvl2(e.article5, e.article5 || '/', '')||nvl2(e.article6, e.article6 || '/', '')
     )
     WHEN MATCHED THEN

     UPDATE
           SET
               l.opening_balance          = l.opening_balance + e.opening_balance,
               l.closing_balance         = l.closing_balance + e.opening_balance,
               l.opening_balance_cu       = l.opening_balance_cu + e.opening_balance_cu,
               l.closing_balance_cu      = l.closing_balance_cu + e.opening_balance_cu
         WHERE l.entity_id = p_entity_id
           AND l.book_code = p_book_code
           AND l.year_month = p_year_month
           and l.account_id = e.account_id
           and l.currency_code = e.currency_code
           and l.currency_cu_code = e.currency_cu_code
           and l.article1 = e.article2
           and l.article2 = e.article1
           and l.article11 = e.article3
           and l.article5 = e.article5
           and l.article6 = e.article6
           and l.article = nvl2(e.article2, e.article2 || '/', '')||nvl2(e.article1, e.article1 || '/', '')||nvl2(e.article3, e.article3 || '/', '')
         ||nvl2(e.article5, e.article5 || '/', '')||nvl2(e.article6, e.article6 || '/', '')

     WHEN NOT MATCHED THEN
      --末级明细科目信息
    INSERT
      (article_id,
       entity_id,
       book_code,
       year_month,
       root_account_id,
       account_id,
       currency_code,
       currency_cu_code,
       article,
       article1,
       article2,
       article3,
       article4,
       article5,
       article6,
       article7,
       article8,
       article9,
       article10,
       article11,
       article12,
       article13,
       article14,
       article15,
       article16,
       article17,
       debit_amount,
       debit_amount_cu,
       credit_amount,
       credit_amount_cu,
       debit_amount_quarter,
       debit_amount_quarter_cu,
       credit_amount_quarter,
       credit_amount_quarter_cu,
       debit_amount_year,
       debit_amount_year_cu,
       credit_amount_year,
       credit_amount_year_cu,
       opening_balance,
       closing_balance,
       opening_balance_cu,
       closing_balance_cu,
       create_time,
       creator_id,
       serial_no)
      values( ACC_SEQ_ITEM_ARTICLE_BALANCE.nextval,
             p_entity_id,
             p_book_code,
             p_year_month,
             acc_pack_common.func_get_base_account_id(e.account_id),
             e.account_id ,
             e.currency_code,
             e.currency_cu_code,
             nvl2(e.article2, e.article2 || '/', '')||nvl2(e.article1, e.article1 || '/', '')||nvl2(e.article3, e.article3 || '/', '')
         ||nvl2(e.article5, e.article5 || '/', '')||nvl2(e.article6, e.article6 || '/', ''),
             e.article2,
             e.article1,
             '',-- AS article3,
             '',-- AS article4,
             e.article5,-- AS article5,
             e.article6,-- AS article6,
             '',-- AS article7,
             '',-- AS article8,
             '',-- AS article9,
             '',-- AS article10,
             e.article3,-- AS article11,
             '',-- AS article12,
             '',-- AS article13,
             '',-- AS article14,
             '',-- AS article15,
             '',-- AS article16,
             '',-- AS article17,
             0,-- debitamount,
             0,-- debitamount_cu,
             0,-- ceditamount,
             0,-- ceditamount_cu,
             0,-- AS debit_amount_quarter,
             0,-- AS debit_amount_quarter_cu,
             0,-- AS credit_amount_quarter,
             0,-- AS credit_amount_quarter_cu,
             0,-- AS debit_amount_year,
             0,-- AS debit_amount_year,
             0,--AS credit_amount_year,
             0,-- AS credit_amount_year_cu,
             e.opening_balance,-- AS opening_balance,
             e.opening_balance,-- AS closing_balance,
             e.opening_balance_cu,-- AS opening_balance_cu,
             e.opening_balance_cu,-- AS closing_balance_cu,
             SYSDATE,
             p_user_id,
             v_serial_no);

     update acc_temp_article_balance t set t.back_is = '1' where t.task_code = v_trnas_task_code;
    end if;


  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(to_char(SQLCODE) || '::' || substr(SQLERRM, 1, 200));
      --抛出异常提示信息
      raise_application_error(-20003, '[专项余额数据生成异常]' || to_char(SQLCODE) || '::' ||
                           substr(SQLERRM, 1, 200));
  END proc_article_balance;

  PROCEDURE proc_ledger_balance(p_entity_id IN NUMBER,
                                p_book_code IN VARCHAR2,
                                p_year_month IN VARCHAR2,
                                p_user_id IN NUMBER) IS
    /***********************************************************************
      NAME : proc_ledger_balance
      DESCRIPTION : 生成科目余额数据，按科目上级层层汇总
      DATE :2021-06-01
      AUTHOR :wuyh
    ***********************************************************************/
    v_previous_year  VARCHAR2(4); --上一个年
    v_pre_year_month VARCHAR2(6); --上一个月会计期间
    v_count          NUMBER(10);
    --REC_account_level  record;--接收一条数据记录
    v_same_year   NUMBER(10); --会计期间年份
    v_same_quater NUMBER(10); --会计期间月份
    v_serial_no   NUMBER(10);

    v_trans_count NUMBER(10);--过渡期数据
    v_trnas_task_code VARCHAR2(11);
  BEGIN

    SELECT coalesce(MAX(serial_no) + 1, 1)
      INTO v_serial_no
      FROM acc_buss_ledger_balancehis
     WHERE entity_id = p_entity_id
       AND book_code = p_book_code
       AND year_month = p_year_month;

    SELECT COUNT(ledger_balance_id)
      INTO v_count
      FROM acc_buss_ledger_balance t
     WHERE entity_id = p_entity_id
       AND book_code = p_book_code
       AND year_month = p_year_month;

    IF v_count > 0 THEN
      --删除数据
      DELETE FROM ACC_BUSS_LEDGER_BALANCE
       WHERE entity_id = P_entity_id
         AND BOOK_CODE = P_BOOK_CODE
         AND YEAR_MONTH = P_YEAR_MONTH;
      dbms_output.put_line('已存在当前会计期间科目余额数据:' || p_year_month);
    END IF;

    IF substr(p_year_month, 5, 2) = '13' THEN
      v_pre_year_month := substr(p_year_month, 1, 4) || '12';
      v_same_quater    := 0;
      v_same_year      := 1;
    ELSIF substr(p_year_month, 5, 2) = '01' THEN
      v_previous_year := to_char(add_months(trunc(to_date(p_year_month || '01',
                                                          'yyyy/mm/dd')), -1),
                                 'yyyy');
      SELECT nvl(MAX(year_month),
                 to_char(add_months(trunc(to_date(p_year_month || '01',
                                                   'yyyy/mm/dd')), -1),
                          'yyyymm'))
        INTO v_pre_year_month
        FROM acc_conf_annual_period
       WHERE entity_id = p_entity_id
         AND book_code = p_book_code
         AND year_month = v_previous_year || '13'
         AND valid_is = '1'
         AND audit_state = '1';
      v_same_quater := 0;
      v_same_year   := 0;
    ELSE
      --获取上个月的会计期间
      v_pre_year_month := to_char(add_months(trunc(to_date(p_year_month || '01',
                                                           'yyyy/mm/dd')), -1),
                                  'yyyymm');
      --是否相同季度，年度
      v_same_quater := acc_pack_common.func_year_month_dimension(p_year_month,
                                                                 v_pre_year_month,
                                                                 '3');

      v_same_year := acc_pack_common.func_year_month_dimension(p_year_month,
                                                               v_pre_year_month,
                                                               '12');

    END IF;

    --末级明细科目信息
    INSERT INTO acc_buss_ledger_balance
      (ledger_balance_id,
       entity_id,
       book_code,
       year_month,
       root_account_id,
       account_id,
       currency_code,
       currency_cu_code,
       debit_amount,
       debit_amount_cu,
       credit_amount,
       credit_amount_cu,
       debit_amount_quarter,
       debit_amount_quarter_cu,
       credit_amount_quarter,
       credit_amount_quarter_cu,
       debit_amount_year,
       debit_amount_year_cu,
       credit_amount_year,
       credit_amount_year_cu,
       opening_balance,
       closing_balance,
       opening_balance_cu,
       closing_balance_cu,
       create_time,
       creator_id,
       update_time,
       updator_id,
       serial_no)
      SELECT acc_seq_item_ledger_balance.nextval,
             p_entity_id,
             p_book_code,
             p_year_month,
             acc_pack_common.func_get_base_account_id(coalesce(b.account_id,
                                                           c.account_id)) AS root_account_id,
             coalesce(b.account_id, c.account_id) AS item_id,
             coalesce(b.currency_code, c.currency_code) AS currency_code,
             coalesce(b.currency_cu_code, c.currency_cu_code) AS currency_cu_code,
             coalesce(b.debitamount, 0),
             coalesce(b.debitamount_cu, 0),
             coalesce(b.ceditamount, 0),
             coalesce(b.ceditamount_cu, 0),
             (CASE v_same_quater
               WHEN 1 THEN
                coalesce(c.debit_amount_quarter, 0) +
                coalesce(b.debitamount, 0)
               ELSE
                coalesce(b.debitamount, 0)
             END) AS debit_amount_quarter,
             (CASE v_same_quater
               WHEN 1 THEN
                coalesce(c.debit_amount_quarter_cu, 0) +
                coalesce(b.debitamount_cu, 0)
               ELSE
                coalesce(b.debitamount_cu, 0)
             END) AS debit_amount_quarter_cu,
             (CASE v_same_quater
               WHEN 1 THEN
                coalesce(c.credit_amount_quarter, 0) +
                coalesce(b.ceditamount, 0)
               ELSE
                coalesce(b.ceditamount, 0)
             END) AS credit_amount_quarter,
             (CASE v_same_quater
               WHEN 1 THEN
                coalesce(c.credit_amount_quarter_cu, 0) +
                coalesce(b.ceditamount_cu, 0)
               ELSE
                coalesce(b.ceditamount_cu, 0)
             END) AS credit_amount_quarter_cu,

             (CASE v_same_year
               WHEN 1 THEN
                coalesce(c.debit_amount_year, 0) +
                coalesce(b.debitamount, 0)
               ELSE
                coalesce(b.debitamount, 0)
             END) AS debit_amount_year,
             (CASE v_same_year
               WHEN 1 THEN
                coalesce(c.debit_amount_year_cu, 0) +
                coalesce(b.debitamount_cu, 0)
               ELSE
                coalesce(b.debitamount_cu, 0)
             END) AS debit_amount_year,
             (CASE v_same_year
               WHEN 1 THEN
                coalesce(c.credit_amount_year, 0) +
                coalesce(b.ceditamount, 0)
               ELSE
                coalesce(b.ceditamount, 0)
             END) AS credit_amount_year,
             (CASE v_same_year
               WHEN 1 THEN
                coalesce(c.credit_amount_year_cu, 0) +
                coalesce(b.ceditamount_cu, 0)
               ELSE
                coalesce(b.ceditamount_cu, 0)
             END) AS credit_amount_year_cu,
             coalesce(c.closing_balance, 0) AS opening_balance,
             coalesce(c.closing_balance, 0) + coalesce(b.debitamount, 0) -
             coalesce(b.ceditamount, 0) AS closing_balance,
             coalesce(c.closing_balance_cu, 0) AS opening_balance_cu,
             coalesce(c.closing_balance_cu, 0) +
             coalesce(b.debitamount_cu, 0) - coalesce(b.ceditamount_cu, 0),
             SYSDATE,
             p_user_id,
             NULL,
             NULL,
             v_serial_no
        FROM (SELECT a.entity_id,
                     a.book_code,
                     a.year_month,
                     b.account_id,
                     b.currency_code,
                     b.currency_cu_code,
                     SUM(CASE b.account_entry_code
                           WHEN 'D' THEN
                            b.amount
                           ELSE
                            0
                         END) AS debitamount,
                     SUM(CASE b.account_entry_code
                           WHEN 'D' THEN
                            b.amount_cu
                           ELSE
                            0
                         END) AS debitamount_cu,
                     SUM(CASE b.account_entry_code
                           WHEN 'C' THEN
                            b.amount
                           ELSE
                            0
                         END) AS ceditamount,
                     SUM(CASE b.account_entry_code
                           WHEN 'C' THEN
                            b.amount_cu
                           ELSE
                            0
                         END) AS ceditamount_cu
                FROM acc_buss_voucher a
                LEFT JOIN acc_buss_voucher_detail b
                  ON a.voucher_id = b.voucher_id
               WHERE a.entity_id = p_entity_id
                 AND a.book_code = p_book_code
                 AND a.year_month = p_year_month
                 AND a.valid_is = '1'
                 AND a.audit_state='1'
               GROUP BY a.entity_id,
                        a.book_code,
                        a.year_month,
                        b.currency_code,
                        b.account_id,
                        b.currency_code,
                        b.currency_cu_code) b
        FULL JOIN (SELECT *
                     FROM acc_buss_ledger_balance c
                    WHERE c.entity_id = p_entity_id
                      AND c.book_code = p_book_code
                      AND c.year_month = v_pre_year_month) c
          ON b.account_id = c.account_id
         AND c.currency_code = b.currency_code;



    COMMIT;
    --过渡期数据 start
    SELECT count(1), coalesce(max(t.task_code), 'T')
      into v_trans_count, v_trnas_task_code
      FROM acc_temp_ledger_balance t
     where t.year_month = p_year_month
       and t.entity_id = p_entity_id
       and t.book_code = p_book_code
       and t.transitional_is = 'T'
       --and coalesce(t.back_is,'0') !='1'
       ;--更新过余额表就不用再次更新
    --v_trans_count := 0;
    if v_trans_count > 0 then
      dbms_output.put_line('过渡期科目余额');
     merge into acc_buss_ledger_balance l
     USING (
     SELECT t.account_id,
            t.entity_id,
            t.year_month,
            t.book_code,
            t.currency_code,
            t.currency_cu_code,
            sum(t.opening_balance) as opening_balance,
            sum(t.opening_balance_cu) as opening_balance_cu
       FROM acc_temp_ledger_balance t
      where t.transitional_is = 'T'
        and t.task_code = v_trnas_task_code
      group by t.account_id,
               t.entity_id,
               t.year_month,
               t.book_code,
               t.currency_code,
               t.currency_cu_code) e
     on (l.entity_id = e.entity_id and l.year_month = e.year_month and l.book_code = e.book_code and l.account_id = e.account_id and l.currency_code = e.currency_code and l.currency_cu_code = e.currency_cu_code )
     WHEN MATCHED THEN

     UPDATE
           SET
               l.opening_balance          = l.opening_balance + e.opening_balance,
               l.closing_balance         = l.closing_balance + e.opening_balance,
               l.opening_balance_cu       = l.opening_balance_cu + e.opening_balance_cu,
               l.closing_balance_cu      = l.closing_balance_cu + e.opening_balance_cu
         WHERE l.entity_id = p_entity_id
           AND l.book_code = p_book_code
           AND l.year_month = p_year_month
           and l.account_id = e.account_id
           and l.currency_code = e.currency_code
           and l.currency_cu_code = e.currency_cu_code

     WHEN NOT MATCHED THEN

     INSERT
          (ledger_balance_id,
           entity_id,
           book_code,
           year_month,
           root_account_id,
           account_id,
           currency_code,
           currency_cu_code,
           debit_amount,
           debit_amount_cu,
           credit_amount,
           credit_amount_cu,
           debit_amount_quarter,
           debit_amount_quarter_cu,
           credit_amount_quarter,
           credit_amount_quarter_cu,
           debit_amount_year,
           debit_amount_year_cu,
           credit_amount_year,
           credit_amount_year_cu,
           opening_balance,
           closing_balance,
           opening_balance_cu,
           closing_balance_cu,
           create_time,
           creator_id,
           update_time,
           updator_id,
           serial_no)
        VALUES
          (acc_seq_item_ledger_balance.nextval,
           e.entity_id,
           e.book_code,
           e.year_month,
           acc_pack_common.func_get_base_account_id(e.account_id),
           e.account_id,
           e.currency_code,
           e.currency_cu_code,
           0 ,--debit_amount,
           0 ,--debit_amount_cu,
           0 ,--credit_amount,
           0 ,--credit_amount_cu,
           0 ,--debit_amount_quarter,
           0 ,--debit_amount_quarter_cu,
           0 ,--credit_amount_quarter,
           0 ,--credit_amount_quarter_cu,
           0 ,--debit_amount_year,
           0 ,--debit_amount_year_cu,
           0 ,--credit_amount_year,
           0 ,--credit_amount_year_cu,
           e.opening_balance,
           e.opening_balance,--期末等于期初
           e.opening_balance_cu,
           e.opening_balance_cu,--期末等于期初
           SYSDATE,
           p_user_id,
           NULL,
           NULL,
           v_serial_no);

           update acc_temp_ledger_balance t set t.back_is = '1' where t.task_code = v_trnas_task_code;

    end if;
    commit;


    --汇总信息
    FOR rec_account_level IN (SELECT DISTINCT account_level
                             FROM bpluser.bbs_account
                            ORDER BY account_level DESC) LOOP
      dbms_output.put_line('科目等级:' || rec_account_level.account_level);

      MERGE INTO acc_buss_ledger_balance t
      USING (SELECT d.entity_id,
                    d.book_code,
                    d.year_month,
                    d.root_account_id,
                    item.upper_account_id,
                    d.currency_code,
                    d.currency_cu_code,
                    SUM(d.debit_amount) AS debit_amount,
                    SUM(d.debit_amount_cu) AS debit_amount_cu,
                    SUM(d.credit_amount) AS credit_amount,
                    SUM(d.credit_amount_cu) AS credit_amount_cu,
                    SUM(d.debit_amount_quarter) AS debit_amount_quarter,--******** start
                    SUM(d.debit_amount_quarter_cu) debit_amount_quarter_cu,
                    SUM(d.credit_amount_quarter) AS credit_amount_quarter,
                    SUM(d.credit_amount_quarter_cu) AS credit_amount_quarter_cu,
                    SUM(d.debit_amount_year) AS debit_amount_year,
                    SUM(d.debit_amount_year_cu) AS debit_amount_year_cu,
                    SUM(d.credit_amount_year) AS credit_amount_year,
                    SUM(d.credit_amount_year_cu) AS credit_amount_year_cu,--******** end
                    SUM(d.opening_balance) AS opening_balance,
                    SUM(d.opening_balance + d.debit_amount - d.credit_amount) AS closing_balance,
                    SUM(d.opening_balance_cu) AS opening_balance_cu,
                    SUM(d.opening_balance_cu + d.debit_amount_cu - d.credit_amount_cu) AS closing_balance_cu
               FROM acc_buss_ledger_balance d
               LEFT JOIN bpluser.bbs_account item
                 ON d.account_id = item.account_id
              WHERE item.valid_is = '1'
                AND item.audit_state = '1'
                AND item.account_level = rec_account_level.account_level
                AND item.upper_account_id <> 0
                AND d.entity_id = p_entity_id
                AND d.book_code = p_book_code
                AND d.year_month = p_year_month
              GROUP BY d.entity_id,
                       d.book_code,
                       d.year_month,
                       d.root_account_id,
                       item.upper_account_id,
                       d.currency_code,
                       d.currency_cu_code,
                       d.entity_id) c
      ON (t.entity_id = c.entity_id AND t.book_code = c.book_code AND t.year_month = c.year_month AND t.root_account_id = c.root_account_id AND t.account_id = c.upper_account_id AND t.currency_code = c.currency_code AND t.currency_cu_code = c.currency_cu_code)
      WHEN MATCHED THEN
        UPDATE
           SET t.debit_amount             = c.debit_amount,
               t.debit_amount_cu          = c.debit_amount_cu,
               t.credit_amount            = c.credit_amount,
               t.credit_amount_cu         = c.credit_amount_cu,
               t.debit_amount_quarter     = c.debit_amount_quarter,
               t.debit_amount_quarter_cu  = c.debit_amount_quarter_cu,
               t.credit_amount_quarter    = c.credit_amount_quarter,
               t.credit_amount_quarter_cu = c.credit_amount_quarter_cu,
               t.debit_amount_year        = c.debit_amount_year,
               t.debit_amount_year_cu     = c.debit_amount_year_cu,
               t.credit_amount_year       = c.credit_amount_year,
               t.credit_amount_year_cu    = c.credit_amount_year_cu,
               t.opening_balance          = c.opening_balance,
               t.closing_balance         = c.closing_balance,
               t.opening_balance_cu       = c.opening_balance_cu,
               t.closing_balance_cu      = c.closing_balance_cu
         WHERE t.entity_id = p_entity_id
           AND t.book_code = p_book_code
           AND t.year_month = p_year_month
      WHEN NOT MATCHED THEN
        INSERT
          (ledger_balance_id,
           entity_id,
           book_code,
           year_month,
           root_account_id,
           account_id,
           currency_code,
           currency_cu_code,
           debit_amount,
           debit_amount_cu,
           credit_amount,
           credit_amount_cu,
           debit_amount_quarter,
           debit_amount_quarter_cu,
           credit_amount_quarter,
           credit_amount_quarter_cu,
           debit_amount_year,
           debit_amount_year_cu,
           credit_amount_year,
           credit_amount_year_cu,
           opening_balance,
           closing_balance,
           opening_balance_cu,
           closing_balance_cu,
           create_time,
           creator_id,
           update_time,
           updator_id,
           serial_no)
        VALUES
          (acc_seq_item_ledger_balance.nextval,
           c.entity_id,
           c.book_code,
           c.year_month,
           c.root_account_id,
           c.upper_account_id,
           c.currency_code,
           c.currency_cu_code,
           c.debit_amount,
           c.debit_amount_cu,
           c.credit_amount,
           c.credit_amount_cu,
           c.debit_amount_quarter,
           c.debit_amount_quarter_cu,
           c.credit_amount_quarter,
           c.credit_amount_quarter_cu,
           c.debit_amount_year,
           c.debit_amount_year_cu,
           c.credit_amount_year,
           c.credit_amount_year_cu,
           c.opening_balance,
           c.closing_balance,
           c.opening_balance_cu,
           c.closing_balance_cu,
           SYSDATE,
           p_user_id,
           NULL,
           NULL,
           v_serial_no);
      COMMIT;

    END LOOP;
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line('[科目余额数据生成异常]' || to_char(SQLCODE) || '::' ||
                           substr(SQLERRM, 1, 200));
      --抛出异常提示信息
      raise_application_error(-20003, '[科目余额数据生成异常]' || to_char(SQLCODE) || '::' ||
                           substr(SQLERRM, 1, 200));
  END proc_ledger_balance;

  PROCEDURE proc_add_balancehis(p_entity_id IN NUMBER,
                                p_book_code IN VARCHAR2,
                                p_year_month IN VARCHAR2,
                                p_user_id IN NUMBER) IS
    /***********************************************************************
      NAME :acc_pack_buss_balance_proc_add_balancehis
      DESCRIPTION : 记录总账余额数据轨迹
      DATE :2021-06-01
      AUTHOR :wuyh
    ***********************************************************************/

  BEGIN
    --科目余额轨迹
    INSERT INTO accuser.acc_buss_ledger_balancehis
      (ledger_balance_his_id,
       ledger_balance_id,
       serial_no,
       entity_id,
       book_code,
       year_month,
       root_account_id,
       account_id,
       currency_code,
       currency_cu_code,
       debit_amount,
       debit_amount_cu,
       credit_amount,
       credit_amount_cu,
       debit_amount_quarter,
       debit_amount_quarter_cu,
       credit_amount_quarter,
       credit_amount_quarter_cu,
       debit_amount_year,
       debit_amount_year_cu,
       credit_amount_year,
       credit_amount_year_cu,
       opening_balance,
       closing_balance,
       opening_balance_cu,
       closing_balance_cu,
       create_time,
       creator_id,
       update_time,
       updator_id)
      SELECT ACC_SEQ_ITEM_LEDGER_BALANCEHIS.nextval,
             ledger_balance_id,
             serial_no,
             entity_id,
             book_code,
             year_month,
             root_account_id,
             account_id,
             currency_code,
             currency_cu_code,
             debit_amount,
             debit_amount_cu,
             credit_amount,
             credit_amount_cu,
             debit_amount_quarter,
             debit_amount_quarter_cu,
             credit_amount_quarter,
             credit_amount_quarter_cu,
             debit_amount_year,
             debit_amount_year_cu,
             credit_amount_year,
             credit_amount_year_cu,
             opening_balance,
             closing_balance,
             opening_balance_cu,
             closing_balance_cu,
             create_time,
             creator_id,
             update_time,
             updator_id
        FROM acc_buss_ledger_balance
       WHERE entity_id = p_entity_id
         AND book_code = p_book_code
         AND year_month = p_year_month;

    --专项余额轨迹
    INSERT INTO accuser.acc_buss_article_balancehis
      (article_his_id,
       article_id,
       serial_no,
       entity_id,
       book_code,
       year_month,
       root_account_id,
       account_id,
       article1,
       article2,
       article3,
       article4,
       article5,
       article6,
       article7,
       article8,
       article9,
       article10,
       article11,
       article12,
       article13,
       article14,
       article15,
       article16,
       article17,
       currency_code,
       currency_cu_code,
       debit_amount,
       debit_amount_cu,
       credit_amount,
       credit_amount_cu,
       debit_amount_quarter,
       debit_amount_quarter_cu,
       credit_amount_quarter,
       credit_amount_quarter_cu,
       debit_amount_year,
       debit_amount_year_cu,
       credit_amount_year,
       credit_amount_year_cu,
       opening_balance,
       closing_balance,
       opening_balance_cu,
       closing_balance_cu,
       create_time,
       creator_id,
       update_time,
       updator_id)
      SELECT acc_seq_item_atc_balancehis.nextval,
             article_id,
             serial_no,
             entity_id,
             book_code,
             year_month,
             root_account_id,
             account_id,
             article1,
             article2,
             article3,
             article4,
             article5,
             article6,
             article7,
             article8,
             article9,
             article10,
             article11,
             article12,
             article13,
             article14,
             article15,
             article16,
             article17,
             currency_code,
             currency_cu_code,
             debit_amount,
             debit_amount_cu,
             credit_amount,
             credit_amount_cu,
             debit_amount_quarter,
             debit_amount_quarter_cu,
             credit_amount_quarter,
             credit_amount_quarter_cu,
             debit_amount_year,
             debit_amount_year_cu,
             credit_amount_year,
             credit_amount_year_cu,
             opening_balance,
             closing_balance,
             opening_balance_cu,
             closing_balance_cu,
             create_time,
             creator_id,
             update_time,
             updator_id
        FROM acc_buss_article_balance
       WHERE entity_id = p_entity_id
         AND book_code = p_book_code
         AND year_month = p_year_month;

    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN
      --抛出异常提示信息
      dbms_output.put_line(to_char(SQLCODE) || '::' || substr(SQLERRM, 1, 200));
            --抛出异常提示信息
      raise_application_error(-20003, SQLERRM);

  END proc_add_balancehis;

END acc_pack_buss_balance;
/

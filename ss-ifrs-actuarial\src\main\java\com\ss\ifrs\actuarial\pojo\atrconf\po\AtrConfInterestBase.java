package com.ss.ifrs.actuarial.pojo.atrconf.po;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AtrConfInterestBase {
    private Long id;
    
    /**
     * 编码
     */
    private String code;
    
    /**
     * 值
     */
    private BigDecimal value;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 修改人ID
     */
    private Long updatorId;
    
    /**
     * 修改时间
     */
    private Date updateTime;
}

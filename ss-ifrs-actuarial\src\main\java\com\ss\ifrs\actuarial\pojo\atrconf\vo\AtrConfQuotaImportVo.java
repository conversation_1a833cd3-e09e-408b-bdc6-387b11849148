/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-09-16 14:50:26
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-09-16 14:50:26<br/>
 * Description: 指标配置表<br/>
 * Table Name: ATR_CONF_QUOTA<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "指标配置表")
public class AtrConfQuotaImportVo implements Serializable {
    /**
     * Database column: ATR_CONF_QUOTA.QUOTA_ID
     * Database remarks: quotaId|主键
     */
    @ApiModelProperty(value = "quotaId|主键", required = true)
    private Long quotaId;

    /**
     * Database column: ATR_CONF_QUOTA.CENTER_ID
     * Database remarks: entityId|业务单位
     */
    @ApiModelProperty(value = "entityId|业务单位", required = true)
    @NotNull(message = "TheBusiness Unit can't be null|业务单位不能为空|業務單位不能為空")
    private Long entityId;

    /**
     * Database column: ATR_CONF_QUOTA.MODEL_DEF_ID
     * Database remarks: model_def_id|计量模型
     */
    @ApiModelProperty(value = "model_def_id|计量模型", required = true)
    private String businessSourceCode;

    private String quotaClass;

    private String yearMonth;

    /**
     * Database column: ATR_CONF_QUOTA.QUOTA_DEF_ID
     * Database remarks: quotaDefId|指标定义
     */
    @ApiModelProperty(value = "quotaDefId|指标定义", required = true)
    @NotNull(message = "The quotaDefId can't be null|指标定义不能为空|指標定義不能為空")
    private Long quotaDefId;

    private String quotaValueType;

    private String percentHundredIs;

    /**
     * Database column: ATR_CONF_QUOTA.QUOTA_VALUE
     * Database remarks: quotaValue|指标值
     */
    @ApiModelProperty(value = "quotaValue|指标值", required = false)
    @ExcelProperty(value = "Assumption_Value")
    private String quotaValue;

    /**
     * Database column: ATR_CONF_QUOTA.VALID_IS
     * Database remarks: validIs|是否有效
     */
    @ApiModelProperty(value = "validIs|是否有效", required = true)
    @NotBlank(message = "The Status cannot be empty|状态不能为空|狀態不能為空")
    @Size(max = 1,message = "The Status must be a character|状态只能是1个字符|狀態只能是1個字符")
    private String validIs;

    /**
     * Database column: ATR_CONF_QUOTA.AUDIT_STATE
     * Database remarks: auditState|审核状态
     */
    @ApiModelProperty(value = "auditState|审核状态", required = false)
    private String auditState;


    /**
     * Database column: ATR_CONF_QUOTA.CREATOR_ID
     * Database remarks: creatorId|创建人
     */
    @ApiModelProperty(value = "creatorId|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_CONF_QUOTA.CREATE_TIME
     * Database remarks: createTime|创建时间
     */
    @ApiModelProperty(value = "createTime|创建时间", required = false)
    private Date createTime;

    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Integer serialNo;

    /**
     * Database column: ATR_CONF_QUOTA_NEW.dimension
     * Database remarks: dimension|假设颗粒度类型( 合同组合、合同组、保单)
     */
    @ApiModelProperty(value = "dimension|假设颗粒度类型( 合同组合、合同组、保单)", required = false)
    private String dimension;

    /**
     * Database column: ATR_CONF_QUOTA_NEW.dimension_value
     * Database remarks: dimension_value|假设颗粒度值
     */
    @ApiModelProperty(value = "dimension_value|假设颗粒度值", required = true)
    @ExcelProperty(value = "Dimension_Value")
    private String dimensionValue;

    private String quotaType;

    @ExcelProperty(value = "Assumption_Code")
    private String quotaCode;

    @ExcelProperty(value = "Assumption_Name")
    private String quotaName;

    private String quotaCName;
    private String quotaLName;
    private String quotaEName;

    private String creatorName;

    private String updatorName;

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/15
     * @Description 区分新增和编辑或查看
     *
     */
    private String type;


    private String msgType;
    /**
     * 导出文件名
     */
    private String templateFileName;

    private String targetRouter;
    private String language;

    private static final long serialVersionUID = 1L;

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    public String getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(String quotaValue) {
        this.quotaValue = quotaValue;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public String getQuotaName() {
        return quotaName;
    }

    public void setQuotaName(String quotaName) {
        this.quotaName = quotaName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getDimensionValue() {
        return dimensionValue;
    }

    public void setDimensionValue(String dimensionValue) {
        this.dimensionValue = dimensionValue;
    }

    public String getQuotaType() {
        return quotaType;
    }

    public void setQuotaType(String quotaType) {
        this.quotaType = quotaType;
    }

    public String getQuotaCName() {
        return quotaCName;
    }

    public void setQuotaCName(String quotaCName) {
        this.quotaCName = quotaCName;
    }

    public String getQuotaLName() {
        return quotaLName;
    }

    public void setQuotaLName(String quotaLName) {
        this.quotaLName = quotaLName;
    }

    public String getQuotaEName() {
        return quotaEName;
    }

    public void setQuotaEName(String quotaEName) {
        this.quotaEName = quotaEName;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public String getQuotaValueType() {
        return quotaValueType;
    }

    public void setQuotaValueType(String quotaValueType) {
        this.quotaValueType = quotaValueType;
    }

    public String getPercentHundredIs() {
        return percentHundredIs;
    }

    public void setPercentHundredIs(String percentHundredIs) {
        this.percentHundredIs = percentHundredIs;
    }

    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public String getQuotaClass() {
        return quotaClass;
    }

    public void setQuotaClass(String quotaClass) {
        this.quotaClass = quotaClass;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}
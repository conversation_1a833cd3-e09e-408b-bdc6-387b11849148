# SQL special_process_type逻辑修正说明

## 问题描述

### 原有逻辑的问题
在`AtrBussLrcDdCustDao.xml`的`partitionBaseData`方法中，`special_process_type`的判断逻辑存在缺陷：

**错误场景示例**：
- 保单号：POLICY_001
- 原单：year_month=202412, endorse_type_code=01
- 批单：year_month=202501, endorse_type_code=15
- 当前评估期：202501

**原有错误逻辑**：
- 原单记录：dap_year_month=202412 ≠ 当前评估期，被错误标记为special_process_type=2
- 批单记录：dap_year_month=202501 = 当前评估期，正确标记为special_process_type=1

**问题**：同一保单号下的记录被标记为不同的special_process_type，导致处理逻辑不一致。

## 修正后的逻辑

### 核心原则
1. **以批单为准**：判断依据是包含15/16批改类型的批单的year_month，而不是每条记录自身的dap_year_month
2. **保单级别统一**：同一保单号下的所有记录应该有相同的special_process_type
3. **优先级明确**：当前月的15/16批单优先级最高

### 修正后的SQL逻辑

#### 1. special_process_check CTE增强
```sql
special_process_check AS (
    SELECT
        policy_no,
        -- 检查是否存在15/16类型的批单在当前评估月
        CASE WHEN MAX(CASE WHEN year_month = #{yearMonth,jdbcType=VARCHAR} 
                           AND EXISTS (
                               SELECT 1 FROM unnest(string_to_array(endorse_type_code, ',')) as endorse_code
                               WHERE trim(endorse_code) IN ('15', '16')
                           ) THEN 1 ELSE 0 END) = 1
             THEN true ELSE false END as has_current_month_1516,
        -- 检查是否存在15/16类型的批单（不限月份）
        CASE WHEN MAX(CASE WHEN EXISTS (
                               SELECT 1 FROM unnest(string_to_array(endorse_type_code, ',')) as endorse_code
                               WHERE trim(endorse_code) IN ('15', '16')
                           ) THEN 1 ELSE 0 END) = 1
             THEN true ELSE false END as has_any_1516
    FROM atr_dap_dd_unit check_unit
    WHERE check_unit.entity_id = #{entityId,jdbcType=BIGINT}
      AND check_unit.year_month <= #{yearMonth,jdbcType=VARCHAR}
      AND check_unit.endorse_type_code IS NOT NULL
    GROUP BY policy_no
)
```

#### 2. special_process_type计算逻辑
```sql
CASE
    WHEN spc.policy_no IS NULL THEN 0                                           -- 无15/16批改类型
    WHEN spc.has_current_month_1516 = true THEN 1                              -- 当月有15/16批单
    WHEN spc.has_any_1516 = true AND spc.has_current_month_1516 = false THEN 2 -- 历史有15/16批单但当月没有
    ELSE 0                                                                      -- 其他情况
END as special_process_type
```

## 修正前后对比

### 修正前
| 记录类型 | year_month | endorse_type_code | dap_year_month | special_process_type | 问题 |
|----------|------------|-------------------|----------------|---------------------|------|
| 原单     | 202412     | 01                | 202412         | 2                   | ❌ 错误 |
| 批单     | 202501     | 15                | 202501         | 1                   | ✅ 正确 |

### 修正后
| 记录类型 | year_month | endorse_type_code | dap_year_month | special_process_type | 说明 |
|----------|------------|-------------------|----------------|---------------------|------|
| 原单     | 202412     | 01                | 202412         | 1                   | ✅ 统一为1 |
| 批单     | 202501     | 15                | 202501         | 1                   | ✅ 统一为1 |

## 业务逻辑说明

### special_process_type含义
- **0**：正常处理（无15/16批改类型）
- **1**：只计算第0期现金流（当月有15/16批单）
- **2**：不计算发展期现金流（历史有15/16批单但当月没有）

### 判断流程
1. **检查保单号**：是否存在endorse_type_code包含15或16的记录
2. **检查时间**：15/16批单是否在当前评估月
3. **统一标记**：同一保单号下所有记录使用相同的special_process_type

## 测试验证

### 测试场景1：当月有15/16批单
- 保单：POLICY_001
- 原单：202412, endorse_type_code=01
- 批单：202501, endorse_type_code=15
- 评估期：202501
- **预期**：所有记录special_process_type=1

### 测试场景2：历史有15/16批单，当月没有
- 保单：POLICY_002  
- 原单：202412, endorse_type_code=01
- 批单：202412, endorse_type_code=16
- 评估期：202501
- **预期**：所有记录special_process_type=2

### 测试场景3：无15/16批单
- 保单：POLICY_003
- 原单：202412, endorse_type_code=01
- 批单：202501, endorse_type_code=02
- 评估期：202501
- **预期**：所有记录special_process_type=0

## 影响范围

### 正面影响
1. **逻辑一致性**：同一保单号下所有记录处理逻辑统一
2. **业务准确性**：正确识别需要特殊处理的保单
3. **数据完整性**：避免因逻辑错误导致的数据遗漏

### 风险控制
1. **向后兼容**：不影响正常业务记录的处理
2. **性能优化**：通过CTE预计算，避免重复查询
3. **逻辑清晰**：判断条件更加明确，易于维护

## 部署建议

1. **测试验证**：在测试环境充分验证各种场景
2. **数据对比**：对比修正前后的计算结果
3. **监控观察**：部署后密切关注相关业务指标
4. **回滚准备**：准备快速回滚方案以应对意外情况

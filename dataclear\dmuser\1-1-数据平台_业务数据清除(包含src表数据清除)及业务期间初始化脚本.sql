DO $$

DECLARE
cur record;
v_ods_sql varchar;
v_dm_sql varchar;
v_stat_sql varchar;

begin
for cur in (
    SELECT biz_code,biz_type_id, type_c_name, type_group, display_no  FROM "dm_conf_table" where valid_is = '1'
	GROUP BY biz_code,biz_type_id, type_c_name, type_group, display_no 
	ORDER BY CASE WHEN type_group = '7' THEN '0' ELSE type_group END, type_group, display_no ) loop 

    v_ods_sql:= 'truncate table odsuser.ods_'||cur.biz_code||';';
EXECUTE v_ods_sql;

v_dm_sql:= 'truncate table dm_'||cur.biz_code||';';
EXECUTE v_dm_sql;

v_stat_sql:= 'truncate table dm_stat_'||cur.biz_code||';';
EXECUTE v_stat_sql;

end loop;
end $$;


--3、清除计量单元表数据
TRUNCATE TABLE dmuser.dm_buss_cmunit_direct;
TRUNCATE TABLE dmuser.dm_buss_cmunit_fac_outwards;
TRUNCATE TABLE dmuser.dm_buss_cmunit_treaty_inward;
TRUNCATE TABLE dmuser.dm_buss_cmunit_treaty_outward;

--4、清除文件上传日志表
TRUNCATE TABLE dmuser.dm_duct_draw_log;

--5、清除数据校验信号表
TRUNCATE table odsuser.ods_data_push_signal;

--6、清除校验日志
TRUNCATE TABLE dmuser.dm_log_check_rule;
TRUNCATE TABLE dmuser.dm_log_data_verify;
TRUNCATE TABLE dmuser.dm_log_data_verify_detail;

--7、清除盈亏日志
TRUNCATE TABLE dmuser.dm_log_buss_cmunit;
TRUNCATE TABLE dmuser.dm_log_buss_cmunit_detail;
TRUNCATE TABLE dmuser.dm_duct_direct_profit_unit;
TRUNCATE TABLE dmuser.dm_duct_direct_profit_amount;
TRUNCATE TABLE dmuser.dm_duct_direct_profit_param;

TRUNCATE TABLE DM_DUCT_PROFITABLE_TI_QUOTA;
TRUNCATE TABLE DM_DUCT_PROFITABLE_TI_QUOTA_DEVLOP;
TRUNCATE TABLE DM_DUCT_PROFITABLE_TI_CMUNIT_AMOUNT;
TRUNCATE TABLE DM_DUCT_PROFITABLE_TI_SET_AMOUNT;
TRUNCATE TABLE DM_DUCT_CMUNIT_PROFITABLE_TI_PAA;
TRUNCATE TABLE DM_DUCT_CMUNIT_PROFITABLE_TI_BBA;

TRUNCATE TABLE DM_DUCT_PROFITABLE_DD_QUOTA;
TRUNCATE TABLE DM_DUCT_PROFITABLE_DD_QUOTA_DEVLOP;
TRUNCATE TABLE DM_DUCT_PROFITABLE_DD_CMUNIT_AMOUNT;
TRUNCATE TABLE DM_DUCT_PROFITABLE_DD_SET_AMOUNT;
TRUNCATE TABLE DM_DUCT_CMUNIT_PROFITABLE_DD_PAA;
TRUNCATE TABLE DM_DUCT_CMUNIT_PROFITABLE_DD_BBA;


TRUNCATE TABLE dmuser.dm_duct_profit_param_value;
TRUNCATE TABLE dmuser.dm_duct_profit_unit;
TRUNCATE TABLE dmuser.dm_duct_profit_unit_sub;

TRUNCATE TABLE dmuser.dm_duct_treaty_profit_amount;
TRUNCATE TABLE dmuser.dm_duct_treaty_profit_param;
TRUNCATE TABLE dmuser.dm_duct_treaty_profit_unit;

truncate table dm_need_compensate_policy;
truncate table dm_model_compensate_data;
truncate table dm_log_compensate_policy;

-- 清除流程节点轨迹表
truncate table dm_track_process;
truncate table dm_track_workflow;

--清除所有业务期间
TRUNCATE TABLE dmuser.dm_conf_bussperiod_detail;
delete from dmuser.dm_conf_bussperiod;

SELECT setval('dmuser.DM_SEQ_CONF_BUSSPERIOD', 1, true);
SELECT setval('dmuser.DM_SEQ_CONF_BUSSPERIOD_DETAIL', 1, true);

DO $$

DECLARE
cur record;
v_year_month varchar := '202412';

begin

--业务期间初始化 为201101
insert into dmuser.dm_conf_bussperiod (BUSS_PERIOD_ID, entity_id, YEAR_MONTH, PERIOD_STATE, VALID_IS, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME)
values (Nextval('DM_SEQ_CONF_BUSSPERIOD'), 1, v_year_month, '0', '1', 1, LOCALTIMESTAMP, 0, null);

--增加业务详细表数据
for cur in (
    select biz_type_id ,'1' as DIRECTION,BIZ_CODE  from dm_conf_table where VALID_IS = '1'
      union all
        select biz_type_id ,'0' as DIRECTION,BIZ_CODE from dm_conf_table_output where VALID_IS = '1'
        order by DIRECTION desc ,biz_type_id
) loop
  insert into dmuser.dm_conf_bussperiod_detail (PERIOD_DETAIL_ID, BUSS_PERIOD_ID, BIZ_TYPE_ID, BIZ_CODE, EXEC_RESULT, DIRECTION, READY_STATE, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME)
values (Nextval('DM_SEQ_CONF_BUSSPERIOD_DETAIL'),(select BUSS_PERIOD_ID from dmuser.dm_conf_bussperiod where year_month = v_year_month and entity_id = 1),  cur.biz_type_id, cur.BIZ_CODE, null, cur.DIRECTION, '0', 1, LOCALTIMESTAMP, null, null);

end loop;

end $$;




--清理文件交换表项并初始化
truncate table dm_buss_file_exchange_ctl;
TRUNCATE table dm_buss_file_exchange_model;
TRUNCATE table dm_buss_file_exchange_signal ;
TRUNCATE table dm_log_file_exchange_file_line ;
TRUNCATE table dm_log_file_exchange_trace ;

SELECT setval('dmuser.dm_seq_buss_file_exchange_ctl', 1, true);

INSERT INTO dmuser.dm_buss_file_exchange_ctl (ctl_id, year_month, buss_date, status, create_time)
VALUES (nextval('dm_seq_buss_file_exchange_ctl'), '202412', '2024-12-31', '0', LOCALTIMESTAMP);

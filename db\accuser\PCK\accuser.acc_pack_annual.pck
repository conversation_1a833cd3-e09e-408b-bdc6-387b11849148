create or replace package acc_pack_annual is

    PROCEDURE proc_annual_data_conversion(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER);

    PROCEDURE proc_annual_balance_check(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER);

    PROCEDURE proc_annual_entrance(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER);

    PROCEDURE proc_annual_colse_period(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER);

    PROCEDURE proc_annual_generate_voucher(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER);

    PROCEDURE proc_annual_refresh_balance(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER);

    PROCEDURE proc_annual_summary_balance(p_entity_id IN NUMBER, p_book_code IN VARCHAR2, p_year_month IN VARCHAR2, p_user_id IN NUMBER);

    PROCEDURE proc_annual_sync_data(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER);

    --过渡期，科目余额数据转换
    PROCEDURE proc_annual_trans_period(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER);

    --过渡期，科目余额对冲
    --PROCEDURE proc_annual_trans_revoke(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER);

end acc_pack_annual;
/
create or replace package body acc_pack_annual is
    --
    /***********************************************************************
  NAME :acc_pack_annual_proc_annual_data_conversion
  DESCRIPTION : 同步现行账套财务凭证，并根据科目方式为手工类的做转化到17账套(科目和凭证号)
  DATE :2021-06-01
  AUTHOR :wuyh
  ***********************************************************************/
    PROCEDURE proc_annual_data_conversion(P_ENTITY_ID IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER) IS

        v_annual_period VARCHAR(6);
        v_reckoning_state  VARCHAR(1);

        v_year_month VARCHAR(6);
        v_ready_state  VARCHAR(1);
        V_YEAR_END   VARCHAR(6);
        v_char       varchar(1);
        v_year_end_flag    VARCHAR(6);
        v_proc_id numeric;
        v_voucher_count numeric;
        v_tran_ym   VARCHAR(6);
    BEGIN

        --判断是否是过渡期当月 20230615
        begin
            SELECT t.code_code into v_tran_ym
            FROM acc_conf_code t
            where t.upper_code_id =
                  (SELECT a.code_id
                   FROM acc_v_conf_code a
                   where a.code_code_idx = 'TransitionPeriod')
              and t.valid_is = '1';

        EXCEPTION
            WHEN OTHERS THEN
                --抛出异常提示信息
                --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
                --RAISE EXCEPTION '[EXCEPTION]测试异常：%, %',v_text1,v_text2;
                dbms_output.put_line('[EXCEPTION]proc_annual_data_conversion：' ||
                                     to_char(SQLCODE) || ';' ||
                                     substr(SQLERRM, 1, 200));
        end;

        --过渡期特殊处理
        if p_yearmonth < v_tran_ym then
            return ;
        end if;

        SELECT MAX(YEAR_MONTH), MAX(RECKONING_STATE)
        INTO V_ANNUAL_PERIOD, V_RECKONING_STATE
        FROM ACC_CONF_ANNUAL_PERIOD
        WHERE ENTITY_ID = P_ENTITY_ID
          AND BOOK_CODE = P_BOOKCODE
          AND YEAR_MONTH = P_YEARMONTH
          AND VALID_IS = '1'
          AND AUDIT_STATE = '1';

        SELECT MAX(P.YEAR_MONTH), MAX(BPD.READY_STATE)
        INTO V_YEAR_MONTH, V_READY_STATE
        FROM ACC_CONF_ACCOUNTPERIOD_DETAIL BPD
                 LEFT JOIN ACC_CONF_ACCOUNTPERIOD P
                           ON BPD.PERIOD_ID = P.PERIOD_ID
                 LEFT JOIN ACC_CONF_TABLE T
                           ON BPD.BIZ_TYPE_ID = T.BIZ_TYPE_ID
        WHERE P.ENTITY_ID = P_ENTITY_ID
          AND P.YEAR_MONTH = P_YEARMONTH
          and t.biz_code ='EXT_DM_VOUCHER';

        IF V_ANNUAL_PERIOD IS NOT NULL AND SUBSTR(V_ANNUAL_PERIOD, 5, 2) = '13' THEN
            SELECT NVL(EXT_YEAR_END_FLAG, '')
            INTO V_YEAR_END_FLAG
            FROM BPLUSER.BBS_CONF_ACCOUNT_SET B
            WHERE B.ENTITY_ID = P_ENTITY_ID
              AND ROWNUM = 1;
            V_YEAR_END := SUBSTR(P_YEARMONTH, 1, 4) || V_YEAR_END_FLAG;
            IF V_RECKONING_STATE = '1' THEN
                --抛出异常，中断事务
                SELECT '*' INTO V_CHAR FROM DUAL WHERE 1 = 2;
                --RAISE EXCEPTION '年结期间已结转：会计期间: %',p_yearmonth;
            END IF;
            DBMS_OUTPUT.PUT_LINE('同步业务期间%的财务凭证信息执行状态完成1: ' || V_YEAR_END);
        ELSIF V_YEAR_MONTH IS NOT NULL THEN
            V_YEAR_END := V_YEAR_MONTH;
        END IF;


        SELECT COUNT(B.VOUCHER_NO)
        INTO V_VOUCHER_COUNT
        FROM ACCUSER.ACC_EXT_VOUCHER B
        WHERE B.ENTITY_ID = P_ENTITY_ID
          AND B.YEAR_MONTH = V_YEAR_END
          AND B.POSTING_TYPE_CODE = 'FI' --条件1:手工凭证
          AND NOT EXISTS (SELECT 1 --条件2:未被转化过
                          FROM ACCUSER.ACC_BUSS_VOUCHER B1
                          WHERE B.ENTITY_ID = B1.ENTITY_ID
                            AND B.YEAR_MONTH = B1.YEAR_MONTH
                            AND B.VOUCHER_NO = B1.EXT_VOUCHER_NO)
          AND NOT EXISTS ( --条件3:凭证借贷平衡
                SELECT 1
                FROM ACCUSER.ACC_EXT_VOUCHER_DETAIL B2
                WHERE B.VOUCHER_NO = B2.VOUCHER_NO
                GROUP BY B2.VOUCHER_NO
                HAVING SUM(CASE
                               WHEN B2.ACCOUNT_ENTRY_CODE = 'D' THEN
                                   B2.AMOUNT_CU
                               ELSE
                                   0
                    END) <> SUM(CASE
                                    WHEN B2.ACCOUNT_ENTRY_CODE = 'C' THEN
                                        B2.AMOUNT_CU
                                    ELSE
                                        0
                    END))
          AND NOT EXISTS (SELECT 1 --条件4:有旧准则和17准则的科目映射拼装
                          FROM ACCUSER.ACC_EXT_VOUCHER_DETAIL B2
                          WHERE B.VOUCHER_NO = B2.VOUCHER_NO
                            AND NOT EXISTS
                              (SELECT 1
                               FROM BPLUSER.bbs_conf_account_mapping ITEM
                               WHERE B2.ACCOUNT_ID = ITEM.OTHER_account_id
                                 AND B.BOOK_CODE = ITEM.OTHER_BOOK_CODE
                                 AND ITEM.VALID_IS = '1'
                                 AND ITEM.AUDIT_STATE = '1'
                                 AND ITEM.BASE_account_id IS NOT NULL))
          AND NOT EXISTS (select 1         --条件5: 准备金科目凭证不做转化
                          from accuser.acc_ext_voucher_detail b2
                          WHERE b.voucher_no = b2.voucher_no
                            and exists (select 1 from bpluser.bbs_conf_account_reserve ar where b2.ACCOUNT_ID = ar.account_id AND b.entity_id = ar.entity_id and b.book_code = ar.book_code and ar.valid_is = '1'));
        /* AND NOT EXISTS
   (SELECT 1 --条件5:不是待摊科目（待摊科目走费用分摊进入会计平台）
            FROM ACCUSER.ACC_EXT_VOUCHER_DETAIL B2
           WHERE B.VOUCHER_NO = B2.VOUCHER_NO
             AND EXISTS (SELECT 1
                    FROM EXPUSER.EXP_ACCOUNTCODE ITEM
                   WHERE B2.ACCOUNT_ID = ITEM.ACCOUNT_ID
                     AND ITEM.VALID_IS = '1'
                     AND ITEM.AUDIT_STATE = '1'))*/


        IF V_VOUCHER_COUNT = '0' THEN
            RETURN; --手工凭证符合转化规则个数为0
        END IF;


        --财务凭证流程id
        v_proc_id := bpluser.bpl_pack_common.func_get_procid('ACC_ACCOUNTENTRY_FI_ACR');

        accuser.acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                        p_yearmonth ,
                                                        null,
                                                        null ,
                                                        v_proc_id  ,
                                                        'data_sync:data conversion' ,
                                                        '1' ,
                                                        'start insert into acc_buss_voucher ' );
        --凭证信息
        INSERT INTO accuser.acc_buss_voucher
        (voucher_id,
         ENTITY_ID,
         book_code,
         PROC_ID,
         POSTING_TYPE_CODE,
         voucher_no,
         year_month,
         effective_date,
         STATE,
         remark,
         create_time,
         creator_id,
         ext_voucher_no,
         valid_is,
         audit_state,
         task_code)
        SELECT acc_seq_buss_voucher.Nextval,
               b.ENTITY_ID,
               p_bookcode as bookcode,
               v_proc_id as proc_id,
               '07' as POSTING_TYPE_CODE, --'手工凭证'
               acc_pack_voucher.func_generate_voucher_no(b.ENTITY_ID,
                                                         p_bookcode,
                                                         p_yearmonth,
                                                         '07',
                                                         v_proc_id || '',
                                                         rownum), --I17的凭证号
               b.year_month,
               b.effective_date,
               '1' as STATE,
               b.remark,
               LOCALTIMESTAMP,    -- 创建时间
               p_userid,          --creator_id
               b.voucher_no,      --现行财务凭证号，用于映射和回溯
               '1' as valid_is,
               '1' as audit_state,
               b.task_code AS task_code
        from accuser.acc_ext_voucher b
        WHERE b.ENTITY_ID = P_ENTITY_ID
          AND b.year_month = V_YEAR_END
          AND b.POSTING_TYPE_CODE  = 'FI'  --条件1:手工凭证
          AND NOT EXISTS (select 1        --条件2:未被转化过
                          from accuser.acc_buss_voucher b1
                          WHERE b.ENTITY_ID = b1.ENTITY_ID
                            and b.year_month = b1.year_month
                            and b.voucher_no = b1.ext_voucher_no)
          AND NOT EXISTS (                --条件3:凭证借贷平衡
                select 1
                from accuser.acc_ext_voucher_detail b2
                WHERE b.voucher_no = b2.voucher_no
                group BY b2.voucher_no
                HAVING sum(case when b2.ACCOUNT_ENTRY_CODE = 'D' then b2.AMOUNT_CU else 0 end) <> sum(case when b2.ACCOUNT_ENTRY_CODE = 'C' then b2.AMOUNT_CU else 0  end)
            )
          AND NOT EXISTS(select 1         --条件4:有旧准则和17准则的科目映射拼装
                         from accuser.acc_ext_voucher_detail b2
                         WHERE b.voucher_no = b2.voucher_no
                           and not exists (select 1 from bpluser.bbs_conf_account_mapping item where b2.ACCOUNT_ID = item.other_account_id and b.book_code = item.other_book_code and item.valid_is = '1'
                                                                                                 and item.audit_state = '1' AND item.base_account_id IS NOT NULL) )
          AND NOT EXISTS (select 1         --条件5: 准备金科目凭证不做转化
                          from accuser.acc_ext_voucher_detail b2
                          WHERE b.voucher_no = b2.voucher_no
                            and exists (select 1 from bpluser.bbs_conf_account_reserve ar where b2.ACCOUNT_ID = ar.account_id AND b.entity_id = ar.entity_id and b.book_code = ar.book_code and ar.valid_is = '1'));
        /*AND NOT EXISTS (select 1        --条件5:不是待摊科目（待摊科目走费用分摊进入会计平台）
              from accuser.acc_ext_voucher_detail b2
             WHERE b.voucher_no = b2.voucher_no
               AND exists (select 1 from  expuser.exp_accountcode item where b2.ACCOUNT_ID = item.ACCOUNT_ID and item.valid_is = '1' and item.audit_state = '1' ))*/--#BUG 7392 :1. 费用分摊需要增加手工转换

        accuser.acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                        p_yearmonth ,
                                                        null,
                                                        null ,
                                                        v_proc_id  ,
                                                        'data_sync:data conversion' ,
                                                        '1' ,
                                                        'step2 insert into acc_buss_voucher_detail ' );
        INSERT INTO accuser.acc_buss_voucher_detail (
            voucher_dtl_id,
            voucher_id,
            account_id,
            currency_code,
            currency_cu_code,
            ACCOUNT_ENTRY_CODE,
            AMOUNT,
            AMOUNT_CU,
            exchange_rate,
            remark,
            article1,
            article2,
            article3,
            article4,
            article5,
            article6,
            article7,
            article8,
            article9,
            article10,
            article11,
            article12,
            article13,
            article14,
            article15,
            article16,
            article17,
            create_time,
            creator_id,
            ENTRY_DATA_ID
        )
        SELECT
            acc_seq_buss_voucher_detail.Nextval,
            voucher_id,
            account_id,
            currency_code,
            currency_cu_code,
            ACCOUNT_ENTRY_CODE,
            AMOUNT,
            AMOUNT_CU,
            exchange_rate,
            remark,
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE1','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE2','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE3','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE4','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE5','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE6','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE7','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE8','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE9','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE10','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE11','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE12','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE13','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE14','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE15','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE16','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            acc_pack_common.func_analy_model_mapping('ACC_EXT_VOUCHER_DETAIL','ACC_BUSS_VOUCHER_DETAIL','ARTICLE17','ext_voucher_dtl_id','=',b.ext_voucher_dtl_id||'', 'number'),
            create_time,
            creator_id,
            b.ext_voucher_dtl_id
        FROM (
                 SELECT
                     b.ext_voucher_dtl_id as ext_voucher_dtl_id,
                     c.voucher_id  as voucher_id,
                     item.base_account_id as account_id,
                     b.currency_code,
                     b.currency_cu_code,
                     b.ACCOUNT_ENTRY_CODE,
                     sum(b.AMOUNT) as AMOUNT,
                     sum(b.AMOUNT_CU) as AMOUNT_CU,
                     b.exchange_rate,
                     b.remark,
                     b.article1,
                     b.article2,
                     b.article3,
                     b.article4,
                     b.article5,
                     b.article6,
                     b.article7,
                     b.article8,
                     b.article9,
                     b.article10,
                     b.article11,
                     b.article12,
                     b.article13,
                     b.article14,
                     b.article15,
                     b.article16,
                     b.article17,
                     LOCALTIMESTAMP as create_time,    -- 创建时间
                     p_userid as creator_id            --creator_id
                 from accuser.acc_buss_voucher c
                          left join acc_ext_voucher a
                                    on a.ENTITY_ID = c.ENTITY_ID
                                        and a.voucher_no = c.ext_voucher_no
                          left join acc_ext_voucher_detail b
                                    on a.voucher_no = b.voucher_no
                          left join bpluser.bbs_conf_account_mapping item
                                    on a.book_code = item.other_book_code
                                        and c.book_code = item.base_book_code
                                        and b.account_id = item.other_account_id
                                        and item.valid_is = '1'
                          left join bpluser.bbs_account it
                                    on item.base_account_id= it.account_id
                 WHERE a.voucher_no = b.voucher_no
                   and a.voucher_no = c.ext_voucher_no
                   and c.entity_id = p_entity_id
                   and c.year_month = p_yearmonth
                   and item.valid_is='1'
                   and item.audit_state='1'
                   and it.valid_is='1'
                   and it.audit_state='1'
                   and NOT EXISTS (select 1
                                   from accuser.acc_buss_voucher_detail vd
                                   WHERE c.voucher_id = vd.voucher_id)
                 group by
                     b.ext_voucher_dtl_id,
                     c.voucher_id,
                     item.base_account_id,
                     b.currency_code,
                     b.currency_cu_code,
                     b.ACCOUNT_ENTRY_CODE,
                     b.exchange_rate,
                     b.remark,
                     b.article1,
                     b.article2,
                     b.article3,
                     b.article4,
                     b.article5,
                     b.article6,
                     b.article7,
                     b.article8,
                     b.article9,
                     b.article10,
                     b.article11,
                     b.article12,
                     b.article13,
                     b.article14,
                     b.article15,
                     b.article16,
                     b.article17,
                     LOCALTIMESTAMP,
                     p_userid
             ) b;

        accuser.acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                        p_yearmonth ,
                                                        null,
                                                        null ,
                                                        v_proc_id  ,
                                                        'data_sync:data conversion' ,
                                                        '1' ,
                                                        'step3 update acc_buss_voucher_detail ' );
        UPDATE accuser.acc_buss_voucher_detail vd SET article=
                                                              nvl2(article1, article1 || '/', '') ||
                                                              nvl2(article2, article2 || '/', '') ||
                                                              nvl2(article3, article3 || '/', '') ||
                                                              nvl2(article4, article4 || '/', '') ||
                                                              nvl2(article5, article5 || '/', '') ||
                                                              nvl2(article6, article6 || '/', '') ||
                                                              nvl2(article7, article7 || '/', '') ||
                                                              nvl2(article8, article8 || '/', '') ||
                                                              nvl2(article9, article9 || '/', '') ||
                                                              nvl2(article10, article10 || '/', '') ||
                                                              nvl2(article11, article11 || '/', '') ||
                                                              nvl2(article12, article12 || '/', '') ||
                                                              nvl2(article13, article13 || '/', '') ||
                                                              nvl2(article14, article14 || '/', '') ||
                                                              nvl2(article15, article15 || '/', '') ||
                                                              nvl2(article16, article16 || '/', '') ||
                                                              nvl2(article17, article17 || '/', '')
        WHERE EXISTS (SELECT 1 FROM accuser.acc_buss_voucher v
                      WHERE vd.voucher_id = v.voucher_id
                        AND v.ENTITY_ID = P_ENTITY_ID
                        AND v.year_month = p_yearmonth
                        AND v.POSTING_TYPE_CODE = '07');

        COMMIT;
        --生成余额数据
        accuser.acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                        p_yearmonth ,
                                                        null,
                                                        null ,
                                                        v_proc_id  ,
                                                        'data_sync:data conversion' ,
                                                        '1' ,
                                                        'step4 proc_balance_entrance ' );
        acc_pack_buss_balance.proc_balance_entrance(p_entity_id, p_bookcode, p_yearmonth, p_userid);

        accuser.acc_pack_voucher.proc_account_entry_log(p_entity_id,
                                                        p_yearmonth ,
                                                        null,
                                                        null ,
                                                        v_proc_id  ,
                                                        'data_sync:data conversion' ,
                                                        '1' ,
                                                        'end ' );

    EXCEPTION
        WHEN OTHERS THEN
            if v_reckoning_state = '0' then
                dbms_output.put_line('**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 年结期间已结转：会计期间: %'||p_yearmonth);
            else
                dbms_output.put_line('**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 插入数据异常，请检查');
            end if;
            dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
            --往外层抛出异常信息
            raise_application_error(-20003, '年结手工凭证转化17凭证异常:'||'**出错行数: '|| dbms_utility.format_error_backtrace()
                || '**出错问题: '||SUBSTR(SQLERRM, 1, 200) );
    END proc_annual_data_conversion;

    /***********************************************************************
  NAME :JS_CHECK
  DESCRIPTION :年结检查
  DATE :2022-5-17
  AUTHOR :CHENJUNFENG
  ***********************************************************************/
    /**
	p_type :
	  ProfitLoss 1 损益类（收入类）
		Charge     2 损益类（支出类）
		Profit     3 本年利润
		Distribute 4 利润分配
		All        5 所有科目
	*/
    PROCEDURE proc_annual_balance_check(P_ENTITY_ID IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER) IS
        v_char  VARCHAR(1);
        v_debit_amount_year   numeric(16,2);
        v_credit_amount_year  numeric(16,2);
        v_closing_balance    numeric(16,2);
        v_actlogid            numeric; --业务日志表ID
        v_taskcode VARCHAR(64);--
        v_annual_proc_id numeric(4,0);--
        v_annual_check1 numeric(4,0);--
        v_annual_check2 numeric(4,0);--
        v_annual_check3 numeric(4,0);--
        v_annual_check4 numeric(4,0);--
        v_annual_check5 numeric(4,0);--
        v_check_result char := '1' ;--
        v_errormessage             VARCHAR(256); --规则校验不通过信息
        v_busmessage               VARCHAR(256):=''; --业务异常主键
        v_checkexpr     VARCHAR(256);
    BEGIN
        if p_yearmonth is null or substr(p_yearmonth,5,6) != '13' then
            SELECT '*' into v_char FROM dual where 1=2;
        end if;
        v_annual_proc_id:= bpluser.bpl_pack_common.func_get_procid('ACC_ANNUALRECKONING');
        v_annual_check1 := bpluser.bpl_pack_common.func_get_procid('ACC_ANNUALRECKONING1_YCC');
        v_annual_check2 := bpluser.bpl_pack_common.func_get_procid('ACC_ANNUALRECKONING2_YCC');
        v_annual_check3 := bpluser.bpl_pack_common.func_get_procid('ACC_ANNUALRECKONING3_YCC');
        v_annual_check4 := bpluser.bpl_pack_common.func_get_procid('ACC_ANNUALRECKONING4_YCC');
        v_annual_check5 := bpluser.bpl_pack_common.func_get_procid('ACC_ANNUALRECKONING5_YCC');

        -- 借、贷方金额相等；余额为0
        select sum(debit_amount_year_cu)  ,sum(credit_amount_year_cu) ,sum(closing_balance_cu) into v_debit_amount_year,v_credit_amount_year,v_closing_balance from acc_buss_ledger_balance t where book_code = p_bookcode and ENTITY_ID = P_ENTITY_ID and year_month = p_yearmonth and ACCOUNT_ID in(
            select a.ACCOUNT_ID from bpluser.bbs_account a where a.ACCOUNT_ID = t.ACCOUNT_ID and a.book_code = t.book_code and a.account_category_code = '4' and a.account_entry_code = 'C'
        );

        v_taskcode := BPLUSER.bpl_pack_common.func_get_taskcode('ACC', 'M', 'ACT');
        v_busmessage:='accAnnualRemark001';
        v_actlogid := bpluser.bpl_seq_log_action.nextval; --按节点获取新的业务日志ID
        if v_closing_balance is null or v_closing_balance <> 0 then
            v_check_result :='0';
        end if;
        -- 将当前校验规则写入业务日志表(精确到节点)
        bpluser.bpl_pack_action_log.proc_add_actionlog(v_actlogid,
                                                       v_taskcode,
                                                       'ACC',
                                                       P_ENTITY_ID,
                                                       p_bookcode,
                                                       p_yearmonth,
                                                       v_annual_check1,
                                                       v_annual_proc_id,
                                                       v_check_result,
                                                       p_userid --创建人员
            );


        -- 将当前校验规则写入业务日志详情表(精确到规则)
        bpluser.bpl_pack_action_log.proc_add_annual_logdetail(v_actlogid,
                                                              null,
                                                              v_checkexpr,
                                                              v_check_result, --规则校验状态
                                                              v_errormessage,
                                                              v_busmessage,
                                                              CAST(v_debit_amount_year as VARCHAR),
                                                              CAST(v_credit_amount_year as VARCHAR),
                                                              CAST(v_closing_balance as VARCHAR),
                                                              '1',
                                                              p_userid --创建人员
            );


        -- 借、贷方金额相等；余额为0
        select sum(debit_amount_year_cu)  ,sum(credit_amount_year_cu) ,sum(closing_balance_cu) into v_debit_amount_year,v_credit_amount_year,v_closing_balance from acc_buss_ledger_balance t where book_code = p_bookcode and ENTITY_ID = P_ENTITY_ID and year_month = p_yearmonth and ACCOUNT_ID in(
            select a.ACCOUNT_ID from bpluser.bbs_account a where a.ACCOUNT_ID = t.ACCOUNT_ID and a.book_code = t.book_code and a.account_category_code = '4' and a.account_entry_code = 'D'
        );
        v_busmessage :='accAnnualRemark002';
        v_actlogid := bpluser.bpl_seq_log_action.nextval; --按节点获取新的业务日志ID
        v_check_result :='1';
        if v_closing_balance is null or v_closing_balance <> 0 then
            v_check_result :='0';
        end if;
        -- 将当前校验规则写入业务日志表(精确到节点)
        bpluser.bpl_pack_action_log.proc_add_actionlog(v_actlogid,
                                                       v_taskcode,
                                                       'ACC',
                                                       P_ENTITY_ID,
                                                       p_bookcode,
                                                       p_yearmonth,
                                                       v_annual_check2,
                                                       v_annual_proc_id,
                                                       v_check_result,
                                                       p_userid --创建人员
            );


        -- 将当前校验规则写入业务日志详情表(精确到规则)
        bpluser.bpl_pack_action_log.proc_add_annual_logdetail(v_actlogid,
                                                              null,
                                                              v_checkexpr,
                                                              v_check_result,
                                                              v_errormessage,
                                                              v_busmessage,
                                                              CAST(v_debit_amount_year as VARCHAR),
                                                              CAST(v_credit_amount_year as VARCHAR),
                                                              CAST(v_closing_balance as VARCHAR),
                                                              '1',
                                                              p_userid --创建人员
            );

        -- 借、贷方金额相等；余额为0
        select sum(debit_amount_year_cu)  ,sum(credit_amount_year_cu) ,sum(closing_balance_cu) into v_debit_amount_year,v_credit_amount_year,v_closing_balance from acc_buss_ledger_balance t where book_code = p_bookcode and ENTITY_ID = P_ENTITY_ID and year_month = p_yearmonth and ACCOUNT_ID in (
            select account_id from bpluser.bbs_conf_account_js a WHERE a.entity_id=t.entity_id AND a.book_code = t.book_code and a.final_type = '3'
        );
        v_busmessage :='accAnnualRemark003';
        v_actlogid := bpluser.bpl_seq_log_action.nextval; --按节点获取新的业务日志ID
        v_check_result :='1';
        if v_closing_balance is null or v_closing_balance <> 0 then
            v_check_result :='0';
        end if;
        -- 将当前校验规则写入业务日志表(精确到节点)
        bpluser.bpl_pack_action_log.proc_add_actionlog(v_actlogid,
                                                       v_taskcode,
                                                       'ACC',
                                                       P_ENTITY_ID,
                                                       p_bookcode,
                                                       p_yearmonth,
                                                       v_annual_check3,
                                                       v_annual_proc_id,
                                                       v_check_result,
                                                       p_userid --创建人员
            );


        -- 将当前校验规则写入业务日志详情表(精确到规则)
        bpluser.bpl_pack_action_log.proc_add_annual_logdetail(v_actlogid,
                                                              null,
                                                              v_checkexpr,
                                                              v_check_result, --规则校验状态
                                                              v_errormessage,
                                                              v_busmessage,
                                                              CAST(v_debit_amount_year as VARCHAR),
                                                              CAST(v_credit_amount_year as VARCHAR),
                                                              CAST(v_closing_balance as VARCHAR),
                                                              '1',
                                                              p_userid --创建人员
            );
        -- 余额不为0
        select sum(debit_amount_year_cu)  ,sum(credit_amount_year_cu) ,sum(closing_balance_cu) into v_debit_amount_year,v_credit_amount_year,v_closing_balance
        from acc_buss_ledger_balance t left join bpluser.bbs_v_account item
                                                 on t.account_id= item.account_id
        where t.book_code = p_bookcode
          and t.entity_id = p_entity_id
          and t.year_month = p_yearmonth
          and item.final_level_is='1'
          and t.account_id in (
            select account_id from bpluser.bbs_conf_account_js a where a.entity_id=t.entity_id AND a.book_code = t.book_code and a.final_type = '4'
        );
        v_busmessage :='accAnnualRemark004';
        v_actlogid := bpluser.bpl_seq_log_action.nextval; --按节点获取新的业务日志ID
        v_check_result :='0';     --利润分配项目，每年借贷不平衡是正常状态
        if v_closing_balance IS NOT null AND v_closing_balance <> 0 then
            v_check_result :='1';
        end if;
        -- 将当前校验规则写入业务日志表(精确到节点)
        bpluser.bpl_pack_action_log.proc_add_actionlog(v_actlogid,
                                                       v_taskcode,
                                                       'ACC',
                                                       P_ENTITY_ID,
                                                       p_bookcode,
                                                       p_yearmonth,
                                                       v_annual_check4,
                                                       v_annual_proc_id,
                                                       v_check_result,
                                                       p_userid --创建人员
            );


        -- 将当前校验规则写入业务日志详情表(精确到规则)
        bpluser.bpl_pack_action_log.proc_add_annual_logdetail(v_actlogid,
                                                              null,
                                                              v_checkexpr,
                                                              v_check_result, --规则校验状态
                                                              v_errormessage,
                                                              v_busmessage,
                                                              CAST(v_debit_amount_year as VARCHAR),
                                                              CAST(v_credit_amount_year as VARCHAR),
                                                              CAST(v_closing_balance as VARCHAR),
                                                              '0',
                                                              p_userid --创建人员
            );
        -- 借、贷方金额相等；余额为0
        select sum(debit_amount_year_cu)  ,sum(credit_amount_year_cu) ,sum(closing_balance_cu) into v_debit_amount_year,v_credit_amount_year,v_closing_balance
        from acc_buss_ledger_balance t left join bpluser.bbs_v_account item
                                                 on t.account_id= item.account_id
        where t.book_code = p_bookcode
          and t.entity_id = p_entity_id
          and t.year_month = p_yearmonth
          and item.account_level='1';

        v_actlogid := bpluser.bpl_seq_log_action.nextval; --按节点获取新的业务日志ID
        v_check_result :='1';
        v_busmessage :='accAnnualRemark005';
        if v_closing_balance is null or v_closing_balance <> 0 then
            v_check_result :='0';
        end if;
        -- 将当前校验规则写入业务日志表(精确到节点)
        bpluser.bpl_pack_action_log.proc_add_actionlog(v_actlogid,
                                                       v_taskcode,
                                                       'ACC',
                                                       p_entity_id,
                                                       p_bookcode,
                                                       p_yearmonth,
                                                       v_annual_check5,
                                                       v_annual_proc_id,
                                                       v_check_result,
                                                       p_userid --创建人员
            );


        -- 将当前校验规则写入业务日志详情表(精确到规则)
        bpluser.bpl_pack_action_log.proc_add_annual_logdetail(v_actlogid,
                                                              null,
                                                              v_checkexpr,
                                                              v_check_result, --规则校验状态
                                                              v_errormessage,
                                                              v_busmessage,
                                                              CAST(v_debit_amount_year as VARCHAR),
                                                              CAST(v_credit_amount_year as VARCHAR),
                                                              CAST(v_closing_balance as VARCHAR),
                                                              '1',
                                                              p_userid --创建人员
            );

        bpluser.bpl_pack_check.proc_checkrules(v_taskcode, P_ENTITY_ID, p_bookcode, p_yearmonth, v_annual_proc_id,  p_userid);

    EXCEPTION
        WHEN OTHERS THEN
            dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
            dbms_output.put_line('**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 年结期间检查异常：会计期间: %'||p_yearmonth);
            --往外层抛出异常信息
            raise_application_error(-20003, '年结期间检查异常:'||'**出错行数: '|| dbms_utility.format_error_backtrace()
                || '**出错问题: '||SUBSTR(SQLERRM, 1, 200) );
    END proc_annual_balance_check;

    PROCEDURE proc_annual_entrance(P_ENTITY_ID IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER) IS
        v_yearmonth VARCHAR(6);
        v_base_currency_code           VARCHAR(3);
        v_char  VARCHAR(1);
        v_errcode                 VARCHAR(32); --错误编码
        v_errmsg                  VARCHAR(200); --错误信息
        v_reckoning_state         VARCHAR(1);
    BEGIN
        acc_pack_voucher.proc_account_entry_log(p_entity_id ,
                                                p_yearmonth,
                                                p_yearmonth,
                                                p_userid,
                                                143,
                                                'proc_annual_entrance' ,
                                                '1' ,
                                                'start' );
        IF substr(p_yearmonth,5,2)='12' THEN
            v_yearmonth := substr(p_yearmonth,0,4) ||'13';

            select MAX(currency_code)
            into v_base_currency_code
            from ACC_CONF_ANNUAL_PERIOD
            where ENTITY_ID = P_ENTITY_ID
              and book_code = p_bookcode
              and year_month=v_yearmonth
              AND RECKONING_STATE = '0'
              and valid_is = '1'
              and audit_state='1';
            if v_base_currency_code IS NOT null THEN
                proc_annual_generate_voucher(P_ENTITY_ID,p_bookcode,v_yearmonth, p_userid );
                proc_annual_balance_check(P_ENTITY_ID,p_bookcode,v_yearmonth, p_userid );
                proc_annual_colse_period(P_ENTITY_ID,p_bookcode,v_yearmonth, p_userid );
            END IF;
        END IF;
        acc_pack_voucher.proc_account_entry_log(p_entity_id ,
                                                v_yearmonth,
                                                p_yearmonth,
                                                p_userid,
                                                143,
                                                'proc_annual_entrance' ,
                                                '1' ,
                                                'end' );
    EXCEPTION
        WHEN OTHERS THEN
            acc_pack_voucher.proc_account_entry_log(p_entity_id ,
                                                    v_yearmonth,
                                                    p_yearmonth,
                                                    p_userid,
                                                    143,
                                                    'proc_annual_entrance'||SUBSTR(SQLERRM, 1, 200) ,
                                                    '2' ,
                                                    'end' );
    END proc_annual_entrance;




    /***********************************************************************
  NAME :acc_pack_annual_proc_annual_colse_period
  DESCRIPTION :年结关闭
  DATE :2022-5-17
  ***********************************************************************/
    PROCEDURE proc_annual_colse_period(P_ENTITY_ID IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER) IS
        v_yearmonth VARCHAR(6);
        v_reckoning_state  VARCHAR(1);
        v_char  VARCHAR(1);
        v_annual_proc_id numeric;
        v_annual_eckoning_id numeric;
        v_actlogid            numeric; --业务日志表ID
        v_errcode              VARCHAR(32); --错误编码
        v_errmsg               VARCHAR(32); --错误信息
        v_taskcode             VARCHAR(32); --规则校验不通过信息
        v_errormessage         VARCHAR(32); --规则校验不通过信息
        v_busmessage           VARCHAR(32):=''; --业务异常主键
        v_check_result				 VARCHAR(32):=''; --业务异常主键
    BEGIN
        select year_month, reckoning_state
        into v_yearmonth,v_reckoning_state
        from ACC_CONF_ANNUAL_PERIOD
        where ENTITY_ID = P_ENTITY_ID
          and book_code = p_bookcode
          and year_month=p_yearmonth
          and valid_is = '1'
          and audit_state='1';

        v_taskcode := BPLUSER.bpl_pack_common.func_get_taskcode('ACC', 'M', 'ACT');
        v_actlogid := bpluser.bpl_seq_log_action.nextval;

        if p_yearmonth is null or substr(p_yearmonth,5,6) != '13' then
            v_errcode := 'AL001';
            v_errmsg := '账期不是结算月';
            select '1' into v_char from dual where 1=2;
        end if;

        if v_yearmonth is null or v_yearmonth = '' then
            v_errcode := 'AL002';
            v_errmsg := '不存在结算账期';
            select '1' into v_char from dual where 1=2;
        end if;

        if v_reckoning_state ='1' then
            v_errcode := 'AL003';
            v_errmsg := '年结期间已结转';
            select '1' into v_char from dual where 1=2;
        end if;

        v_annual_proc_id:= bpluser.bpl_pack_common.func_get_procid('ACC_ANNUALRECKONING');
        v_annual_eckoning_id := bpluser.bpl_pack_common.func_get_procid('ACC_ANNUALRECKONING_FUN');

        v_busmessage:='AccAnnualEnd'; --年结结束
        v_check_result :='1';

        acc_pack_annual.proc_annual_refresh_balance(P_ENTITY_ID, p_bookcode, p_yearmonth, p_userid);

        update ACC_CONF_ANNUAL_PERIOD set
                                          reckoning_state='1', updator_id=p_userid,	update_time=LOCALTIMESTAMP
        where ENTITY_ID = P_ENTITY_ID
          and book_code = p_bookcode
          and year_month=p_yearmonth
          and valid_is = '1'
          and audit_state='1';

        -- 将当前校验规则写入业务日志表(精确到节点)
        bpluser.bpl_pack_action_log.proc_add_actionlog(v_actlogid,
                                                       v_taskcode,
                                                       'ACC',
                                                       P_ENTITY_ID,
                                                       p_bookcode,
                                                       p_yearmonth,
                                                       v_annual_eckoning_id,
                                                       v_annual_proc_id,
                                                       v_check_result,
                                                       p_userid --创建人员
            );


        -- 将当前校验规则写入业务日志详情表(精确到规则)
        bpluser.bpl_pack_action_log.proc_add_annual_logdetail(v_actlogid,
                                                              null,
                                                              '',
                                                              v_check_result, --规则校验状态
                                                              v_errormessage,
                                                              v_busmessage,
                                                              '',
                                                              '',
                                                              '',
                                                              '0',
                                                              p_userid --创建人员
            );


    EXCEPTION
        WHEN OTHERS THEN
            v_taskcode := BPLUSER.bpl_pack_common.func_get_taskcode('ACC', 'M', 'ACT');
            v_actlogid := bpluser.bpl_seq_log_action.nextval;
            --提示异常信息
            v_check_result :='0';
            -- 将当前校验规则写入业务日志表(精确到节点)
            bpluser.bpl_pack_action_log.proc_add_actionlog(v_actlogid,
                                                           v_taskcode,
                                                           'ACC',
                                                           P_ENTITY_ID,
                                                           p_bookcode,
                                                           p_yearmonth,
                                                           v_annual_eckoning_id,
                                                           v_annual_proc_id,
                                                           v_check_result,
                                                           p_userid --创建人员
                );

            -- 将当前校验规则写入业务日志详情表(精确到规则)
            bpluser.bpl_pack_action_log.proc_add_annual_logdetail(v_actlogid,
                                                                  null,
                                                                  '',
                                                                  v_check_result, --规则校验状态
                                                                  v_errcode,
                                                                  '',
                                                                  '',
                                                                  '',
                                                                  '',
                                                                  '0',
                                                                  p_userid --创建人员
                );
            dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
            dbms_output.put_line('**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 年结期间结转异常：会计期间: %'||p_yearmonth);
            --往外层抛出异常信息
            raise_application_error(-20003, '**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 结转关闭余额异常，请检查');	END proc_annual_colse_period;

    PROCEDURE proc_annual_generate_voucher(P_ENTITY_ID IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER) IS
        v_base_currency_code           VARCHAR(3);
        v_char  VARCHAR(1);
        v_errcode                 VARCHAR(32); --错误编码
        v_errmsg                  VARCHAR(200); --错误信息
        v_reckoning_state         VARCHAR(1);
        V_INCOME_VOUCHERNO        VARCHAR(32);
        V_EXPENSE_VOUCHERNO       VARCHAR(32);
        V_YEAR_VOUCHERNO          VARCHAR(32);
        V_VOUCHERDATE             DATE;
        V_INCOME_VOUCHER_ID       numeric;
        V_EXPENSE_VOUCHER_ID       numeric;
        V_YEAR_VOUCHER_ID         numeric;
        V_PROC_ID                 ACC_BUSS_VOUCHER.PROC_ID%type;--数据类型
        v_exch_date               DATE := sysdate;
        V_LAST_YEARMONTH          VARCHAR(6);
        V_VOUCHER_TYPE            VARCHAR(6);
        V_VOUCHER_COUNT            numeric;
        v_insert_count            numeric;
    BEGIN
        select currency_code,reckoning_state
        into v_base_currency_code,v_reckoning_state
        from ACC_CONF_ANNUAL_PERIOD
        where ENTITY_ID = P_ENTITY_ID
          and book_code = p_bookcode
          and year_month=p_yearmonth
          and valid_is = '1'
          and audit_state='1';

        if v_base_currency_code is null then
            v_errcode := 'AG001';
            v_errmsg := '年结期间不存在或者无效';
            select '1' into v_char from dual where 1=2;
        end if;

        if v_reckoning_state ='1' then
            v_errcode := 'AG002';
            v_errmsg := '年结期间已结转';
            select '1' into v_char from dual where 1=2;
        end if;

        select count(*) into V_VOUCHER_COUNT
        FROM ACC_BUSS_VOUCHER post
                 LEFT JOIN acc_buss_voucher_detail postdtl
                           on post.VOUCHER_ID = postdtl.VOUCHER_ID
                 LEFT JOIN bpluser.bbs_conf_account_js js
                           on js.account_id = postdtl.account_id AND post.entity_id=js.entity_id AND post.book_code = js.book_code
        where post.entity_id =  p_entity_id
          AND post.BOOK_CODE = p_bookcode
          AND post.YEAR_MONTH = p_yearmonth
          AND post.posting_type_code = '06';

        if V_VOUCHER_COUNT>0 then
            v_errcode := 'AG003';
            v_errmsg := '此决算月已经生成了结转凭证';
            select '1' into v_char from dual where 1=2;
        end if;

        --凭证类型决算自转
        V_VOUCHER_TYPE := '06';
        V_LAST_YEARMONTH := substr(p_yearmonth, 0, 4) || '12';

        v_exch_date := TRUNC(last_day(TO_DATE(V_LAST_YEARMONTH || '01','YYYYMMDD')));

        V_PROC_ID := bpluser.bpl_pack_common.func_get_procid('ACC_ACCOUNTENTRY');
        --获取凭证日期
        V_VOUCHERDATE := acc_pack_common.func_correct_voucher_date(V_LAST_YEARMONTH);
        --凭证新主键ID
        V_INCOME_VOUCHER_ID := acc_seq_buss_voucher.NEXTVAL;
        --凭证号校正
        acc_pack_voucher.proc_correct_voucher_key(P_ENTITY_ID ,
                                                  p_bookcode ,
                                                  p_yearmonth ,
                                                  V_VOUCHER_TYPE ,
                                                  V_PROC_ID||'' );
        --获取凭证号码=（核算单位+账套+年结期间+节点+凭证类型【决算自转】）
        V_INCOME_VOUCHERNO := acc_pack_voucher.func_generate_voucher_no(P_ENTITY_ID, p_bookcode, p_yearmonth, V_VOUCHER_TYPE, V_PROC_ID||'',1);

        --3 个凭证号，1-结转损益(收入类)，2-结转损益损(支出类)，3-结转本年利润
        --1-结转损益(收入类)
        INSERT INTO ACC_BUSS_VOUCHER
        (VOUCHER_ID,
         ENTITY_ID,
         BOOK_CODE,
         POSTING_TYPE_CODE,
         PROC_ID,
         VOUCHER_NO,
         YEAR_MONTH,
         EFFECTIVE_DATE,
         STATE,
         REMARK,
         valid_is,
         audit_state,
         CREATE_TIME,
         CREATOR_ID)
        values
            (V_INCOME_VOUCHER_ID,
             P_ENTITY_ID,
             p_bookcode,
             V_VOUCHER_TYPE,--凭证类型
             V_PROC_ID,--数据类型
             V_INCOME_VOUCHERNO,
             P_YEARMONTH,
             V_VOUCHERDATE,
             '1' , --1正常 2 被冲  3 冲销
             'C.F. LossProfit(Income)',
             '1', --1 有效
             '1', --1 有效
             LOCALTIMESTAMP,
             p_userid);

        dbms_output.put_line('插入结转凭证(支入类)凭证id=' || V_INCOME_VOUCHER_ID|| ' 凭证号=' ||V_INCOME_VOUCHERNO);
        BEGIN
            --凭证分录处理
            dbms_output.put_line( '插入凭证明细表分录结转凭证(支入类)贷方数据成功');

            --1.1 损益收入类贷方 - 专项
            INSERT INTO ACC_BUSS_VOUCHER_DETAIL (
                VOUCHER_DTL_ID,
                VOUCHER_ID,
                account_id,
                currency_code,
                currency_cu_code,
                ACCOUNT_ENTRY_CODE,
                AMOUNT,
                AMOUNT_CU,
                exchange_rate,
                REMARK,
                ARTICLE1,
                ARTICLE2,
                ARTICLE3,
                ARTICLE4,
                ARTICLE5,
                ARTICLE6,
                ARTICLE7,
                ARTICLE8,
                ARTICLE9,
                ARTICLE10,
                ARTICLE11,
                ARTICLE12,
                ARTICLE13,
                ARTICLE14,
                ARTICLE15,
                ARTICLE16,
                ARTICLE17,
                ARTICLE,
                CREATE_TIME,
                CREATOR_ID,
                UPDATE_TIME,
                UPDATOR_ID
            ) SELECT
                  acc_seq_buss_voucher_detail.NEXTVAL as VOUCHER_DTL_ID,t.*
            from (
                     select
                         V_INCOME_VOUCHER_ID as VOUCHER_ID, --新凭证主表ID
                         t.ACCOUNT_ID,--借方科目编码
                         t.currency_code,--原币
                         t.currency_cu_code,--本位币
                         (CASE WHEN (round(SUM(debit_amount_year - credit_amount_year), 2 ) ) >= 0 THEN 'C' ELSE 'D' END ) AS ACCOUNT_ENTRY_CODE,
                         abs(SUM(debit_amount_year -  credit_amount_year)) AS AMOUNT,--原币金额
                         abs(SUM (debit_amount_year_cu - credit_amount_year_cu))/*abs(round(SUM (debit_amount_year - credit_amount_year) * acc_pack_common.func_get_exchange_rate(t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2'), 2 ))*/ AS AMOUNT_CU,--本位币金额
                         1/*acc_pack_common.func_get_exchange_rate(t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2')*/ AS exchange_rate,
                         'C.F. LossProfit(Income) Article' as REMARK,
                         article1 as ARTICLE1,
                         article2,
                         article3,
                         article4,
                         article5,
                         article6,
                         article7,
                         article8,
                         article9 as ARTICLE9,
                         ARTICLE10,
                         ARTICLE11,
                         ARTICLE12,
                         ARTICLE13,
                         ARTICLE14,
                         ARTICLE15,
                         ARTICLE16,
                         ARTICLE17,
                         ARTICLE,
                         LOCALTIMESTAMP as CREATE_TIME,
                         p_userid as CREATOR_ID,
                         LOCALTIMESTAMP as UPDATE_TIME,
                         p_userid as UPDATOR_ID
                     FROM acc_buss_article_balance t
                              left join bpluser.bbs_v_account item
                                        on t.account_id = item.account_id and t.entity_id = item.entity_id and t.book_code = item.book_code
                     where t.entity_id = p_entity_id
                       and t.BOOK_CODE = p_bookcode
                       and t.year_month= V_LAST_YEARMONTH
                       and item.account_category_code='4'
                       and item.account_entry_code='C'
                       and item.final_level_is='1'
                     group by t.ACCOUNT_ID, t.currency_code, t.currency_cu_code, t.ENTITY_ID,article1,
                              article2, article3, article4, article5, article6,
                              article7, article8, article9, ARTICLE10,  ARTICLE11,
                              ARTICLE12, ARTICLE13, ARTICLE14, ARTICLE15, ARTICLE16, ARTICLE17,ARTICLE
                     HAVING abs(SUM(debit_amount_year-credit_amount_year))<>0

                     /*union all
          --3325/3326已经改成4325/4326所以这一串程序不需要了
          select
          V_INCOME_VOUCHER_ID as VOUCHER_ID, --新凭证主表ID
          t.ACCOUNT_ID,--借方科目编码
          t.currency_code,--原币
          t.currency_cu_code,--本位币
          (CASE WHEN (round(SUM(debit_amount_year - credit_amount_year), 2 ) ) >= 0 THEN 'C' ELSE 'D' END ) AS ACCOUNT_ENTRY_CODE,
          abs(SUM(debit_amount_year -  credit_amount_year)) AS AMOUNT,--原币金额
          abs(SUM (debit_amount_year_cu - credit_amount_year_cu))\*abs(round(SUM (debit_amount_year - credit_amount_year) * acc_pack_common.func_get_exchange_rate(t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2'), 2 ))*\ AS AMOUNT_CU,--本位币金额
          1 \*acc_pack_common.func_get_exchange_rate(t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2')*\ AS exchange_rate,
          'C.F. LossProfit(Income) Article' as REMARK,
          article1 as ARTICLE1,
          article2,
          article3,
          article4,
          article5,
          article6,
          article7,
          article8,
          article9 as ARTICLE9,
          ARTICLE10,
          ARTICLE11,
          ARTICLE12,
          ARTICLE13,
          ARTICLE14,
          ARTICLE15,
          ARTICLE16,
          ARTICLE17,
          ARTICLE,
          LOCALTIMESTAMP as CREATE_TIME,
          p_userid as CREATOR_ID,
          LOCALTIMESTAMP as UPDATE_TIME,
          p_userid as UPDATOR_ID
        FROM acc_buss_article_balance t
        left join bpluser.bbs_v_account item
        on t.ACCOUNT_ID = item.ACCOUNT_ID and t.ENTITY_ID = item.ENTITY_ID and t.book_code = item.book_code
        where t.ENTITY_ID = P_ENTITY_ID
          and t.BOOK_CODE = p_bookcode
          and t.year_month= V_LAST_YEARMONTH
          --and item.account_category_code='4'
          and item.account_entry_code='C'
          and item.final_level_is='1'
          and substr(item.account_code,1,4) in ('3325','3326') --其它综合收益需要结转，因为I17定义成了33所以需要特殊处理，有些通过定为6开头就不需要特殊处理
          group by t.ACCOUNT_ID, t.currency_code, t.currency_cu_code, t.ENTITY_ID,article1,
            article2, article3, article4, article5, article6,
            article7, article8, article9, ARTICLE10,  ARTICLE11,
            ARTICLE12, ARTICLE13, ARTICLE14, ARTICLE15, ARTICLE16, ARTICLE17,ARTICLE
          HAVING abs(SUM(debit_amount_year-credit_amount_year))<>0*/

                 ) t;


            --1.2 损益收入类贷方-总账余额
            INSERT INTO ACC_BUSS_VOUCHER_DETAIL (
                VOUCHER_DTL_ID,
                VOUCHER_ID,
                account_id,
                currency_code,
                currency_cu_code,
                ACCOUNT_ENTRY_CODE,
                AMOUNT,
                AMOUNT_CU,
                exchange_rate,
                REMARK,
                ARTICLE1,
                ARTICLE2,
                ARTICLE3,
                ARTICLE4,
                ARTICLE5,
                ARTICLE6,
                ARTICLE7,
                ARTICLE8,
                ARTICLE9,
                CREATE_TIME,
                CREATOR_ID,
                UPDATE_TIME,
                UPDATOR_ID
            ) SELECT
                  acc_seq_buss_voucher_detail.NEXTVAL as VOUCHER_DTL_ID,t.*
            from (
                     select
                         V_INCOME_VOUCHER_ID as VOUCHER_ID, --新凭证主表ID
                         t.ACCOUNT_ID,--借方科目编码
                         t.currency_code,--原币
                         t.currency_cu_code,--本位币
                         ( CASE WHEN (round(SUM(debit_amount_year -  credit_amount_year), 2 ) ) +
                                     COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.amount else dtl.amount *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_INCOME_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0) >= 0 THEN 'C' ELSE 'D' END ) AS ACCOUNT_ENTRY_CODE,--减专项
                         abs(SUM(debit_amount_year -  credit_amount_year) +
                             COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.amount else dtl.amount *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_INCOME_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0)) AS AMOUNT,--原币金额    --减专项
                         abs(round((SUM (debit_amount_year_cu -  credit_amount_year_cu) +
                                    COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.AMOUNT_CU else dtl.AMOUNT_CU *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_INCOME_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0) ) /**acc_pack_common.func_get_exchange_rate(t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2')*/, 2 )) AS AMOUNT_CU,--本位币金额 --减专项
                         1 /*acc_pack_common.func_get_exchange_rate(t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2')*/ AS exchange_rate,
                         'C.F. LossProfit(Income) Ledger' as REMARK,
                         NULL as ARTICLE1,
                         NULL as ARTICLE2,
                         NULL as ARTICLE3,
                         NULL as ARTICLE4,
                         NULL as ARTICLE5,
                         NULL as ARTICLE6,
                         NULL as ARTICLE7,
                         NULL as ARTICLE8,
                         NULL as ARTICLE9,
                         LOCALTIMESTAMP as CREATE_TIME,
                         p_userid as CREATOR_ID,
                         LOCALTIMESTAMP as UPDATE_TIME,
                         p_userid as UPDATOR_ID
                     FROM acc_buss_ledger_balance t
                              left join  bpluser.bbs_v_account item on t.ACCOUNT_ID = item.ACCOUNT_ID and t.ENTITY_ID = item.ENTITY_ID and t.book_code = item.book_code
                     where t.ENTITY_ID = P_ENTITY_ID
                       and t.BOOK_CODE = p_bookcode
                       and t.year_month= V_LAST_YEARMONTH
                       and item.account_category_code='4'
                       and item.account_entry_code='C'
                       and item.final_level_is='1'
                     group by t.ACCOUNT_ID, t.currency_code, t.currency_cu_code, t.ENTITY_ID
                     HAVING abs(SUM(debit_amount_year -  credit_amount_year) +
                                COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.amount else dtl.amount *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_INCOME_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0)) <>0

                     /*union all

        select
          V_INCOME_VOUCHER_ID as VOUCHER_ID, --新凭证主表ID
          t.ACCOUNT_ID,--借方科目编码
          t.currency_code,--原币
          t.currency_cu_code,--本位币
          ( CASE WHEN (round(SUM(debit_amount_year -  credit_amount_year), 2 ) ) +
          COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.account else dtl.account *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_INCOME_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0) >= 0 THEN 'C' ELSE 'D' END ) AS ACCOUNT_ENTRY_CODE,--减专项
          abs(SUM(debit_amount_year -  credit_amount_year) +
          COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.account else dtl.account *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_INCOME_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0)) AS AMOUNT,--原币金额    --减专项
          abs(round((SUM (debit_amount_year_cu -  credit_amount_year_cu) +
          COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.AMOUNT_CU else dtl.AMOUNT_CU *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_INCOME_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0) ) \**acc_pack_common.func_get_exchange_rate(t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2')*\, 2 )) AS AMOUNT_CU,--本位币金额 --减专项
          1 \*acc_pack_common.func_get_exchange_rate(t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2')*\ AS exchange_rate,
          'C.F. LossProfit(Income) Ledger' as REMARK,
          NULL as ARTICLE1,
          NULL as ARTICLE2,
          NULL as ARTICLE3,
          NULL as ARTICLE4,
          NULL as ARTICLE5,
          NULL as ARTICLE6,
          NULL as ARTICLE7,
          NULL as ARTICLE8,
          NULL as ARTICLE9,
          LOCALTIMESTAMP as CREATE_TIME,
          p_userid as CREATOR_ID,
          LOCALTIMESTAMP as UPDATE_TIME,
          p_userid as UPDATOR_ID
        FROM acc_buss_ledger_balance t
        left join  bpluser.bbs_v_account item on t.ACCOUNT_ID = item.ACCOUNT_ID and t.ENTITY_ID = item.ENTITY_ID and t.book_code = item.book_code
        where t.ENTITY_ID = P_ENTITY_ID
          and t.BOOK_CODE = p_bookcode
          and t.year_month= V_LAST_YEARMONTH
          --and item.account_category_code='4'
          and item.account_entry_code='C'
          and item.final_level_is='1'
          and substr(item.account_code,1,4) in ('3325','3326') --其它综合收益需要结转，因为I17定义成了33所以需要特殊处理，有些通过定为6开头就不需要特殊处理
          group by t.ACCOUNT_ID, t.currency_code, t.currency_cu_code, t.ENTITY_ID
          HAVING abs(SUM(debit_amount_year -  credit_amount_year) +
          COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.account else dtl.account *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_INCOME_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0)) <>0
*/

                 ) t;



            dbms_output.put_line('插入凭证明细表资产负债贷方数据成功');
            --1.3. 资产负债贷方-本年利润科目
            INSERT INTO ACC_BUSS_VOUCHER_DETAIL (
                VOUCHER_DTL_ID,
                VOUCHER_ID,
                account_id,
                currency_code,
                currency_cu_code,
                ACCOUNT_ENTRY_CODE,
                amount,
                AMOUNT_CU,
                exchange_rate,
                REMARK,
                CREATE_TIME,
                CREATOR_ID,
                UPDATE_TIME,
                UPDATOR_ID
            ) SELECT
                  acc_seq_buss_voucher_detail.NEXTVAL as VOUCHER_DTL_ID,t.*
            from (
                     select
                         V_INCOME_VOUCHER_ID as VOUCHER_ID, --新凭证主表ID
                         ( SELECT js.ACCOUNT_ID FROM bpluser.bbs_conf_account_js js WHERE js.entity_id=P_ENTITY_ID AND js.book_code = p_bookcode and js.final_type = '3' and rownum =1 ) as account_id,  --A.ACCOUNT_ID,--借方科目编码
                         A.currency_code,--原币
                         A.currency_cu_code,--本位币
                         ( CASE WHEN sum(case when a.ACCOUNT_ENTRY_CODE = 'D' then a.amount else a.amount *-1 end ) >= 0 THEN 'C' ELSE 'D' END ) AS ACCOUNT_ENTRY_CODE,
                         abs(sum(case when a.ACCOUNT_ENTRY_CODE = 'D' then a.amount else a.amount *-1 end )) AS AMOUNT,--原币金额
                         abs(round(SUM (case when a.ACCOUNT_ENTRY_CODE = 'D' then a.AMOUNT_CU else a.AMOUNT_CU *-1 end) /**acc_pack_common.func_get_exchange_rate(P_ENTITY_ID, v_exch_date, a.currency_code, a.currency_cu_code, '2')*/, 2 )) AS AMOUNT_CU,--本位币金额
                         1 /*acc_pack_common.func_get_exchange_rate(P_ENTITY_ID, v_exch_date, a.currency_code, a.currency_cu_code, '2')*/ AS exchange_rate,
                         'C.F. LossProfit(Income) JS' as REMARK,
                         LOCALTIMESTAMP as CREATE_TIME,
                         p_userid as CREATOR_ID,
                         LOCALTIMESTAMP as UPDATE_TIME,
                         p_userid as UPDATOR_ID
                     FROM ACC_BUSS_VOUCHER_DETAIL a
                     where a.VOUCHER_ID = V_INCOME_VOUCHER_ID
                     group by  A.currency_code, A.currency_cu_code
                 )t;
        END;


        --2-结转损益损(支出类) start
        --凭证新主键ID
        V_EXPENSE_VOUCHER_ID := acc_seq_buss_voucher.NEXTVAL;
        --凭证号校正
        acc_pack_voucher.proc_correct_voucher_key(P_ENTITY_ID ,
                                                  p_bookcode ,
                                                  p_yearmonth ,
                                                  V_VOUCHER_TYPE ,
                                                  V_PROC_ID||'' );
        --获取凭证号码
        V_EXPENSE_VOUCHERNO := acc_pack_voucher.func_generate_voucher_no(P_ENTITY_ID, p_bookcode, p_yearmonth, V_VOUCHER_TYPE, V_PROC_ID||'', 1);

        INSERT INTO ACC_BUSS_VOUCHER
        (VOUCHER_ID,
         ENTITY_ID,
         BOOK_CODE,
         POSTING_TYPE_CODE,
         PROC_ID,
         VOUCHER_NO,
         YEAR_MONTH,
         EFFECTIVE_DATE,
         STATE,
         REMARK,
         VALID_IS,
         AUDIT_STATE,
         CREATE_TIME,
         CREATOR_ID)
            (select
                 V_EXPENSE_VOUCHER_ID,
                 P_ENTITY_ID as ENTITY_ID,
                 p_bookcode as BOOK_CODE,
                 V_VOUCHER_TYPE,--凭证类型
                 V_PROC_ID,--数据类型
                 V_EXPENSE_VOUCHERNO as VOUCHER_NO,
                 P_YEARMONTH,
                 V_VOUCHERDATE,
                 '1' as STATE, --1正常 2 被冲  3 冲销
                 'C.F. LossProfit(Expense)' as REMARK ,
                 '1' as VALID_IS, --1 有效
                 '1' as AUDIT_STATE,
                 LOCALTIMESTAMP as CREATE_TIME,
                 p_userid as CREATOR_ID
             from dual);

        dbms_output.put_line('插入结转凭证(支出类):凭证id='|| V_EXPENSE_VOUCHER_ID || '正凭证号=' ||V_EXPENSE_VOUCHERNO);
        BEGIN
            --凭证分录处理
            dbms_output.put_line('插入凭证明细表分录结转凭证支出贷方数据成功');

            --2.1 损益支出类借方-专项
            INSERT INTO ACC_BUSS_VOUCHER_DETAIL (
                VOUCHER_DTL_ID,
                VOUCHER_ID,
                account_id,
                currency_code,
                currency_cu_code,
                ACCOUNT_ENTRY_CODE,
                AMOUNT,
                AMOUNT_CU,
                exchange_rate,
                REMARK,
                ARTICLE1,
                ARTICLE2,
                ARTICLE3,
                ARTICLE4,
                ARTICLE5,
                ARTICLE6,
                ARTICLE7,
                ARTICLE8,
                ARTICLE9,
                ARTICLE10,
                ARTICLE11,
                ARTICLE12,
                ARTICLE13,
                ARTICLE14,
                ARTICLE15,
                ARTICLE16,
                ARTICLE17,
                ARTICLE,
                CREATE_TIME,
                CREATOR_ID,
                UPDATE_TIME,
                UPDATOR_ID
            ) SELECT
                  acc_seq_buss_voucher_detail.NEXTVAL as VOUCHER_DTL_ID,t.*
            from (
                     select
                         V_EXPENSE_VOUCHER_ID as VOUCHER_ID, --新凭证主表ID
                         t.ACCOUNT_ID,--借方科目编码
                         t.currency_code,--原币
                         t.currency_cu_code,--本位币
                         ( CASE WHEN (round(SUM(debit_amount_year -  credit_amount_year), 2 ) ) >= 0 THEN 'C' ELSE 'D' END ) AS ACCOUNT_ENTRY_CODE,
                         abs(SUM(debit_amount_year -  credit_amount_year)) AS AMOUNT,--原币金额
                         abs(round(SUM (debit_amount_year_cu -  credit_amount_year_cu) /**acc_pack_common.func_get_exchange_rate ( t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2' )*/, 2 )) AS AMOUNT_CU,--本位币金额
                         1 /*acc_pack_common.func_get_exchange_rate(t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2')*/ AS exchange_rate,
                         'C.F. LossProfit(Expense) Article' as REMARK,
                         article1 as ARTICLE1,
                         article2,
                         article3,
                         article4,
                         article5,
                         article6,
                         article7,
                         article8,
                         article9 as ARTICLE9,
                         ARTICLE10,
                         ARTICLE11,
                         ARTICLE12,
                         ARTICLE13,
                         ARTICLE14,
                         ARTICLE15,
                         ARTICLE16,
                         ARTICLE17,
                         ARTICLE,
                         LOCALTIMESTAMP as CREATE_TIME,
                         p_userid as CREATOR_ID,
                         LOCALTIMESTAMP as UPDATE_TIME,
                         p_userid as UPDATOR_ID
                     FROM acc_buss_article_balance t
                              left join  bpluser.bbs_v_account item on t.ACCOUNT_ID = item.ACCOUNT_ID and t.ENTITY_ID = item.ENTITY_ID and t.book_code = item.book_code
                     where t.ENTITY_ID = P_ENTITY_ID
                       and t.BOOK_CODE = p_bookcode
                       and t.year_month= V_LAST_YEARMONTH
                       and item.account_category_code='4'
                       and item.account_entry_code='D'
                       and item.final_level_is='1'
                     group by t.ACCOUNT_ID, t.currency_code, t.currency_cu_code, t.ENTITY_ID,article1,
                              article2, article3, article4, article5, article6,
                              article7, article8, article9, ARTICLE10,  ARTICLE11,
                              ARTICLE12, ARTICLE13, ARTICLE14, ARTICLE15, ARTICLE16, ARTICLE17,ARTICLE
                     HAVING abs(SUM(debit_amount_year -  credit_amount_year)) <>0
                 ) t;


            --2.2 损益收入类贷方-总账余额
            INSERT INTO ACC_BUSS_VOUCHER_DETAIL (
                VOUCHER_DTL_ID,
                VOUCHER_ID,
                account_id,
                currency_code,
                currency_cu_code,
                ACCOUNT_ENTRY_CODE,
                AMOUNT,
                AMOUNT_CU,
                exchange_rate,
                REMARK,
                ARTICLE1,
                ARTICLE2,
                ARTICLE3,
                ARTICLE4,
                ARTICLE5,
                ARTICLE6,
                ARTICLE7,
                ARTICLE8,
                ARTICLE9,
                CREATE_TIME,
                CREATOR_ID,
                UPDATE_TIME,
                UPDATOR_ID
            ) SELECT
                  acc_seq_buss_voucher_detail.NEXTVAL as VOUCHER_DTL_ID,t.*
            from (
                     select
                         V_EXPENSE_VOUCHER_ID, --新凭证主表ID
                         t.ACCOUNT_ID,--借方科目编码
                         t.currency_code,--原币
                         t.currency_cu_code,--本位币
                         ( CASE WHEN (round(SUM(debit_amount_year -  credit_amount_year), 2 ) ) +
                                     COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.AMOUNT else dtl.AMOUNT *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_EXPENSE_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0) >= 0 THEN 'C' ELSE 'D' END ) AS ACCOUNT_ENTRY_CODE,--减专项
                         abs(SUM(debit_amount_year -  credit_amount_year)+
                             COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.AMOUNT else dtl.AMOUNT *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_EXPENSE_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0)) AS AMOUNT,--原币金额    --减专项
                         abs(round((SUM (debit_amount_year_cu -  credit_amount_year_cu) +
                                    COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.AMOUNT_CU else dtl.AMOUNT_CU *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_EXPENSE_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0) ) /**acc_pack_common.func_get_exchange_rate ( t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2' )*/, 2 )) AS AMOUNT_CU,--本位币金额 --减专项
                         1 /*acc_pack_common.func_get_exchange_rate(t.ENTITY_ID, v_exch_date, t.currency_code, t.currency_cu_code, '2')*/ AS exchange_rate,
                         'C.F. LossProfit(Expense) Ledger' as REMARK,
                         NULL as ARTICLE1,
                         NULL as ARTICLE2,
                         NULL as ARTICLE3,
                         NULL as ARTICLE4,
                         NULL as ARTICLE5,
                         NULL as ARTICLE6,
                         NULL as ARTICLE7,
                         NULL as ARTICLE8,
                         NULL as ARTICLE9,
                         LOCALTIMESTAMP as CREATE_TIME,
                         p_userid as CREATOR_ID,
                         LOCALTIMESTAMP as UPDATE_TIME,
                         p_userid as UPDATOR_ID
                     FROM acc_buss_ledger_balance t
                              left join  bpluser.bbs_v_account item on t.ACCOUNT_ID = item.ACCOUNT_ID and t.ENTITY_ID = item.ENTITY_ID and t.book_code = item.book_code
                     where t.ENTITY_ID = P_ENTITY_ID
                       and t.BOOK_CODE = p_bookcode
                       and t.year_month= V_LAST_YEARMONTH
                       and item.account_category_code='4'
                       and item.account_entry_code='D'
                       and item.final_level_is='1'
                     group by t.ACCOUNT_ID, t.currency_code, t.currency_cu_code, t.ENTITY_ID
                     HAVING abs(SUM(debit_amount_year -  credit_amount_year) +
                                COALESCE((select sum(case when dtl.ACCOUNT_ENTRY_CODE = 'D' then dtl.AMOUNT else dtl.AMOUNT *-1 end ) from ACC_BUSS_VOUCHER_DETAIL dtl where dtl.voucher_id = V_EXPENSE_VOUCHER_ID and dtl.ACCOUNT_ID = t.ACCOUNT_ID and dtl.currency_code =t.currency_code  and dtl.currency_cu_code = t.currency_cu_code),0)) <>0
                 )t;

            dbms_output.put_line('插入凭证明细表资产负债贷方数据成功');
            --2.3. 资产负债贷方-本年利润科目
            INSERT INTO ACC_BUSS_VOUCHER_DETAIL (
                VOUCHER_DTL_ID,
                VOUCHER_ID,
                account_id,
                currency_code,
                currency_cu_code,
                ACCOUNT_ENTRY_CODE,
                amount,
                AMOUNT_CU,
                exchange_rate,
                REMARK,
                CREATE_TIME,
                CREATOR_ID,
                UPDATE_TIME,
                UPDATOR_ID
            ) SELECT
                  acc_seq_buss_voucher_detail.NEXTVAL as VOUCHER_DTL_ID,t.*
            from (
                     select
                         V_EXPENSE_VOUCHER_ID, --新凭证主表ID
                         (SELECT js.ACCOUNT_ID FROM bpluser.bbs_conf_account_js js where js.entity_id=P_ENTITY_ID AND js.book_code = p_bookcode and js.final_type = '3' and rownum=1 ),--A.ACCOUNT_ID,--借方科目编码
                         A.currency_code,--原币
                         A.currency_cu_code,--本位币
                         ( CASE WHEN sum(case when a.ACCOUNT_ENTRY_CODE = 'D' then a.amount else a.amount *-1 end ) >= 0 THEN 'C' ELSE 'D' END ) AS ACCOUNT_ENTRY_CODE,
                         abs(sum(case when a.ACCOUNT_ENTRY_CODE = 'D' then a.amount else a.amount *-1 end )) AS AMOUNT,--原币金额
                         abs(round(SUM (case when a.ACCOUNT_ENTRY_CODE = 'D' then a.AMOUNT_CU else a.AMOUNT_CU *-1 end) /**acc_pack_common.func_get_exchange_rate(P_ENTITY_ID, v_exch_date, a.currency_code, a.currency_cu_code, '2')*/, 2 )) AS AMOUNT_CU,--本位币金额
                         1 /*acc_pack_common.func_get_exchange_rate(P_ENTITY_ID, v_exch_date, a.currency_code, a.currency_cu_code, '2')*/ AS exchange_rate,
                         'C.F. LossProfit(Expense) JS' as REMARK,
                         LOCALTIMESTAMP as CREATE_TIME,
                         p_userid as CREATOR_ID,
                         LOCALTIMESTAMP as UPDATE_TIME,
                         p_userid as UPDATOR_ID
                     FROM ACC_BUSS_VOUCHER_DETAIL a
                     where a.VOUCHER_ID = V_EXPENSE_VOUCHER_ID
                     group by  a.currency_code, a.currency_cu_code
                 )t;
        END;
        -- 2 end

        --3-结转本年利润 start
        --凭证新主键ID
        V_YEAR_VOUCHER_ID := acc_seq_buss_voucher.NEXTVAL;
        --凭证号校正
        acc_pack_voucher.proc_correct_voucher_key(P_ENTITY_ID ,
                                                  p_bookcode ,
                                                  p_yearmonth ,
                                                  V_VOUCHER_TYPE ,
                                                  V_PROC_ID||'' );
        --获取凭证号码
        V_YEAR_VOUCHERNO := acc_pack_voucher.func_generate_voucher_no(P_ENTITY_ID, p_bookcode, p_yearmonth,V_VOUCHER_TYPE, V_PROC_ID||'', 1);
        INSERT INTO ACC_BUSS_VOUCHER
        (VOUCHER_ID,
         ENTITY_ID,
         BOOK_CODE,
         POSTING_TYPE_CODE,
         PROC_ID,
         VOUCHER_NO,
         YEAR_MONTH,
         EFFECTIVE_DATE,
         STATE,
         REMARK,
         VALID_IS,
         AUDIT_STATE,
         CREATE_TIME,
         CREATOR_ID)
        values
            (V_YEAR_VOUCHER_ID,
             P_ENTITY_ID,
             p_bookcode,
             V_VOUCHER_TYPE,--凭证类型
             V_PROC_ID,--数据类型
             V_YEAR_VOUCHERNO,
             P_YEARMONTH,
             V_VOUCHERDATE,
             '1', --1正常 2 被冲  3 冲销
             'Profit Balance Of This Year Carry Forward',
             '1', --1 有效
             '1', --1 有效
             LOCALTIMESTAMP,
             p_userid);

        dbms_output.put_line('插入本年利润凭证:id=' || V_EXPENSE_VOUCHER_ID || ' 凭证号' ||V_EXPENSE_VOUCHERNO);
        BEGIN
            --凭证分录处理
            dbms_output.put_line('插入凭证明细表分录本年利润数据成功');

            --3.1.本年未分配利润贷方
            INSERT INTO ACC_BUSS_VOUCHER_DETAIL (
                VOUCHER_DTL_ID,
                VOUCHER_ID,
                account_id,
                currency_code,
                currency_cu_code,
                ACCOUNT_ENTRY_CODE,
                amount,
                AMOUNT_CU,
                exchange_rate,
                REMARK,
                ARTICLE1,
                ARTICLE2,
                ARTICLE3,
                ARTICLE4,
                ARTICLE5,
                ARTICLE6,
                ARTICLE7,
                ARTICLE8,
                ARTICLE9,
                CREATE_TIME,
                CREATOR_ID,
                UPDATE_TIME,
                UPDATOR_ID
            ) SELECT
                  acc_seq_buss_voucher_detail.nextval,
                  V_YEAR_VOUCHER_ID, --新凭证主表ID
                  d.ACCOUNT_ID,--借方科目编码
                  d.currency_code,--原币
                  d.currency_cu_code,--本位币
                  (CASE WHEN d.ACCOUNT_ENTRY_CODE ='D' THEN 'C' ELSE 'D' END ) AS ACCOUNT_ENTRY_CODE,
                  d.amount AS AMOUNT,--原币金额
                  d.AMOUNT_CU AS AMOUNT_CU,--本位币金额
                  d.exchange_rate  AS exchange_rate,
                  'Profit Balance Of This Year Carry Forward' as REMARK,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  LOCALTIMESTAMP as CREATE_TIME,
                  p_userid as CREATOR_ID,
                  LOCALTIMESTAMP as UPDATE_TIME,
                  p_userid as UPDATOR_ID
            FROM acc_buss_voucher t
                     left join acc_buss_voucher_detail d on t.voucher_id = d.voucher_id
                     left join bpluser.bbs_conf_account_js js on js.ACCOUNT_ID = d.ACCOUNT_ID AND t.entity_id=js.entity_id AND t.book_code = js.book_code
            where t.ENTITY_ID = P_ENTITY_ID
              and t.BOOK_CODE = p_bookcode
              and t.year_month= p_yearmonth
              and t.voucher_id <> V_YEAR_VOUCHER_ID
              and js.final_type='3';

            --3.1.本年未分配利润贷方
            INSERT INTO ACC_BUSS_VOUCHER_DETAIL (
                VOUCHER_DTL_ID,
                VOUCHER_ID,
                account_id,
                currency_code,
                currency_cu_code,
                ACCOUNT_ENTRY_CODE,
                amount,
                AMOUNT_CU,
                exchange_rate,
                REMARK,
                ARTICLE1,
                ARTICLE2,
                ARTICLE3,
                ARTICLE4,
                ARTICLE5,
                ARTICLE6,
                ARTICLE7,
                ARTICLE8,
                ARTICLE9,
                CREATE_TIME,
                CREATOR_ID,
                UPDATE_TIME,
                UPDATOR_ID
            ) SELECT
                  acc_seq_buss_voucher_detail.nextval as VOUCHER_DTL_ID,
                  V_YEAR_VOUCHER_ID as VOUCHER_ID, --新凭证主表ID
                  (SELECT js.ACCOUNT_ID FROM bpluser.bbs_conf_account_js js
                   where js.entity_id=P_ENTITY_ID AND js.book_code = p_bookcode and js.final_type = '4' and rownum = 1) as account_id,--借方科目编码
                  d.currency_code,--原币
                  d.currency_cu_code,--本位币
                  ACCOUNT_ENTRY_CODE,
                  d.amount AS AMOUNT,--原币金额
                  d.AMOUNT_CU AS AMOUNT_CU,--本位币金额
                  d.exchange_rate  AS exchange_rate,
                  'Profit Balance Of This Year Carry Forward' as REMARK,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  NULL,
                  LOCALTIMESTAMP as CREATE_TIME,
                  p_userid as CREATOR_ID,
                  LOCALTIMESTAMP as UPDATE_TIME,
                  p_userid as UPDATOR_ID
            FROM acc_buss_voucher t
                     left join acc_buss_voucher_detail d on t.voucher_id = d.voucher_id
                     left join bpluser.bbs_conf_account_js js on js.ACCOUNT_ID = d.ACCOUNT_ID AND t.entity_id=js.entity_id AND t.book_code = js.book_code
            where t.ENTITY_ID = P_ENTITY_ID
              and t.BOOK_CODE = p_bookcode
              and t.year_month= p_yearmonth
              and t.voucher_id <> V_YEAR_VOUCHER_ID
              and js.final_type='3';
        END;

        --  end
        select count(1) into v_insert_count from ACC_BUSS_VOUCHER_DETAIL where VOUCHER_ID = V_INCOME_VOUCHER_ID;
        if v_insert_count = 0 then
            v_errcode := 'AG004';
            v_errmsg := '插入结转损益(支入类)凭证分录没有数据';
            select '1' into v_char from dual where 1=2;
        end if;
        select count(1) into v_insert_count from ACC_BUSS_VOUCHER_DETAIL where VOUCHER_ID = V_EXPENSE_VOUCHER_ID;
        if v_insert_count = 0 then
            v_errcode := 'AG005';
            v_errmsg := '插入结转损益(支出类)凭证分录没有数据';
            select '1' into v_char from dual where 1=2;
        end if;
        select count(1) into v_insert_count from ACC_BUSS_VOUCHER_DETAIL where VOUCHER_ID = V_YEAR_VOUCHER_ID;
        if v_insert_count = 0 then
            v_errcode := 'AG006';
            v_errmsg := '插入本年利润余额结转凭证分录没有数据';
            select '1' into v_char from dual where 1=2;
        end if;

        acc_pack_buss_balance.proc_balance_entrance(P_ENTITY_ID, p_bookcode, p_yearmonth, p_userid);

    EXCEPTION
        WHEN OTHERS THEN
            --删除借贷异常的凭证信息
            DELETE FROM ACC_BUSS_VOUCHER_DETAIL  WHERE VOUCHER_ID = V_INCOME_VOUCHER_ID;
            DELETE FROM ACC_BUSS_VOUCHER  WHERE VOUCHER_ID = V_INCOME_VOUCHER_ID;

            --删除借贷异常的凭证信息
            DELETE FROM ACC_BUSS_VOUCHER_DETAIL  WHERE VOUCHER_ID = V_EXPENSE_VOUCHER_ID;
            DELETE FROM ACC_BUSS_VOUCHER  WHERE VOUCHER_ID = V_EXPENSE_VOUCHER_ID;

            --删除借贷异常的凭证信息
            DELETE FROM ACC_BUSS_VOUCHER_DETAIL  WHERE VOUCHER_ID = V_YEAR_VOUCHER_ID;
            DELETE FROM ACC_BUSS_VOUCHER  WHERE VOUCHER_ID = V_YEAR_VOUCHER_ID;
            COMMIT;
            dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
            dbms_output.put_line('**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 结转异常，请检查');
            --往外层抛出异常信息
            raise_application_error(-20003, '**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 生成年结凭证异常，请检查');
    END proc_annual_generate_voucher;

    PROCEDURE proc_annual_refresh_balance(P_ENTITY_ID IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER) IS
        v_char  VARCHAR(1);
        v_errcode              VARCHAR(32); --错误编码
        v_errmsg               VARCHAR(200); --错误信息
        v_base_currency_code VARCHAR(3);
        v_reckoning_state  VARCHAR(1);
        V_LAST_YEARMONTH   VARCHAR(6);
        v_serial_no numeric;

    BEGIN
        SELECT currency_code, reckoning_state
        into v_base_currency_code,v_reckoning_state
        from ACC_CONF_ANNUAL_PERIOD
        where ENTITY_ID = P_ENTITY_ID
          and book_code = p_bookcode
          and year_month=p_yearmonth
          and valid_is = '1'
          and audit_state='1';

        if v_base_currency_code is null then
            v_errcode := 'AG001';
            v_errmsg := '年结期间不存在或者无效';
            select '1' into v_char from dual where 1=2;
        end if;

        if v_reckoning_state ='1' then
            v_errcode := 'AG002';
            v_errmsg := '年结期间已结转';
            select '1' into v_char from dual where 1=2;
        end if;

        for REC_YEARMONTH IN (select year_month from acc_conf_accountperiod where ENTITY_ID=P_ENTITY_ID and book_code = p_bookcode AND EXECUTION_STATE='3' and year_month>p_yearmonth order by year_month asc) loop
                SELECT
                    coalesce(MAX(serial_no), 1) into v_serial_no
                FROM acc_buss_ledger_balance
                WHERE ENTITY_ID = P_ENTITY_ID
                  AND book_code = p_bookcode
                  AND year_month = REC_YEARMONTH.year_month;

                acc_pack_buss_balance.proc_add_balancehis(P_ENTITY_ID, p_bookcode, REC_YEARMONTH.year_month, p_userid);

                INSERT INTO ACC_BUSS_LEDGER_BALANCE
                SELECT acc_seq_item_ledger_balance.nextval AS LEDGER_BALANCE_ID,
                       ENTITY_ID,
                       BOOK_CODE,
                       REC_YEARMONTH.year_month AS YEAR_MONTH,
                       root_account_id,
                       account_id,
                       currency_code,
                       currency_cu_code,
                       0 AS DEBIT_AMOUNT,
                       0 AS DEBIT_AMOUNT_CU,
                       0 AS CREDIT_AMOUNT,
                       0 AS CREDIT_AMOUNT_CU,
                       0 AS DEBIT_AMOUNT_QUARTER,
                       0 AS DEBIT_AMOUNT_QUARTER_CU,
                       0 AS CREDIT_AMOUNT_QUARTER,
                       0 AS CREDIT_AMOUNT_QUARTER_CU,
                       0 AS DEBIT_AMOUNT_YEAR,
                       0 AS DEBIT_AMOUNT_YEAR_CU,
                       0 AS CREDIT_AMOUNT_YEAR,
                       0 AS CREDIT_AMOUNT_YEAR_CU,
                       closing_BALANCE,
                       closing_BALANCE,
                       closing_BALANCE_CU,
                       closing_BALANCE_CU,
                       CREATE_TIME,
                       CREATOR_ID,
                       UPDATE_TIME,
                       UPDATOR_ID,
                       v_serial_no AS SERIAL_NO
                FROM ACC_BUSS_LEDGER_BALANCE T
                WHERE t.ENTITY_ID = P_ENTITY_ID
                  AND t.book_code = p_bookcode
                  AND YEAR_MONTH = p_yearmonth
                  AND T.ACCOUNT_ID IN (SELECT account_id
                                       FROM BPLUSER.bbs_conf_account_js A
                                       WHERE A.entity_id=P_ENTITY_ID AND A.BOOK_CODE = p_bookcode
                                         AND A.FINAL_TYPE = '4')
                  AND not EXISTS(SELECT 1 FROM  ACC_BUSS_LEDGER_BALANCE B WHERE  t.ENTITY_ID = b.ENTITY_ID
                                                                            AND t.book_code = b.book_code
                                                                            AND b.YEAR_MONTH = REC_YEARMONTH.year_month AND T.ACCOUNT_ID=b.ACCOUNT_ID ) ;


                INSERT INTO ACC_BUSS_ARTICLE_BALANCE
                SELECT ACC_SEQ_ITEM_ARTICLE_BALANCE.NEXTVAL AS ARTICLE_ID,
                       ENTITY_ID,
                       BOOK_CODE,
                       REC_YEARMONTH.year_month AS YEAR_MONTH,
                       root_account_id,
                       account_id,
                       ARTICLE1,
                       ARTICLE2,
                       ARTICLE3,
                       ARTICLE4,
                       ARTICLE5,
                       ARTICLE6,
                       ARTICLE7,
                       ARTICLE8,
                       ARTICLE9,
                       ARTICLE10,
                       ARTICLE11,
                       ARTICLE12,
                       ARTICLE13,
                       ARTICLE14,
                       ARTICLE15,
                       ARTICLE16,
                       ARTICLE17,
                       currency_code,
                       currency_cu_code,
                       0 AS DEBIT_AMOUNT,
                       0 AS DEBIT_AMOUNT_CU,
                       0 AS CREDIT_AMOUNT,
                       0 AS CREDIT_AMOUNT_CU,
                       0 AS DEBIT_AMOUNT_QUARTER,
                       0 AS DEBIT_AMOUNT_QUARTER_CU,
                       0 AS CREDIT_AMOUNT_QUARTER,
                       0 AS CREDIT_AMOUNT_QUARTER_CU,
                       0 AS DEBIT_AMOUNT_YEAR,
                       0 AS DEBIT_AMOUNT_YEAR_CU,
                       0 AS CREDIT_AMOUNT_YEAR,
                       0 AS CREDIT_AMOUNT_YEAR_CU,
                       closing_BALANCE,
                       closing_BALANCE,
                       closing_BALANCE_CU,
                       closing_BALANCE_CU,
                       CREATE_TIME,
                       CREATOR_ID,
                       UPDATE_TIME,
                       UPDATOR_ID,
                       v_serial_no AS SERIAL_NO,
                       ARTICLE
                FROM ACC_BUSS_ARTICLE_BALANCE T
                WHERE t.ENTITY_ID = P_ENTITY_ID
                  AND t.book_code = p_bookcode
                  AND YEAR_MONTH = P_YEARMONTH
                  AND T.ACCOUNT_ID IN (SELECT account_id
                                       FROM BPLUSER.bbs_conf_account_js A
                                       WHERE A.entity_id=P_ENTITY_ID AND A.BOOK_CODE = P_BOOKCODE
                                         AND A.FINAL_TYPE = '4')
                  AND not EXISTS(SELECT 1 FROM  ACC_BUSS_ARTICLE_BALANCE B WHERE  t.ENTITY_ID = b.ENTITY_ID
                                                                             AND t.book_code = b.book_code
                                                                             AND b.YEAR_MONTH = REC_YEARMONTH.year_month AND T.ACCOUNT_ID=b.ACCOUNT_ID );

                if((substr(p_yearmonth,0,4)+1 ||'01') = REC_YEARMONTH.year_month ) THEN

                    MERGE INTO acc_buss_ledger_balance t
                    USING (SELECT d.ENTITY_ID,
                                  d.book_code,
                                  d.year_month,
                                  d.root_account_id,
                                  d.account_id,
                                  d.currency_code,
                                  d.currency_cu_code,
                                  SUM(d.closing_balance) AS closing_balance,
                                  SUM(d.closing_balance_cu) AS closing_balance_cu
                           FROM acc_buss_ledger_balance d
                           WHERE d.ENTITY_ID = p_ENTITY_ID
                             AND d.book_code = p_bookcode
                             AND d.year_month = p_yearmonth
                           GROUP BY d.ENTITY_ID,
                                    d.book_code,
                                    d.year_month,
                                    d.root_account_id,
                                    d.account_id,
                                    d.currency_code,
                                    d.currency_cu_code) c
                    ON (t.year_month = REC_YEARMONTH.year_month and t.ENTITY_ID = c.ENTITY_ID AND t.book_code = c.book_code AND t.root_account_id = c.root_account_id AND t.ACCOUNT_ID = c.account_id AND t.currency_code = c.currency_code AND t.currency_cu_code = c.currency_cu_code)
                    WHEN MATCHED THEN
                        UPDATE
                        SET SERIAL_NO=v_serial_no,
                            opening_balance = coalesce(c.closing_balance,0),
                            closing_balance = coalesce(c.closing_balance,0) + (t.debit_amount-t.credit_amount),
                            opening_balance_cu = coalesce(c.closing_balance_cu,0),
                            closing_balance_cu = coalesce(c.closing_balance_cu,0) + (t.debit_amount_cu - t.credit_amount_cu)
                        WHERE t.ENTITY_ID =  P_ENTITY_ID
                          AND t.book_code =  p_bookcode
                          AND t.year_month = REC_YEARMONTH.year_month
                    WHEN NOT MATCHED THEN
                        INSERT
                        (ledger_balance_id,
                         entity_id,
                         book_code,
                         year_month,
                         root_account_id,
                         account_id,
                         currency_code,
                         currency_cu_code,
                         debit_amount,
                         debit_amount_cu,
                         credit_amount,
                         credit_amount_cu,
                         debit_amount_quarter,
                         debit_amount_quarter_cu,
                         credit_amount_quarter,
                         credit_amount_quarter_cu,
                         debit_amount_year,
                         debit_amount_year_cu,
                         credit_amount_year,
                         credit_amount_year_cu,
                         opening_balance,
                         closing_balance,
                         opening_balance_cu,
                         closing_balance_cu,
                         create_time,
                         creator_id,
                         update_time,
                         updator_id,
                         serial_no)
                        VALUES
                            (acc_seq_item_ledger_balance.nextval,
                             c.entity_id,
                             c.book_code,
                             REC_YEARMONTH.year_month,
                             c.root_account_id,
                             c.account_id,
                             c.currency_code,
                             c.currency_cu_code,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             c.closing_balance,
                             c.closing_balance,
                             c.closing_balance_cu,
                             c.closing_balance_cu,
                             SYSDATE,
                             p_userid,
                             NULL,
                             NULL,
                             v_serial_no);

                    MERGE INTO acc_buss_article_balance t
                    USING (SELECT d.ENTITY_ID,
                                  d.book_code,
                                  d.year_month,
                                  d.root_account_id,
                                  d.account_id,
                                  d.currency_code,
                                  d.currency_cu_code,
                                  d.article,
                                  d.article1,
                                  d.article2,
                                  d.article3,
                                  d.article4,
                                  d.article5,
                                  d.article6,
                                  d.article7,
                                  d.article8,
                                  d.article9,
                                  d.article10,
                                  d.article11,
                                  d.article12,
                                  d.article13,
                                  d.article14,
                                  d.article15,
                                  d.article16,
                                  d.article17,
                                  SUM(d.closing_balance) AS closing_balance,
                                  SUM(d.closing_balance_cu) AS closing_balance_cu
                           FROM acc_buss_article_balance d
                           WHERE d.ENTITY_ID = P_ENTITY_ID
                             AND d.book_code =  p_bookcode
                             AND d.year_month = p_yearmonth
                           GROUP BY d.ENTITY_ID,
                                    d.book_code,
                                    d.year_month,
                                    d.root_account_id,
                                    d.account_id,
                                    d.currency_code,
                                    d.currency_cu_code,
                                    d.article,
                                    d.article1,
                                    d.article2,
                                    d.article3,
                                    d.article4,
                                    d.article5,
                                    d.article6,
                                    d.article7,
                                    d.article8,
                                    d.article9,
                                    d.article10,
                                    d.article11,
                                    d.article12,
                                    d.article13,
                                    d.article14,
                                    d.article15,
                                    d.article16,
                                    d.article17) c
                    ON (t.year_month = REC_YEARMONTH.year_month and t.ENTITY_ID = c.ENTITY_ID AND t.book_code = c.book_code AND t.root_account_id = c.root_account_id AND t.ACCOUNT_ID = c.account_id AND t.currency_code = c.currency_code AND t.currency_cu_code = c.currency_cu_code AND (t.article= c.article or (t.article is null and c.article is null)))
                    WHEN MATCHED THEN
                        UPDATE
                        SET SERIAL_NO=v_serial_no,
                            opening_balance = coalesce(c.closing_balance,0),
                            closing_balance = coalesce(c.closing_balance,0) + (t.debit_amount-t.credit_amount),
                            opening_balance_cu = coalesce(c.closing_balance_cu,0),
                            closing_balance_cu = coalesce(c.closing_balance_cu,0) + (t.debit_amount_cu - t.credit_amount_cu)
                        WHERE t.ENTITY_ID =  P_ENTITY_ID
                          AND t.book_code =  p_bookcode
                          AND t.year_month = REC_YEARMONTH.year_month
                    WHEN NOT MATCHED THEN
                        INSERT
                        (article_id,
                         entity_id,
                         book_code,
                         year_month,
                         root_account_id,
                         account_id,
                         currency_code,
                         currency_cu_code,
                         article,
                         article1,
                         article2,
                         article3,
                         article4,
                         article5,
                         article6,
                         article7,
                         article8,
                         article9,
                         article10,
                         article11,
                         article12,
                         article13,
                         article14,
                         article15,
                         article16,
                         article17,
                         debit_amount,
                         debit_amount_cu,
                         credit_amount,
                         credit_amount_cu,
                         debit_amount_quarter,
                         debit_amount_quarter_cu,
                         credit_amount_quarter,
                         credit_amount_quarter_cu,
                         debit_amount_year,
                         debit_amount_year_cu,
                         credit_amount_year,
                         credit_amount_year_cu,
                         opening_balance,
                         closing_balance,
                         opening_balance_cu,
                         closing_balance_cu,
                         create_time,
                         creator_id,
                         serial_no)
                        VALUES
                            (ACC_SEQ_ITEM_ARTICLE_BALANCE.nextval,
                             c.entity_id,
                             c.book_code,
                             REC_YEARMONTH.year_month,
                             c.root_account_id,
                             c.account_id,
                             c.currency_code,
                             c.currency_cu_code,
                             c.article,
                             c.article1,
                             c.article2,
                             c.article3,
                             c.article4,
                             c.article5,
                             c.article6,
                             c.article7,
                             c.article8,
                             c.article9,
                             c.article10,
                             c.article11,
                             c.article12,
                             c.article13,
                             c.article14,
                             c.article15,
                             c.article16,
                             c.article17,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             c.closing_balance,
                             c.closing_balance,
                             c.closing_balance_cu,
                             c.closing_balance_cu,
                             SYSDATE,
                             p_userid,
                             v_serial_no);

                    COMMIT;
                else
                    MERGE INTO acc_buss_ledger_balance t
                    USING (SELECT d.ENTITY_ID,
                                  d.book_code,
                                  d.year_month,
                                  d.root_account_id,
                                  d.account_id,
                                  d.currency_code,
                                  d.currency_cu_code,
                                  SUM(d.closing_balance) AS closing_balance,
                                  SUM(d.closing_balance_cu) AS closing_balance_cu
                           FROM acc_buss_ledger_balance d
                           WHERE d.ENTITY_ID = p_ENTITY_ID
                             AND d.book_code = p_bookcode
                             AND d.year_month = to_char(add_months(trunc(to_date(REC_YEARMONTH.year_month||'01', 'yyyy/mm/dd')),-1),'yyyymm')
                           GROUP BY d.ENTITY_ID,
                                    d.book_code,
                                    d.year_month,
                                    d.root_account_id,
                                    d.account_id,
                                    d.currency_code,
                                    d.currency_cu_code) c
                    ON (t.year_month = REC_YEARMONTH.year_month and t.ENTITY_ID = c.ENTITY_ID AND t.book_code = c.book_code AND t.root_account_id = c.root_account_id AND t.ACCOUNT_ID = c.account_id AND t.currency_code = c.currency_code AND t.currency_cu_code = c.currency_cu_code)
                    WHEN MATCHED THEN
                        UPDATE
                        SET SERIAL_NO=v_serial_no,
                            opening_balance = coalesce(c.closing_balance,0),
                            closing_balance = coalesce(c.closing_balance,0) + (t.debit_amount-t.credit_amount),
                            opening_balance_cu = coalesce(c.closing_balance_cu,0),
                            closing_balance_cu = coalesce(c.closing_balance_cu,0) + (t.debit_amount_cu - t.credit_amount_cu)
                        WHERE t.ENTITY_ID =  P_ENTITY_ID
                          AND t.book_code =  p_bookcode
                          AND t.year_month = REC_YEARMONTH.year_month
                          AND EXISTS (select 1 from acc_buss_ledger_balance b where t.ENTITY_ID = b.ENTITY_ID
                                                                                AND t.book_code = b.book_code
                                                                                AND t.ACCOUNT_ID = b.ACCOUNT_ID
                                                                                AND t.currency_code = b.currency_code
                                                                                and b.year_month = to_char(add_months(trunc(to_date(REC_YEARMONTH.year_month||'01', 'yyyy/mm/dd')),-1),'yyyymm') )
                    WHEN NOT MATCHED THEN
                        INSERT
                        (ledger_balance_id,
                         entity_id,
                         book_code,
                         year_month,
                         root_account_id,
                         account_id,
                         currency_code,
                         currency_cu_code,
                         debit_amount,
                         debit_amount_cu,
                         credit_amount,
                         credit_amount_cu,
                         debit_amount_quarter,
                         debit_amount_quarter_cu,
                         credit_amount_quarter,
                         credit_amount_quarter_cu,
                         debit_amount_year,
                         debit_amount_year_cu,
                         credit_amount_year,
                         credit_amount_year_cu,
                         opening_balance,
                         closing_balance,
                         opening_balance_cu,
                         closing_balance_cu,
                         create_time,
                         creator_id,
                         serial_no)
                        VALUES
                            (acc_seq_item_ledger_balance.nextval,
                             c.entity_id,
                             c.book_code,
                             REC_YEARMONTH.year_month,
                             c.root_account_id,
                             c.account_id,
                             c.currency_code,
                             c.currency_cu_code,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             c.closing_balance,
                             c.closing_balance,
                             c.closing_balance_cu,
                             c.closing_balance_cu,
                             SYSDATE,
                             p_userid,
                             v_serial_no);

                    MERGE INTO acc_buss_article_balance t
                    USING (SELECT d.ENTITY_ID,
                                  d.book_code,
                                  d.year_month,
                                  d.root_account_id,
                                  d.account_id,
                                  d.currency_code,
                                  d.currency_cu_code,
                                  d.article,
                                  d.article1,
                                  d.article2,
                                  d.article3,
                                  d.article4,
                                  d.article5,
                                  d.article6,
                                  d.article7,
                                  d.article8,
                                  d.article9,
                                  d.article10,
                                  d.article11,
                                  d.article12,
                                  d.article13,
                                  d.article14,
                                  d.article15,
                                  d.article16,
                                  d.article17,
                                  SUM(d.closing_balance) AS closing_balance,
                                  SUM(d.closing_balance_cu) AS closing_balance_cu
                           FROM acc_buss_article_balance d
                           WHERE d.ENTITY_ID = P_ENTITY_ID
                             AND d.book_code =  p_bookcode
                             AND d.year_month = to_char(add_months(trunc(to_date(REC_YEARMONTH.year_month||'01', 'yyyy/mm/dd')),-1),'yyyymm')
                           GROUP BY d.ENTITY_ID,
                                    d.book_code,
                                    d.year_month,
                                    d.root_account_id,
                                    d.account_id,
                                    d.currency_code,
                                    d.currency_cu_code,
                                    d.article,
                                    d.article1,
                                    d.article2,
                                    d.article3,
                                    d.article4,
                                    d.article5,
                                    d.article6,
                                    d.article7,
                                    d.article8,
                                    d.article9,
                                    d.article10,
                                    d.article11,
                                    d.article12,
                                    d.article13,
                                    d.article14,
                                    d.article15,
                                    d.article16,
                                    d.article17) c
                    ON (t.year_month = REC_YEARMONTH.year_month and t.ENTITY_ID = c.ENTITY_ID AND t.book_code = c.book_code AND t.root_account_id = c.root_account_id AND t.ACCOUNT_ID = c.account_id AND t.currency_code = c.currency_code AND t.currency_cu_code = c.currency_cu_code AND (t.article= c.article or (t.article is null and c.article is null)))
                    WHEN MATCHED THEN
                        UPDATE
                        SET SERIAL_NO=v_serial_no,
                            opening_balance = coalesce(c.closing_balance,0),
                            closing_balance = coalesce(c.closing_balance,0) + (t.debit_amount-t.credit_amount),
                            opening_balance_cu = coalesce(c.closing_balance_cu,0),
                            closing_balance_cu = coalesce(c.closing_balance_cu,0) + (t.debit_amount_cu - t.credit_amount_cu)
                        WHERE t.ENTITY_ID =  P_ENTITY_ID
                          AND t.book_code =  p_bookcode
                          AND t.year_month = REC_YEARMONTH.year_month
                          AND EXISTS (select 1 from acc_buss_article_balance b where t.ENTITY_ID = b.ENTITY_ID
                                                                                 AND t.book_code = b.book_code
                                                                                 AND t.ACCOUNT_ID = b.ACCOUNT_ID
                                                                                 AND t.currency_code = b.currency_code
                                                                                 and b.year_month = to_char(add_months(trunc(to_date(REC_YEARMONTH.year_month||'01', 'yyyy/mm/dd')),-1),'yyyymm')
                                                                                 AND (t.article= b.article or (t.article is null and b.article is null)) )
                    WHEN NOT MATCHED THEN
                        INSERT
                        (article_id,
                         entity_id,
                         book_code,
                         year_month,
                         root_account_id,
                         account_id,
                         currency_code,
                         currency_cu_code,
                         article,
                         article1,
                         article2,
                         article3,
                         article4,
                         article5,
                         article6,
                         article7,
                         article8,
                         article9,
                         article10,
                         article11,
                         article12,
                         article13,
                         article14,
                         article15,
                         article16,
                         article17,
                         debit_amount,
                         debit_amount_cu,
                         credit_amount,
                         credit_amount_cu,
                         debit_amount_quarter,
                         debit_amount_quarter_cu,
                         credit_amount_quarter,
                         credit_amount_quarter_cu,
                         debit_amount_year,
                         debit_amount_year_cu,
                         credit_amount_year,
                         credit_amount_year_cu,
                         opening_balance,
                         closing_balance,
                         opening_balance_cu,
                         closing_balance_cu,
                         create_time,
                         creator_id,
                         serial_no)
                        VALUES
                            (ACC_SEQ_ITEM_ARTICLE_BALANCE.nextval,
                             c.entity_id,
                             c.book_code,
                             REC_YEARMONTH.year_month,
                             c.root_account_id,
                             c.account_id,
                             c.currency_code,
                             c.currency_cu_code,
                             c.article,
                             c.article1,
                             c.article2,
                             c.article3,
                             c.article4,
                             c.article5,
                             c.article6,
                             c.article7,
                             c.article8,
                             c.article9,
                             c.article10,
                             c.article11,
                             c.article12,
                             c.article13,
                             c.article14,
                             c.article15,
                             c.article16,
                             c.article17,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             0,
                             c.closing_balance,
                             c.closing_balance,
                             c.closing_balance_cu,
                             c.closing_balance_cu,
                             SYSDATE,
                             p_userid,
                             v_serial_no);
                    COMMIT;
                end if;
                acc_pack_annual.proc_annual_summary_balance(P_ENTITY_ID, p_bookcode, REC_YEARMONTH.year_month, p_userid);

            end loop;



    EXCEPTION
        WHEN OTHERS THEN
            --抛出异常提示信息
            dbms_output.put_line('**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 年结期间检查异常：会计期间: %'||p_yearmonth);
            --往外层抛出异常信息
            raise_application_error(-20003, '**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 结转刷新余额异常，请检查');
    END proc_annual_refresh_balance;

    PROCEDURE proc_annual_summary_balance(p_ENTITY_ID IN NUMBER, p_book_code IN VARCHAR2, p_year_month IN VARCHAR2, p_user_id IN NUMBER) IS
        v_serial_no numeric;
    BEGIN
        SELECT
            coalesce(MAX(serial_no), 1) into v_serial_no
        FROM acc_buss_ledger_balance
        WHERE ENTITY_ID = p_ENTITY_ID
          AND book_code = p_book_code
          AND year_month = p_year_month;


        --汇总信息
        for REC_ACCOUNT_LEVEL IN (select DISTINCT ACCOUNT_LEVEL from bpluser.bbs_account order by ACCOUNT_LEVEL desc) loop
                dbms_output.put_line( '科目等级:='||REC_ACCOUNT_LEVEL.ACCOUNT_LEVEL);

                MERGE INTO acc_buss_ledger_balance t
                USING (SELECT d.ENTITY_ID,
                              d.book_code,
                              d.year_month,
                              d.root_account_id,
                              item.upper_account_id,
                              d.currency_code,
                              d.currency_cu_code,
                              SUM(d.debit_amount) AS debit_amount,
                              SUM(d.debit_amount_cu) AS debit_amount_cu,
                              SUM(d.credit_amount) AS credit_amount,
                              SUM(d.credit_amount_cu) AS credit_amount_cu,
                              SUM(d.debit_amount) AS debit_amount_quarter,
                              SUM(d.debit_amount_cu) debit_amount_quarter_cu,
                              SUM(d.credit_amount) AS credit_amount_quarter,
                              SUM(d.credit_amount_cu) AS credit_amount_quarter_cu,
                              SUM(d.debit_amount) AS debit_amount_year,
                              SUM(d.debit_amount_cu) AS debit_amount_year_cu,
                              SUM(d.credit_amount) AS credit_amount_year,
                              SUM(d.credit_amount_cu) AS credit_amount_year_cu,
                              SUM(d.opening_balance) AS opening_balance,
                              SUM(d.opening_balance + d.debit_amount - d.credit_amount) AS closing_balance,
                              SUM(d.opening_balance_cu) AS opening_balance_cu,
                              SUM(d.opening_balance_cu + d.debit_amount_cu - d.credit_amount_cu) AS closing_balance_cu
                       FROM acc_buss_ledger_balance d
                                LEFT JOIN bpluser.bbs_account item
                                          ON d.ACCOUNT_ID = item.ACCOUNT_ID
                       WHERE item.valid_is = '1'
                         AND item.audit_state = '1'
                         AND item.ACCOUNT_LEVEL = rec_ACCOUNT_LEVEL.ACCOUNT_LEVEL
                         AND item.upper_account_id <> 0
                         AND d.ENTITY_ID = p_ENTITY_ID
                         AND d.book_code = p_book_code
                         AND d.year_month = p_year_month
                       GROUP BY d.ENTITY_ID,
                                d.book_code,
                                d.year_month,
                                d.root_account_id,
                                item.upper_account_id,
                                d.currency_code,
                                d.currency_cu_code,
                                d.ENTITY_ID) c
                ON (t.ENTITY_ID = c.ENTITY_ID AND t.book_code = c.book_code AND t.year_month = c.year_month AND t.root_account_id = c.root_account_id AND t.ACCOUNT_ID = c.upper_account_id AND t.currency_code = c.currency_code AND t.currency_cu_code = c.currency_cu_code)
                WHEN MATCHED THEN
                    UPDATE
                    SET t.debit_amount             = c.debit_amount,
                        t.debit_amount_cu          = c.debit_amount_cu,
                        t.credit_amount            = c.credit_amount,
                        t.credit_amount_cu         = c.credit_amount_cu,
                        t.debit_amount_quarter     = c.debit_amount_quarter,
                        t.debit_amount_quarter_cu  = c.debit_amount_quarter_cu,
                        t.credit_amount_quarter    = c.credit_amount_quarter,
                        t.credit_amount_quarter_cu = c.credit_amount_quarter_cu,
                        t.debit_amount_year        = c.debit_amount_year,
                        t.debit_amount_year_cu     = c.debit_amount_year_cu,
                        t.credit_amount_year       = c.credit_amount_year,
                        t.credit_amount_year_cu    = c.credit_amount_year_cu,
                        t.opening_balance          = c.opening_balance,
                        t.closing_balance         = c.closing_balance,
                        t.opening_balance_cu       = c.opening_balance_cu,
                        t.closing_balance_cu      = c.closing_balance_cu
                    WHERE t.ENTITY_ID = p_ENTITY_ID
                      AND t.book_code = p_book_code
                      AND t.year_month = p_year_month
                WHEN NOT MATCHED THEN
                    INSERT
                    (ledger_balance_id,
                     ENTITY_ID,
                     book_code,
                     year_month,
                     root_account_id,
                     account_id,
                     currency_code,
                     currency_cu_code,
                     debit_amount,
                     debit_amount_cu,
                     credit_amount,
                     credit_amount_cu,
                     debit_amount_quarter,
                     debit_amount_quarter_cu,
                     credit_amount_quarter,
                     credit_amount_quarter_cu,
                     debit_amount_year,
                     debit_amount_year_cu,
                     credit_amount_year,
                     credit_amount_year_cu,
                     opening_balance,
                     closing_balance,
                     opening_balance_cu,
                     closing_balance_cu,
                     create_time,
                     creator_id,
                     update_time,
                     updator_id,
                     serial_no)
                    VALUES
                        (acc_seq_item_ledger_balance.nextval,
                         c.ENTITY_ID,
                         c.book_code,
                         c.year_month,
                         c.root_account_id,
                         c.upper_account_id,
                         c.currency_code,
                         c.currency_cu_code,
                         c.debit_amount,
                         c.debit_amount_cu,
                         c.credit_amount,
                         c.credit_amount_cu,
                         c.debit_amount_quarter,
                         c.debit_amount_quarter_cu,
                         c.credit_amount_quarter,
                         c.credit_amount_quarter_cu,
                         c.debit_amount_year,
                         c.debit_amount_year_cu,
                         c.credit_amount_year,
                         c.credit_amount_year_cu,
                         c.opening_balance,
                         c.closing_balance,
                         c.opening_balance_cu,
                         c.closing_balance_cu,
                         SYSDATE,
                         p_user_id,
                         NULL,
                         NULL,
                         v_serial_no);
                COMMIT;
            end loop;


    EXCEPTION
        WHEN OTHERS THEN
            --抛出异常提示信息
            dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
            dbms_output.put_line('**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 年结期间汇总异常：会计期间: %'||p_year_month);
            raise_application_error(-20003, '**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 年结期间汇总异常，请检查');
    END proc_annual_summary_balance;



    /***********************************************************************
  NAME :acc_pack_annual_proc_annual_sync_data
  DESCRIPTION : 同步现行准则年结数据
  DATE :2021-06-01
  AUTHOR :wuyh
  ***********************************************************************/
    PROCEDURE proc_annual_sync_data(P_ENTITY_ID IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER) IS
        v_char  VARCHAR(1);
        v_errcode              VARCHAR(32); --错误编码
        v_errmsg               VARCHAR(32); --错误信息
        v_year_month VARCHAR(6);
        v_reckoning_state  VARCHAR(1);
        V_YEAR_END   VARCHAR(6);
        v_year_end_flag  VARCHAR(6);
    BEGIN
        select year_month, reckoning_state
        into v_year_month,v_reckoning_state
        from ACC_CONF_ANNUAL_PERIOD
        where ENTITY_ID = P_ENTITY_ID
          and book_code = p_bookcode
          and year_month=p_yearmonth
          and valid_is = '1'
          and audit_state='1';

        if v_year_month is null then
            v_errcode := 'AG001';
            v_errmsg := '年结期间不存在或者无效';
            select '1' into v_char from dual where 1=2;
        end if;

        if v_reckoning_state ='1' then
            v_errcode := 'AG002';
            v_errmsg := '年结期间已结转';
            select '1' into v_char from dual where 1=2;
        end if;

        select EXT_YEAR_END_FLAG into v_year_end_flag from bpluser.bbs_conf_account_set b where b.ENTITY_ID = P_ENTITY_ID;

        if v_year_end_flag is null then
            v_errcode := 'AG003';
            v_errmsg := '现行账套的年结标识未配置';
            select '1' into v_char from dual where 1=2;
        end if;

        V_YEAR_END := substr(p_yearmonth,0,4) || v_year_end_flag;


        delete from acc_ext_article_balance b
        WHERE b.ENTITY_ID = P_ENTITY_ID
          and b.year_month = V_YEAR_END;

        delete from acc_ext_ledger_balance b
        WHERE b.ENTITY_ID = P_ENTITY_ID
          and b.year_month = V_YEAR_END;

        delete from acc_ext_voucher_detail b
        WHERE exists (select 1 from acc_ext_voucher a
                      WHERE a.ENTITY_ID = P_ENTITY_ID
                        and a.year_month = V_YEAR_END
                        and b.voucher_no = a.voucher_no);

        delete from acc_ext_voucher b
        WHERE b.ENTITY_ID = P_ENTITY_ID
          and b.year_month = V_YEAR_END;

        INSERT INTO acc_ext_article_balance (
            article_id,
            ENTITY_ID,
            book_code,
            year_month,
            root_account_id,
            account_id,
            article1,
            article2,
            article3,
            article4,
            article5,
            article6,
            article7,
            article8,
            article9,
            article10,
            article11,
            article12,
            article13,
            article14,
            article15,
            article16,
            article17,
            currency_code,
            currency_cu_code,
            debit_amount,
            debit_amount_cu,
            credit_amount,
            credit_amount_cu,
            debit_amount_quarter,
            debit_amount_quarter_cu,
            credit_amount_quarter,
            credit_amount_quarter_cu,
            debit_amount_year,
            debit_amount_year_cu,
            credit_amount_year,
            credit_amount_year_cu,
            opening_balance,
            closing_balance,
            opening_balance_cu,
            closing_balance_cu,
            create_time,
            creator_id
        ) SELECT
              acc_seq_ext_article_balance.nextval,
              b.ENTITY_ID,
              b.book_code,
              b.year_month,
              b.root_account_id,
              b.ACCOUNT_ID,
              article1,
              article2,
              article3,
              article4,
              article5,
              article6,
              article7,
              article8,
              article9,
              article10,
              article11,
              article12,
              article13,
              article14,
              article15,
              article16,
              article17,
              currency_code,
              currency_cu_code,
              debit_amount,
              debit_amount_cu,
              credit_amount,
              credit_amount_cu,
              debit_amount_quarter,
              debit_amount_quarter_cu,
              credit_amount_quarter,
              credit_amount_quarter_cu,
              debit_amount_year,
              debit_amount_year_cu,
              credit_amount_year,
              credit_amount_year_cu,
              opening_balance,
              closing_balance,
              opening_balance_cu,
              closing_balance_cu,
              LOCALTIMESTAMP, -- 创建时间
              p_userid --creator_id
        FROM dmuser.dm_fin_article_balance b left join bpluser.bbs_account item
                                                       on b.ACCOUNT_ID= item.ACCOUNT_ID
        WHERE b.ENTITY_ID = P_ENTITY_ID
          and b.year_month = V_YEAR_END
          and item.final_level_is='1';

        -- TODO
        INSERT INTO accuser.acc_ext_ledger_balance (
            ledger_balance_id,
            ENTITY_ID,
            book_code,
            year_month,
            root_account_id,
            account_id,
            currency_code,
            currency_cu_code,
            debit_amount,
            debit_amount_cu,
            credit_amount,
            credit_amount_cu,
            debit_amount_quarter,
            debit_amount_quarter_cu,
            credit_amount_quarter,
            credit_amount_quarter_cu,
            debit_amount_year,
            debit_amount_year_cu,
            credit_amount_year,
            credit_amount_year_cu,
            opening_balance,
            closing_balance,
            opening_balance_cu,
            closing_balance_cu,
            create_time,
            creator_id
        )
        SELECT
            acc_seq_ext_ledger_balance.nextval,
            b.ENTITY_ID,
            b.book_code,
            b.year_month,
            b.root_account_id,
            b.ACCOUNT_ID,
            currency_code,
            currency_cu_code,
            debit_amount,
            debit_amount_cu,
            credit_amount,
            credit_amount_cu,
            debit_amount_quarter,
            debit_amount_quarter_cu,
            credit_amount_quarter,
            credit_amount_quarter_cu,
            debit_amount_year,
            debit_amount_year_cu,
            credit_amount_year,
            credit_amount_year_cu,
            opening_balance,
            closing_balance,
            opening_balance_cu,
            closing_balance_cu,
            LOCALTIMESTAMP, -- 创建时间
            p_userid --creator_id
        FROM dmuser.dm_fin_ledger_balance b left join bpluser.bbs_account item
                                                      on b.ACCOUNT_ID= item.ACCOUNT_ID
        WHERE b.ENTITY_ID = P_ENTITY_ID
          and b.year_month = V_YEAR_END
          and item.final_level_is='1';




        INSERT INTO accuser.acc_ext_voucher (
            ext_voucher_id,
            voucher_no,
            ENTITY_ID,
            book_code,
            POSTING_TYPE_CODE,
            year_month,
            effective_date,
            VOUCHER_STATUS_CODE,
            remark,
            BU_VOUCHER_NO,
            task_code,
            create_time,
            creator_id 	)
        SELECT
            acc_seq_ext_voucher.nextval,
            voucher_no,
            ENTITY_ID,
            book_code,
            POSTING_TYPE_CODE,
            year_month,
            effective_date,
            VOUCHER_STATUS_CODE,
            remark,
            BU_VOUCHER_NO,
            task_code,
            LOCALTIMESTAMP, -- 创建时间
            p_userid --creator_id
        from dmuser.dm_fin_voucher b
        WHERE b.ENTITY_ID = P_ENTITY_ID and b.year_month =V_YEAR_END;



        --写入科目专项余额数据表
        INSERT INTO accuser.acc_ext_voucher_detail (
            ext_voucher_dtl_id,
            voucher_no,
            voucher_seq_no,
            account_id,
            currency_code,
            currency_cu_code,
            ACCOUNT_ENTRY_CODE,
            AMOUNT,
            AMOUNT_CU,
            exchange_rate,
            remark,
            article1,
            article2,
            article3,
            article4,
            article5,
            article6,
            article7,
            article8,
            article9,
            article10,
            article11,
            article12,
            article13,
            article14,
            article15,
            article16,
            article17,
            task_code,
            create_time,
            creator_id
        ) SELECT
              acc_seq_ext_voucherdetail.nextval,
              b.voucher_no,
              b.voucher_seq_no,
              b.ACCOUNT_ID,
              b.currency_code,
              b.currency_cu_code,
              b.ACCOUNT_ENTRY_CODE,
              b.AMOUNT,
              b.AMOUNT_CU,
              b.exchange_rate,
              b.remark,
              b.article1,
              b.article2,
              b.article3,
              b.article4,
              b.article5,
              b.article6,
              b.article7,
              b.article8,
              b.article9,
              b.article10,
              b.article11,
              b.article12,
              b.article13,
              b.article14,
              b.article15,
              b.article16,
              b.article17,
              b.task_code,
              LOCALTIMESTAMP, -- 创建时间
              p_userid --creator_id
        from dmuser.dm_fin_voucher a,dmuser.dm_fin_voucher_detail b
        WHERE a.voucher_no = b.voucher_no
          and a.ENTITY_ID = P_ENTITY_ID
          and a.year_month = V_YEAR_END;
        acc_pack_annual.proc_annual_data_conversion(P_ENTITY_ID, p_bookcode, p_yearmonth, p_userid);
    EXCEPTION
        WHEN OTHERS THEN
            --抛出异常提示信息
            dbms_output.put_line('**SQLERRM: ' || SUBSTR(SQLERRM, 1, 200) || ', 年结期间同步数据平台数据：会计期间: %'||p_yearmonth);
    END proc_annual_sync_data;

    /***********************************************************************
  NAME :acc_pack_annual_proc_annual_trans_period
  DESCRIPTION : 过渡期，将I4账套的科目余额数据转换到I17账套作为期初余额
  PARAM: p_bookcode 为 BookI4
  DATE :2023-06-13
  AUTHOR :chenjunfeng
  ***********************************************************************/
    PROCEDURE proc_annual_trans_period(P_ENTITY_ID  IN NUMBER,
                                       p_bookcode  IN VARCHAR2,
                                       p_yearmonth IN VARCHAR2,
                                       p_userid    IN NUMBER) IS

        v_char      varchar(1);
        v_ym_count  number(11);
        v_err_code  varchar2(10);
        v_err_msg   varchar2(1000);
        v_trans_seq number(11);
        v_tran_ym   varchar2(6);
        v_last_ym   varchar2(6);
    BEGIN

        if P_ENTITY_ID is null or p_bookcode is null or p_yearmonth is null then
            v_err_code := '001';
            SELECT '*' into v_char FROM dual where 1 = 2;
        end if;

        --判断是否是过渡期当月 20230615
        begin
            SELECT t.code_code
            into v_tran_ym
            FROM acc_conf_code t
            where t.upper_code_id =
                  (SELECT a.code_id
                   FROM acc_v_conf_code a
                   where a.code_code_idx = 'TransitionPeriod')
              and t.valid_is = '1';
        EXCEPTION
            WHEN OTHERS THEN
                --抛出异常提示信息
                --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
                --RAISE EXCEPTION '[EXCEPTION]测试异常：%, %',v_text1,v_text2;
                dbms_output.put_line('[EXCEPTION]proc_annual_trans_period：' ||
                                     to_char(SQLCODE) || ';' ||
                                     substr(SQLERRM, 1, 200));
        end;

        if v_tran_ym != p_yearmonth then
            v_err_code := '008';
            SELECT '*' into v_char FROM dual where 1 = 2;
        end if;

        v_last_ym := to_char(add_months(to_date(p_yearmonth||'01','YYYYMMDD'),-1),'YYYY')||'JS';

        SELECT count(t.ledger_balance_id)
        into v_ym_count
        FROM acc_ext_ledger_balance t
        where t.book_code = p_bookcode
          and t.year_month = v_last_ym
          and t.ENTITY_ID = P_ENTITY_ID;

        if v_ym_count = 0 then
            v_err_code := '002';
            SELECT '*' into v_char FROM dual where 1 = 2;
        end if;

        v_err_code := '003';
        --记录无映射关系科目
        SELECT COALESCE(MAX(to_number(substr(t.transitional_is, -5))), 0) + 1
        INTO v_trans_seq
        FROM ACC_TEMP_LEDGER_BALANCE t
        where t.transitional_is = 'R';

        DELETE FROM ACC_TEMP_LEDGER_BALANCE t where t.year_month = p_yearmonth;

        insert into ACC_TEMP_LEDGER_BALANCE
        (TASK_CODE,
         LEDGER_BALANCE_ID,
         TRANSITIONAL_IS,
         account_id,
         account_code,
         account_c_NAME,
         ENTITY_ID,
         YEAR_MONTH,
         BOOK_CODE,
         currency_code,
         currency_cu_code,
         opening_BALANCE,
         opening_BALANCE_CU,
         CREATE_ID,
         CREATE_TIME)
        select 'R' || lpad(v_trans_seq, 5, 0) as TASK_CODE,
               t.ledger_balance_id,
               'R',
               t.ACCOUNT_ID,
               i.account_code,
               i.account_c_name,
               t.ENTITY_ID,
               p_yearmonth,
               t.book_code,
               t.currency_code,
               t.currency_cu_code,
               t.closing_balance as opening_balance,
               t.closing_balance_cu as opening_balance_cu,
               p_userid,
               sysdate
        from acc_ext_ledger_balance t
                 left join bpluser.bbs_account i
                           on i.ACCOUNT_ID = t.ACCOUNT_ID
        where t.year_month = v_last_ym
          and t.book_code = p_bookcode
          and t.ENTITY_ID = P_ENTITY_ID
          and i.final_level_is = '1'
          and not exists (select 1
                          from bpluser.bbs_conf_account_mapping m
                          where m.other_account_code = i.account_code
                            and m.other_account_id = i.ACCOUNT_ID
                            and m.valid_is = '1')
        /*order by t.ACCOUNT_ID,
              i.account_code,
              i.account_c_name,
              t.ENTITY_ID,
              t.year_month,
              t.book_code,
              t.currency_code,
              t.currency_cu_code*/;

        v_err_code := '004';
        --正式转换映射科目
        SELECT COALESCE(MAX(to_number(substr(t.transitional_is, -5))), 0) + 1
        INTO v_trans_seq
        FROM ACC_TEMP_LEDGER_BALANCE t
        where t.transitional_is = 'T';

        insert into ACC_TEMP_LEDGER_BALANCE
        (TASK_CODE,
         LEDGER_BALANCE_ID,
         TRANSITIONAL_IS,
         account_id,
         account_code,
         account_c_NAME,
         ENTITY_ID,
         YEAR_MONTH,
         BOOK_CODE,
         currency_code,
         currency_cu_code,
         opening_BALANCE,
         opening_BALANCE_CU,
         CREATE_ID,
         CREATE_TIME)
        select 'T' || lpad(v_trans_seq, 5, 0) as TASK_CODE,
               t.ledger_balance_id,
               'T',
               p.base_account_id,
               p.base_account_code,
               '' as account_c_name,
               t.ENTITY_ID,
               p_yearmonth,
               'BookI17' as book_code,
               t.currency_code,
               t.currency_cu_code,
               (case when i.account_code = '1002/01/01/01/' then
                             t.closing_balance - coalesce((select sum(a.closing_balance) from acc_ext_ledger_balance a , bpluser.bbs_account v
                                                           where  a.account_id = v.account_id and v.upper_account_id = i.account_id
                                                             and a.year_month = t.year_month and a.book_code = t.book_code
                                                             and a.entity_id = t.entity_id and a.currency_code = t.currency_code
                                                             and a.currency_cu_code = t.currency_cu_code),0)
                     else t.closing_balance end) as opening_balance,
               (case when i.account_code = '1002/01/01/01/' then
                             t.closing_balance_cu - coalesce((select sum(a.closing_balance_cu) from acc_ext_ledger_balance a , bpluser.bbs_account v
                                                              where  a.account_id = v.account_id and v.upper_account_id = i.account_id
                                                                and a.year_month = t.year_month and a.book_code = t.book_code
                                                                and a.entity_id = t.entity_id and a.currency_code = t.currency_code
                                                                and a.currency_cu_code = t.currency_cu_code),0)
                     else t.closing_balance_cu end) as opening_balance_cu,
               p_userid,
               sysdate
        from acc_ext_ledger_balance t
                 left join bpluser.bbs_account i
                           on i.ACCOUNT_ID = t.ACCOUNT_ID
                               and i.book_code = t.book_code
                 left join bpluser.bbs_conf_account_mapping p
                           on p.other_account_code = i.account_code
                               and p.other_book_code = t.book_code
        where t.year_month = v_last_ym
          and t.book_code = p_bookcode
          and t.ENTITY_ID = P_ENTITY_ID
          and (i.final_level_is = '1' /*or i.account_code = '1002/01/01/01/'*/)
            /*and exists (select 1
              from bpluser.bbs_conf_account_mapping m
             where m.other_account_code = i.account_code
               and m.other_account_id = i.ACCOUNT_ID
               and m.valid_is = '1')*/
          and p.valid_is = '1'
        /*and not exists(
        select * from bpluser.bbs_conf_account_reserve r where t.entity_id = r.entity_id
        and r.book_code = t.book_code and r.account_id = t.account_id
       )*/
        /*order by p.root_account_id,
              i.account_code,
              i.account_c_name,
              t.ENTITY_ID,
              t.year_month,
              t.book_code,
              t.currency_code,
              t.currency_cu_code*/;

        COMMIT;

        --专项
        v_ym_count := 0;
        SELECT count(t.article_id)
        into v_ym_count
        FROM acc_ext_article_balance t
        where t.book_code = p_bookcode
          and t.year_month = v_last_ym
          and t.ENTITY_ID = P_ENTITY_ID;

        if v_ym_count = 0 then
            v_err_code := '005';
            SELECT '*' into v_char FROM dual where 1 = 2;
        end if;

        v_err_code := '006';
        --记录无映射关系科目
        SELECT COALESCE(MAX(to_number(substr(t.transitional_is, -5))), 0) + 1
        INTO v_trans_seq
        FROM ACC_TEMP_ARTICLE_BALANCE t
        where t.transitional_is = 'R';

        DELETE FROM acc_temp_article_balance;

        insert into ACC_TEMP_ARTICLE_BALANCE
        (TASK_CODE,
         ARTICLE_ID,
         TRANSITIONAL_IS,
         BACK_IS,
         ENTITY_ID,
         BOOK_CODE,
         YEAR_MONTH,
            --root_account_id,
         account_id,
         account_code,
         account_c_NAME,
         ARTICLE,
         ARTICLE1,
         ARTICLE2,
         ARTICLE3,
         ARTICLE4,
         ARTICLE5,
         ARTICLE6,
         ARTICLE7,
         ARTICLE8,
         ARTICLE9,
         ARTICLE10,
         ARTICLE11,
         ARTICLE12,
         ARTICLE13,
         ARTICLE14,
         ARTICLE15,
         ARTICLE16,
         ARTICLE17,
         currency_code,
         currency_cu_code,
         opening_BALANCE,
         closing_BALANCE,
         opening_BALANCE_CU,
         closing_BALANCE_CU,
         CREATE_TIME,
         CREATOR_ID)
        select 'R' || lpad(v_trans_seq, 5, 0) as TASK_CODE,
               t.article_id,
               'R',
               '',
               t.ENTITY_ID,
               t.book_code,
               p_yearmonth,
               t.ACCOUNT_ID,
               i.account_code,
               i.account_c_name,
               t.article,
               t.article1,
               t.article2,
               t.article3,
               t.article4,
               t.article5,
               t.article6,
               t.article7,
               t.article8,
               t.article9,
               t.article10,
               t.article11,
               t.article12,
               t.article13,
               t.article14,
               t.article15,
               t.article16,
               t.article17,
               t.currency_code,
               t.currency_cu_code,
               t.closing_balance as opening_balance,
               t.closing_balance as closing_balance,
               t.closing_balance_cu as opening_balance_cu,
               t.closing_balance_cu as closing_balance_cu,
               sysdate,
               p_userid
        from acc_ext_article_balance t
                 left join bpluser.bbs_account i
                           on i.ACCOUNT_ID = t.ACCOUNT_ID
        where t.year_month = v_last_ym
          and t.book_code = p_bookcode
          and t.ENTITY_ID = P_ENTITY_ID
          and i.final_level_is = '1'
          and not exists (select 1
                          from bpluser.bbs_conf_account_mapping m
                          where m.other_account_code = i.account_code
                            and m.other_account_id = i.ACCOUNT_ID
                            and m.valid_is = '1')
        /*order by t.ACCOUNT_ID,
              i.account_code,
              i.account_c_name,
              t.ENTITY_ID,
              t.year_month,
              t.book_code,
              t.currency_code,
              t.currency_cu_code*/;

        v_err_code := '007';
        --正式转换映射科目
        SELECT COALESCE(MAX(to_number(substr(t.transitional_is, -5))), 0) + 1
        INTO v_trans_seq
        FROM ACC_TEMP_ARTICLE_BALANCE t
        where t.transitional_is = 'T';

        insert into ACC_TEMP_ARTICLE_BALANCE
        (TASK_CODE,
         ARTICLE_ID,
         TRANSITIONAL_IS,
         BACK_IS,
         ENTITY_ID,
         BOOK_CODE,
         YEAR_MONTH,
            --root_account_id,
         account_id,
         account_code,
         account_c_NAME,
         ARTICLE,
         ARTICLE1,
         ARTICLE2,
         ARTICLE3,
         ARTICLE4,
         ARTICLE5,
         ARTICLE6,
         ARTICLE7,
         ARTICLE8,
         ARTICLE9,
         ARTICLE10,
         ARTICLE11,
         ARTICLE12,
         ARTICLE13,
         ARTICLE14,
         ARTICLE15,
         ARTICLE16,
         ARTICLE17,
         currency_code,
         currency_cu_code,
         opening_BALANCE,
         closing_BALANCE,
         opening_BALANCE_CU,
         closing_BALANCE_CU,
         CREATE_TIME,
         CREATOR_ID)
        select 'T' || lpad(v_trans_seq, 5, 0) as TASK_CODE,
               t.article_id,
               'T',
               '',
               t.ENTITY_ID,
               'BookI17' book_code,
               p_yearmonth,
               p.base_account_id,
               p.base_account_code,
               '',
               t.article,
               t.article1,
               t.article2,
               t.article3,
               t.article4,
               t.article5,
               t.article6,
               t.article7,
               t.article8,
               t.article9,
               t.article10,
               t.article11,
               t.article12,
               t.article13,
               t.article14,
               t.article15,
               t.article16,
               t.article17,
               t.currency_code,
               t.currency_cu_code,
               t.closing_balance as opening_balance,
               t.closing_balance as closing_balance,
               t.closing_balance_cu as opening_balance_cu,
               t.closing_balance_cu as closing_balance_cu,
               sysdate,
               p_userid
        from acc_ext_article_balance t
                 left join bpluser.bbs_account i
                           on i.ACCOUNT_ID = t.ACCOUNT_ID
                               and t.book_code = i.book_code
                 left join bpluser.bbs_conf_account_mapping p
                           on p.other_account_code = i.account_code
                               and p.other_book_code = t.book_code
        where t.year_month = v_last_ym
          and t.book_code = p_bookcode
          and t.ENTITY_ID = P_ENTITY_ID
          and (i.final_level_is = '1' /*or i.account_code = '1002/01/01/01/'*/)
            /*and exists (select 1
              from bpluser.bbs_conf_account_mapping m
             where m.other_account_code = i.account_code
               and m.other_account_id = i.ACCOUNT_ID
               and m.valid_is = '1')*/
          and p.valid_is = '1'
        /*order by p.root_account_id,
              p.base_account_code,
              t.ENTITY_ID,
              t.year_month,
              t.book_code,
              t.currency_code,
              t.currency_cu_code*/;
        dbms_output.put_line('提取转换数据成功！ ');
        commit;
    EXCEPTION
        WHEN OTHERS THEN
            --RAISE NOTICE '**SQLERRM: %, 插入数据异常，请检查',
            --SQLERRM;
            rollback;
            if v_err_code = '001' then
                v_err_msg := '001 P_ENTITY_ID is null or p_bookcode is null or p_yearmonth is null !!! ' ||
                             dbms_utility.format_error_backtrace() ||
                             SUBSTR(SQLERRM, 1, 200);
            elsif v_err_code = '002' then
                v_err_msg := '002 acc_ext_ledger_balance is null !!! ' ||
                             dbms_utility.format_error_backtrace() ||
                             SUBSTR(SQLERRM, 1, 200);
            elsif v_err_code = '003' then
                v_err_msg := '003 insert into type of "R" is error !!! ' ||
                             dbms_utility.format_error_backtrace() ||
                             SUBSTR(SQLERRM, 1, 200);
            elsif v_err_code = '004' then
                v_err_msg := '004 insert into type of "T" is error !!! ' ||
                             dbms_utility.format_error_backtrace() ||
                             SUBSTR(SQLERRM, 1, 200);
            elsif v_err_code = '005' then
                v_err_msg := '005 acc_ext_article_balance is null !!! ' ||
                             dbms_utility.format_error_backtrace() ||
                             SUBSTR(SQLERRM, 1, 200);
            elsif v_err_code = '006' then
                v_err_msg := '006 insert into type of "R" is error !!! ' ||
                             dbms_utility.format_error_backtrace() ||
                             SUBSTR(SQLERRM, 1, 200);
            elsif v_err_code = '007' then
                v_err_msg := '007 insert into type of "T" is error !!! ' ||
                             dbms_utility.format_error_backtrace() ||
                             SUBSTR(SQLERRM, 1, 200);
            elsif v_err_code = '008' then
                v_err_msg := '008 不是过渡期会计期间或没有配置过渡期期间，请检查 !!! ' ||
                             dbms_utility.format_error_backtrace() ||
                             SUBSTR(SQLERRM, 1, 200);
            else
                v_err_msg := dbms_utility.format_error_backtrace() ||
                             SUBSTR(SQLERRM, 1, 200);
            end if;
            dbms_output.put_line('v_err_msg: '||v_err_msg);
    END proc_annual_trans_period;


    /***********************************************************************
  NAME :acc_pack_annual_proc_annual_trans_revoke
  DESCRIPTION : 过渡期，将预计现金流对冲现行入账的而特殊处理保费和佣金的科目，因为没有做实际现金,需要对冲特殊处理的科目
  PARAM: p_bookcode 为 BookI17
  DATE :2023-06-14
  AUTHOR :chenjunfeng
  ***********************************************************************/
    /*PROCEDURE proc_annual_trans_revoke(P_ENTITY_ID IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_userid IN NUMBER) IS


  v_char       varchar(1);
  v_ym_count number(11);
  v_err_code varchar2(10);
  v_err_msg varchar2(1000);
  v_trans_seq number(11);
  BEGIN

  if P_ENTITY_ID is null or p_bookcode is null or p_yearmonth is null then
    v_err_code := '001';
    SELECT '*' into v_char FROM dual where 1=2;
  end if;

  SELECT count(t.ledger_balance_id)
    into v_ym_count
    FROM acc_buss_ledger_balance t
   where t.book_code = p_bookcode
     and t.year_month = p_yearmonth
     and t.ENTITY_ID = P_ENTITY_ID;

  if v_ym_count = 0 then
    v_err_code := '002';
    SELECT '*' into v_char FROM dual where 1=2;
  end if;

  v_err_code := '003';
  --记录无映射关系科目
SELECT COALESCE(MAX(to_number(substr(t.transitional_is, -5))), 0) + 1 INTO v_trans_seq
  FROM ACC_TEMP_LEDGER_BALANCE t
 where t.transitional_is = 'V';--冲销标识

insert into ACC_TEMP_LEDGER_BALANCE
  (TASK_CODE,
   LEDGER_BALANCE_ID,
   TRANSITIONAL_IS,
   account_id,
   account_code,
   account_c_NAME,
   ENTITY_ID,
   YEAR_MONTH,
   BOOK_CODE,
   currency_code,
   currency_cu_code,
   opening_BALANCE,
   opening_BALANCE_CU,
   CREATE_ID,
   CREATE_TIME)
  select 'V' || lpad(v_trans_seq, 5, 0) as TASK_CODE,
         t.ledger_balance_id,
         'V',
         t.ACCOUNT_ID,
         i.account_code,
         i.account_c_name,
         t.ENTITY_ID,
         t.year_month,
         t.book_code,
         t.currency_code,
         t.currency_cu_code,
         t.opening_balance as opening_balance,
         t.opening_balance_cu as opening_balance_cu,
         p_userid,
         sysdate
    from acc_buss_ledger_balance t
    left join bpluser.bbs_account i
      on i.ACCOUNT_ID = t.ACCOUNT_ID
   where t.year_month = p_yearmonth
     and t.book_code = p_bookcode
     and t.ENTITY_ID = P_ENTITY_ID
     and i.final_level_is = '1'
     and not exists (select 1
            from bpluser.bbs_conf_account_mapping m
           where m.other_account_code = i.account_code
             and m.other_account_id = i.ACCOUNT_ID)
   order by t.ACCOUNT_ID,
            i.account_code,
            i.account_c_name,
            t.ENTITY_ID,
            t.year_month,
            t.book_code,
            t.currency_code,
            t.currency_cu_code;
    COMMIT;


  EXCEPTION
  WHEN OTHERS THEN
    --RAISE NOTICE '**SQLERRM: %, 插入数据异常，请检查',
    --SQLERRM;
    rollback;
    if v_err_code = '001' then
      v_err_msg := 'P_ENTITY_ID is null or p_bookcode is null or p_yearmonth is null !!! '||dbms_utility.format_error_backtrace()||SUBSTR(SQLERRM, 1, 200);
    elsif v_err_code ='002' then
      v_err_msg := 'acc_buss_ledger_balance is null !!! ' ||dbms_utility.format_error_backtrace()||SUBSTR(SQLERRM, 1, 200);
    elsif v_err_code ='003' then
      v_err_msg := 'insert into type of "R" is error !!! '||dbms_utility.format_error_backtrace()||SUBSTR(SQLERRM, 1, 200);
    elsif v_err_code ='004' then
      v_err_msg := 'insert into type of "T" is error !!! '||dbms_utility.format_error_backtrace()||SUBSTR(SQLERRM, 1, 200);
    else
      v_err_msg := SUBSTR(SQLERRM, 1, 200);
    end if;

  END proc_annual_trans_revoke;*/
end acc_pack_annual;
/

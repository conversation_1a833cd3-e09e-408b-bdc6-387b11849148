DELETE FROM bpluser.bbs_conf_model_mapping
        WHERE source_table= 'ACC_DAP_ENTRY_DATA'
          and mapping_table = 'ACC_BUSS_VOUCHER_DETAIL' ;	
		  
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1045, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'EXTEND_COLUMN4', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE4', 'Factor', '计量因子', '计量因子', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1046, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'EXTEND_COLUMN6', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE6', 'Business Line', '业务线', '業務線', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1047, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'CASHFLOW_ARTICLE', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE7', 'Cashflow Article', '现金流专项', '現金流專項', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1044, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'EVALUATE_APPROACH', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE3', 'Assessment Method', '评估方法', '评估方法', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1043, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'DEPT_MENT_ID', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE2', 'Dept Article', '部门专项', '部門專項', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1042, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'RISK_CODE', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE1', 'Risk  Article', '风险专项', '風險專項', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1058, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'CHANNEL_CODE', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE18', 'Channel Code', '渠道', '渠道', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1053, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'CURRENT_PREVIOUS_IS', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE15', 'Current Previous Is', '当年/往年', '當年/往年', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1049, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'ICG_NO', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE9', 'Group ID', '合同组', '合同組', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1052, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'EXTEND_COLUMN8', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE14', 'Profit Or Loss', '盈亏标识', '盈虧標識', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1055, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'RISK_CLASS_CODE', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE10', 'Risk Class Code', '险类', '險類', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1056, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'CLAUSE_CODE', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE11', 'Clause Code', '险别/条款代码', '險別/條款代碼', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1048, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'PORTFOLIO_NO', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE8', 'Portfolio ID', '合同组合', '合同組合', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1060, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'PRODUCT_ID', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE20', 'Product Id', '产品段', '產品段', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1059, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'DETAIL_ID', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE19', 'Detail Id', '明细段', '明細段', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1051, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'POSTING_TYPE_CODE', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE13', 'Voucher Type', '凭证类型', '憑證類型', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1054, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'POLICY_NO', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE5', 'Policy No', '保单号', '保單號', NULL, NULL, '0', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1057, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'RI_BROKER_CODE', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE16', 'RI Broker Code', '对手方', '對手方', NULL, NULL, '0', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1062, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'BUDGET_CODE', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE22', 'Budget Code', '单据类型', '單據類型', NULL, NULL, '0', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1050, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'TREATY_CODE', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE12', 'Treaty Code', '合约号', '合約號', NULL, NULL, '0', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1061, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'SUP_PRODUCT_ID', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE21', 'Sur Product Id', '补充产品段', '補充產品段', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1069, 1, 'ACC', 'ARTICLE', 'ACC_DAP_ENTRY_DATA', 'BACKTRACKING_METHOD', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE17', 'Backtracking Method', '追溯方法', '追溯方法', '', '(CASE WHEN column_rule =''1'' THEN  ''(case when (select b.record_flag from acc_conf_account_article b, acc_conf_article_code a  where b.article_code = a.article_code and  a.article_mapping_code = ''''''||mapping_column||''''''
				and a.check_is = ''''1'''' and b.account_id = {account_id} ) = ''''2'''' then null else ''|| source_column||'' end) '' ELSE source_column  end)', '1', NULL, NULL, NULL);

commit;


DELETE from  bpluser.bbs_conf_model_mapping
        WHERE source_table= 'ACC_EXT_VOUCHER_DETAIL'
          and mapping_table = 'ACC_BUSS_VOUCHER_DETAIL' ;	
		  
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1063, 1, 'ACC', 'ARTICLE', 'ACC_EXT_VOUCHER_DETAIL', 'ARTICLE2', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE20', 'Product Id', '产品段', '產品段', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1064, 1, 'ACC', 'ARTICLE', 'ACC_EXT_VOUCHER_DETAIL', 'ARTICLE4', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE19', 'Detail Id', '明细段', '明細段', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1065, 1, 'ACC', 'ARTICLE', 'ACC_EXT_VOUCHER_DETAIL', 'ARTICLE5', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE2', 'Dept Article', '部门段', '部門段', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1066, 1, 'ACC', 'ARTICLE', 'ACC_EXT_VOUCHER_DETAIL', 'ARTICLE6', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE7', 'Cashflow Article', '现金流段', '現金流段', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1067, 1, 'ACC', 'ARTICLE', 'ACC_EXT_VOUCHER_DETAIL', 'ARTICLE7', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE18', 'Channel Code', '渠道段', '渠道段', NULL, NULL, '1', NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_model_mapping (mapping_id, entity_id, system_code, mapping_type, source_table, source_column, mapping_table, mapping_column, mapping_e_name, mapping_c_name, mapping_l_name, column_rule, rule_config, valid_is, create_time, creator_id, remark) VALUES (1068, 1, 'ACC', 'ARTICLE', 'ACC_EXT_VOUCHER_DETAIL', 'ARTICLE8', 'ACC_BUSS_VOUCHER_DETAIL', 'ARTICLE21', 'Sup Product Id', '补充产品段', '補充產品段', NULL, NULL, '1', NULL, NULL, NULL);
COMMIT;


DELETE from bbs_conf_account_buss t where t.buss_type = 'ANNUAL';
INSERT INTO bpluser.bbs_conf_account_buss (buss_account_id, entity_id, book_code, buss_type, account_id, account_code, final_type, valid_is, remark, creator_id, create_time, serial_no, account_entry_code) VALUES (2, 1, '02', 'ANNUAL', 1939323, '************', '4', '1', '往年留存收益', NULL, NULL, NULL, NULL);
INSERT INTO bpluser.bbs_conf_account_buss (buss_account_id, entity_id, book_code, buss_type, account_id, account_code, final_type, valid_is, remark, creator_id, create_time, serial_no, account_entry_code) VALUES (1, 1, '02', 'ANNUAL', 1938850, '************', '3', '1', '本年度留存收益', NULL, NULL, NULL, NULL);
commit;
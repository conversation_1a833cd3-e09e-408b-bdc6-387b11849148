create or replace package acc_pack_multireconcil is

  FUNCTION func_account_calculate(p_entity_id     IN NUMBER,
                                  p_bookcode     IN VARCHAR2,
                                  p_yearmonth    IN VARCHAR2,
                                  p_expression   IN VARCHAR2,
                                  p_recon_rst_id IN NUMBER,
                                  p_recon_type   IN VARCHAR2) RETURN NUMBER;
  PROCEDURE proc_add_multireconcilhis(p_entity_id  IN NUMBER,
                                      p_bookcode  IN VARCHAR2,
                                      p_yearmonth IN VARCHAR2,
                                      p_serial_no IN NUMBER);
  PROCEDURE proc_multireconcil(p_entity_id  IN NUMBER,
                               p_bookcode  IN VARCHAR2,
                               p_yearmonth IN VARCHAR2,
                               p_userid    IN NUMBER);
  PROCEDURE proc_save_calculate_detail(p_entity_id     number,
                                       p_bookcode     varchar2,
                                       p_yearmonth    varchar2,
                                       p_account_id      number,
                                       p_recon_rst_id number,
                                       p_recon_type   varchar2,
                                       p_book_flaf    varchar2);

end acc_pack_multireconcil;
/
create or replace package body acc_pack_multireconcil is
--
  /***********************************************************************
  DESCRIPTION : 规则脚本解析计算函数-科目对账时
  DATE :2020-12-29
  AUTHOR :YINXH
  -------
  MODIFY LOG
  UPDATE DATE :
  UPDATE BY   : LY
  UPDATE DESC : 对账来源：根据账套是否有会计期间,存在取入账数据/不存在取现行准则数据
***********************************************************************/
  FUNCTION func_account_calculate(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2,
                    p_expression IN VARCHAR2, p_recon_rst_id IN NUMBER, p_recon_type IN VARCHAR2) RETURN NUMBER IS

    v_tmp_expression  VARCHAR2(500); --去除运算符的规则计算脚本
    v_expression      VARCHAR2(500); --完整解析后的规则计算脚本
    v_book_code       VARCHAR2(500); --当前系统账套
    v_account_id         NUMBER(16); --科目ID
    v_item_value      NUMBER(32, 8); --科目对应的数值
    v_item_result     NUMBER(32, 8); --计算结果值
    rec_item          BPL_PACK_COMMON.Type_Array_Table;
    v_rec_item        VARCHAR2(200);
  BEGIN

  --判断当前系统是否有此账套
  SELECT max(book_code) INTO v_book_code
    FROM acc_conf_accountperiod
  WHERE entity_id = p_entity_id--核算单位ID
    AND book_code = p_bookcode --账套编码
    AND year_month = p_yearmonth --会计期间
    AND valid_is = '1'
    AND ROWNUM=1;
  --给解析表达式初始值
    v_expression := p_expression;


    --计算场景：四则运算示例：[2020001]/[2020002]+([2020003]-[2020004])*0.3-202
    --rec_item:=bpl_pack_common.Func_Arithmometer_Destructor(v_expression, '\[(.+?)\]', true);
    for rec_item in (SELECT DISTINCT regexp_substr(v_expression, '\[(.+?)\]', 1, LEVEL) AS cal_item FROM dual CONNECT BY regexp_substr(v_expression, '\[(.+?)\]', 1, LEVEL) IS NOT NULL) LOOP
      v_rec_item:=regexp_replace(rec_item.cal_item, '\[|\]');

        -- 1.3.1 计算公式中存在当前计算因子
        IF instr(p_expression, v_rec_item) > 0 THEN
           dbms_output.put_line('12'||v_rec_item);
            -- 1.3.1.2 查询判断计算项是科目项是否已配置，
            select account_id  into v_account_id from
						(SELECT ba.account_id
              FROM bpluser.bbs_v_account ba
             WHERE ba.account_code = v_rec_item
               AND ba.entity_id = p_entity_id --核算单位ID
               AND ba.book_code = p_bookcode --账套编码
               AND ba.valid_is = '1'
						   AND not exists (select 1 from bpluser.bbs_conf_account_set b where ba.entity_id = b.entity_id and ba.book_code =b.book_code )
               AND ROWNUM=1
						UNION
			        SELECT ba.account_id
              FROM bpluser.bbs_account ba
             WHERE ba.account_code = v_rec_item
               AND ba.entity_id = p_entity_id --核算单位ID
               AND ba.book_code = p_bookcode --账套编码
							 AND exists (select 1 from bpluser.bbs_conf_account_set b where ba.entity_id = b.entity_id and ba.book_code =b.book_code )
               AND ba.valid_is = '1'
               AND ROWNUM=1) ;
      --当前系统有此账套
      IF v_book_code IS NOT NULL THEN
        SELECT SUM(case when avd.account_entry_code='C' then avd.amount_cu * -1 else avd.amount_cu end)
          INTO v_item_value
          FROM ACC_BUSS_VOUCHER_DETAIL avd,
               ACC_BUSS_VOUCHER       avr
         WHERE avd.voucher_id = avr.voucher_id
           AND avr.entity_id = p_entity_id --核算单位ID
           AND avr.book_code = p_bookcode --账套编码
           AND avr.year_month = p_yearmonth --会计期间
           AND avd.account_id = v_account_id
           AND avr.valid_is = '1'
           AND avr.audit_state = '1';
         acc_pack_multireconcil.proc_save_calculate_detail(p_entity_id, p_bookcode, p_yearmonth, v_account_id, p_recon_rst_id, p_recon_type, '1');
      ELSE
        -- 现行准则科目(每个业务单位只同步一个现行账套)
        SELECT SUM(debit_amount_cu-credit_amount_cu)
          INTO v_item_value
          FROM acc_ext_ledger_balance ad
         WHERE ad.entity_id = p_entity_id --核算单位ID
           AND ad.book_code = p_bookcode --账套编码
           AND ad.year_month = p_yearmonth --会计期间
           AND ad.account_id = v_account_id;
         acc_pack_multireconcil.proc_save_calculate_detail(p_entity_id, p_bookcode, p_yearmonth, v_account_id, p_recon_rst_id, p_recon_type, '2');
      END IF;
     dbms_output.put_line('22'||v_expression);
          dbms_output.put_line(v_rec_item);

      -- 未取到科目值，补0
      if v_item_value is null then
        v_item_value := 0.00;
      end if;
            --如果查询到值，用正则(需转义处理)匹配替换公式的对应科目编码
            IF v_item_value > 0 THEN
              -- 当科目对应值为正数时，直接替换[科目编码]为科目值(字符类型)
              v_expression := REPLACE(v_expression, ('[' || v_rec_item || ']'), cast(v_item_value as VARCHAR));
            ELSIF v_item_value < 0 THEN
              -- 当科目对应值为负数时，需要添加小括号
              v_expression := REPLACE(v_expression, ('[' || v_rec_item || ']'), '(' || cast(v_item_value as VARCHAR) || ')');
            ELSE
              -- 若查询不到科目对应的值，作为除数时，需要替换为1
              IF instr(p_expression, ('/' || '[' || v_rec_item || ']')) > 0 THEN
                v_expression := REPLACE(v_expression, ('/' || '[' || v_rec_item || ']'), '/1');
              ELSE
                --不作为除数时，直接替换为0
                v_expression := REPLACE(v_expression, ('[' || v_rec_item || ']'), '0');
              END IF;
            END IF;
        END IF;
      END LOOP;


    --拼接执行SQL脚本，返回计算值
      EXECUTE immediate 'select (' || v_expression || ')' || ' from dual' INTO v_item_result ;
    --返回计算结果值
    RETURN v_item_result;
  EXCEPTION
    WHEN OTHERS THEN
      --RAISE INFO '**异常公式：%,%',p_bookcode,p_expression;
      --RAISE INFO '**异常信息：%',SQLERRM;
      dbms_output.put_line('**异常信息：' || SUBSTR( SQLERRM, 1, 200)||dbms_utility.format_error_backtrace());
    RETURN NULL;

    -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息
  END func_account_calculate;

  /*-------保存对准则对账轨迹信息
   MODIFY LOG

    UPDATE DATE :
    UPDATE BY   :
    UPDATE DESC :
  ***********************************************************************/
  PROCEDURE proc_add_multireconcilhis(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2,
                                      p_yearmonth IN VARCHAR2, p_serial_no IN NUMBER) IS

  BEGIN

    -- 写入业务日志明细轨迹表
    INSERT INTO acc_buss_multi_reconrstdtlhis
       (recon_rst_dtl_his_id,
        recon_rst_dtl_id,
        recon_rst_id,
        source_type,
        recon_type,
        business_id,
        business_no,
        LAST_ACCOUNTING_DATE,
        risk_code,
        dept_id,
        account_id,
        account_entry_code,
        currency_code,
        currency_cu_code,
        exchange_rate,
        amount,
        amount_cu,
        creator_id,
        create_time,
        UPDATOR_ID,
        UPDATE_TIME)
      SELECT acc_seq_buss_mul_recrstdtlhis.nextval,
          recon_rst_dtl_id,
          recon_rst_id,
          source_type,
          recon_type,
          business_id,
          business_no,
          LAST_ACCOUNTING_DATE,
          risk_code,
          dept_id,
          account_id,
          account_entry_code,
          currency_code,
          currency_cu_code,
          exchange_rate,
          amount,
          amount_cu,
          creator_id,
          create_time,
          UPDATOR_ID,
          UPDATE_TIME
        FROM acc_buss_multi_reconrstdtl b
       WHERE b.recon_rst_id IN ( SELECT recon_rst_id FROM acc_buss_multi_reconrst a
       WHERE  a.entity_id = p_entity_id
          AND a.book_code = p_bookcode
          AND a.year_month = p_yearmonth
          AND a.serial_no =  p_serial_no);



    -- 写入业务日志明细轨迹表
    INSERT INTO acc_buss_multi_reconrsthis
       (RECON_RST_HIS_ID,
        RECON_RST_ID,
        entity_id,
        BOOK_CODE,
        YEAR_MONTH,
        SERIAL_NO,
        MULTI_SCENARIO_ID,
        RECON_ID,
        account_code_BASE,
        account_code_OTHER,
        book_code_other,
        ACCOUNT_BALANCE_BASE,
        ACCOUNT_BALANCE_OTHER,
        RECONCIL_RESULT,
        CREATOR_ID,
        CREATE_TIME,
        UPDATOR_ID,
        UPDATE_TIME)
      SELECT acc_seq_multireconcilresulthis.nextval,
             RECON_RST_ID,
             entity_id,
             BOOK_CODE,
             YEAR_MONTH,
             SERIAL_NO,
             MULTI_SCENARIO_ID,
             RECON_ID,
             account_code_BASE,
             account_code_OTHER,
             book_code_other,
             ACCOUNT_BALANCE_BASE,
             ACCOUNT_BALANCE_OTHER,
             RECONCIL_RESULT,
             CREATOR_ID,
             CREATE_TIME,
             UPDATOR_ID,
             UPDATE_TIME
        FROM acc_buss_multi_reconrst b
       WHERE  entity_id = p_entity_id
            AND book_code = p_bookcode
            AND year_month = p_yearmonth
            AND serial_no =  p_serial_no;

    -- 清除业务操作日志明细表的旧数据
  DELETE FROM acc_buss_multi_reconrstdtl b
     WHERE b.recon_rst_id IN ( SELECT recon_rst_id FROM acc_buss_multi_reconrst a
       WHERE  a.entity_id = p_entity_id
          AND a.book_code = p_bookcode
          AND a.year_month = p_yearmonth
          AND a.serial_no =  p_serial_no);

    DELETE FROM acc_buss_multi_reconrst
     WHERE entity_id = p_entity_id
            AND book_code = p_bookcode
            AND year_month = p_yearmonth
            AND serial_no =  p_serial_no;

   -- COMMIT; --提交事务
  EXCEPTION
    WHEN OTHERS THEN
    --raise EXCEPTION '**SQLERRM：% 【插入业务日志轨迹表异常，请检查】', SQLERRM;
    dbms_output.put_line('**SQLERRM：% 【插入业务日志轨迹表异常，请检查】' || to_char(SQLCODE) || '::'|| SUBSTR(SQLERRM, 1, 200)||dbms_utility.format_error_backtrace());
  END proc_add_multireconcilhis;

  /***********************************************************************
  NAME :ACC_PROC_MULTIRECONCIL
  DESCRIPTION :多准则账开始
  DATE :2020-10-31
  AUTHOR :wuyh
  -------
  MODIFY LOG
  UPDATE DATE :
  UPDATE BY : LY
  UPDATE DESC :
  ***********************************************************************/
  PROCEDURE proc_multireconcil(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2,
                               p_yearmonth IN VARCHAR2, p_userid IN NUMBER) IS

  V_COUNT      NUMBER(10);
  V_LOG_ID     NUMBER(10);
  V_NUMERIC    NUMBER(10);
  V_PROC_ID    NUMBER(10);
  V_SERIAL_NO  NUMBER(10);

  BEGIN

  --获取多准则对账当前次数
  SELECT COALESCE(MAX(serial_no), 0)
    INTO v_serial_no
    FROM acc_buss_multi_reconrst T
   WHERE t.entity_id = p_entity_id
     and t.book_code = p_bookcode
     and t.year_month = p_yearmonth;

  acc_pack_multireconcil.proc_add_multireconcilhis(p_entity_id, p_bookcode, p_yearmonth, v_serial_no);

  --根据有效的对账规则生成结果
  INSERT INTO acc_buss_multi_reconrst (
              recon_rst_id,
              entity_id,
              book_code,
              year_month,
              serial_no,
              multi_scenario_id,
              recon_id,
              account_code_base,
              account_code_other,
              book_code_other,
              ACCOUNT_BALANCE_base,
              ACCOUNT_BALANCE_other,
              --reconcil_result,
              creator_id,
              create_time)
      SELECT acc_seq_multireconcilresult.nextval as recon_rst_id,
            -- mt.recon_rst_id,
             mt.entity_id,
             mt.book_code,
             p_yearmonth,
             v_serial_no + 1,
             mt.multi_scenario_id,
             mt.recon_id,
             mt.account_code_base,
             mt.account_code_other,
             mt.book_code_other,
             acc_pack_multireconcil.func_account_calculate(mt.entity_id, mt.book_code, p_yearmonth, mt.account_code_base,acc_seq_multireconcilresult.currval, 'base')ACCOUNT_BALANCE_base,
             acc_pack_multireconcil.func_account_calculate(mt.entity_id, mt.book_code_other, p_yearmonth, mt.account_code_other, acc_seq_multireconcilresult.currval, 'other')ACCOUNT_BALANCE_other,
             --mt.ACCOUNT_BALANCE_base,
             --mt.ACCOUNT_BALANCE_other,
             --(CASE WHEN mt.ACCOUNT_BALANCE_base = mt.ACCOUNT_BALANCE_other THEN '1' ELSE '0' END) reconcil_result,
             p_userid,
             LOCALTIMESTAMP
        FROM (SELECT cc.entity_id,
                 cc.book_code,
                 sce.multi_scenario_id as multi_scenario_id,
                 cc.recon_id,
                 cc.account_code_base,
                 cc.account_code_other,
                 cc.book_code_other
                 FROM acc_conf_multicriteriarecon cc
            LEFT JOIN acc_conf_multi_scene_ruleref cr
              on cr.recon_id = cc.recon_id
            LEFT JOIN acc_conf_multi_scene sce
              on sce.multi_scenario_id = cr.multi_scene_id
            WHERE cc.entity_id = p_entity_id
              AND cc.book_code = p_bookcode
              AND sce.valid_is = '1'
              AND sce.audit_state = '1'
              AND cc.valid_is = '1'
              AND cc.audit_state = '1'
        )mt;

  --更新对账结果
  update acc_buss_multi_reconrst a
   set reconcil_result = (CASE WHEN a.ACCOUNT_BALANCE_base = a.ACCOUNT_BALANCE_other THEN '1' ELSE '0' END)
   WHERE a.entity_id = p_entity_id
     AND a.book_code = p_bookcode
     AND a.year_month = p_yearmonth
     AND a.serial_no =  v_serial_no + 1;


  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(to_char(SQLCODE) || '::'|| SUBSTR(SQLERRM, 1, 200)||dbms_utility.format_error_backtrace());
  END proc_multireconcil;

  /***********************************************************************
   DESCRIPTION : 对账表达式计算明细结果保存
   DATE        : 2021-12-29
   AUTHOR      : wuyh
  ***********************************************************************/
  PROCEDURE proc_save_calculate_detail (p_entity_id number, p_bookcode varchar2, p_yearmonth varchar2, p_account_id number, p_recon_rst_id number, p_recon_type varchar2, p_book_flaf varchar2) IS

    v_book_flaf   VARCHAR2(100); --去除运算符的规则计算脚本
    v_account_id     NUMBER(10); --科目ID
  BEGIN
    v_book_flaf := p_book_flaf;
    v_account_id := p_account_id;
    --当前系统有此账套
    IF p_book_flaf = '1' THEN
       INSERT INTO acc_buss_multi_reconrstdtl(
            recon_rst_dtl_id,
            recon_rst_id,
            source_type,
            recon_type,
            business_id,
            business_no,
            LAST_ACCOUNTING_DATE,
            risk_code,
            dept_id,
            account_id,
            account_entry_code,
            currency_code,
            currency_cu_code,
            exchange_rate,
            amount,
            amount_cu,
            --creator_id,
            create_time)
    SELECT acc_seq_buss_multi_reconrstdtl.nextval as recon_rst_dtl_id,
             p_recon_rst_id,
             1,
             p_recon_type,
             vt.business_id,
             vt.business_no,
             vt.LAST_ACCOUNTING_DATE,
             vt.risk_code,
             vt.dept_id,
             vt.account_id,
             vt.account_entry_code,
             vt.currency_code,
             vt.currency_cu_code,
             vt.exchange_rate,
             vt.amount,
             vt.amount_cu,
            -- p_userid,
            LOCALTIMESTAMP
        FROM ( SELECT avr.voucher_id as business_id,avr.voucher_no as business_no, to_char(avr.EFFECTIVE_DATE,'yyyy-mm-dd') as LAST_ACCOUNTING_DATE, avd.account_id, avd.article1 as risk_code, CAST(avd.article2 AS number ) as dept_id,avd.account_entry_code,avd.currency_code,avd.currency_cu_code,avd.amount, avd.amount_cu, avd.exchange_rate
                FROM ACC_BUSS_VOUCHER_DETAIL avd,
                     ACC_BUSS_VOUCHER       avr
               WHERE avd.voucher_id = avr.voucher_id
                 AND avr.entity_id = p_entity_id --核算单位ID
                 AND avr.book_code = p_bookcode --账套编码
                 AND avr.year_month = p_yearmonth --会计期间
                 AND avd.account_id = v_account_id
                 AND avr.valid_is = '1'
                 AND avr.audit_state = '1'
              ) vt
          where vt.account_id is not null;
      ELSIF p_book_flaf = '2' THEN
         -- 现行准则科目(每个业务单位只同步一个现行账套
				 INSERT INTO acc_buss_multi_reconrstdtl
					 (recon_rst_dtl_id,
						recon_rst_id,
						source_type,
						recon_type,
						business_id,
						business_no,
						LAST_ACCOUNTING_DATE,
						account_id,
						account_entry_code,
						currency_code,
						currency_cu_code,
						amount,
						amount_cu,
						create_time)
					 SELECT acc_seq_buss_multi_reconrstdtl.nextval as recon_rst_dtl_id,
									p_recon_rst_id,
									1,
									p_recon_type,
									business_id,
									business_no,
									LAST_ACCOUNTING_DATE,
									vt.account_id,
									vt.account_entry_code,
									vt.currency_code,
									vt.currency_cu_code,
									amount,
									vt.amount_cu,
									LOCALTIMESTAMP
						 FROM (
									 SELECT  avr.ext_voucher_id as business_id,
													 avr.voucher_no as business_no,
													 to_char(avr.EFFECTIVE_DATE, 'yyyy-mm-dd') as LAST_ACCOUNTING_DATE,
													 avd.account_id,
													 avd.account_entry_code,
													 avd.currency_code,
													 avd.currency_cu_code,
													 avd.amount,
													 avd.amount_cu,
													 avd.exchange_rate
										 FROM ACC_EXT_VOUCHER avr,ACC_EXT_VOUCHER_DETAIL avd
										WHERE avd.voucher_no = avr.voucher_no
											AND avr.entity_id = p_entity_id
											AND avr.book_code = p_bookcode
											AND avr.year_month = p_yearmonth
											AND avd.account_id = v_account_id) vt
						where vt.account_id is not null;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
    --RAISE INFO '**异常信息：%',SQLERRM;
    dbms_output.put_line('**异常信息：' || to_char(SQLCODE) || '::'|| SUBSTR(SQLERRM, 1, 200)||dbms_utility.format_error_backtrace());
    -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息

  END proc_save_calculate_detail;

end acc_pack_multireconcil;
/

# 任务：endorse_type_code特殊处理功能增强
创建时间：2025-01-28
评估结果：高理解深度 + 模块变更 + 中风险

## 需求分析

### 原有需求回顾
根据记忆和文档，已经实现了针对endorse_type_code为15和16的特殊处理：
1. 数据格式处理：endorse_type_code格式为"01,15,16"逗号分隔
2. 关联查询：根据policy_no查询相同policy_no的记录
3. 时间条件：year_month等于当前评估期时执行特殊操作
4. 现金流计算：只计算第0期现金流
5. 已赚计算：第0期已赚金额=签单保费-历史累计已赚
6. 后续处理：下个月不计算发展期但保单正常进来

### 新的调整需求
针对endorse_type_code为15和16的特殊处理逻辑需要进行以下调整：

1. **发展期现金流计算调整**：
   - 当前只考虑第0期，需要支持第0期和第1期
   - 判断依据：累计应收 vs 累计实收的关系
   - 累计应收 > 累计实收时：差额放在第1期(dev_no=1)
   - 累计应收 ≤ 累计实收时：只有第0期现金流

2. **累计应收范围明确**：
   - 保费(premium)
   - 净额结算(netFee)
   - 跟单获取费用(iacf)

3. **specialProcessType=2的调整**：
   - 已赚部分(ed_*)值全部为0
   - 仍需处理当期实收(dev_no=0)
   - 应收-累计实收差额放在dev_no=1

4. **约束条件**：
   - 仅适用于endorse_type_code包含15或16的记录
   - 其他正常业务记录保持原有逻辑

## 当前实现分析

### 1. 数据模型
- `AtrBussLrcDdIcu`类已添加：
  - `endorseTypeCode`字段：存储批改类型代码
  - `specialProcessType`字段：特殊处理类型（0:正常, 1:只计算第0期, 2:不计算发展期）

### 2. SQL实现
在`AtrBussLrcDdCustDao.xml`的`partitionBaseData`方法中：
- 已实现endorse_type_code字段查询（第84行）
- 已实现special_process_type计算逻辑（第103-167行）：
  - 检查policy_no是否包含15或16的endorse_type_code
  - 根据year_month判断特殊处理类型

### 3. 业务逻辑
在`AtrBussLrcDdService.java`的`calcIcu`方法中：
- 已实现特殊处理逻辑（第417-423行）
- 已实现第0期特殊计算（第487-495行）
- 已实现不计算发展期的处理（第496-500行）

## 执行计划

### 阶段1：需求确认 - 预计10分钟
- 与用户确认具体的调整需求
- 了解在现有基础上需要做哪些额外调整

### 阶段2：代码分析 - 预计15分钟
- 深入分析现有实现的完整性
- 识别可能需要调整的部分

### 阶段3：实现调整 - 预计30分钟
- 根据确认的需求进行代码调整
- 确保不影响现有逻辑

### 阶段4：测试验证 - 预计15分钟
- 验证调整后的功能正确性
- 确保符合业务规范

## 当前状态
已完成：阶段4 - 测试验证
进度：100%

## 已完成
- [✓] 分析现有实现
- [✓] 理解业务需求背景
- [✓] 确认具体调整需求
- [✓] 修改maxDevNo计算逻辑（支持第0期和第1期）
- [✓] 调整已赚保费计算逻辑
- [✓] 修改应收保费现金流分配
- [✓] 调整净额结算手续费处理
- [✓] 修改跟单获取费用分配
- [✓] 调整非跟单获取费用处理
- [✓] 修正业务类型判断逻辑
- [✓] 修正SQL中special_process_type判断逻辑
- [✓] 重构净额结算手续费计算逻辑（代码结构优化）
- [✓] 创建测试场景文档
- [✓] 编写功能增强总结
- [✓] 创建SQL修正说明文档
- [✓] 创建代码重构说明文档

## 任务完成
所有调整已完成，代码符合需求规范：
1. ✅ 支持动态发展期计算（第0期+第1期）
2. ✅ 累计应收范围明确（保费+净额结算+iacf）
3. ✅ specialProcessType=2的已赚为0但现金流正常
4. ✅ 仅适用于endorse_type_code包含15或16的记录
5. ✅ 不影响现有正常业务逻辑

## 关键调整记录

### 重要逻辑修正（2025-01-28）

#### Java层面修正
- **判断条件修正**：从"累计应收 > 累计实收"改为"累计应收 ≠ 累计实收"
- **现金流处理**：第1期差额可以为负数（表示超收情况）
- **适用范围**：所有费用类型（保费、净额结算、跟单获取费用）都按此逻辑处理

#### SQL层面修正
- **special_process_check CTE增强**：增加has_current_month_1516和has_any_1516两个判断字段
- **special_process_type逻辑修正**：以批单的year_month为准，确保同一保单号下所有记录的处理逻辑一致
- **核心问题解决**：修正了原单和批单被标记为不同special_process_type的问题

## 风险点
- 潜在风险1：调整可能影响现有的特殊处理逻辑
  应对措施：仔细分析现有代码，确保向后兼容
- 潜在风险2：业务逻辑复杂，可能遗漏边界情况
  应对措施：详细测试各种场景，包括负数现金流情况

CREATE OR REPLACE PACKAGE bpl_pack_data_sync IS

  PROCEDURE add_public_data(p_biz_code VARCHAR2,p_task_code varchar2, p_user_id number);

END bpl_pack_data_sync;
/
CREATE OR REPLACE PACKAGE BODY bpl_pack_data_sync IS

  PROCEDURE add_public_data(p_biz_code VARCHAR2,p_task_code varchar2, p_user_id number) IS
    /***********************************************************************
    NAME : add_public_data
    DESCRIPTION : 同步新增基础公共数据(后续将统一使用此存过)
    DATE :2022-08-25
    AUTHOR :YXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/
    v_error_msg  VARCHAR2(2000);
  BEGIN

    IF p_biz_code = 'BASE_ENTITY' THEN
        -- 1、写入base_ENTITY机构表
        FOR rec_entity IN (SELECT entity_code, upper_entity_code, entity_level
                                                     FROM (SELECT entity_code, upper_entity_code, length(entity_code) entity_level
                                                                    FROM dmuser.dm_base_entity) t
                                                        ORDER BY entity_level ASC, upper_entity_code
                                                ) LOOP
            MERGE INTO bpl_company a
            USING (SELECT nvl((SELECT company_id
                                FROM bpl_company
                               WHERE company_code = b.upper_entity_code
                                 AND rownum <= 1), 0) upper_company_id,
                          b.entity_code,
                          b.entity_type_code,
                          b.entity_c_name,
                          b.entity_l_name,
                          b.entity_e_name,
                          b.short_name,
                          b.address,
                          b.phone_no,
                          b.email,
                          b.fax,
                          b.website,
                          b.remark,
                          b.display_no,
                          b.valid_is
                     FROM dmuser.dm_base_entity b
                    where task_code = p_task_code
                      AND length(entity_code) = rec_entity.entity_level
                    ) c
            ON (a.company_code = c.entity_code)
            WHEN MATCHED THEN
              UPDATE
                 SET a.upper_company_id = c.upper_company_id,
                     a.company_type_code     = c.entity_type_code,
                     a.company_c_name   = c.entity_c_name,
                     a.company_l_name   = c.entity_l_name,
                     a.company_e_name   = c.entity_e_name,
                     a.short_name        = c.short_name,
                     a.address          = c.address,
                     a.mobile_phone     = c.phone_no,
                     a.valid_is         = c.valid_is,
                     a.UPDATE_TIME        = sysdate,
                     a.UPDATOR_ID         = p_user_id

            WHEN NOT MATCHED THEN
              INSERT
                (a.company_id,
                 a.upper_company_id,
                 a.company_code,
                 a.company_type_code,
                 a.company_c_name,
                 a.company_l_name,
                 a.company_e_name,
                 a.short_name,
                 a.address,
                 a.mobile_phone,
                 a.email,
                 a.fax,
                 a.website,
                 a.remark,
                 a.display_no,
                 a.valid_is,
                 a.CREATE_TIME,
                 a.CREATOR_ID)
              VALUES
                (bpl_seq_company.nextval,
                 c.upper_company_id,
                 c.entity_code,
                 c.entity_type_code,
                 c.entity_c_name,
                 c.entity_l_name,
                 c.entity_e_name,
                 c.short_name,
                 c.address,
                 c.phone_no,
                 c.email,
                 c.fax,
                 c.website,
                 c.remark,
                 c.display_no,
                 c.valid_is,
                 sysdate,
                 p_user_id);
           COMMIT;
        END LOOP;

    ELSIF p_biz_code = 'BASE_ACCOUNT' THEN
      BEGIN
        -- 2、写入base_account科目体系表
        FOR rec_item IN (SELECT DISTINCT account_level FROM dmuser.dm_base_account tgt ORDER BY account_level) LOOP

          MERGE INTO bpluser.bbs_account a
          USING (SELECT (CASE
                          WHEN account_level = 1 THEN
                           0
                          ELSE
                           nvl((SELECT t.account_id
                                 FROM bbs_account t
                                WHERE t.entity_id = b.entity_id
                                  AND t.book_code = b.book_code
                                  AND t.account_code = b.upper_account_code
                                  AND rownum = 1), 0)
                        END) upper_account_id,
                        b.account_level,
                        b.entity_id,
                        b.book_code,
                        b.account_code,
                        b.account_e_name,
                        b.account_c_name,
                        b.account_l_name,
                        b.final_level_is,
                        b.account_category_code,
                        b.account_entry_code
                   FROM (SELECT t.*,
                                row_number() over(PARTITION BY t.account_code ORDER BY t.draw_time DESC) AS order_id
                           FROM dmuser.dm_base_account t where task_code = p_task_code) b
                  WHERE account_level = rec_item.account_level
                    AND order_id = 1 ) cc
          ON (a.account_code = cc.account_code AND a.entity_id = cc.entity_id AND a.book_code = cc.book_code)
          WHEN MATCHED THEN
            UPDATE
               SET a.upper_account_id = cc.upper_account_id,
                   a.account_level    = cc.account_level,
                   a.account_e_name   = cc.account_e_name,
                   a.account_c_name   = cc.account_c_name,
                   a.account_l_name   = cc.account_l_name,
                   a.final_level_is    = cc.final_level_is,
                   a.account_category_code     = cc.account_category_code,
                   a.account_entry_code     = cc.account_entry_code,
                   a.UPDATE_TIME        = sysdate,
                   a.UPDATOR_ID         = p_user_id
          WHEN NOT MATCHED THEN
            INSERT
              (a.account_id,
               a.upper_account_id,
               a.account_level,
               a.entity_id,
               a.book_code,
               a.account_code,
               a.account_e_name,
               a.account_c_name,
               a.account_l_name,
               a.final_level_is,
               a.account_category_code,
               a.account_entry_code,
               a.valid_is,
               a.audit_state,
               a.CREATE_TIME,
               a.CREATOR_ID,
               a.ACCOUNT_CODE_IDX)
            VALUES
              (bpl_seq_configaccountcode.nextval,
               cc.upper_account_id,
               cc.account_level,
               cc.entity_id,
               cc.book_code,
               cc.account_code,
               cc.account_e_name,
               cc.account_c_name,
               cc.account_l_name,
               cc.final_level_is,
               cc.account_category_code,
               cc.account_entry_code,
               '1',
               '1',
               sysdate,
               p_user_id,
               cc.account_code);

        END LOOP;
      END;

    ELSIF p_biz_code = 'BASE_CURRENCY' THEN
      -- 3、写入currency_code币别配置表表
      MERGE INTO bbs_conf_currency a
      USING (SELECT b.currency_code,
                    b.currency_c_name,
                    b.currency_l_name,
                    b.currency_e_name
               FROM (SELECT t.*,
                            row_number() over(PARTITION BY currency_code ORDER BY draw_time DESC) AS order_id
                       FROM dmuser.dm_base_currency t where task_code = p_task_code) b
              WHERE order_id = 1 ) c
      ON (a.currency_code = c.currency_code)
      WHEN MATCHED THEN
        UPDATE
           SET a.currency_c_name = c.currency_c_name,
               a.currency_l_name = c.currency_l_name,
               a.currency_e_name = c.currency_e_name,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.currency_id,
           a.currency_code,
           a.currency_c_name,
           a.currency_l_name,
           a.currency_e_name,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bpl_seq_currency.nextval,
           c.currency_code,
           c.currency_c_name,
           c.currency_l_name,
           c.currency_e_name,
           '1',
           '1',
           sysdate,
           p_user_id);

    ELSIF p_biz_code = 'BASE_CURRENCY_RATE' THEN
      -- 4、写入currency_code_rate兑换率配置表表
      MERGE INTO bbs_conf_currencyrate a
      USING (SELECT b.entity_id,
                    b.effective_date,
                    b.currency_code,
                    b.exch_currency_code,
                    b.exchange_rate,
                    b.frequency_code
               FROM dmuser.dm_base_currency_rate b
               where task_code = p_task_code ) c
      ON (a.entity_id = c.entity_id AND a.currency_code = c.currency_code AND a.exch_currency_code = c.exch_currency_code AND a.effective_date = c.effective_date AND a.frequency_code = c.frequency_code)
      WHEN MATCHED THEN
        UPDATE SET a.exchange_rate = c.exchange_rate,
                   a.UPDATE_TIME        = sysdate,
                   a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.rate_id,
           a.entity_id,
           a.effective_date,
           a.currency_code,
           a.exch_currency_code,
           a.exchange_rate,
           a.frequency_code,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bpl_seq_currencyrate.nextval,
           c.entity_id,
           c.effective_date,
           c.currency_code,
           c.exch_currency_code,
           c.exchange_rate,
           c.frequency_code,
           '1',
           '1',
           sysdate,
           p_user_id);

    ELSIF p_biz_code = 'BASE_PRODUCT' THEN
      -- 5、写入base_product產品定義表
      MERGE INTO bbs_conf_product a
      USING (SELECT DISTINCT b.entity_id,
                             b.product_code,
                             b.product_c_name,
                             b.product_l_name,
                             b.product_e_name,
                             b.remark
               FROM (SELECT t.*,
                            row_number() over(PARTITION BY product_code ORDER BY draw_time DESC) AS order_id
                       FROM dmuser.dm_base_product t where task_code = p_task_code) b
              WHERE order_id = 1 ) c
      ON (a.product_code = c.product_code AND a.entity_id = c.entity_id)
      WHEN MATCHED THEN
        UPDATE
           SET a.product_c_name = c.product_c_name,
               a.product_l_name = c.product_l_name,
               a.product_e_name = c.product_e_name,
               a.remark         = c.remark,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.product_id,
           a.entity_id,
           a.product_code,
           a.product_c_name,
           a.product_l_name,
           a.product_e_name,
           a.remark,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bbs_seq_conf_product.nextval,
           c.entity_id,
           c.product_code,
           c.product_c_name,
           c.product_l_name,
           c.product_e_name,
           c.remark,
           '1',
           '1',
           sysdate,
           p_user_id);

    ELSIF p_biz_code = 'BASE_RISK_CLASS' THEN
      -- 6、写入base_risk_class_code险类定义表
      MERGE INTO bbs_conf_risk_class a
      USING (SELECT b.entity_id,
                    b.risk_class_code,
                    b.class_c_name,
                    b.class_l_name,
                    b.class_e_name
               FROM dmuser.dm_base_risk_class b
              WHERE class_c_name IS NOT NULL
                AND class_e_name IS NOT NULL
                AND class_l_name IS NOT NULL
                and task_code = p_task_code ) c
      ON (a.risk_class_code = c.risk_class_code and a.entity_id = c.entity_id)
      WHEN MATCHED THEN
        UPDATE
           SET a.class_c_name = c.class_c_name,
               a.class_l_name = c.class_l_name,
               a.class_e_name = c.class_e_name,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.risk_class_id,
           a.entity_id,
           a.risk_class_code,
           a.class_c_name,
           a.class_l_name,
           a.class_e_name,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bbs_seq_risk_class.nextval,
           c.entity_id,
           c.risk_class_code,
           c.class_c_name,
           c.class_l_name,
           c.class_e_name,
           '1',
           '1',
           sysdate,
           p_user_id);

    ELSIF p_biz_code = 'BASE_RISK' THEN
      -- 7、写入base_risk风险定义表
      MERGE INTO bbs_conf_risk a
      USING (SELECT b.entity_id,
                    b.risk_code,
                    b.risk_c_name,
                    b.risk_l_name,
                    b.risk_e_name,
                    b.risk_class_code,
                    b.risk_type_code
               FROM dmuser.dm_base_risk b
               where task_code = p_task_code ) c
      ON (a.risk_code = c.risk_code and a.entity_id = c.entity_id and a.risk_type_code = c.risk_type_code)
      WHEN MATCHED THEN
        UPDATE
           SET a.risk_c_name = c.risk_c_name,
               a.risk_l_name = c.risk_l_name,
               a.risk_e_name = c.risk_e_name,
               a.risk_class_code  = c.risk_class_code,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.risk_id,
           a.entity_id,
           a.risk_code,
           a.risk_c_name,
           a.risk_l_name,
           a.risk_e_name,
           a.risk_class_code,
           a.valid_is,
           a.audit_state,
           a.risk_type_code,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bpl_seq_risk.nextval,
           c.entity_id,
           c.risk_code,
           c.risk_c_name,
           c.risk_l_name,
           c.risk_e_name,
           c.risk_class_code,
           '1',
           '1',
           c.risk_type_code,
           sysdate,
           p_user_id);

    ELSIF p_biz_code = 'BASE_RISK_MAPPING' THEN
      -- 8、BASE_RISK_MAPPING业务与精算财务再保风险映射表
      MERGE INTO bbs_conf_risk_mapping a
      USING (SELECT b.entity_id,
                    b.risk_code,
                    b.risk_mapping_code,
                    b.risk_type_code
               FROM dmuser.dm_base_risk_mapping b
               where task_code = p_task_code ) c
      ON (a.entity_id = c.entity_id AND a.risk_code = c.risk_code and A.risk_type_code = C.risk_type_code)
      WHEN MATCHED THEN
        UPDATE
           SET a.risk_mapping_code = c.risk_mapping_code,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.risk_mapping_id,
           a.entity_id,
           a.risk_code,
           a.risk_mapping_code,
           a.risk_type_code,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bbs_seq_conf_risk_mapping.nextval,
           c.entity_id,
           c.risk_code,
           c.risk_mapping_code,
           c.risk_type_code,
           '1',
           '1',
           sysdate,
           p_user_id);

    ELSIF p_biz_code = 'REINS_TREATY_CLASS' THEN
      -- 9、REINS_TREATY_CLASS合約大類定義表
      MERGE INTO bbs_conf_treaty_class a
      USING (SELECT b.entity_id,
                    b.treaty_class_code,
                    b.class_c_name,
                    b.class_l_name,
                    b.class_e_name,
                    b.valid_is
               FROM dmuser.dm_reins_treaty_class b
               where task_code = p_task_code ) c
      ON (a.entity_id = c.entity_id and a.treaty_class_code = c.treaty_class_code)
      WHEN MATCHED THEN
        UPDATE
           SET a.class_c_name = c.class_c_name,
               a.class_l_name = c.class_l_name,
               a.class_e_name = c.class_e_name,
               a.valid_is     = c.valid_is,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.treaty_class_id,
           a.entity_id,
           a.treaty_class_code,
           a.class_c_name,
           a.class_l_name,
           a.class_e_name,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bpl_seq_treaty_class.nextval,
           c.entity_id,
           c.treaty_class_code,
           c.class_c_name,
           c.class_l_name,
           c.class_e_name,
           c.valid_is,
           '1',
           sysdate,
           p_user_id);

    ELSIF p_biz_code = 'REINS_BASE_TREATY' THEN
      -- 10、REINS_CONF_TREATY再保合约基础信息配置表
      MERGE INTO bbs_conf_base_treaty a
      USING (SELECT b.entity_id,
                    b.base_treaty_no,
                    b.treaty_c_name,
                    b.treaty_e_name,
                    b.treaty_l_name,
                    b.treaty_type_code,
                    b.treaty_class_code,
                    b.base_treaty_out_no
               FROM dmuser.dm_reins_base_treaty b
               where task_code = p_task_code ) c
      ON (a.base_treaty_no = c.base_treaty_no AND a.entity_id = c.entity_id)
      WHEN MATCHED THEN
        UPDATE
           SET a.treaty_c_name = c.treaty_c_name,
               a.treaty_e_name = c.treaty_e_name,
               a.treaty_l_name = c.treaty_l_name,
               a.treaty_type_code   = c.treaty_type_code,
               a.treaty_class_code   = c.treaty_class_code,
               a.base_treaty_out_no = c.base_treaty_out_no,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.conf_treaty_id,
           a.entity_id,
           a.base_treaty_no,
           a.treaty_c_name,
           a.treaty_e_name,
           a.treaty_l_name,
           a.treaty_type_code,
           a.treaty_class_code,
           a.base_treaty_out_no,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bbs_seq_conf_conf_treaty.nextval,
           c.entity_id,
           c.base_treaty_no,
           c.treaty_c_name,
           c.treaty_e_name,
           c.treaty_l_name,
           c.treaty_type_code,
           c.treaty_class_code,
           c.base_treaty_out_no,
           '1',
           '1',
           sysdate,
           p_user_id);
    ELSIF p_biz_code = 'REINS_TREATY' THEN
      -- 11、REINS_TREATY合约信息表
      MERGE INTO bbs_conf_treaty a
      USING (SELECT b.entity_id,
                    b.treaty_no,
                    b.ri_direction_code,
                    b.effective_date,
                    b.expiry_date,
                    b.issue_date,
                    b.treaty_type_code,
                    b.gepi_amount,
                    b.nepi_amount,
                    b.epi_currency_code,
                    b.premium,
                    b.currency_code,
                    b.billing_frequency_code,
                    b.floating_charge_is,
                    b.profit_fee_is,
                    b.min_return_amount,
                    b.related_party,
                    b.offshore_is,
                    b.base_treaty_id,
                    b.limit_amount
               FROM dmuser.dm_reins_treaty b
               where task_code = p_task_code ) c
      ON (a.treaty_no = c.treaty_no AND a.entity_id = c.entity_id)
      WHEN MATCHED THEN
        UPDATE
           SET a.ri_direction_code   = c.ri_direction_code,
               a.effective_date    = c.effective_date,
               a.expiry_date       = c.expiry_date,
               a.issue_date    = c.issue_date,
               a.treaty_type_code       = c.treaty_type_code,
               a.gepi_amount       = c.gepi_amount,
               a.nepi_amount       = c.nepi_amount,
               a.epi_currency_code      = c.epi_currency_code,
               a.premium           = c.premium,
               a.currency_code          = c.currency_code,
               a.billing_frequency_code = c.billing_frequency_code,
               a.floating_charge_is   = c.floating_charge_is,
               a.profit_fee_is     = c.profit_fee_is,
               a.min_return_amount = c.min_return_amount,
               a.related_party     = c.related_party,
               a.offshore_is       = c.offshore_is,
               a.base_treaty_id    = c.base_treaty_id,
               a.UPDATE_TIME       = sysdate,
               a.UPDATOR_ID        = p_user_id,
               a.limit_amount      = c.limit_amount

      WHEN NOT MATCHED THEN
        INSERT
          (a.treaty_id,
           a.entity_id,
           a.treaty_no,
           a.ri_direction_code,
           a.effective_date,
           a.expiry_date,
           a.issue_date,
           a.treaty_type_code,
           a.gepi_amount,
           a.nepi_amount,
           a.epi_currency_code,
           a.premium,
           a.currency_code,
           a.billing_frequency_code,
           a.floating_charge_is,
           a.profit_fee_is,
           a.min_return_amount,
           a.related_party,
           a.offshore_is,
           a.base_treaty_id,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID,
           a.limit_amount)
        VALUES
          (bbs_seq_conf_treaty.nextval,
           c.entity_id,
           c.treaty_no,
           c.ri_direction_code,
           c.effective_date,
           c.expiry_date,
           c.issue_date,
           c.treaty_type_code,
           c.gepi_amount,
           c.nepi_amount,
           c.epi_currency_code,
           c.premium,
           c.currency_code,
           c.billing_frequency_code,
           c.floating_charge_is,
           c.profit_fee_is,
           c.min_return_amount,
           c.related_party,
           c.offshore_is,
           c.base_treaty_id,
           '1',
           '1',
           sysdate,
           p_user_id,
           c.limit_amount);

    ELSIF  p_biz_code = 'REINS_TREATY_PAYMENT_PLAN' THEN
       -- 12、REINS_TREATY_PLAN合约缴费计划配置
       MERGE INTO bbs_conf_reins_treaty_payment_plan a
      USING (SELECT b.entity_id,
                    b.treaty_no,
                    b.est_payment_seq_no,
                    b.est_payment_date
               FROM dmuser.dm_reins_treaty_payment_plan b
               where task_code = p_task_code ) c
      ON (a.treaty_no = c.treaty_no AND a.est_payment_seq_no = c.est_payment_seq_no AND a.entity_id = c.entity_id)
      WHEN MATCHED THEN
        UPDATE
           SET a.est_payment_date    = c.est_payment_date,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.treaty_plan_id,
           a.entity_id,
           a.treaty_no,
           a.est_payment_seq_no,
           a.est_payment_date,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bbs_seq_conf_treaty_payment_plan.nextval,
           c.entity_id,
           c.treaty_no,
           c.est_payment_seq_no,
           c.est_payment_date,
           '1',
           '1',
           sysdate,
           p_user_id);


    ELSIF p_biz_code = 'REINS_TREATY_SECTION' THEN
      -- 12、REINS_TREATY_SECTION再保合约分项表
      MERGE INTO bbs_conf_treaty_section a
      USING (SELECT b.entity_id,
                    b.treaty_no,
                    b.section_no_code,
                    b.section_rate,
                    b.epi_currency_code,
                    b.gepi_amount,
                    b.nepi_amount,
                    b.profit_commission_rate,
                    b.currency_code,
                    b.premium,
                    b.limit_amount
               FROM dmuser.dm_reins_treaty_section b
               where task_code = p_task_code ) c
      ON (a.treaty_no = c.treaty_no AND a.section_no_code = c.section_no_code AND a.entity_id = c.entity_id)
      WHEN MATCHED THEN
        UPDATE
           SET a.section_rate    = c.section_rate,
               a.epi_currency_code    = c.epi_currency_code,
               a.gepi_amount     = c.gepi_amount,
               a.nepi_amount     = c.nepi_amount,
               a.profit_commission_rate = c.profit_commission_rate,
               a.currency_code        = c.currency_code,
               a.premium         = c.premium,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id,
               a.limit_amount       = c.limit_amount
      WHEN NOT MATCHED THEN
        INSERT
          (a.treaty_section_id,
           a.entity_id,
           a.treaty_no,
           a.section_no_code,
           a.section_rate,
           a.epi_currency_code,
           a.gepi_amount,
           a.nepi_amount,
           a.profit_commission_rate,
           a.currency_code,
           a.premium,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID,
           a.limit_amount)
        VALUES
          (bbs_seq_conf_treaty_section.nextval,
           c.entity_id,
           c.treaty_no,
           c.section_no_code,
           c.section_rate,
           c.epi_currency_code,
           c.gepi_amount,
           c.nepi_amount,
           c.profit_commission_rate,
           c.currency_code,
           c.premium,
           '1',
           '1',
           sysdate,
           p_user_id,
           c.limit_amount);

    ELSIF p_biz_code = 'REINS_RISK' THEN
      -- 13、reins_risk再保合约风险信息表
      MERGE INTO bbs_conf_reins_risk a
      USING (SELECT b.entity_id,
                    b.treaty_no,
                    b.section_no_code,
                    b.ri_risk_code,
                    b.ri_class_code,
                    b.risk_code,
                    b.premium,
                    b.currency_code
               FROM dmuser.dm_reins_risk b
               where task_code = p_task_code ) c
      ON (a.risk_code = c.risk_code AND a.entity_id = c.entity_id AND a.treaty_no = c.treaty_no AND a.section_no_code = c.section_no_code AND a.ri_risk_code = c.ri_risk_code)
      WHEN MATCHED THEN
        UPDATE
           SET a.ri_class_code = c.ri_class_code,
               a.premium     = c.premium,
               a.currency_code    = c.currency_code,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.reins_risk_id,
           a.entity_id,
           a.treaty_no,
           a.section_no_code,
           a.ri_risk_code,
           a.ri_class_code,
           a.risk_code,
           a.premium,
           a.currency_code,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bbs_seq_conf_reins_risk.nextval,
           c.entity_id,
           c.treaty_no,
           c.section_no_code,
           c.ri_risk_code,
           c.ri_class_code,
           c.risk_code,
           c.premium,
           c.currency_code,
           '1',
           '1',
           sysdate,
           p_user_id);

    ELSIF p_biz_code = 'REINS_REINSURER' THEN
      -- 14、REINS_REINSURER再保人信息表
      MERGE INTO bbs_conf_reins_reinsurer a
      USING (SELECT b.entity_id,
                    b.treaty_no,
                    b.section_no_code,
                    b.reinsurer_type_code,
                    b.reinsurer_code,
                    b.company_in_is,
                    b.ri_ceding_rate,
                    b.ri_commission_rate
               FROM dmuser.dm_reins_reinsurer b
               where task_code = p_task_code ) c
      ON (a.treaty_no = c.treaty_no AND a.reinsurer_type_code = c.reinsurer_type_code AND a.section_no_code = c.section_no_code AND a.entity_id = c.entity_id AND a.reinsurer_code = c.reinsurer_code)
      WHEN MATCHED THEN
        UPDATE
           SET a.company_in_is         = c.company_in_is,
               a.ri_ceding_rate     = c.ri_ceding_rate,
               a.ri_commission_rate = c.ri_commission_rate,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.reins_reinsurer_id,
           a.entity_id,
           a.treaty_no,
           a.section_no_code,
           a.reinsurer_type_code,
           a.reinsurer_code,
           a.company_in_is,
           a.ri_ceding_rate,
           a.ri_commission_rate,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bbs_seq_reins_reinsurer.nextval,
           c.entity_id,
           c.treaty_no,
           c.section_no_code,
           c.reinsurer_type_code,
           c.reinsurer_code,
           c.company_in_is,
           c.ri_ceding_rate,
           c.ri_commission_rate,
           '1',
           '1',
           sysdate,
           p_user_id);

    ELSIF p_biz_code = 'REINS_FLOAT_CHARGE' THEN
      -- 15、REINS_FLOAT_CHARGE浮动手续费率表
      MERGE INTO bbs_conf_reins_float_charge a
      USING (SELECT b.entity_id,
                    b.floating_rate_no,
                    b.floating_rate_serial_no,
                    b.ri_direction_code,
                    b.treaty_no,
                    b.section_no_code,
                    b.fixed_value,
                    b.min_loss_rate,
                    b.max_loss_rate,
                    b.floating_commission_rate
               FROM dmuser.dm_reins_float_charge b
               where task_code = p_task_code ) c
      ON (a.entity_id = c.entity_id AND a.floating_rate_no = c.floating_rate_no
      AND a.floating_rate_serial_no = c.floating_rate_serial_no AND a.ri_direction_code = c.ri_direction_code
      AND a.treaty_no = c.treaty_no AND a.section_no_code = c.section_no_code)
      WHEN MATCHED THEN
        UPDATE
           SET a.fixed_value    = c.fixed_value,
               a.min_loss_rate  = c.min_loss_rate,
               a.max_loss_rate  = c.max_loss_rate,
               a.floating_commission_rate = c.floating_commission_rate,
               a.UPDATE_TIME        = sysdate,
               a.UPDATOR_ID         = p_user_id
      WHEN NOT MATCHED THEN
        INSERT
          (a.float_charge_id,
           a.entity_id,
           a.floating_rate_no,
           a.floating_rate_serial_no,
           a.ri_direction_code,
           a.treaty_no,
           a.section_no_code,
           a.fixed_value,
           a.min_loss_rate,
           a.max_loss_rate,
           a.floating_commission_rate,
           a.valid_is,
           a.audit_state,
           a.CREATE_TIME,
           a.CREATOR_ID)
        VALUES
          (bbs_seq_conf_risk_mapping.nextval,
           c.entity_id,
           c.floating_rate_no,
           c.floating_rate_serial_no,
           c.ri_direction_code,
           c.treaty_no,
           c.section_no_code,
           c.fixed_value,
           c.min_loss_rate,
           c.max_loss_rate,
           c.floating_commission_rate,
           '1',
           '1',
           sysdate,
           p_user_id);

    END IF;

  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(to_char(SQLCODE) || '::' || substr(SQLERRM, 1, 200));
      --意外处理
      v_error_msg := '[EXCEPTION][数据校验]proc_data_verify:' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);
  END add_public_data;

END bpl_pack_data_sync;
/

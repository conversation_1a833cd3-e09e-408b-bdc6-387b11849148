/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-07-08 15:17:45
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.ss.platform.core.annotation.SsTranslateCode;
import com.ss.platform.core.constant.SystemConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-07-08 15:17:45<br/>
 * Description: LRC计量方式配置表<br/>
 * Table Name: atr_conf_risk_ref<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC计量方式配置表")
public class AtrConfRiskRefVo implements Serializable {
    /**
     * Database column: atr_conf_risk_ref.risk_ref_id
     * Database remarks: risk_ref_id|主键
     */
    @ApiModelProperty(value = "risk_ref_id|主键", required = true)
    private Long riskRefId;

    /**
     * Database column: atr_conf_risk_ref.center_id
     * Database remarks: center_id|核算单位ID
     */
    @ApiModelProperty(value = "center_id|核算单位ID", required = false)
    @NotNull(message = "The Center Id can't be null|业务单位不能为空|業務單位不能為空")
    //@DecimalMax(value = "2048", message = "Center Id must be less than 2048|业务单位必须小于2048|業務單位必須小於2048")
    private Long entityId;

    /**
     * Database column: atr_conf_risk_ref.atr_type
     * Database remarks: atr_type|风险分布模式
     */
    @ApiModelProperty(value = "atr_type|计量方式", required = false)
    @NotBlank(message = "The atr type can't be null|计量方式不能为空|計量方式不能為空")
    @Size(max = 10, message = "The atr type's length is too long|计量方式过长|計量方式險種編碼過長")
    @SsTranslateCode(context = SystemConstant.BbsIdentity.APP_CONTEXT,codeCodeIdx = "QualificationType")
    private String atrType;

    /**
     * Database column: atr_conf_risk_ref.business_model
     * Database remarks: Business_Model|业务模型：D-直保 T-合约 F-临分
     */
    @ApiModelProperty(value = "Business_Model|业务模型：D-直保 T-合约 F-临分", required = true)
    private String businessModel;

    /**
     * Database column: atr_conf_risk_ref.business_direction
     * Database remarks: Business_Direction|业务方向：D-不区分 I-分入 O-分出
     */
    @ApiModelProperty(value = "Business_Direction|业务方向：D-不区分 I-分入 O-分出", required = true)
    private String businessDirection;

    /**
     * Database column: atr_conf_risk_ref.loa_code
     * Database remarks: Loa_Code|loa代码
     */
    @ApiModelProperty(value = "Loa_Code|loa代码", required = true)
    private String loaCode;

    /**
     * Database column: atr_conf_risk_ref.checked_id
     * Database remarks: checked_id|审核人
     */
    @ApiModelProperty(value = "checked_id|审核人", required = false)
    private Long checkedId;

    /**
     * Database column: atr_conf_risk_ref.checked_time
     * Database remarks: checked_time|审核时间
     */
    @ApiModelProperty(value = "checked_time|审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: atr_conf_risk_ref.checked_state
     * Database remarks: checked_state|审核状态
     */
    @ApiModelProperty(value = "checked_state|审核状态", required = false)
    @SsTranslateCode(context = SystemConstant.BbsIdentity.APP_CONTEXT,codeCodeIdx = "AuditStatus/View")
    private String auditState;

    /**
     * Database column: atr_conf_risk_ref.checked_msg
     * Database remarks: checked_msg|审核意见
     */
    @ApiModelProperty(value = "checked_msg|审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: atr_conf_risk_ref.valid_is
     * Database remarks: valid_is|是否有效
     */
    @ApiModelProperty(value = "valid_is|是否有效", required = false)
    private String validIs;

    /**
     * Database column: atr_conf_risk_ref.remark
     * Database remarks: remark|备注
     */
    @ApiModelProperty(value = "remark|备注", required = false)
    private String remark;

    /**
     * Database column: atr_conf_risk_ref.creator_id
     * Database remarks: creator_id|创建人
     */
    @ApiModelProperty(value = "creator_id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_risk_ref.create_time
     * Database remarks: create_time|创建时间
     */
    @ApiModelProperty(value = "create_time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_risk_ref.updator_id
     * Database remarks: updator_id|最后修改人
     */
    @ApiModelProperty(value = "updator_id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_conf_risk_ref.update_time
     * Database remarks: update_time|最后修改时间
     */
    @ApiModelProperty(value = "update_time|最后修改时间", required = false)
    private Date updateTime;

    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Integer serialNo;
    
    /* 以下为业务数据传输字段 */
    private String entityCode;
    
    private String entityEName;
    
    private String entityCName;
    
    private String entityLName;

    private String creatorName;

    private String updatorName;

    private String loaEName;
    
    private String loaCName;
    
    private String loaLName;

    @SsTranslateCode(context = SystemConstant.BbsIdentity.APP_CONTEXT,codeCodeIdx = "BusinessModel/Base")
    private String model;

    private String templateFileName;

    private String targetRouter;

    private String language;

    private static final long serialVersionUID = 1L;

    public Long getRiskRefId() {
        return riskRefId;
    }

    public void setRiskRefId(Long riskRefId) {
        this.riskRefId = riskRefId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getAtrType() {
        return atrType;
    }

    public void setAtrType(String atrType) {
        this.atrType = atrType;
    }

    public String getBusinessModel() {
        return businessModel;
    }

    public void setBusinessModel(String businessModel) {
        this.businessModel = businessModel;
    }

    public String getBusinessDirection() {
        return businessDirection;
    }

    public void setBusinessDirection(String businessDirection) {
        this.businessDirection = businessDirection;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getEntityCode() {
        return entityCode;
    }
    
    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }
    
    public String getEntityEName() {
        return entityEName;
    }
    
    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }
    
    public String getEntityCName() {
        return entityCName;
    }
    
    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }
    
    public String getEntityLName() {
        return entityLName;
    }
    
    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }
    
    public String getLoaEName() {
        return loaEName;
    }
    
    public void setLoaEName(String loaEName) {
        this.loaEName = loaEName;
    }
    
    public String getLoaCName() {
        return loaCName;
    }
    
    public void setLoaCName(String loaCName) {
        this.loaCName = loaCName;
    }
    
    public String getLoaLName() {
        return loaLName;
    }
    
    public void setLoaLName(String loaLName) {
        this.loaLName = loaLName;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}
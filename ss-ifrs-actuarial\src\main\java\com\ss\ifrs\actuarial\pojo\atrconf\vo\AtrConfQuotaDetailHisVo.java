/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-11-10 17:08:27
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-10 17:08:27<br/>
 * Description: null<br/>
 * Table Name: bbs_conf_quota_detailhis<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 * <AUTHOR>
 */
@ApiModel(value = "指标配置明细轨迹表")
public class AtrConfQuotaDetailHisVo implements Serializable {
    /**
     * Database column: bbs_conf_quota_detailhis.conf_quota_detailhis_id
     * Database remarks: quotaDetailHisId|主键
     */
    @ApiModelProperty(value = "quotaDetailHisId|主键", required = true)
    private Long quotaDetailHisId;

    /**
     * Database column: bbs_conf_quota_detailhis.conf_quota_detail_id
     * Database remarks: quotaDetailId|主键
     */
    @ApiModelProperty(value = "quotaDetailId|主键", required = true)
    private Long quotaDetailId;

    /**
     * Database column: bbs_conf_quota_detailhis.conf_quota_id
     * Database remarks: quotaId|指标
     */
    @ApiModelProperty(value = "quotaId|指标", required = true)
    private Long quotaId;

    /**
     * Database column: bbs_conf_quota_detailhis.quota_period
     * Database remarks: quotaPeriod|发展期
     */
    @ApiModelProperty(value = "quotaPeriod|发展期", required = false)
    private Long quotaPeriod;

    /**
     * Database column: bbs_conf_quota_detailhis.quota_value
     * Database remarks: quotaValue|指标值
     */
    @ApiModelProperty(value = "quotaValue|指标值", required = false)
    private String quotaValue;

    /**
     * Database column: bbs_conf_quota_detailhis.creator_id
     * Database remarks: creatorId|创建人
     */
    @ApiModelProperty(value = "creatorId|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: bbs_conf_quota_detailhis.create_time
     * Database remarks: createTime|创建时间
     */
    @ApiModelProperty(value = "createTime|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: bbs_conf_quota_detailhis.updator_id
     * Database remarks: updatorId|最后修改人
     */
    @ApiModelProperty(value = "updatorId|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: bbs_conf_quota_detailhis.update_time
     * Database remarks: updateTime|最后修改时间
     */
    @ApiModelProperty(value = "updateTime|最后修改时间", required = false)
    private Date updateTime;

    /**
     * Database column: bbs_conf_quota_detailhis.oper_type
     * Database remarks: operType|操作类型
     */
    @ApiModelProperty(value = "operType|操作类型", required = false)
    private String operType;

    /**
     * Database column: bbs_conf_quota_detailhis.oper_id
     * Database remarks: operId|操作人
     */
    @ApiModelProperty(value = "operId|操作人", required = false)
    private Long operId;

    /**
     * Database column: bbs_conf_quota_detailhis.oper_time
     * Database remarks: operTime|操作时间
     */
    @ApiModelProperty(value = "operTime|操作时间", required = false)
    private Date operTime;

    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Integer serialNo;

    private static final long serialVersionUID = 1L;

    public Long getQuotaDetailHisId() {
        return quotaDetailHisId;
    }

    public void setQuotaDetailHisId(Long quotaDetailHisId) {
        this.quotaDetailHisId = quotaDetailHisId;
    }

    public Long getQuotaDetailId() {
        return quotaDetailId;
    }

    public void setQuotaDetailId(Long quotaDetailId) {
        this.quotaDetailId = quotaDetailId;
    }

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getQuotaPeriod() {
        return quotaPeriod;
    }

    public void setQuotaPeriod(Long quotaPeriod) {
        this.quotaPeriod = quotaPeriod;
    }

    public String getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(String quotaValue) {
        this.quotaValue = quotaValue;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperType() {
        return operType;
    }

    public void setOperType(String operType) {
        this.operType = operType;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }
}
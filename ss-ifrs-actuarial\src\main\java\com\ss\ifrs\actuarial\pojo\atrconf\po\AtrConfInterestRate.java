/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-06-29 10:33:37
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-06-29 10:33:37<br/>
 * Description: 无风险利率曲线配置<br/>
 * Table Name: atr_conf_interest_rate<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "无风险利率曲线配置")
public class AtrConfInterestRate implements Serializable {
    /**
     * Database column: atr_conf_interest_rate.interest_rate_id
     * Database remarks: interest_rate_id|主键
     */
    @ApiModelProperty(value = "interest_rate_id|主键", required = true)
    private Long interestRateId;

    /**
     * Database column: atr_conf_interest_rate.center_id
     * Database remarks: Center_Id|核算单位ID
     */
    @ApiModelProperty(value = "Center_Id|核算单位ID", required = false)
    private Long entityId;

    /**
     * Database column: atr_conf_interest_rate.currency
     * Database remarks: currency|币种
     */
    @ApiModelProperty(value = "currency|币种", required = false)
    private String currencyCode;

    /**
     * Database column: atr_conf_interest_rate.year_month
     * Database remarks: year_month|适用期
     */
    @ApiModelProperty(value = "year_month|适用期", required = false)
    private String yearMonth;

    /**
     * Database column: atr_conf_interest_rate.serial_no
     * Database remarks: null
     */
    private String versionNo;

    /**
     * Database column: atr_conf_interest_rate.confirm_is
     * Database remarks: null
     */
    private String confirmIs;

    /**
     * Database column: atr_conf_interest_rate.confirm_id
     * Database remarks: null
     */
    private Long confirmId;

    /**
     * Database column: atr_conf_interest_rate.confirm_time
     * Database remarks: null
     */
    private Date confirmTime;


    /**
     * Database column: atr_conf_interest_rate.valid_is
     * Database remarks: Valid_Is|是否有效
     */
    @ApiModelProperty(value = "Valid_Is|是否有效", required = false)
    private String validIs;

    /**
     * Database column: atr_conf_interest_rate.remark
     * Database remarks: remark|备注
     */
    @ApiModelProperty(value = "remark|备注", required = false)
    private String remark;

    /**
     * Database column: atr_conf_interest_rate.creator_id
     * Database remarks: Creator_Id|创建人
     */
    @ApiModelProperty(value = "Creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_interest_rate.create_time
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_interest_rate.updator_id
     * Database remarks: Updator_Id|最后修改人
     */
    @ApiModelProperty(value = "Updator_Id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_conf_interest_rate.update_time
     * Database remarks: Update_Time|最后修改时间
     */
    @ApiModelProperty(value = "Update_Time|最后修改时间", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getInterestRateId() {
        return interestRateId;
    }

    public void setInterestRateId(Long interestRateId) {
        this.interestRateId = interestRateId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
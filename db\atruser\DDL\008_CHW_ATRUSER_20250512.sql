drop table if exists atr_buss_fo_lrc_g;

-- 创建表 atr_buss_fo_lrc_g
CREATE TABLE atr_buss_fo_lrc_g (
                                   id BIGINT NOT NULL,
                                   action_no VARCHAR(32) NOT NULL,
                                   entity_id BIGINT NOT NULL,
                                   year_month VARCHAR(6) NOT NULL,
                                   risk_class_code VARCHAR(20) NOT NULL,
                                   portfolio_no VARCHAR(60) NOT NULL,
                                   icg_no VARCHAR(60) NOT NULL,
                                   total_premium DECIMAL(21, 4) NOT NULL,
                                   total_net_fee DECIMAL(21, 4) NOT NULL,
                                   ed_premium_rate DECIMAL(16, 15) NOT NULL,
                                   ed_net_charge_rate DECIMAL(16, 15) NOT NULL,
                                   inv_amount DECIMAL(21, 4) NOT NULL,
                                   mt_rate DECIMAL(16, 15) NOT NULL,
                                   claim_rate DECIMAL(16, 15) NOT NULL,
                                   ra_rate DECIMAL(16, 15) NOT NULL,
                                   PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE atr_buss_fo_lrc_g IS 'LRC 合同组 (临分分出)';

-- 添加字段注释
COMMENT ON COLUMN atr_buss_fo_lrc_g.id IS 'ID';
COMMENT ON COLUMN atr_buss_fo_lrc_g.action_no IS '执行编号';
COMMENT ON COLUMN atr_buss_fo_lrc_g.entity_id IS '业务单位ID';
COMMENT ON COLUMN atr_buss_fo_lrc_g.year_month IS '评估期年月';
COMMENT ON COLUMN atr_buss_fo_lrc_g.risk_class_code IS '险类代码';
COMMENT ON COLUMN atr_buss_fo_lrc_g.portfolio_no IS '合同组合编号';
COMMENT ON COLUMN atr_buss_fo_lrc_g.icg_no IS '合同组号码';
COMMENT ON COLUMN atr_buss_fo_lrc_g.total_premium IS '总保费';
COMMENT ON COLUMN atr_buss_fo_lrc_g.total_net_fee IS '总净额结算手续费';
COMMENT ON COLUMN atr_buss_fo_lrc_g.ed_premium_rate IS '当期赚取比例_保费';
COMMENT ON COLUMN atr_buss_fo_lrc_g.ed_net_charge_rate IS '当期赚取比例_净额结算';
COMMENT ON COLUMN atr_buss_fo_lrc_g.inv_amount IS '当期确认的投资成分';
COMMENT ON COLUMN atr_buss_fo_lrc_g.mt_rate IS '维持费用率';
COMMENT ON COLUMN atr_buss_fo_lrc_g.claim_rate IS '赔付率';
COMMENT ON COLUMN atr_buss_fo_lrc_g.ra_rate IS '未到期非金融风险调整率';
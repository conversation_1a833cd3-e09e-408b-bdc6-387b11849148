# LRC导出OOM优化方案

## 问题描述
原有的LRC导出功能在处理大数据量时会出现JVM OOM（内存溢出）问题。主要原因是：

1. **分页查询仍然加载大量数据到内存**：虽然使用了分页，但每页数据量仍然很大
2. **数据在内存中累积**：多个sheet的数据同时存在于内存中
3. **没有使用流式处理**：数据批量加载而非流式处理

## 优化方案

### 1. 采用流式导出架构
- 使用MyBatis的`ResultHandler`接口实现流式查询
- 配置`@Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = Integer.MIN_VALUE)`
- 数据逐行处理，避免内存累积

### 2. 利用现有的CommonResultHandler
- 继承`CommonResultHandler<Map<String, Object>>`
- 实现`LrcExportResultHandler`专用处理器
- 支持大文件自动分sheet和zip打包

### 3. 重构导出流程
- 修改`becfDownload`方法调用新的`streamExportLrcData`方法
- 为每个导出配置单独创建流式查询
- 每个sheet独立处理，避免数据交叉影响

## 技术实现

### 核心类修改
- `AtrBussLrcCashFlowServiceImpl.java`
  - 添加`LrcExportResultHandler`内部类
  - 新增`streamExportLrcData`流式导出方法
  - 添加辅助方法：`findDevNoList`、`getDataCount`、`executeStreamQuery`

### 流式查询方法
系统中已有的流式查询方法：
- `AtrBussDDLrcGDao.findLrcHandelIcgDetail`
- `AtrBussDDLrcUDao.findLrcHandelDetailPage`
- `AtrBussFOLrcGDao.findLrcHandelIcgDetail`
- `AtrBussFOLrcUDao.findLrcHandelDetailPage`
- `AtrBussTILrcGDao.findLrcHandelIcgDetail`
- `AtrBussTILrcUDao.findLrcHandelDetailPage`
- `AtrBussTOLrcGDao.findLrcHandelIcgDetail`
- `AtrBussTOLrcUDao.findLrcHandelDetailPage`

## 优化效果

### 内存使用优化
- **原方案**：数据分页加载，每页数据在内存中累积
- **新方案**：数据流式处理，内存使用量恒定且极小

### 支持更大数据量
- **原方案**：受JVM堆内存限制，大数据量导致OOM
- **新方案**：理论上支持无限大数据量导出

### 性能提升
- 减少GC压力
- 降低内存峰值使用
- 提高导出稳定性

## 使用说明

### 调用方式
导出接口调用方式保持不变，系统会自动使用流式导出：

```java
atrBussLrcCashFlowService.becfDownload(atrBussBecfViewVo, request, response);
```

### 配置要求
确保数据库连接池配置支持长连接：
- 连接超时时间适当延长
- 查询超时时间根据数据量调整

### 监控建议
- 监控导出任务执行时间
- 观察JVM内存使用情况
- 记录导出文件大小和记录数

## 兼容性说明

- **向后兼容**：原有接口调用方式不变
- **数据格式兼容**：导出的Excel格式和内容保持一致
- **功能兼容**：支持所有业务类型（DD、FO、TI、TO、TX）

## 注意事项

1. **事务管理**：流式查询期间保持数据库连接，注意事务边界
2. **错误处理**：确保ResultHandler正确关闭，避免资源泄露
3. **并发控制**：大数据量导出时注意系统负载
4. **磁盘空间**：确保有足够磁盘空间存储临时文件

## 测试建议

1. **功能测试**：验证各业务类型导出功能正常
2. **性能测试**：使用大数据量测试内存使用情况
3. **压力测试**：并发导出测试系统稳定性
4. **异常测试**：测试各种异常情况下的资源清理

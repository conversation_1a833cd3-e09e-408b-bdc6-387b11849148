/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2024-03-21
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import com.ss.ifrs.actuarial.util.abp.Tab;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Description: 中债国债收益率曲线<br/>
 * Table Name: atr_conf_cb_treasury_yc<br/>
 */
@Tab("atr_conf_cb_treasury_yc")
@ApiModel(value = "中债国债收益率曲线")
public class AtrConfCbTreasuryYc implements Serializable {
    
    /**
     * Database column: atr_conf_cb_treasury_yc.interest_rate_id
     * Database remarks: interest_rate_id|外键，关联atr_conf_interest_rate表
     */
    @ApiModelProperty(value = "interest_rate_id|外键，关联atr_conf_interest_rate表", required = true)
    private Long interestRateId;

    /**
     * Database column: atr_conf_cb_treasury_yc.tenor_years
     * Database remarks: tenor_years|标准期限(年)
     */
    @ApiModelProperty(value = "tenor_years|标准期限(年)", required = true)
    private BigDecimal tenorYears;

    /**
     * Database column: atr_conf_cb_treasury_yc.avg_value
     * Database remarks: avg_value|平均值(%)
     */
    @ApiModelProperty(value = "avg_value|平均值(%)", required = true)
    private BigDecimal avgValue;

    /**
     * Database column: atr_conf_cb_treasury_yc.rev_tenor_years
     * Database remarks: rev_tenor_years|反转后标准期限(年)
     */
    @ApiModelProperty(value = "rev_tenor_years|反转后标准期限(年)", required = true)
    private BigDecimal revTenorYears;

    /**
     * Database column: atr_conf_cb_treasury_yc.creator_id
     * Database remarks: creator_id|创建人
     */
    @ApiModelProperty(value = "creator_id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_cb_treasury_yc.created_time
     * Database remarks: created_time|创建时间
     */
    @ApiModelProperty(value = "created_time|创建时间", required = true)
    private Date createdTime;

    private static final long serialVersionUID = 1L;

    public Long getInterestRateId() {
        return interestRateId;
    }

    public void setInterestRateId(Long interestRateId) {
        this.interestRateId = interestRateId;
    }

    public BigDecimal getTenorYears() {
        return tenorYears;
    }

    public void setTenorYears(BigDecimal tenorYears) {
        this.tenorYears = tenorYears;
    }

    public BigDecimal getAvgValue() {
        return avgValue;
    }

    public void setAvgValue(BigDecimal avgValue) {
        this.avgValue = avgValue;
    }

    public BigDecimal getRevTenorYears() {
        return revTenorYears;
    }

    public void setRevTenorYears(BigDecimal revTenorYears) {
        this.revTenorYears = revTenorYears;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }
} 

call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_BUSS_DUTY');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_BUSS_DUTY_HIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_ACCOUNTITEM_MAP');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_ACCOUNT_MAP');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_ACCOUNT_YEAR_MONTH');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_ACCOUNT_YEAR_MONTH_HIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_COA_DEAL');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_CONF_TREATY');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_CONF_TREATYHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_FEE_TYPE_MAPPING');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_FEE_TYPE_MP_HIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_FLOAT_CHARGE');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_FLOAT_CHARGEHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_LOA');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_LOA_DETAIL');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_LOA_DETAILHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_LOA_HIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_LOA_TIC');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_LOA_TICHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_MODEL_MAPPING');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_PRODUCT');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_PRODUCTHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_QUOTA_DEF_FACT');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_QUOTA_DEF_FACTHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_REINS_FLOATCHR');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_REINS_FLOATCHRHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_REINS_PRODUCT');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_REINS_PRODUCTHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_REINS_RISK');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_RISK_CLASSHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_RISK_MAPPING');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_RISK_MAPPINGHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_TREATY');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_TREATYHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_TREATY_PAYMENT_PLAN');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_TREATY_PLAN');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_TREATY_PLANHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_TREATY_SECTION');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_TREATY_SECTIONHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_REINS_REINSURER');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_REINS_REINSURERHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_RISKHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_RISK_CLASS');
call bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_CONF_EMAIL_RULE');
call bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_CONF_EMAIL_RULEHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_CONF_EMAIL_TEMP');
call bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_CONF_EMAIL_TEMPHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_CONF_EMAIL_TEMPLATE');
call bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_CONF_OPER_TRACK_LOG');
call bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_EMAIL_LOG');
call bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_EXPORT_TRACK_LOG');
call bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_LOG_EMAIL');
call bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_SQL_OPEL_LOG');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_ACT_PROC');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_ACT_PROC_INST');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_BLOB_TRIGGERS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CALENDARS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CENTER_RUNTIME');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CODE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CODE_CONFIG');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CODE_TYPE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_COMPANY');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONFIGACCOUNTCODE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONFIGACCOUNTCODEHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONFIGCENTER');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONFIGENTITYHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONFIGPERIOD');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONFIGPERIODHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONFIGPERIODLOG');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONFIGRUNTIME');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_ACCOUNT_SET');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_ACCOUNT_SET_HIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_ADAPT');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_CHECKRULE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_CHECKRULEHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_CODE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_CODE_HIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_QUOTA');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_QUOTAHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_QUOTA_DEF');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_QUOTA_DEFHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_QUOTA_DETAIL');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_QUOTA_DETAILHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_REGULAR_RULE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_REGULAR_RULEHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CURRENCY');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CURRENCYHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CURRENCYRATE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CURRENCYRATEHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_JOB_TASK_LOG');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_LGX_TEMP');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_LOCKS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_LOGAUDITCONFIG');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_LOG_ACTION');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_LOG_ACTIONHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_LOG_ACTION_DETAIL');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_LOG_ACTION_DETAILHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_MENU');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_MESSAGE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_MESSAGE_CONFIG');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_MESSAGE_FILE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_OPERLOG');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_OPERLOGCONFIG');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_OPERLOGHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_QRTZ_CONF_METHOD_PARAM');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_QRTZ_CONF_TASK');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_QRTZ_CONF_TASK_DETAIL');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_RISK');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_MENU');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_MENU_TASK');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_ROLE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_ROLE_MENU');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_ROLE_MENU_TASK');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_ROLE_TASK');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_TASK');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_USER');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_USERHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_USER_ROLE');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SCHEDULER');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SCHEDULE_CONFIG');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SCHEDULE_JOB_DETAILS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SCHEDULE_JOB_LOG');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SCHEDULE_TRIGGERS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SIMPLE_TRIGGERS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SIMPROP_TRIGGERS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SYS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_TASKID');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_TREATY_CLASS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_TREATY_CLASSHIS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_TRIGGER_GRPS');
call bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_USER');
call bpl_pack_commonutils.DROP_SEQUENCE('SEQ_BPL_LOG_AUDIT_CONFIG');


call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_BUSS_DUTY');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_BUSS_DUTY_HIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_ACCOUNTITEM_MAP');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_ACCOUNT_MAP');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_ACCOUNT_YEAR_MONTH');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_ACCOUNT_YEAR_MONTH_HIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_COA_DEAL');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_CONF_TREATY');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_CONF_TREATYHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_FEE_TYPE_MAPPING');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_FEE_TYPE_MP_HIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_FLOAT_CHARGE');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_FLOAT_CHARGEHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_LOA');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_LOA_DETAIL');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_LOA_DETAILHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_LOA_HIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_LOA_TIC');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_LOA_TICHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_MODEL_MAPPING');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_PRODUCT');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_PRODUCTHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_QUOTA_DEF_FACT');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_QUOTA_DEF_FACTHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_REINS_FLOATCHR');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_REINS_FLOATCHRHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_REINS_PRODUCT');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_REINS_PRODUCTHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_REINS_RISK');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_RISK_CLASSHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_RISK_MAPPING');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_RISK_MAPPINGHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_TREATY');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_TREATYHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_TREATY_PAYMENT_PLAN');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_TREATY_PLAN');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_TREATY_PLANHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_TREATY_SECTION');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_CONF_TREATY_SECTIONHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_REINS_REINSURER');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_REINS_REINSURERHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_RISKHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BBS_SEQ_RISK_CLASS');
call bpl_pack_commonutils.ADD_SEQUENCE('BMS_SEQ_CONF_EMAIL_RULE');
call bpl_pack_commonutils.ADD_SEQUENCE('BMS_SEQ_CONF_EMAIL_RULEHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BMS_SEQ_CONF_EMAIL_TEMP');
call bpl_pack_commonutils.ADD_SEQUENCE('BMS_SEQ_CONF_EMAIL_TEMPHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BMS_SEQ_CONF_EMAIL_TEMPLATE');
call bpl_pack_commonutils.ADD_SEQUENCE('BMS_SEQ_CONF_OPER_TRACK_LOG');
call bpl_pack_commonutils.ADD_SEQUENCE('BMS_SEQ_EMAIL_LOG');
call bpl_pack_commonutils.ADD_SEQUENCE('BMS_SEQ_EXPORT_TRACK_LOG');
call bpl_pack_commonutils.ADD_SEQUENCE('BMS_SEQ_LOG_EMAIL');
call bpl_pack_commonutils.ADD_SEQUENCE('BMS_SEQ_SQL_OPEL_LOG');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_ACT_PROC');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_ACT_PROC_INST');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_BLOB_TRIGGERS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CALENDARS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CENTER_RUNTIME');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CODE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CODE_CONFIG');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CODE_TYPE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_COMPANY');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONFIGACCOUNTCODE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONFIGACCOUNTCODEHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONFIGCENTER');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONFIGENTITYHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONFIGPERIOD');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONFIGPERIODHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONFIGPERIODLOG');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONFIGRUNTIME');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_ACCOUNT_SET');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_ACCOUNT_SET_HIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_ADAPT');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_CHECKRULE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_CHECKRULEHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_CODE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_CODE_HIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_QUOTA');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_QUOTAHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_QUOTA_DEF');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_QUOTA_DEFHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_QUOTA_DETAIL');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_QUOTA_DETAILHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_REGULAR_RULE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CONF_REGULAR_RULEHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CURRENCY');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CURRENCYHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CURRENCYRATE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_CURRENCYRATEHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_JOB_TASK_LOG');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_LOCKS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_LOGAUDITCONFIG');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_LOG_ACTION');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_LOG_ACTIONHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_LOG_ACTION_DETAIL');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_LOG_ACTION_DETAILHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_MENU');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_MESSAGE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_MESSAGE_CONFIG');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_MESSAGE_FILE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_OPERLOG');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_OPERLOGCONFIG');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_OPERLOGHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_QRTZ_CONF_METHOD_PARAM');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_QRTZ_CONF_TASK');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_QRTZ_CONF_TASK_DETAIL');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_RISK');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SAA_MENU');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SAA_MENU_TASK');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SAA_ROLE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SAA_ROLE_MENU');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SAA_ROLE_MENU_TASK');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SAA_ROLE_TASK');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SAA_TASK');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SAA_USER');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SAA_USERHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SAA_USER_ROLE');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SCHEDULER');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SCHEDULE_CONFIG');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SCHEDULE_JOB_DETAILS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SCHEDULE_JOB_LOG');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SCHEDULE_TRIGGERS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SIMPLE_TRIGGERS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SIMPROP_TRIGGERS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_SYS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_TASKID');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_TREATY_CLASS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_TREATY_CLASSHIS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_TRIGGER_GRPS');
call bpl_pack_commonutils.ADD_SEQUENCE('BPL_SEQ_USER');
call bpl_pack_commonutils.ADD_SEQUENCE('SEQ_BPL_LOG_AUDIT_CONFIG');

/
--sequence 
DECLARE v_count NUMBER(15);
BEGIN

  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_PUB_TASK_LOG');
  SELECT NVL(max(TASK_LOG_ID),0)+1 INTO v_count FROM BPL_LOG_PUB_TASK;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_PUB_TASK_LOG
              minvalue 1
              maxvalue ************************9999
              start with 200
              increment by 1';

  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_QRTZ_CONF_TASK');
  SELECT NVL(max(CONF_TASK_ID),0)+1 INTO v_count FROM bpl_qrtz_conf_task;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_QRTZ_CONF_TASK
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_QRTZ_CONF_TASK_DETAIL');
  SELECT NVL(max(TASK_DETAIL_ID),0)+1 INTO v_count FROM bpl_qrtz_conf_task_detail;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_QRTZ_CONF_TASK_DETAIL
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_OPERLOG');
  SELECT NVL(max(CONFIG_ID),0)+1 INTO v_count FROM bms_conf_operlog;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_OPERLOG
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';
        
 
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_COMPANY');
  SELECT NVL(max(COMPANY_ID),0)+1 INTO v_count FROM bpl_company;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_COMPANY
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

 
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_ACCOUNT_SET');
  SELECT NVL(max(ACCOUNT_SET_ID),0)+1 INTO v_count FROM bbs_conf_account_set;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_CONF_ACCOUNT_SET
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';
        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_LOA');
  SELECT NVL(max(LOA_ID),0)+1 INTO v_count FROM bbs_conf_loa;
  EXECUTE IMMEDIATE 'create sequence BBS_SEQ_CONF_LOA
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_LOA_DETAIL');
  SELECT NVL(max(LOA_DETAIL_ID),0)+1 INTO v_count FROM bbs_conf_loa_detail;
  EXECUTE IMMEDIATE 'create sequence BBS_SEQ_CONF_LOA_DETAIL
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_LOA_TIC');
  SELECT NVL(max(LOA_TIC_ID),0)+1 INTO v_count FROM bbs_conf_loa_tic;
  EXECUTE IMMEDIATE 'create sequence BBS_SEQ_CONF_LOA_TIC
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_PRODUCT');
  SELECT NVL(max(PRODUCT_ID),0)+1 INTO v_count FROM bbs_conf_product;
  EXECUTE IMMEDIATE 'create sequence BBS_SEQ_CONF_PRODUCT
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';
        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_CONF_TREATY');
  SELECT NVL(max(CONF_TREATY_ID),0)+1 INTO v_count FROM bbs_conf_base_treaty;
  EXECUTE IMMEDIATE 'create sequence BBS_SEQ_CONF_CONF_TREATY
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';  
        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('bpl_seq_act_proc');
  SELECT NVL(max(PROC_ID),0)+1 INTO v_count FROM bpl_act_re_procdef;
  EXECUTE IMMEDIATE 'create sequence bpl_seq_act_proc
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

    bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_CONF_EMAIL_TEMP');
  SELECT NVL(max(EMAIL_TEMP_ID),0)+1 INTO v_count FROM bms_conf_email_temp;
  EXECUTE IMMEDIATE 'create sequence BMS_SEQ_CONF_EMAIL_TEMP
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

  
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BMS_SEQ_CONF_EMAIL_RULE');
  SELECT NVL(max(EMAIL_RULE_ID),0)+1 INTO v_count FROM bms_conf_email_rule;
  EXECUTE IMMEDIATE 'create sequence BMS_SEQ_CONF_EMAIL_RULE
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

 bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_REGULAR_RULE');
  SELECT NVL(max(RULE_ID),0)+1 INTO v_count FROM bbs_conf_regular_rule;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_CONF_REGULAR_RULE
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

 bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_FEE_TYPE_MAPPING');
  SELECT NVL(max(MAPPING_ID),0)+1 INTO v_count FROM bbs_conf_fee_type_mapping;
  EXECUTE IMMEDIATE 'create sequence BBS_SEQ_CONF_FEE_TYPE_MAPPING
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';

 bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_CHECKRULE');
  SELECT NVL(max(CHECK_RULE_ID),0)+1 INTO v_count FROM bpl_conf_checkrule;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_CONF_CHECKRULE
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';
        
 bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_CODE');
  SELECT NVL(max(CODE_ID),0)+1 INTO v_count FROM bpl_conf_code;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_CONF_CODE
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';  
        
 bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CODE_CONFIG');
  SELECT NVL(max(CODE_CONF_ID),0)+1 INTO v_count FROM bpl_code_config;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_CODE_CONFIG
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1'; 

  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_CONF_ADAPT');
  SELECT NVL(max(ID),0)+1 INTO v_count FROM BPL_CONF_ADAPT;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_CONF_ADAPT
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';
        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_USER');
  SELECT NVL(max(USER_ID),0)+1 INTO v_count FROM bpl_saa_user;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_SAA_USER
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1 
        CYCLE';
        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_USER_ROLE');
  SELECT NVL(max(USER_ROLE_ID),0)+1 INTO v_count FROM bpl_saa_user_role;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_SAA_USER_ROLE
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1 
        CYCLE';
        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_ROLE');
  SELECT NVL(max(ROLE_ID),0)+1 INTO v_count FROM bpl_saa_role;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_SAA_ROLE
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1 
        CYCLE';   
        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_ROLE_TASK');
  SELECT NVL(max(ROLE_TASK_ID),0)+1 INTO v_count FROM bpl_saa_role_task;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_SAA_ROLE_TASK
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1 
        CYCLE ';
        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_ROLE_MENU');
  SELECT NVL(max(ROLE_MENU_ID),0)+1 INTO v_count FROM bpl_saa_role_menu;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_SAA_ROLE_MENU
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1
        CYCLE ';
        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_ROLE_MENU_TASK');
  SELECT NVL(max(ROLE_MENU_TASK_ID),0)+1 INTO v_count FROM bpl_saa_role_menu_task;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_SAA_ROLE_MENU_TASK
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1
        CYCLE ';
        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_TASK');
  SELECT NVL(max(TASK_ID),0)+1 INTO v_count FROM bpl_saa_task;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_SAA_TASK
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1
        CYCLE ';        
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_MENU');
  SELECT NVL(max(MENU_ID),0)+1 INTO v_count FROM bpl_saa_menu;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_SAA_MENU
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1
        CYCLE ';

    bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BPL_SEQ_SAA_MENU_TASK');
  SELECT NVL(max(MENU_TASK_ID),0)+1 INTO v_count FROM bpl_saa_menu_task;
  EXECUTE IMMEDIATE 'create sequence BPL_SEQ_SAA_MENU_TASK
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1
        CYCLE ';
        
bpluser.bpl_pack_commonutils.DROP_SEQUENCE('bpl_seq_sys');
  SELECT NVL(max(SYS_ID),0)+1 INTO v_count FROM bpl_saa_basic_system;
  EXECUTE IMMEDIATE 'create sequence bpl_seq_sys
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1
        CYCLE ';      
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_ACCOUNT_MAP');
  SELECT NVL(max(MAPPING_ID),0)+1 INTO v_count FROM bbs_conf_account_mapping;
  EXECUTE IMMEDIATE 'create sequence BBS_SEQ_CONF_ACCOUNT_MAP
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1
        CYCLE ';  

 bpluser.bpl_pack_commonutils.DROP_SEQUENCE('BBS_SEQ_CONF_MODEL_MAPPING');
  SELECT NVL(max(MAPPING_ID),0)+1 INTO v_count FROM bbs_conf_model_mapping;
  EXECUTE IMMEDIATE 'create sequence BBS_SEQ_CONF_MODEL_MAPPING
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1
        CYCLE ';  

  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('bpl_seq_configaccountcode');
  SELECT NVL(max(ACCOUNT_ID),0)+1 INTO v_count FROM bbs_account;
  EXECUTE IMMEDIATE 'create sequence bpl_seq_configaccountcode
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1
        CYCLE ';  
  bpluser.bpl_pack_commonutils.DROP_SEQUENCE('bpl_seq_risk');
  SELECT NVL(max(risk_id),0)+1 INTO v_count FROM bbs_conf_risk;
  EXECUTE IMMEDIATE 'create sequence bpl_seq_risk
              minvalue 1
              maxvalue ************************9999
              start with '||v_count||'
              increment by 1';
end;

/

--bpluser.bpl_seq_log_action 权限赋予acc用户
GRANT SELECT ON bpluser.bpl_seq_log_action TO accuser;

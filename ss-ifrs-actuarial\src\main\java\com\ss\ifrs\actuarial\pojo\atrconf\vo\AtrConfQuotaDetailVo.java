/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-11-10 17:08:27
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-10 17:08:27<br/>
 * Description: null<br/>
 * Table Name: bbs_conf_quota_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 * <AUTHOR>
 */
@ApiModel(value = "指标配置明细表")
public class AtrConfQuotaDetailVo implements Serializable {
    /**
     * Database column: bbs_conf_quota_detail.quota_detail_id
     * Database remarks: quotaDetailId|主键
     */
    @ApiModelProperty(value = "quotaDetailId|主键", required = true)
    private Long quotaDetailId;

    /**
     * Database column: bbs_conf_quota_detail.quota_id
     * Database remarks: quotaId|指标
     */
    @ApiModelProperty(value = "quotaId|指标", required = true)
    private Long quotaId;

    /**
     * Database column: bbs_conf_quota_detail.quota_period
     * Database remarks: quotaPeriod|发展期
     */
    @ApiModelProperty(value = "quotaPeriod|发展期", required = false)
    private Long quotaPeriod;

    /**
     * Database column: bbs_conf_quota_detail.quota_value
     * Database remarks: quotaValue|指标值
     */
    @ApiModelProperty(value = "quotaValue|指标值", required = false)
    private String quotaValue;

    /**
     * Database column: bbs_conf_quota_detail.creator_id
     * Database remarks: creatorId|创建人
     */
    @ApiModelProperty(value = "creatorId|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: bbs_conf_quota_detail.create_time
     * Database remarks: createTime|创建时间
     */
    @ApiModelProperty(value = "createTime|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: bbs_conf_quota_detail.updator_id
     * Database remarks: updatorId|最后修改人
     */
    @ApiModelProperty(value = "updatorId|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: bbs_conf_quota_detail.update_time
     * Database remarks: updateTime|最后修改时间
     */
    @ApiModelProperty(value = "updateTime|最后修改时间", required = false)
    private Date updateTime;

    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Integer serialNo;

    private Long entityId;

    private Long modelDefId;

    @ApiModelProperty(value = "year_month|评估期", required = true)
    private String yearMonth;

    private String loaCode;

    private String dimension;

    private String dimensionValue;

    private String quotaCode;
    private String codeType;
    private String businessModel;
    private String percentHundredIs;

    List<AtrConfQuotaVo> atrConfQuotaVoList;

    // 用于区分新增与编辑查看
    private String type;

    private static final long serialVersionUID = 1L;

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public Long getQuotaDetailId() {
        return quotaDetailId;
    }

    public void setQuotaDetailId(Long quotaDetailId) {
        this.quotaDetailId = quotaDetailId;
    }

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getQuotaPeriod() {
        return quotaPeriod;
    }

    public void setQuotaPeriod(Long quotaPeriod) {
        this.quotaPeriod = quotaPeriod;
    }

    public String getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(String quotaValue) {
        this.quotaValue = quotaValue;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public String getBusinessModel() {
        return businessModel;
    }

    public void setBusinessModel(String businessModel) {
        this.businessModel = businessModel;
    }

    public String getPercentHundredIs() {
        return percentHundredIs;
    }

    public void setPercentHundredIs(String percentHundredIs) {
        this.percentHundredIs = percentHundredIs;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<AtrConfQuotaVo> getAtrConfQuotaVoList() {
        return atrConfQuotaVoList;
    }

    public void setAtrConfQuotaVoList(List<AtrConfQuotaVo> bbsConfQuotaVoList) {
        this.atrConfQuotaVoList = bbsConfQuotaVoList;
    }

    public Long getModelDefId() {
        return modelDefId;
    }

    public void setModelDefId(Long modelDefId) {
        this.modelDefId = modelDefId;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getDimensionValue() {
        return dimensionValue;
    }

    public void setDimensionValue(String dimensionValue) {
        this.dimensionValue = dimensionValue;
    }
}
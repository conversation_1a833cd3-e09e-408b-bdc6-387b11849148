truncate table RPT_BUSS_REPORT;
truncate table RPT_BUSS_REPORTHIS;
truncate table RPT_BUSS_REPORT_ITEM_DATA;
truncate table RPT_BUSS_REPORT_ITEM_DATAHIS;

truncate table RPT_CONF_CODE_HIS;
truncate table RPT_CONF_ITEM_RULE_ARTICLEHIS;
truncate table RPT_CONF_ITEM_RULE_SUB_HIS;
truncate table RPT_CONF_REPORT_ITEMHIS;
truncate table RPT_CONF_REPORT_ITEM_RULEHIS;
truncate table RPT_CONF_REPORT_TEMPLATEHIS;

truncate table RPT_DAP_ARTICLE_BALANCE;
truncate table RPT_DAP_ARTICLE_BAL_HIS;
truncate table RPT_DAP_LEDGER_BALANCE;
truncate table RPT_DAP_LEDGER_BALANCEHIS;


truncate table RPT_DUCT_REPORT_ITEM_DATA_SUB;
truncate table RPT_DUCT_REPORT_ITEM_DATA;
truncate table RPT_DUCT_REPORT_ITEM_DATAHIS;
truncate table RPT_DUCT_REPORT_TEMPLATE;
truncate table RPT_DUCT_REPORT_TEMPLATEHIS;
truncate table RPT_DUCT_REPORT_TEMP_DETAIL;
truncate table RPT_DUCT_REPORT_TEMP_DETAILHIS;
truncate table RPT_LOG_REPORT_ITEM_DRAW_TASK;

--列项提数的维度数据表
TRUNCATE TABLE rpt_buss_dimension_data;
TRUNCATE TABLE rpt_buss_dimension_datahis;
TRUNCATE TABLE rpt_duct_dimension_data;
TRUNCATE TABLE rpt_duct_dimension_data_sub;

--计量报表相关表
truncate table RPT_DAP_QTC_EVALUATE;
truncate table RPT_DAP_QTC_EVALUATEHIS;
truncate table RPT_BUSS_TRIAL_REPORT_ITEM_DATAHIS;
truncate table RPT_BUSS_TRIAL_REPORT_ITEM_DATA;
truncate table RPT_BUSS_TRIAL_REPORT_RECORD;
truncate table RPT_BUSS_REPORT_QTC_CONDITION;


-- 报表生成任务相关表
TRUNCATE TABLE RPT_BUSS_REPORT_TASK;
TRUNCATE TABLE RPT_BUSS_REPORT_TASK_ITEM;
TRUNCATE TABLE rpt_buss_report_task_condition;
TRUNCATE TABLE rpt_buss_report_task_template;


--初始化业务期间
--业务期间相关表
TRUNCATE TABLE RPT_CONF_BUSSPERIOD_DETAIL;
TRUNCATE TABLE RPT_CONF_BUSSPERIOD;

--===配置数据初始化===
--业务期间配置
insert into rpt_conf_bussperiod (BUSS_PERIOD_ID, entity_id, BOOK_CODE, YEAR_MONTH, EXECUTION_STATE, VALID_IS, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME)
values (rpt_seq_conf_bussprd.nextval, 1, 'BookI17', '202101', '0', '1', 1, sysdate, 1, sysdate);



  --提交事务
  COMMIT;

/
--插入业务期间详情任务配置
DECLARE
BEGIN
  --1、循环xxx_conf_period
  FOR cur_period IN (SELECT buss_period_id,
                            execution_state
                       FROM rpt_conf_bussperiod
                      WHERE valid_is = '1') LOOP
    --2、循环xxx_conf_table
    FOR cur_table IN (SELECT biz_type_id,
                             biz_code,
                             direction,
               system_code
                        FROM rpt_conf_table) LOOP
    
    
    --插入xxx_conf_bussperiod_detail
    INSERT INTO rptuser.rpt_conf_bussperiod_detail
    (period_detail_id,
     buss_period_id,
     biz_type_id,
     task_time,
     exec_result,
     ready_state,
     creator_id,
     create_time,
     updator_id,
     update_time)
    VALUES
    (rpt_seq_conf_bussprd_dtl.NEXTVAL,
     cur_period.buss_period_id,
         cur_table.biz_type_id,
     NULL,
     NULL,
     (CASE WHEN cur_period.execution_state = '1' OR cur_period.execution_state = '3' THEN '1' ELSE '0' END), --业务期间：1-已准备中或3-已完成，则准备状态为1-已准备，其它情况为0-准备中
     1,
     SYSDATE,
     NULL,
     NULL);

      --提交事务
        COMMIT;
    END LOOP;
  END LOOP;

END;

/
begin
    for cur in (select task_code from dmuser.ods_policy_main where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_policy_main a
        using (select id
                 from dmuser.ods_policy_main
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_policy_premium where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_policy_premium a
        using (select id
                 from dmuser.ods_policy_premium
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_policy_payment_plan where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_policy_payment_plan a
        using (select id
                 from dmuser.ods_policy_payment_plan
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_outward where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_outward a
        using (select id
                 from dmuser.ods_reins_outward
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_outward_detail where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_outward_detail a
        using (select id
                 from dmuser.ods_reins_outward_detail
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_bill where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_bill a
        using (select id
                 from dmuser.ods_reins_bill
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_bill_detail where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_bill_detail a
        using (select id
                 from dmuser.ods_reins_bill_detail
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_claim_main where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_claim_main a
        using (select id
                 from dmuser.ods_claim_main
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_claim_loss where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_claim_loss a
        using (select id
                 from dmuser.ods_claim_loss
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_claim_loss_detail where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_claim_loss_detail a
        using (select id
                 from dmuser.ods_claim_loss_detail
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_claim_outstanding where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_claim_outstanding a
        using (select id
                 from dmuser.ods_claim_outstanding
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_acc_payment where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_acc_payment a
        using (select id
                 from dmuser.ods_acc_payment
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_acc_receivable where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_acc_receivable a
        using (select id
                 from dmuser.ods_acc_receivable
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_fin_article_balance where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_fin_article_balance a
        using (select id
                 from dmuser.ods_fin_article_balance
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_fin_ledger_balance where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_fin_ledger_balance a
        using (select id
                 from dmuser.ods_fin_ledger_balance
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_fin_voucher where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_fin_voucher a
        using (select id
                 from dmuser.ods_fin_voucher
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_fin_voucher_detail where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_fin_voucher_detail a
        using (select id
                 from dmuser.ods_fin_voucher_detail
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_base_entity where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_base_entity a
        using (select id
                 from dmuser.ods_base_entity
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_base_account where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_base_account a
        using (select id
                 from dmuser.ods_base_account
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_base_currency where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_base_currency a
        using (select id
                 from dmuser.ods_base_currency
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_base_currency_rate where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_base_currency_rate a
        using (select id
                 from dmuser.ods_base_currency_rate
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_base_product where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_base_product a
        using (select id
                 from dmuser.ods_base_product
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_base_risk_class where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_base_risk_class a
        using (select id
                 from dmuser.ods_base_risk_class
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_base_risk where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_base_risk a
        using (select id
                 from dmuser.ods_base_risk
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_base_risk_mapping where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_base_risk_mapping a
        using (select id
                 from dmuser.ods_base_risk_mapping
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_treaty_class where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_treaty_class a
        using (select id
                 from dmuser.ods_reins_treaty_class
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_base_treaty where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_base_treaty a
        using (select id
                 from dmuser.ods_reins_base_treaty
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_treaty where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_treaty a
        using (select id
                 from dmuser.ods_reins_treaty
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_treaty_section where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_treaty_section a
        using (select id
                 from dmuser.ods_reins_treaty_section
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code
                  from dmuser.ods_reins_treaty_payment_plan
                 where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_treaty_payment_plan a
        using (select id
                 from dmuser.ods_reins_treaty_payment_plan
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_risk where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_risk a
        using (select id
                 from dmuser.ods_reins_risk
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_reinsurer where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_reinsurer a
        using (select id
                 from dmuser.ods_reins_reinsurer
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;
    for cur in (select task_code from dmuser.ods_reins_float_charge where task_status <> '0' group by task_code) loop
        --src回滚初始化
        merge into dmuser.ods_reins_float_charge a
        using (select id
                 from dmuser.ods_reins_float_charge
                where task_code = cur.task_code
                  and task_status <> '0') b
        on (a.id = b.id)
        when matched then
            update set task_status = '0';
        commit;
    end loop;

end;
/
--2、tgt表清除
truncate table dm_claim_loss;
truncate table dm_claim_loss_detail;
truncate table dm_policy_main;
truncate table dm_reins_outward;
truncate table dm_policy_premium;
truncate table dm_reins_bill;
truncate table dm_reins_risk;
truncate table dm_reins_base_treaty;
truncate table dm_reins_reinsurer;
truncate table dm_reins_float_charge;
truncate table dm_acc_payment;
truncate table dm_fin_ledger_balance;
truncate table dm_claim_main;
truncate table dm_claim_outstanding;
truncate table dm_policy_payment_plan;
truncate table dm_reins_treaty;
truncate table dm_reins_treaty_section;
truncate table dm_reins_treaty_class;
truncate table dm_fin_article_balance;
truncate table dm_fin_voucher;
truncate table dm_fin_voucher_detail;
truncate table dm_base_currency;
truncate table dm_base_currency_rate;
truncate table dm_base_risk;
truncate table dm_base_risk_class;
truncate table dm_base_risk_mapping;
truncate table dm_base_account;
truncate table dm_base_entity;
truncate table dm_acc_receivable;
truncate table dm_base_product;
truncate table dm_reins_outward_detail;
truncate table dm_reins_bill_detail;
truncate table dm_reins_treaty_payment_plan;

--3、清除计量单元表数据
TRUNCATE TABLE dm_buss_cmunit_direct;
TRUNCATE TABLE dm_buss_cmunit_fac_outwards;
TRUNCATE TABLE dm_buss_cmunit_treaty;

--更新信号表数据
merge into ods_data_push_signal a
using ods_data_push_signal b
on (a.DATA_PUSH_SIGNAL_ID = b.DATA_PUSH_SIGNAL_ID)
when matched then
  update
     set
      a.task_status = '0',
      START_DEAL_TIME = null,
      DEAL_MSG = null,
      END_DEAL_TIME = null;
delete from ods_data_push_signalhis;


--5、清除文件上传的日志
TRUNCATE TABLE dm_duct_draw_log;

--6、清除校验日志
TRUNCATE TABLE dm_log_check_rule;
TRUNCATE TABLE dm_log_data_verify;
TRUNCATE TABLE dm_log_data_verify_detail;

--7、清除盈亏日志
TRUNCATE TABLE dm_log_buss_cmunit;
TRUNCATE TABLE dm_log_buss_cmunit_detail;
TRUNCATE TABLE dm_duct_direct_profit_unit;
TRUNCATE TABLE dm_duct_direct_profit_amount;
TRUNCATE TABLE dm_duct_direct_profit_param;

TRUNCATE TABLE dm_duct_profit_param_value;
TRUNCATE TABLE dm_duct_profit_unit;
TRUNCATE TABLE dm_duct_profit_unit_sub;

TRUNCATE TABLE dm_duct_treaty_profit_amount;
TRUNCATE TABLE dm_duct_treaty_profit_param;
TRUNCATE TABLE dm_duct_treaty_profit_unit;

--8、9、重新统计
call dm_pack_duct_stat.proc_paring_stat_all();


--10、业务期间初始化
TRUNCATE TABLE dm_conf_bussperiod_detail;
TRUNCATE TABLE dm_conf_bussperiod;

--10、业务期间初始化
TRUNCATE TABLE dm_conf_bussperiod_detail;
TRUNCATE TABLE dm_conf_bussperiod;

/
declare 
v_year_month varchar2(50) := '201101';
--11、增加业务详细表数据
begin 
  
  
  insert into dm_conf_bussperiod (BUSS_PERIOD_ID, entity_id, YEAR_MONTH, EXECUTION_STATE, VALID_IS, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME)
values (DM_SEQ_CONF_BUSSPERIOD.Nextval, 1, v_year_month, '0', '1', 1, sysdate, 0, null);

  for cur in (
		select biz_type_id ,'1' as DIRECTION  from dm_conf_table where VALID_IS = '1'
      union all 
        select biz_type_id ,'0' as DIRECTION from dm_conf_table_output where VALID_IS = '1'
        order by DIRECTION desc ,biz_type_id
) loop 
  insert into dm_conf_bussperiod_detail (PERIOD_DETAIL_ID, BUSS_PERIOD_ID, BIZ_TYPE_ID, TASK_TIME, EXEC_RESULT, DIRECTION, READY_STATE, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME)
values (DM_SEQ_CONF_BUSSPERIOD_DETAIL.Nextval,(select BUSS_PERIOD_ID from dm_conf_bussperiod where year_month = v_year_month),  cur.biz_type_id, null, null, cur.DIRECTION, '0', 1, sysdate, null, null);

commit;

  end loop;
  
end;
/
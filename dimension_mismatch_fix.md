# 维度不匹配问题修复方案

## 问题描述
在 `AtrBussLrcTotService` 中，`preToLrcUls` 索引的维度是到 `treatyNo`、`policyNo`、`endorseSeqNo`、`kindCode` 这4个维度的，但在计算时需要使用 `sectionoCode` 和 `reinsurerCode` 这两个额外的维度。由于维度不匹配，直接获取累计值会存在数据问题。

## 解决方案
创建一个新的6维度索引结构来存储包含 `sectionoCode` 和 `reinsurerCode` 的累计已赚保费和累计已赚手续费，只查询和处理四个核心费用字段：
- `pre_cuml_ed_premium` (上期累计已赚保费)
- `pre_cuml_ed_net_fee` (上期累计已赚手续费)
- `cur_ed_premium` (当期已赚保费)
- `cur_ed_net_fee` (当期已赚手续费)

## 修改内容

### 1. 新增6维度索引
在 `AtrBussLrcTotService` 类中新增：
```java
// 新增：6维度索引，用于存储包含sectionoCode和reinsurerCode的累计数据
private final IndexFactory<AtrBussToLrcTUlR> preToLrcUlsDetailed = new IndexFactory<>();
```

### 2. 新增6维度键生成方法
```java
/**
 * 创建6维度键，包含sectionoCode和reinsurerCode
 * 用于AtrBussToLrcTUlR的详细维度索引
 */
private List<Object> createDetailedUnitKey(Object vo) {
    List<Object> key = new ArrayList<>();
    key.add(EcfUtil.readField(vo, "treatyNo"));
    key.add(EcfUtil.readField(vo, "policyNo"));
    key.add(EcfUtil.readField(vo, "endorseSeqNo"));
    key.add(EcfUtil.readField(vo, "kindCode"));
    key.add(EcfUtil.readField(vo, "sectionoCode"));
    key.add(EcfUtil.readField(vo, "reinsurerCode"));
    return key;
}
```

### 3. 新增DAO方法
在 `AtrBussLrcToDao` 接口中新增：
```java
// 查找上一期的详细维度数据（包含sectionoCode和reinsurerCode）
List<AtrBussToLrcTUlR> listPreBussToLrcTUlRDetailed(Map<String, Object> paramMap);
```

### 4. 新增SQL查询
在 `AtrBussLrcToCustDao.xml` 中新增，只查询必要的四个费用字段：
```xml
<select id="listPreBussToLrcTUlRDetailed" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUlR">
    SELECT r.treaty_no             as treatyNo,
           r.policy_no             AS policyNo,
           r.endorse_seq_no        AS endorseSeqNo,
           r.kind_code             AS kindCode,
           r.sectiono_code         AS sectionoCode,
           r.reinsurer_code        AS reinsurerCode,
           r.pre_cuml_ed_premium   AS preCumlEdPremium,
           r.pre_cuml_ed_net_fee   AS preCumlEdNetFee,
           r.cur_ed_premium        AS curEdPremium,
           r.cur_ed_net_fee        AS curEdNetFee
    FROM atr_buss_to_lrc_t_ul_r r
    where r.year_month = #{lastYearMonth,jdbcType=VARCHAR}
      and r.action_no = #{lastActionNo,jdbcType=VARCHAR}
</select>
```

### 5. 新增数据收集方法
```java
/**
 * 收集包含sectionoCode和reinsurerCode的6维度历史数据
 */
private void collectionPrePolicyInfoDetailed() {
    List<AtrBussToLrcTUlR> atrBussToLrcTUlRs = atrBussLrcToDao.listPreBussToLrcTUlRDetailed(commonParamMap);
    atrBussToLrcTUlRs.forEach(item -> {
        List<?> detailedKey = createDetailedUnitKey(item);
        preToLrcUlsDetailed.add(detailedKey, item);
    });
}
```

### 6. 修改累计值获取逻辑
在 `calcIcp` 方法中：
```java
// 获取历史累计已赚数据 - 使用6维度索引获取精确的累计值
List<Object> detailedKey = createDetailedUnitKey(icu);
AtrBussToLrcTUlR preToLrcUlR = preToLrcUlsDetailed.one(detailedKey);
if (preToLrcUlR != null) {
    BigDecimal preCumlEdPremium = nvl(preToLrcUlR.getPreCumlEdPremium())
            .add(nvl(preToLrcUlR.getCurEdPremium()));
    BigDecimal preCumlEdNetFee = nvl(preToLrcUlR.getPreCumlEdNetFee())
            .add(nvl(preToLrcUlR.getCurEdNetFee()));
    icu.setPreCumlEdPremium(preCumlEdPremium);
    icu.setPreCumlEdNetFee(preCumlEdNetFee);
} else {
    icu.setPreCumlEdPremium(BigDecimal.ZERO);
    icu.setPreCumlEdNetFee(BigDecimal.ZERO);
}
```

### 7. 数据库表结构调整
为 `atr_buss_to_lrc_t_ul_r` 表添加四个费用字段：
```sql
-- 添加当期已赚保费字段
ALTER TABLE atruser.atr_buss_to_lrc_t_ul_r
ADD COLUMN IF NOT EXISTS cur_ed_premium DECIMAL(21,4) DEFAULT 0;

-- 添加当期已赚手续费字段
ALTER TABLE atruser.atr_buss_to_lrc_t_ul_r
ADD COLUMN IF NOT EXISTS cur_ed_net_fee DECIMAL(21,4) DEFAULT 0;

-- 添加上期累计已赚保费字段
ALTER TABLE atruser.atr_buss_to_lrc_t_ul_r
ADD COLUMN IF NOT EXISTS pre_cuml_ed_premium DECIMAL(21,4) DEFAULT 0;

-- 添加上期累计已赚手续费字段
ALTER TABLE atruser.atr_buss_to_lrc_t_ul_r
ADD COLUMN IF NOT EXISTS pre_cuml_ed_net_fee DECIMAL(21,4) DEFAULT 0;
```

### 8. 实体类调整
移除 `AtrBussToLrcTUlR` 类中 `curEdPremium` 和 `curEdNetFee` 字段的 `@IgnoreCol` 注解，使其可以被持久化到数据库。

### 9. 新增聚合方法
为了在汇总到4维度时正确处理累计值，新增了 `getAggregatedPreCumlValues` 方法，该方法能够将所有匹配4维度键的6维度历史数据进行聚合。

## 优势
1. **保持向后兼容**：原有的4维度索引和逻辑保持不变
2. **数据准确性**：通过6维度索引确保累计值计算的准确性
3. **清晰的职责分离**：6维度用于精确计算，4维度用于汇总展示
4. **扩展性好**：如果将来需要更多维度，可以采用类似的方案

## 影响范围
- `AtrBussLrcTotService.java`：新增索引和相关方法
- `AtrBussLrcToDao.java`：新增DAO方法
- `AtrBussLrcToCustDao.xml`：新增SQL查询
- `AtrBussToLrcTUlR.java`：移除字段的@IgnoreCol注解
- `atr_buss_to_lrc_t_ul_r` 数据库表：新增四个费用字段

## 测试建议
1. 验证6维度索引能正确收集历史数据
2. 验证累计值计算的准确性
3. 验证4维度汇总逻辑的正确性
4. 进行性能测试，确保新增索引不会显著影响性能

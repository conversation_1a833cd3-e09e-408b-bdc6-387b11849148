# AtrBussLrcTotService 最终优化方案

## 优化背景
根据用户反馈，之前的按需查询方案效率太低，每次都需要从数据库中读取数据，导致大量IO。用户建议参考 `AtrBussLrcDdService` 的 `calcIcu()` 方法设计，采用批次处理的方式。

## 最终优化方案

### 核心思路
1. **利用现有分片表**: 使用 `atr_temp_to_t_lrc_u` 表的 `pn` 字段进行分片
2. **批次处理**: 每次获取一个分片的基础数据
3. **批量查询**: 根据当前批次的保单信息，批量获取对应的历史详细数据
4. **内存映射**: 在内存中建立映射关系，避免重复查询

### 技术实现

#### 1. DAO层优化
```java
// 新增方法：获取指定分片的基础保单数据
List<AtrBussToLrcTUlR> getPartBasePolicyData(Map<String, Object> paramMap);

// 新增方法：批量获取历史详细数据
List<AtrBussToLrcTUlR> getPreDetailedDataByBatch(
    @Param("policyKeys") List<Map<String, Object>> policyKeys,
    @Param("lastActionNo") String lastActionNo,
    @Param("lastYearMonth") String lastYearMonth);
```

#### 2. Service层重构
```java
private void calcIcp() {
    // 分片批次处理
    for (int partNo = 0; partNo < PARTITION_SIZE; partNo++) {
        Map<String, Object> paramMap = new HashMap<>(commonParamMap);
        paramMap.put("pn", partNo);

        // 1. 获取当前分片的基础数据
        List<AtrBussToLrcTUlR> partPolicyList = getPartPolicyData(paramMap);
        
        if (partPolicyList.isEmpty()) {
            continue; // 跳过空分片
        }

        // 2. 批量获取这批数据对应的历史详细数据
        Map<String, AtrBussToLrcTUlR> preDetailedDataMap = getPreDetailedDataForBatch(partPolicyList);

        // 3. 处理当前分片的数据
        partPolicyList.forEach(icu -> calcIcp(icu, preDetailedDataMap));
    }
}
```

#### 3. 批量查询优化
```java
private Map<String, AtrBussToLrcTUlR> getPreDetailedDataForBatch(List<AtrBussToLrcTUlR> batchPolicyList) {
    // 构建查询参数
    List<Map<String, Object>> policyKeys = new ArrayList<>();
    for (AtrBussToLrcTUlR policy : batchPolicyList) {
        Map<String, Object> key = new HashMap<>();
        key.put("treatyNo", policy.getTreatyNo());
        key.put("policyNo", policy.getPolicyNo());
        key.put("endorseSeqNo", policy.getEndorseSeqNo());
        key.put("kindCode", policy.getKindCode());
        key.put("sectionoCode", policy.getSectionoCode());
        key.put("reinsurerCode", policy.getReinsurerCode());
        policyKeys.add(key);
    }

    // 批量查询历史详细数据
    List<AtrBussToLrcTUlR> preDetailedDataList = atrBussLrcToDao.getPreDetailedDataByBatch(
        policyKeys, lastActionNo, lastYearMonth);

    // 构建映射表，便于快速查找
    Map<String, AtrBussToLrcTUlR> preDetailedDataMap = new HashMap<>();
    for (AtrBussToLrcTUlR preData : preDetailedDataList) {
        String key = createDetailedKey(preData);
        preDetailedDataMap.put(key, preData);
    }

    return preDetailedDataMap;
}
```

## 性能优化效果

### IO优化
- **优化前**: 每条数据都需要单独查询历史数据，产生大量IO
- **优化后**: 每个分片只需要2次查询（基础数据1次 + 历史数据1次），大幅减少IO

### 内存优化
- **优化前**: 全量数据常驻内存（`treatyPolicyList` + `preToLrcUlsDetailed`）
- **优化后**: 只在处理时临时加载当前分片数据，处理完即释放

### 查询效率
- **优化前**: N次单条查询（N为保单数量）
- **优化后**: 2次批量查询 × 分片数量，查询次数大幅减少

## 关键优势

1. **减少IO**: 从N次单条查询改为批量查询，大幅减少数据库IO
2. **内存友好**: 分片处理，避免大量数据常驻内存
3. **无需新表**: 利用现有的分片表结构，无需额外创建表
4. **业务逻辑不变**: 保持原有的6维度匹配和计算逻辑
5. **可扩展性好**: 分片数量可配置，适应不同数据量场景

## 删除的冗余代码

1. **内存索引**: 删除 `preToLrcUlsDetailed` 索引
2. **内存列表**: 删除 `treatyPolicyList` 列表
3. **预加载逻辑**: 简化 `collectionPolicyInfo()` 方法
4. **按需查询**: 删除低效的 `getPreDetailedDataOnDemand()` 方法

## 部署建议

1. **索引优化**: 确保历史数据表有适当的复合索引
2. **分片配置**: 根据数据量调整 `PARTITION_SIZE` 参数
3. **监控指标**: 关注批量查询的性能和内存使用情况
4. **测试验证**: 在大数据量环境下验证性能提升效果

## 总结

这个优化方案成功解决了原有的OOM问题，同时避免了大量IO操作。通过批次处理和批量查询的方式，在保持业务逻辑不变的前提下，大幅提升了系统的性能和稳定性。这是一个既解决内存问题又提高查询效率的优秀方案。

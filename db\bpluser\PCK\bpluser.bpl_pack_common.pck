CREATE OR REPLACE PACKAGE bpl_pack_common IS

  FUNCTION func_exchdate(p_yearmonth VARCHAR2) RETURN DATE;
  FUNCTION func_get_accountset(p_entity_id NUMBER) RETURN VARCHAR2;
  FUNCTION func_get_procid(p_proc_code VARCHAR2) RETURN NUMBER;
  FUNCTION func_get_taskcode(p_platform    VARCHAR2,
                             p_tasktype    VARCHAR2,
                             p_tasksection VARCHAR2) RETURN VARCHAR2;
  FUNCTION func_getexchrate(p_entity_id          NUMBER,
                            p_exchdate           DATE,
                            p_currency_code      VARCHAR,
                            p_exch_currency_code VARCHAR,
                            p_exchtype           VARCHAR) RETURN NUMBER;

  -- Author  : LY
  -- Created : 2019/4/2 15:43:14
  -- Purpose :

  TYPE type_list_table IS TABLE OF VARCHAR2(4000); --无索引列表
  TYPE type_array_table IS TABLE OF VARCHAR2(1000) INDEX BY BINARY_INTEGER; --带索引列表

  /***********************************************************************
   NAME         :Func_Expression_Demerger
   DESCRIPTION  :拆分计算公式(仅支持+-运算符)
   PARAM        : in varchar2  - P_Expression  计算公式
   RETURN       : table Type_List_Table  返回table类型
   DATE         :2012-05-26
   AUTHOR       :ly
  ***********************************************************************/
  FUNCTION func_expression_demerger(p_expression IN VARCHAR2) RETURN type_list_table
    PIPELINED;

  /***********************************************************************
  NAME :Func_Arithmometer_Destructor
  DESCRIPTION : 解释关系运算规则(支持四则运算)
  PARAM : P_Expression 运算规则表达式
  PARAM : v_split 表达式运算符，以 ‘|’号分隔
  PARAM : b_sate
  RETURN : table Type_Array_Table  返回table类型
  DATE :2018-11-15
  AUTHOR :ly
  ***********************************************************************/
  FUNCTION func_arithmometer_destructor(p_expression VARCHAR2,
                                        p_split      VARCHAR2,
                                        p_bstate     BOOLEAN) RETURN type_array_table;

  FUNCTION func_regexp_array(p_formula VARCHAR2,
                             p_pattern VARCHAR2,
                             p_group   BOOLEAN) RETURN type_array_table;

  FUNCTION func_get_rootaccountid(p_account_id IN NUMBER) RETURN NUMBER;

  PROCEDURE run_msg_rule_sql(p_sql     CLOB,
                             p_result  OUT VARCHAR,
                             p_err_msg OUT VARCHAR);


  FUNCTION func_analytical_sql_relyon(p_entity_id IN NUMBER,
                                      p_bookcode  IN VARCHAR2,
                                      p_yearmonth IN VARCHAR2,
                                      p_relyon    IN VARCHAR2,
                                      p_ruleexpr  IN VARCHAR2) RETURN VARCHAR2;

  FUNCTION func_sql_is_execute(p_relyon   IN VARCHAR2,
                               p_ruleexpr IN VARCHAR2) RETURN NUMBER;

END bpl_pack_common;
/
CREATE OR REPLACE PACKAGE BODY bpl_pack_common IS

  /***********************************************************************
  NAME          : bpl_FUNC_VOUCHERDATE
  DESCRIPTION   : 获取凭证日期-依据会计期间的凭证日期
  DATE          : 2020-10-29
  AUTHOR        : LEIHUAN
  BUSINESS RULE : 若会计期间在当前现实时间月份 取当前日期
                  若会计期间不在当前现实时间月份 取会计期间月最后一天
  ***********************************************************************/
  FUNCTION func_exchdate(p_yearmonth VARCHAR2) RETURN DATE IS
    v_text1    CLOB;
    v_text2    CLOB;
    v_exchdate DATE := SYSDATE;
  
  BEGIN
  
    IF to_char(SYSDATE, 'yyyymm') = p_yearmonth THEN
      v_exchdate := trunc(SYSDATE);
    ELSE
      --需要指定格式date，否则会默认为日期+时间
      v_exchdate := trunc(last_day(to_date(p_yearmonth || '01', 'YYYYMMDD')));
    END IF;
  
    RETURN v_exchdate;
  
  EXCEPTION
    WHEN OTHERS THEN
      --抛出异常提示信息
      v_text1 := to_char(SQLCODE);
      v_text2 := substr(SQLERRM, 1, 200);
      dbms_output.put_line(v_text1 || '::' || v_text2);
      RETURN trunc(SYSDATE);
    
  END func_exchdate;

  /***********************************************************************
   NAME         : get_accountset
   DESCRIPTION  : 取机构的现行账套
   PARAM        : in numeric  - p_entity_id  机构id
   RETURN       : V_BOOK_CODE 现行账套
   DATE         : 2021-08-05
  ***********************************************************************/
  FUNCTION func_get_accountset(p_entity_id NUMBER) RETURN VARCHAR2
  
   IS
    v_book_code VARCHAR2(32);
  
  BEGIN
    -- 根据proc_code查询proc_id, proc_code不存在，则返回空值
    SELECT book_code
      INTO v_book_code
      FROM bpluser.bbs_conf_account_set
     WHERE entity_id = p_entity_id
       AND valid_is = '1'
       AND audit_state = '1'
       AND rownum = 1;
  
    RETURN v_book_code;
  
  EXCEPTION
    WHEN OTHERS THEN
      v_book_code := NULL;
      RETURN v_book_code;
    
  END func_get_accountset;

  FUNCTION func_get_procid(p_proc_code VARCHAR2) RETURN NUMBER
  
   IS
    v_proc_id NUMBER;
  BEGIN
    -- 根据proc_code查询proc_id, proc_code不存在，则返回空值
    SELECT proc_id
      INTO v_proc_id
      FROM bpluser.bms_conf_action_procdef
     WHERE proc_code = p_proc_code
       AND rownum = 1;
  
    RETURN v_proc_id;
  
  EXCEPTION
  
    WHEN OTHERS THEN
      v_proc_id := NULL;
      RETURN v_proc_id;
    
  END func_get_procid;

  /***********************************************************************
   NAME         : GET_RASKID
   DESCRIPTION  : 取提数任务号
   PARAM        : in varchar2  - P_PLATFORM  任务平台
   PARAM        : in varchar2  - P_TASKTYPE  任务类型（自动 A /手动 M）
   PARAM        : in varchar2  - P_TASKSECTION  业务类型（各业务版块）
                                 整体GG, 承保UW, 理赔CL, 再保RI, 收付PY, 财务FI, 费用 FE
   RETURN       : V_RESULT 任务号
   DATE         : 2020-09-15
   AUTHOR       : LY
  ***********************************************************************/
  FUNCTION func_get_taskcode(p_platform    VARCHAR2,
                             p_tasktype    VARCHAR2,
                             p_tasksection VARCHAR2) RETURN VARCHAR2
  
   IS
    v_result      VARCHAR2(200);
    v_tasktype    VARCHAR2(10);
    v_tasksection VARCHAR2(10);
    v_seqval      INTEGER;
    v_seqno       VARCHAR2(50);
  
  BEGIN
  
    IF p_tasktype IS NULL THEN
      v_tasktype := 'A'; --all
    ELSE
      v_tasktype := p_tasktype;
    END IF;
  
    IF p_tasksection IS NULL THEN
      v_tasksection := 'GG'; --all
    ELSE
      v_tasksection := p_tasksection;
    END IF;
  
    --获取序列号，先左侧补齐6位数，再截取6位
    v_seqval := bpl_seq_taskid.nextval;
	
	--转换成序列编码
	v_seqno :=	v_seqval || '';
		
	--小于6位需要补齐
    IF length(v_seqno) < 6 THEN
      v_seqno := lpad(v_seqno, 6, '0');
    END IF;
	
	--截取6位序列编码
    v_seqno  := substr(v_seqno, -6, 6);
	
	--拼接任务编码
    v_result := p_platform || v_tasktype || v_tasksection || to_char(localtimestamp, 'YYYYMM') || v_seqno;

    RETURN v_result;
  
  EXCEPTION
    WHEN OTHERS THEN
      --ROLLBACK;
      dbms_output.put_line(to_char(SQLCODE) || '::' || substr(SQLERRM, 1, 200));
  END func_get_taskcode;

  /***********************************************************************
   NAME :BPL_FUNC_GETEXCHRATE
   DESCRIPTION :获取汇率
   DATE :2020-10-29
   AUTHOR :LEIHUAN
   PARAM: [应用单位,兑换日期,原币别,兑换币别,兑换类型]
   BUSINESS RULE : 获取小于等于当前日期的最新汇率
  ***********************************************************************/
  FUNCTION func_getexchrate(p_entity_id          NUMBER,
                            p_exchdate           DATE,
                            p_currency_code      VARCHAR,
                            p_exch_currency_code VARCHAR,
                            p_exchtype           VARCHAR) RETURN NUMBER
  
   IS
    --v_text1 CLOB;
    --v_text2 CLOB;
  
    v_exchrate NUMBER(32, 8) := 1;
    --V_EXCHDATE DATE;
  
  BEGIN
    IF p_exch_currency_code = p_currency_code THEN
      v_exchrate := 1;
    ELSE
      SELECT a.exchange_rate
        INTO v_exchrate
        FROM bpluser.bbs_conf_currencyrate a
       WHERE 1 = 1
         AND a.entity_id = p_entity_id
         AND a.currency_code = p_currency_code
         AND a.exch_currency_code = p_exch_currency_code
         AND a.frequency_code = p_exchtype
         AND a.effective_date <= p_exchdate
         AND a.valid_is = '1'
         AND a.audit_state = '1'
      -- and rownum = 1
       ORDER BY a.effective_date DESC
       FETCH NEXT 1 rows ONLY;
    END IF;
  
    RETURN v_exchrate;
  EXCEPTION
    WHEN OTHERS THEN
      --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
      ---RAISE NOTICE '[EXCEPTION]acc_pack_voucher_func_getexchrate：%; %',v_text1,v_text2;
      RETURN NULL;
  END func_getexchrate;

  /***********************************************************************
   NAME         :Func_Expression_Demerger
   DESCRIPTION  :拆分计算公式(仅支持+-运算符)
   PARAM        : in varchar2  - P_Expression  计算公式
   RETURN       : table Type_List_Table  返回table类型
   DATE         :2012-05-26
   AUTHOR       :ly
  ***********************************************************************/
  FUNCTION func_expression_demerger(p_expression IN VARCHAR2) RETURN type_list_table
    PIPELINED IS
    v_idx NUMBER;
    str   VARCHAR2(4000);
  
    v_expressions VARCHAR2(4000);
  BEGIN
    v_expressions := p_expression;
    WHILE instr(v_expressions, '+', -1) > 0
          OR instr(v_expressions, '-', -1) > 0 LOOP
      IF instr(v_expressions, '+', -1) > instr(v_expressions, '-', -1) THEN
        v_idx := instr(v_expressions, '+', -1);
      ELSIF instr(v_expressions, '-', -1) > instr(v_expressions, '+', -1) THEN
        v_idx := instr(v_expressions, '-', -1);
      END IF;
      str := REPLACE(substr(v_expressions, v_idx), '+', '');
      PIPE ROW(str);
      v_expressions := substr(v_expressions, 1, v_idx - 1);
    END LOOP;
  
    IF length(v_expressions) > 0 THEN
      str := REPLACE(v_expressions, '+', '');
      PIPE ROW(str);
    END IF;
    RETURN;
  
  END func_expression_demerger;

  /***********************************************************************
  NAME :Func_arithmometer_Destructor destructor
  DESCRIPTION : 解释关系运算规则(支持四则运算)
  PARAM : P_Expression 运算规则表达式
  PARAM : P_SPLIT 表达式运算符，多个运算符以 ‘|’号分隔
  PARAM : P_BSTATE 是否包含分隔符
  RETURN : table Type_Array_Table  返回table类型
  DATE :2018-11-15
  AUTHOR :ly
  ***********************************************************************/
  FUNCTION func_arithmometer_destructor(p_expression VARCHAR2,
                                        p_split      VARCHAR2,
                                        p_bstate     BOOLEAN) RETURN type_array_table IS
    ln   NUMBER(4) := 1;
    idx  NUMBER(4) := 1;
    edx  NUMBER(4) := 0;
    iidx NUMBER(4) := 0;
    irow NUMBER(4) := 0;
    i    NUMBER(4) := 0;
  
    itemcell    type_array_table;
    arry_return type_array_table;
  BEGIN
    ln := length(p_split);
    WHILE edx < ln LOOP
    
      i := instr(p_split, '|', idx, 1);
    
      IF i = 0 THEN
        edx := ln + 1;
      ELSE
        edx := i;
      END IF;
    
      irow := irow + 1;
      itemcell(irow) := substr(p_split, idx, edx - idx);
    
      idx := edx + 1;
    
    END LOOP;
    IF ln = 1 THEN
      itemcell(1) := p_split;
    END IF;
  
    ln   := length(p_expression);
    idx  := 1;
    edx  := 0;
    irow := 0;
    WHILE edx < ln LOOP
      iidx := 0;
      FOR j IN 1 .. itemcell.count LOOP
      
        IF p_bstate THEN
          i := instr(p_expression, itemcell(j), idx + 1, 1);
        ELSE
          i := instr(p_expression, itemcell(j), idx, 1);
        END IF;
      
        IF i != 0 THEN
          IF i < iidx
             OR iidx = 0 THEN
            iidx := i;
          END IF;
        END IF;
      
      END LOOP;
    
      IF iidx = 0 THEN
        edx := ln + 1;
      ELSE
        edx := iidx;
      END IF;
      irow := irow + 1;
      IF p_bstate THEN
        arry_return(irow) := substr(p_expression, idx, edx - idx);
      ELSE
        arry_return(irow) := substr(p_expression, idx, edx - idx);
      END IF;
    
      IF p_bstate THEN
        idx := edx;
      ELSE
        idx := edx + 1;
      END IF;
    
      dbms_output.put_line(arry_return(irow));
    END LOOP;
  
    RETURN arry_return;
  END func_arithmometer_destructor;

  FUNCTION func_regexp_array(p_formula VARCHAR2,
                             p_pattern VARCHAR2,
                             p_group   BOOLEAN) RETURN type_array_table IS
    arry_return type_array_table;
    irow        NUMBER(4) := 0;
  BEGIN
    IF p_group THEN
      FOR item IN (SELECT DISTINCT regexp_substr(p_formula, p_pattern, 1, LEVEL, 'c', 1) AS val FROM dual CONNECT BY LEVEL < regexp_count(p_formula, p_pattern) + 1) LOOP
        irow := irow + 1;
        arry_return(irow) := item.val;
      END LOOP;
    ELSE
      FOR item IN (SELECT regexp_substr(p_formula, p_pattern, 1, LEVEL, 'c', 1) AS val FROM dual CONNECT BY LEVEL < regexp_count(p_formula, p_pattern) + 1) LOOP
        irow := irow + 1;
        arry_return(irow) := item.val;
      END LOOP;
    END IF;
  
    RETURN arry_return;
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(SQLERRM || '：分隔表达式失败，表达式：' || p_formula || '，正则：' || p_pattern);
      RETURN arry_return;
  END func_regexp_array;


  /***********************************************************************
    NAME : acc_pack_buss_balance_func_get_baseitemid
    DESCRIPTION : 根据科目id获取顶级科目id
    DATE :2020-12-29
    AUTHOR :YINXH
  ***********************************************************************/
  FUNCTION func_get_rootaccountid(p_account_id IN NUMBER) RETURN NUMBER IS
  
    v_account_id NUMBER; --科目ID
  BEGIN
  
    SELECT t.account_id
      INTO v_account_id
      FROM (WITH tb_result AS (SELECT account_id,
                                      upper_account_id,
                                      account_level
                                 FROM bbs_account c
                                START WITH c.valid_is = '1'
                                       AND account_id = p_account_id
                               CONNECT BY PRIOR upper_account_id = c.account_id)
             SELECT *
               FROM tb_result
              WHERE account_level = 1
                AND rownum = 1) t;
  
  
  
    --返回计算结果值
    RETURN v_account_id;
  EXCEPTION
    WHEN OTHERS THEN
      --RAISE INFO '**异常信息：%',SQLERRM;
      dbms_output.put_line('**异常信息：' || to_char(SQLCODE) || '::' || substr(SQLERRM, 1, 200));
      RETURN NULL;
      -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息
  END func_get_rootaccountid;

  PROCEDURE run_msg_rule_sql(p_sql     CLOB,
                             p_result  OUT VARCHAR,
                             p_err_msg OUT VARCHAR) IS
    v_sql              CLOB;
    v_exec_rule_result NUMBER(12);
  BEGIN
  
    p_result := '1';
    -- 1、脚本自带统计函数 count
    IF instr(upper(p_sql), upper(' count(')) > 0 THEN
      EXECUTE IMMEDIATE p_sql
        INTO v_exec_rule_result; --结果计数值
    
      IF v_exec_rule_result <= 0 THEN
        p_result := '0';
      END IF;
    ELSE
      v_sql := 'select ''1'' from (' || p_sql || ') where rownum <= 1 ';
      EXECUTE IMMEDIATE v_sql
        INTO p_result;
    END IF;
  
  EXCEPTION
    --意外处理
    WHEN OTHERS THEN
      p_result  := '0';
      p_err_msg := substr(SQLERRM, 1, 500);
  END;


  /***********************************************************************
   NAME : func_analytical_sql_relyon
   DESCRIPTION : 脚本附加条件解析函数
   DATE :2021-7-29
  ***********************************************************************/
  FUNCTION func_analytical_sql_relyon(p_entity_id IN NUMBER,
                                      p_bookcode  IN VARCHAR2,
                                      p_yearmonth IN VARCHAR2,
                                      p_relyon    IN VARCHAR2,
                                      p_ruleexpr  IN VARCHAR2) RETURN VARCHAR2 IS
    v_execute_sql   VARCHAR2(4000); --脚本解析结果
    v_centeridflag  VARCHAR2(1); --附件条件勾选标记1：业务单位
    v_bookcodeflag  VARCHAR2(1); --附件条件勾选标记2：账套
    v_yearmonthflag VARCHAR2(1); --附件条件勾选标记3：会计期间
  BEGIN
    BEGIN
      -- 判断脚本是否包含WHERE条件语句，若无则添加 WHERE 1 = 1
      IF instr(upper(p_ruleexpr), upper('where')) > 0 THEN
        v_execute_sql := p_ruleexpr || ' ';
      ELSE
        v_execute_sql := p_ruleexpr || ' where 1 = 1 ';
      END IF;
    
      -- RELY_ON:脚本附件条件，示例：'101'，分别代表已勾选核算单位，未勾选账套，已勾选会计期间
      IF p_relyon IS NOT NULL
         AND length(coalesce(p_relyon, '0')) = 3 THEN
      
        -- 解析附加条件
        v_centeridflag  := substr(p_relyon, 1, 1);
        v_bookcodeflag  := substr(p_relyon, 2, 1);
        v_yearmonthflag := substr(p_relyon, 3, 1);
      
        -- 拼接附加条件1，如果包含{entity_id}，就做替换，否则就做追加
        IF v_centeridflag = '1' THEN
          IF instr(upper(p_ruleexpr), upper('{entity_id}')) > 0 THEN
            v_execute_sql := REPLACE(v_execute_sql, ('{entity_id}'), p_entity_id || '');
          ELSE
            v_execute_sql := v_execute_sql || ' and entity_id = ' || p_entity_id;
          END IF;
        END IF;
      
        -- 拼接附加条件3
        IF v_yearmonthflag = '1' THEN
          IF instr(upper(p_ruleexpr), upper('{year_month}')) > 0 THEN
            v_execute_sql := REPLACE((v_execute_sql), ('{year_month}'), '''' || p_yearmonth || '''');
          ELSE
            v_execute_sql := v_execute_sql || ' and year_month = ''' || p_yearmonth || '''';
          END IF;
        END IF;
      
        -- 拼接附加条件2
        IF v_bookcodeflag = '1' THEN
          IF instr(upper(p_ruleexpr), upper('{book_code}')) > 0 THEN
            v_execute_sql := REPLACE((v_execute_sql), ('{book_code}'), '''' || p_bookcode || '''');
          ELSE
            v_execute_sql := v_execute_sql || ' and book_code = ''' || p_bookcode || '''';
          END IF;
        END IF;
      
      ELSE
        v_execute_sql := p_ruleexpr;
      
      END IF;
    
    EXCEPTION
      WHEN OTHERS THEN
        dbms_output.put_line('**SQLSTATE: ' || to_char(SQLCODE) || '; **SQLERRM: ' || substr(SQLERRM, 1, 200) || ',【脚本解析失败，请检查脚本Sql：' || v_execute_sql || '，附件条件：' || p_relyon || '】');
    END;
    --返回脚本结果
    RETURN v_execute_sql;
  
    -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息
  END func_analytical_sql_relyon;


  /***********************************************************************
    NAME : ACC_PROC_CHECKRULESQLISEXECUTE
    DESCRIPTION : 检查规则脚本是否可执行
    DATE :2021-01-12
    AUTHOR :YINXH
  ***********************************************************************/
  FUNCTION func_sql_is_execute(p_relyon   IN VARCHAR2,
                               p_ruleexpr IN VARCHAR2) RETURN NUMBER IS
  
  
    v_result      NUMBER(10); --脚本执行结果值
    v_execute_sql VARCHAR2(4000); --脚本解析结果
  
    -- 校验SQL默认数据
    v_centerid_check  NUMBER(10) := 1;
    v_bookcode_check  VARCHAR2(4000) := 'BookI17';
    v_yearmonth_check VARCHAR2(4000) := '202101';
    v_ruleexprsql     VARCHAR2(4000); --规则校验执行脚本(拼接附加条件后)
    v_prefix_exprsql  VARCHAR2(4000); --规则校验执行脚本(按from拆分后的前缀部分)
    v_suffix_exprsql  VARCHAR2(4000);
    v_expression_sql  VARCHAR2(4000);
  BEGIN
    -- 调用函数：解析附加条件后拼接成可执行SQL脚本返回
    v_execute_sql := bpl_pack_common.func_analytical_sql_relyon(v_centerid_check, v_bookcode_check, v_yearmonth_check, p_relyon, p_ruleexpr);
    --脚本试执行
    BEGIN
      -- 不分场景试执行脚本
      EXECUTE IMMEDIATE v_execute_sql;
    EXCEPTION
      WHEN OTHERS THEN
        v_result := 0;
        --抛出异常提示信息
        dbms_output.put_line('**SQLSTATE: ' || to_char(SQLCODE) || '; **SQLERRM: ' || substr(SQLERRM, 1, 200) || ',【脚本1执行失败，请检查脚本Sql：' || v_execute_sql);
        RETURN coalesce(v_result, 1);
    END;
  
    --脚本结果列是否单个结果
    BEGIN
      v_ruleexprsql := 'select ''1'' from (' || v_execute_sql || ') where rownum <= 1 ';
      EXECUTE IMMEDIATE v_ruleexprsql;
    EXCEPTION
      WHEN OTHERS THEN
        v_result := 2;
        --抛出异常提示信息
        dbms_output.put_line('**SQLSTATE: ' || to_char(SQLCODE) || '; **SQLERRM: ' || substr(SQLERRM, 1, 200) || ',【脚本2执行失败，请检查脚本Sql：' || v_execute_sql);
      
        RETURN coalesce(v_result, 1);
    END;
    --返回脚本结果 0-执行不通过；1-执行通过
    RETURN coalesce(v_result, 1);
    -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息
  END func_sql_is_execute;


END bpl_pack_common;
/

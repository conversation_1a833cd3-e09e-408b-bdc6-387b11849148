--清除表的注释
comment on table RPT_CONF_CODE_HIS is '';
comment on table RPT_DUCT_REPORT_TEMP_DETAILHIS is '';
comment on table RPT_CONF_REPORT_ITEM_RULE is '';
comment on table RPT_DAP_LEDGER_BALANCE is '';
comment on table RPT_CONF_REPORT_TEMPLATE is '';
comment on table RPT_CONF_REPORT_TEMPLATEHIS is '';
comment on table RPT_DAP_LEDGER_BALANCEHIS is '';
comment on table RPT_DUCT_REPORT_ITEM_DATA_SUB is '';
comment on table RPT_DAP_ARTICLE_BAL_HIS is '';
comment on table RPT_DAP_ARTICLE_BALANCE is '';
comment on table RPT_CONF_ITEM_RULE_ARTICLE is '';
comment on table RPT_BUSS_REPORT_ITEM_DATA is '';
comment on table RPT_CONF_REPORT_ITEMHIS is '';
comment on table RPT_CODE_CONFIG is '';
comment on table RPT_CONF_BUSSPERIOD is '';
comment on table RPT_CONF_REPORT_ITEM_RULEHIS is '';
comment on table RPT_CONF_ITEM_RULE_SUB_HIS is '';
comment on table RPT_CONF_TABLE is '';
comment on table RPT_BUSS_REPORT is '';
comment on table RPT_LOG_REPORT_ITEM_DRAW_TASK is '';
comment on table RPT_CONF_CODE is '';
comment on table RPT_CONF_BUSSPERIOD_DETAIL is '';
comment on table RPT_BUSS_REPORTHIS is '';
comment on table RPT_CONF_ITEM_RULE_SUB is '';
comment on table RPT_DUCT_REPORT_TEMP_DETAIL is '';
comment on table RPT_BUSS_REPORT_ITEM_DATAHIS is '';
comment on table RPT_DUCT_REPORT_TEMPLATEHIS is '';
comment on table RPT_CONF_ITEM_RULE_ARTICLEHIS is '';
comment on table RPT_DUCT_REPORT_ITEM_DATA is '';
comment on table RPT_CONF_REPORT_ITEM is '';
comment on table RPT_DUCT_REPORT_ITEM_DATAHIS is '';
comment on table RPT_DUCT_REPORT_TEMPLATE is '';


--清除表字段和视图字段的注释
comment on column RPT_BUSS_REPORT.CREATOR_ID is '';
comment on column RPT_BUSS_REPORT.CREATE_TIME is '';
comment on column RPT_BUSS_REPORT.UPDATOR_ID is '';
comment on column RPT_BUSS_REPORT.UPDATE_TIME is '';
comment on column RPT_BUSS_REPORT.TEMPLATE_CLASS is '';
comment on column RPT_BUSS_REPORT.EXECUTION_STATE is '';
comment on column RPT_BUSS_REPORT.REPORT_TEMPLATE_FILE_SERIAL_NO is '';
comment on column RPT_BUSS_REPORT.REPORT_TEMPLATE_DUCT_SERIAL_NO is '';
comment on column RPT_BUSS_REPORT.REPORT_ID is '';
comment on column RPT_BUSS_REPORT.SERIAL_NO is '';
comment on column RPT_BUSS_REPORT.ENTITY_ID is '';
comment on column RPT_BUSS_REPORT.BOOK_CODE is '';
comment on column RPT_BUSS_REPORT.YEAR_MONTH is '';
comment on column RPT_BUSS_REPORT.REPORT_CODE is '';
comment on column RPT_BUSS_REPORT.REPORT_C_NAME is '';
comment on column RPT_BUSS_REPORT.REPORT_L_NAME is '';
comment on column RPT_BUSS_REPORT.REPORT_E_NAME is '';
comment on column RPT_BUSS_REPORT.REPORT_FILE_PATH is '';
comment on column RPT_BUSS_REPORT.REPORT_FILE_NAME is '';
comment on column RPT_BUSS_REPORT.REPORT_FILE_TYPE is '';
comment on column RPT_BUSS_REPORT.REPORT_TEMPLATE_ID is '';
comment on column RPT_BUSS_REPORT.VALID_IS is '';
comment on column RPT_BUSS_REPORT.REMARK is '';
comment on column RPT_BUSS_REPORT.DISPLAY_NO is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_HIS_ID is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_ID is '';
comment on column RPT_BUSS_REPORTHIS.SERIAL_NO is '';
comment on column RPT_BUSS_REPORTHIS.ENTITY_ID is '';
comment on column RPT_BUSS_REPORTHIS.BOOK_CODE is '';
comment on column RPT_BUSS_REPORTHIS.YEAR_MONTH is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_CODE is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_C_NAME is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_L_NAME is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_E_NAME is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_FILE_PATH is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_FILE_NAME is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_FILE_TYPE is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_TEMPLATE_ID is '';
comment on column RPT_BUSS_REPORTHIS.VALID_IS is '';
comment on column RPT_BUSS_REPORTHIS.REMARK is '';
comment on column RPT_BUSS_REPORTHIS.DISPLAY_NO is '';
comment on column RPT_BUSS_REPORTHIS.CREATOR_ID is '';
comment on column RPT_BUSS_REPORTHIS.CREATE_TIME is '';
comment on column RPT_BUSS_REPORTHIS.UPDATOR_ID is '';
comment on column RPT_BUSS_REPORTHIS.UPDATE_TIME is '';
comment on column RPT_BUSS_REPORTHIS.TEMPLATE_CLASS is '';
comment on column RPT_BUSS_REPORTHIS.EXECUTION_STATE is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_TEMPLATE_FILE_SERIAL_NO is '';
comment on column RPT_BUSS_REPORTHIS.REPORT_TEMPLATE_DUCT_SERIAL_NO is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.REPORT_ITEM_DATA_ID is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.SERIAL_NO is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.ENTITY_ID is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.BOOK_CODE is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.YEAR_MONTH is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.REPORT_ITEM_ID is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.EXPR_TYPE is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.ARTICLE is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.CURRENCY_CODE is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.AMOUNT is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.CREATOR_ID is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.CREATE_TIME is '';
comment on column RPT_BUSS_REPORT_ITEM_DATA.REPORT_ITEM_RULE_SERIAL_NO is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.REPORT_ITEM_DATA_HIS_ID is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.REPORT_ITEM_DATA_ID is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.SERIAL_NO is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.ENTITY_ID is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.BOOK_CODE is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.YEAR_MONTH is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.REPORT_ITEM_ID is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.EXPR_TYPE is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.ARTICLE is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.CURRENCY_CODE is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.AMOUNT is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.CREATOR_ID is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.CREATE_TIME is '';
comment on column RPT_BUSS_REPORT_ITEM_DATAHIS.REPORT_ITEM_RULE_SERIAL_NO is '';
comment on column RPT_CODE_CONFIG.CODE_CONF_ID is '';
comment on column RPT_CODE_CONFIG.CODE is '';
comment on column RPT_CODE_CONFIG.PO_NAME is '';
comment on column RPT_CODE_CONFIG.SEARCH_PARAMS is '';
comment on column RPT_CODE_CONFIG.CREATOR_ID is '';
comment on column RPT_CODE_CONFIG.CREATE_TIME is '';
comment on column RPT_CODE_CONFIG.UPDATOR_ID is '';
comment on column RPT_CODE_CONFIG.UPDATE_TIME is '';
comment on column RPT_CONF_BUSSPERIOD.BUSS_PERIOD_ID is '';
comment on column RPT_CONF_BUSSPERIOD.ENTITY_ID is '';
comment on column RPT_CONF_BUSSPERIOD.BOOK_CODE is '';
comment on column RPT_CONF_BUSSPERIOD.YEAR_MONTH is '';
comment on column RPT_CONF_BUSSPERIOD.EXECUTION_STATE is '';
comment on column RPT_CONF_BUSSPERIOD.VALID_IS is '';
comment on column RPT_CONF_BUSSPERIOD.CREATOR_ID is '';
comment on column RPT_CONF_BUSSPERIOD.CREATE_TIME is '';
comment on column RPT_CONF_BUSSPERIOD.UPDATOR_ID is '';
comment on column RPT_CONF_BUSSPERIOD.UPDATE_TIME is '';
comment on column RPT_CONF_BUSSPERIOD_DETAIL.PERIOD_DETAIL_ID is '';
comment on column RPT_CONF_BUSSPERIOD_DETAIL.BUSS_PERIOD_ID is '';
comment on column RPT_CONF_BUSSPERIOD_DETAIL.BIZ_TYPE_ID is '';
comment on column RPT_CONF_BUSSPERIOD_DETAIL.TASK_TIME is '';
comment on column RPT_CONF_BUSSPERIOD_DETAIL.EXEC_RESULT is '';
comment on column RPT_CONF_BUSSPERIOD_DETAIL.READY_STATE is '';
comment on column RPT_CONF_BUSSPERIOD_DETAIL.CREATOR_ID is '';
comment on column RPT_CONF_BUSSPERIOD_DETAIL.CREATE_TIME is '';
comment on column RPT_CONF_BUSSPERIOD_DETAIL.UPDATOR_ID is '';
comment on column RPT_CONF_BUSSPERIOD_DETAIL.UPDATE_TIME is '';
comment on column RPT_CONF_CODE.CODE_ID is '';
comment on column RPT_CONF_CODE.UPPER_CODE_ID is '';
comment on column RPT_CONF_CODE.CODE_CODE is '';
comment on column RPT_CONF_CODE.CODE_C_NAME is '';
comment on column RPT_CONF_CODE.CODE_L_NAME is '';
comment on column RPT_CONF_CODE.CODE_E_NAME is '';
comment on column RPT_CONF_CODE.REMARK is '';
comment on column RPT_CONF_CODE.DISPLAY_NO is '';
comment on column RPT_CONF_CODE.VALID_IS is '';
comment on column RPT_CONF_CODE.CREATOR_ID is '';
comment on column RPT_CONF_CODE.CREATE_TIME is '';
comment on column RPT_CONF_CODE.UPDATOR_ID is '';
comment on column RPT_CONF_CODE.UPDATE_TIME is '';
comment on column RPT_CONF_CODE.SERIAL_NO is '';
comment on column RPT_CONF_CODE_HIS.CHECKED_MSG is '';
comment on column RPT_CONF_CODE_HIS.OPER_ID is '';
comment on column RPT_CONF_CODE_HIS.OPER_TYPE is '';
comment on column RPT_CONF_CODE_HIS.OPER_TIME is '';
comment on column RPT_CONF_CODE_HIS.CODE_HIS_ID is '';
comment on column RPT_CONF_CODE_HIS.CODE_ID is '';
comment on column RPT_CONF_CODE_HIS.UPPER_CODE_ID is '';
comment on column RPT_CONF_CODE_HIS.CODE_CODE is '';
comment on column RPT_CONF_CODE_HIS.CODE_C_NAME is '';
comment on column RPT_CONF_CODE_HIS.CODE_L_NAME is '';
comment on column RPT_CONF_CODE_HIS.CODE_E_NAME is '';
comment on column RPT_CONF_CODE_HIS.REMARK is '';
comment on column RPT_CONF_CODE_HIS.DISPLAY_NO is '';
comment on column RPT_CONF_CODE_HIS.VALID_IS is '';
comment on column RPT_CONF_CODE_HIS.CREATOR_ID is '';
comment on column RPT_CONF_CODE_HIS.CREATE_TIME is '';
comment on column RPT_CONF_CODE_HIS.UPDATOR_ID is '';
comment on column RPT_CONF_CODE_HIS.UPDATE_TIME is '';
comment on column RPT_CONF_CODE_HIS.SERIAL_NO is '';
comment on column RPT_CONF_CODE_HIS.AUDIT_STATE is '';
comment on column RPT_CONF_CODE_HIS.CHECKED_TIME is '';
comment on column RPT_CONF_CODE_HIS.CHECKED_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLE.RULE_ARTICLE_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLE.SERIAL_NO is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLE.REPORT_ITEM_RULE_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLE.ARTICLE_TYPE is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLE.ARTICLE_VALUE is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLE.DISPLAY_NO is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLE.CREATOR_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLE.CREATE_TIME is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLE.UPDATOR_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLE.UPDATE_TIME is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.RULE_ARTICLE_HIS_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.RULE_ARTICLE_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.SERIAL_NO is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.REPORT_ITEM_RULE_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.ARTICLE_TYPE is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.ARTICLE_VALUE is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.DISPLAY_NO is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.CREATOR_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.CREATE_TIME is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.UPDATOR_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.UPDATE_TIME is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.OPER_TYPE is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.OPER_ID is '';
comment on column RPT_CONF_ITEM_RULE_ARTICLEHIS.OPER_TIME is '';
comment on column RPT_CONF_ITEM_RULE_SUB.REPORT_ITEM_RULE_TERNARY_ID is '';
comment on column RPT_CONF_ITEM_RULE_SUB.SERIAL_NO is '';
comment on column RPT_CONF_ITEM_RULE_SUB.REPORT_ITEM_RULE_ID is '';
comment on column RPT_CONF_ITEM_RULE_SUB.BASE_EXPR is '';
comment on column RPT_CONF_ITEM_RULE_SUB.EXPR_OPERATOR is '';
comment on column RPT_CONF_ITEM_RULE_SUB.OTHER_EXPR is '';
comment on column RPT_CONF_ITEM_RULE_SUB.TRUE_EXPR is '';
comment on column RPT_CONF_ITEM_RULE_SUB.FALSE_EXPR is '';
comment on column RPT_CONF_ITEM_RULE_SUB.CREATOR_ID is '';
comment on column RPT_CONF_ITEM_RULE_SUB.CREATE_TIME is '';
comment on column RPT_CONF_ITEM_RULE_SUB.UPDATOR_ID is '';
comment on column RPT_CONF_ITEM_RULE_SUB.UPDATE_TIME is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.OTHER_EXPR is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.TRUE_EXPR is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.FALSE_EXPR is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.CREATOR_ID is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.CREATE_TIME is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.UPDATOR_ID is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.UPDATE_TIME is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.OPER_TYPE is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.OPER_ID is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.OPER_TIME is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.REPORT_ITEM_RULE_SUB_HIS_ID is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.REPORT_ITEM_RULE_TERNARY_ID is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.SERIAL_NO is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.REPORT_ITEM_RULE_ID is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.BASE_EXPR is '';
comment on column RPT_CONF_ITEM_RULE_SUB_HIS.EXPR_OPERATOR is '';
comment on column RPT_CONF_REPORT_ITEM.AUDIT_STATE is '';
comment on column RPT_CONF_REPORT_ITEM.CHECKED_ID is '';
comment on column RPT_CONF_REPORT_ITEM.CHECKED_TIME is '';
comment on column RPT_CONF_REPORT_ITEM.CHECKED_MSG is '';
comment on column RPT_CONF_REPORT_ITEM.REPORT_ITEM_ID is '';
comment on column RPT_CONF_REPORT_ITEM.SERIAL_NO is '';
comment on column RPT_CONF_REPORT_ITEM.UPPER_REPORT_ITEM_ID is '';
comment on column RPT_CONF_REPORT_ITEM.REPORT_ITEM_CODE is '';
comment on column RPT_CONF_REPORT_ITEM.REPORT_ITEM_C_NAME is '';
comment on column RPT_CONF_REPORT_ITEM.REPORT_ITEM_L_NAME is '';
comment on column RPT_CONF_REPORT_ITEM.REPORT_ITEM_E_NAME is '';
comment on column RPT_CONF_REPORT_ITEM.VALID_IS is '';
comment on column RPT_CONF_REPORT_ITEM.REMARK is '';
comment on column RPT_CONF_REPORT_ITEM.CREATOR_ID is '';
comment on column RPT_CONF_REPORT_ITEM.CREATE_TIME is '';
comment on column RPT_CONF_REPORT_ITEM.UPDATOR_ID is '';
comment on column RPT_CONF_REPORT_ITEM.UPDATE_TIME is '';
comment on column RPT_CONF_REPORT_ITEMHIS.REPORT_ITEM_HIS_ID is '';
comment on column RPT_CONF_REPORT_ITEMHIS.REPORT_ITEM_ID is '';
comment on column RPT_CONF_REPORT_ITEMHIS.SERIAL_NO is '';
comment on column RPT_CONF_REPORT_ITEMHIS.UPPER_REPORT_ITEM_ID is '';
comment on column RPT_CONF_REPORT_ITEMHIS.REPORT_ITEM_CODE is '';
comment on column RPT_CONF_REPORT_ITEMHIS.REPORT_ITEM_C_NAME is '';
comment on column RPT_CONF_REPORT_ITEMHIS.REPORT_ITEM_L_NAME is '';
comment on column RPT_CONF_REPORT_ITEMHIS.REPORT_ITEM_E_NAME is '';
comment on column RPT_CONF_REPORT_ITEMHIS.VALID_IS is '';
comment on column RPT_CONF_REPORT_ITEMHIS.REMARK is '';
comment on column RPT_CONF_REPORT_ITEMHIS.CREATOR_ID is '';
comment on column RPT_CONF_REPORT_ITEMHIS.CREATE_TIME is '';
comment on column RPT_CONF_REPORT_ITEMHIS.UPDATOR_ID is '';
comment on column RPT_CONF_REPORT_ITEMHIS.UPDATE_TIME is '';
comment on column RPT_CONF_REPORT_ITEMHIS.OPER_TYPE is '';
comment on column RPT_CONF_REPORT_ITEMHIS.OPER_ID is '';
comment on column RPT_CONF_REPORT_ITEMHIS.OPER_TIME is '';
comment on column RPT_CONF_REPORT_ITEMHIS.AUDIT_STATE is '';
comment on column RPT_CONF_REPORT_ITEMHIS.CHECKED_ID is '';
comment on column RPT_CONF_REPORT_ITEMHIS.CHECKED_TIME is '';
comment on column RPT_CONF_REPORT_ITEMHIS.CHECKED_MSG is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.REPORT_ITEM_RULE_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.SERIAL_NO is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.ENTITY_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.BOOK_CODE is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.REPORT_ITEM_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.DEAL_TYPE is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.DATA_SOURCE is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.EXPR_TYPE is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.PERIOD_TYPE is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.EXPR_DESC is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.DEAL_LEVEL is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.VALID_IS is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.REMARK is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.CREATOR_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.CREATE_TIME is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.UPDATOR_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.UPDATE_TIME is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.AUDIT_STATE is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.CHECKED_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.CHECKED_TIME is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.CHECKED_MSG is '';
comment on column RPT_CONF_REPORT_ITEM_RULE.RELY_ON is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.REPORT_ITEM_RULE_HIS_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.REPORT_ITEM_RULE_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.SERIAL_NO is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.ENTITY_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.BOOK_CODE is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.REPORT_ITEM_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.DEAL_TYPE is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.DATA_SOURCE is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.EXPR_TYPE is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.PERIOD_TYPE is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.EXPR_DESC is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.DEAL_LEVEL is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.VALID_IS is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.REMARK is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.CREATOR_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.CREATE_TIME is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.UPDATOR_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.UPDATE_TIME is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.OPER_TYPE is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.OPER_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.OPER_TIME is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.AUDIT_STATE is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.CHECKED_ID is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.CHECKED_TIME is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.CHECKED_MSG is '';
comment on column RPT_CONF_REPORT_ITEM_RULEHIS.RELY_ON is '';
comment on column RPT_CONF_REPORT_TEMPLATE.CHECKED_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATE.CHECKED_TIME is '';
comment on column RPT_CONF_REPORT_TEMPLATE.CHECKED_MSG is '';
comment on column RPT_CONF_REPORT_TEMPLATE.TEMPLATE_CLASS is '';
comment on column RPT_CONF_REPORT_TEMPLATE.REPORT_TEMPLATE_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATE.SERIAL_NO is '';
comment on column RPT_CONF_REPORT_TEMPLATE.REPORT_TEMPLATE_SERIAL_NO is '';
comment on column RPT_CONF_REPORT_TEMPLATE.ENTITY_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATE.BOOK_CODE is '';
comment on column RPT_CONF_REPORT_TEMPLATE.REPORT_TEMPLATE_CODE is '';
comment on column RPT_CONF_REPORT_TEMPLATE.REPORT_TEMPLATE_C_NAME is '';
comment on column RPT_CONF_REPORT_TEMPLATE.REPORT_TEMPLATE_L_NAME is '';
comment on column RPT_CONF_REPORT_TEMPLATE.REPORT_TEMPLATE_E_NAME is '';
comment on column RPT_CONF_REPORT_TEMPLATE.TEMPLATE_FILE_PATH is '';
comment on column RPT_CONF_REPORT_TEMPLATE.TEMPLATE_FILE_NAME is '';
comment on column RPT_CONF_REPORT_TEMPLATE.TEMPLATE_FILE_TYPE is '';
comment on column RPT_CONF_REPORT_TEMPLATE.VALID_IS is '';
comment on column RPT_CONF_REPORT_TEMPLATE.DISPLAY_NO is '';
comment on column RPT_CONF_REPORT_TEMPLATE.REMARK is '';
comment on column RPT_CONF_REPORT_TEMPLATE.CREATOR_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATE.CREATE_TIME is '';
comment on column RPT_CONF_REPORT_TEMPLATE.UPDATOR_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATE.UPDATE_TIME is '';
comment on column RPT_CONF_REPORT_TEMPLATE.AUDIT_STATE is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_HIS_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.SERIAL_NO is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_SERIAL_NO is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.ENTITY_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.BOOK_CODE is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_CODE is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_C_NAME is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_L_NAME is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_E_NAME is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.TEMPLATE_FILE_PATH is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.TEMPLATE_FILE_NAME is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.TEMPLATE_FILE_TYPE is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.VALID_IS is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.DISPLAY_NO is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.REMARK is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.CREATOR_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.CREATE_TIME is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.UPDATOR_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.UPDATE_TIME is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.OPER_TYPE is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.OPER_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.OPER_TIME is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.AUDIT_STATE is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.CHECKED_ID is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.CHECKED_TIME is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.CHECKED_MSG is '';
comment on column RPT_CONF_REPORT_TEMPLATEHIS.TEMPLATE_CLASS is '';
comment on column RPT_CONF_TABLE.BIZ_TYPE_ID is '';
comment on column RPT_CONF_TABLE.BIZ_CODE is '';
comment on column RPT_CONF_TABLE.TYPE_CODE is '';
comment on column RPT_CONF_TABLE.TYPE_E_NAME is '';
comment on column RPT_CONF_TABLE.TYPE_L_NAME is '';
comment on column RPT_CONF_TABLE.TYPE_C_NAME is '';
comment on column RPT_CONF_TABLE.DIRECTION is '';
comment on column RPT_CONF_TABLE.VALID_IS is '';
comment on column RPT_CONF_TABLE.DISPLAY_NO is '';
comment on column RPT_CONF_TABLE.CREATE_TIME is '';
comment on column RPT_CONF_TABLE.CREATOR_ID is '';
comment on column RPT_CONF_TABLE.UPDATE_TIME is '';
comment on column RPT_CONF_TABLE.UPDATOR_ID is '';
comment on column RPT_CONF_TABLE.SYSTEM_CODE is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ART_BLANCE_ID is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ENTITY_ID is '';
comment on column RPT_DAP_ARTICLE_BALANCE.BOOK_CODE is '';
comment on column RPT_DAP_ARTICLE_BALANCE.YEAR_MONTH is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE1 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE2 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE3 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE4 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE5 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE6 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE7 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE8 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE9 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE10 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE11 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE12 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE13 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE14 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE15 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE16 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ARTICLE17 is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CURRENCY_CODE is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CURRENCY_CU_CODE is '';
comment on column RPT_DAP_ARTICLE_BALANCE.DEBIT_AMOUNT is '';
comment on column RPT_DAP_ARTICLE_BALANCE.DEBIT_AMOUNT_CU is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CREDIT_AMOUNT is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CREDIT_AMOUNT_CU is '';
comment on column RPT_DAP_ARTICLE_BALANCE.DEBIT_AMOUNT_QUARTER is '';
comment on column RPT_DAP_ARTICLE_BALANCE.DEBIT_AMOUNT_QUARTER_CU is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CREDIT_AMOUNT_QUARTER is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CREDIT_AMOUNT_QUARTER_CU is '';
comment on column RPT_DAP_ARTICLE_BALANCE.DEBIT_AMOUNT_YEAR is '';
comment on column RPT_DAP_ARTICLE_BALANCE.DEBIT_AMOUNT_YEAR_CU is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CREDIT_AMOUNT_YEAR is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CREDIT_AMOUNT_YEAR_CU is '';
comment on column RPT_DAP_ARTICLE_BALANCE.OPENING_BALANCE_CU is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CLOSING_BALANCE_CU is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CREATE_TIME is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CREATOR_ID is '';
comment on column RPT_DAP_ARTICLE_BALANCE.UPDATE_TIME is '';
comment on column RPT_DAP_ARTICLE_BALANCE.UPDATOR_ID is '';
comment on column RPT_DAP_ARTICLE_BALANCE.OPENING_BALANCE is '';
comment on column RPT_DAP_ARTICLE_BALANCE.CLOSING_BALANCE is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ROOT_ACCOUNT_ID is '';
comment on column RPT_DAP_ARTICLE_BALANCE.ACCOUNT_ID is '';
comment on column RPT_DAP_ARTICLE_BALANCE.SERIAL_NO is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE9 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE10 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE11 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE12 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE13 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE14 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE15 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE16 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE17 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CURRENCY_CODE is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CURRENCY_CU_CODE is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.DEBIT_AMOUNT is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.DEBIT_AMOUNT_CU is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CREDIT_AMOUNT is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CREDIT_AMOUNT_CU is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.DEBIT_AMOUNT_QUARTER is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.DEBIT_AMOUNT_QUARTER_CU is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CREDIT_AMOUNT_QUARTER is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CREDIT_AMOUNT_QUARTER_CU is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.DEBIT_AMOUNT_YEAR is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.DEBIT_AMOUNT_YEAR_CU is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CREDIT_AMOUNT_YEAR is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CREDIT_AMOUNT_YEAR_CU is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.OPENING_BALANCE_CU is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CLOSING_BALANCE_CU is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CREATE_TIME is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CREATOR_ID is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.UPDATE_TIME is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.UPDATOR_ID is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.OPENING_BALANCE is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.CLOSING_BALANCE is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ROOT_ACCOUNT_ID is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ACCOUNT_ID is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.SERIAL_NO is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ART_BLANCE_HIS_ID is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ART_BLANCE_ID is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ENTITY_ID is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.BOOK_CODE is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.YEAR_MONTH is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE1 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE2 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE3 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE4 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE5 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE6 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE7 is '';
comment on column RPT_DAP_ARTICLE_BAL_HIS.ARTICLE8 is '';
comment on column RPT_DAP_LEDGER_BALANCE.LEDGER_BLANCE_ID is '';
comment on column RPT_DAP_LEDGER_BALANCE.ENTITY_ID is '';
comment on column RPT_DAP_LEDGER_BALANCE.BOOK_CODE is '';
comment on column RPT_DAP_LEDGER_BALANCE.YEAR_MONTH is '';
comment on column RPT_DAP_LEDGER_BALANCE.CURRENCY_CODE is '';
comment on column RPT_DAP_LEDGER_BALANCE.CURRENCY_CU_CODE is '';
comment on column RPT_DAP_LEDGER_BALANCE.DEBIT_AMOUNT is '';
comment on column RPT_DAP_LEDGER_BALANCE.DEBIT_AMOUNT_CU is '';
comment on column RPT_DAP_LEDGER_BALANCE.CREDIT_AMOUNT is '';
comment on column RPT_DAP_LEDGER_BALANCE.CREDIT_AMOUNT_CU is '';
comment on column RPT_DAP_LEDGER_BALANCE.DEBIT_AMOUNT_QUARTER is '';
comment on column RPT_DAP_LEDGER_BALANCE.DEBIT_AMOUNT_QUARTER_CU is '';
comment on column RPT_DAP_LEDGER_BALANCE.CREDIT_AMOUNT_QUARTER is '';
comment on column RPT_DAP_LEDGER_BALANCE.CREDIT_AMOUNT_QUARTER_CU is '';
comment on column RPT_DAP_LEDGER_BALANCE.DEBIT_AMOUNT_YEAR is '';
comment on column RPT_DAP_LEDGER_BALANCE.DEBIT_AMOUNT_YEAR_CU is '';
comment on column RPT_DAP_LEDGER_BALANCE.CREDIT_AMOUNT_YEAR is '';
comment on column RPT_DAP_LEDGER_BALANCE.CREDIT_AMOUNT_YEAR_CU is '';
comment on column RPT_DAP_LEDGER_BALANCE.OPENING_BALANCE_CU is '';
comment on column RPT_DAP_LEDGER_BALANCE.CLOSING_BALANCE_CU is '';
comment on column RPT_DAP_LEDGER_BALANCE.CREATE_TIME is '';
comment on column RPT_DAP_LEDGER_BALANCE.CREATOR_ID is '';
comment on column RPT_DAP_LEDGER_BALANCE.UPDATE_TIME is '';
comment on column RPT_DAP_LEDGER_BALANCE.UPDATOR_ID is '';
comment on column RPT_DAP_LEDGER_BALANCE.ROOT_ACCOUNT_ID is '';
comment on column RPT_DAP_LEDGER_BALANCE.ACCOUNT_ID is '';
comment on column RPT_DAP_LEDGER_BALANCE.OPENING_BALANCE is '';
comment on column RPT_DAP_LEDGER_BALANCE.CLOSING_BALANCE is '';
comment on column RPT_DAP_LEDGER_BALANCE.SERIAL_NO is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.LEDGER_BLANCE_HIS_ID is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.LEDGER_BLANCE_ID is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.ENTITY_ID is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.BOOK_CODE is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.YEAR_MONTH is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CURRENCY_CODE is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CURRENCY_CU_CODE is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.DEBIT_AMOUNT is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.DEBIT_AMOUNT_CU is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CREDIT_AMOUNT is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CREDIT_AMOUNT_CU is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.DEBIT_AMOUNT_QUARTER is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.DEBIT_AMOUNT_QUARTER_CU is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CREDIT_AMOUNT_QUARTER is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CREDIT_AMOUNT_QUARTER_CU is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.DEBIT_AMOUNT_YEAR is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.DEBIT_AMOUNT_YEAR_CU is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CREDIT_AMOUNT_YEAR is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CREDIT_AMOUNT_YEAR_CU is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.OPENING_BALANCE_CU is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CLOSING_BALANCE_CU is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CREATE_TIME is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CREATOR_ID is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.UPDATE_TIME is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.UPDATOR_ID is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.ROOT_ACCOUNT_ID is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.ACCOUNT_ID is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.OPENING_BALANCE is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.CLOSING_BALANCE is '';
comment on column RPT_DAP_LEDGER_BALANCEHIS.SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.REPORT_ITEM_DATA_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.ENTITY_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.BOOK_CODE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.YEAR_MONTH is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.REPORT_ITEM_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.EXPR_TYPE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.ARTICLE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.EXPR_DESC is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.EXPR_DESC_ANALYTIC is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.CURRENCY_CODE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.CURRENCY_CU_CODE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.AMOUNT is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.AMOUNT_CU is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.ACC_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.CREATOR_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.CREATE_TIME is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA.REPORT_ITEM_RULE_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.REPORT_ITEM_DATA_HIS_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.REPORT_ITEM_DATA_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.ENTITY_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.BOOK_CODE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.YEAR_MONTH is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.REPORT_ITEM_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.EXPR_TYPE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.ARTICLE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.EXPR_DESC is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.EXPR_DESC_ANALYTIC is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.CURRENCY_CODE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.CURRENCY_CU_CODE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.AMOUNT is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.AMOUNT_CU is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.ACC_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.CREATOR_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.CREATE_TIME is '';
comment on column RPT_DUCT_REPORT_ITEM_DATAHIS.REPORT_ITEM_RULE_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA_SUB.REPORT_ITEM_DATA_DETAILS_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA_SUB.REPORT_ITEM_DATA_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA_SUB.EXPR_CODE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA_SUB.EXPR_DESC is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA_SUB.CURRENCY_CODE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA_SUB.CURRENCY_CU_CODE is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA_SUB.AMOUNT is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA_SUB.AMOUNT_CU is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA_SUB.CREATOR_ID is '';
comment on column RPT_DUCT_REPORT_ITEM_DATA_SUB.CREATE_TIME is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.REPORT_TEMPLATE_DUCT_ID is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.ENTITY_ID is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.BOOK_CODE is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.YEAR_MONTH is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.REPORT_TEMPLATE_ID is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.REPORT_TEMPLATE_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.REPORT_TEMPLATE_FILE_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.SHEET_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.CELL_ROW_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.CELL_COLUMN_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.CELL_CAL_VALUE is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.CELL_RULE_EXPR is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.CREATOR_ID is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.CREATE_TIME is '';
comment on column RPT_DUCT_REPORT_TEMPLATE.CELL_RULE_ANALYTIC is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_DUCT_HIS_ID is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_DUCT_ID is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.ENTITY_ID is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.BOOK_CODE is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.YEAR_MONTH is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_ID is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.REPORT_TEMPLATE_FILE_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.SHEET_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.CELL_ROW_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.CELL_COLUMN_NO is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.CELL_RULE_EXPR is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.CELL_CAL_VALUE is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.CREATOR_ID is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.CREATE_TIME is '';
comment on column RPT_DUCT_REPORT_TEMPLATEHIS.CELL_RULE_ANALYTIC is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAIL.REPORT_TEMPLATE_DUCT_ID is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAIL.TEMPLATE_DUCT_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAIL.REPORT_ITEM_ID is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAIL.REPORT_ITEM_CODE is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAIL.REPORT_ITEM_RULE_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAIL.AMOUNT is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAIL.CREATOR_ID is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAIL.CREATE_TIME is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAIL.REPORT_TEMPLATE_DUCT_DETAIL_ID is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAILHIS.REPORT_TEMPLATE_DUCT_DETAIL_HIS_ID is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAILHIS.REPORT_TEMPLATE_DUCT_DETAIL_ID is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAILHIS.REPORT_TEMPLATE_DUCT_ID is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAILHIS.TEMPLATE_DUCT_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAILHIS.REPORT_ITEM_ID is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAILHIS.REPORT_ITEM_CODE is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAILHIS.REPORT_ITEM_RULE_SERIAL_NO is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAILHIS.AMOUNT is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAILHIS.CREATOR_ID is '';
comment on column RPT_DUCT_REPORT_TEMP_DETAILHIS.CREATE_TIME is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.DRAW_TASK_ID is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.ENTITY_ID is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.BOOK_CODE is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.YEAR_MONTH is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.EXPR_TYPE is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.EXPR_DESC is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.TASK_NAME is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.TASK_PARAMS is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.TAKS_STATUS is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.LOG_MSG is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.OPER_ID is '';
comment on column RPT_LOG_REPORT_ITEM_DRAW_TASK.OPER_TIME is '';
comment on column RPT_V_CONF_CODE.CODE_ID is '';
comment on column RPT_V_CONF_CODE.UPPER_CODE_ID is '';
comment on column RPT_V_CONF_CODE.CODE_CODE is '';
comment on column RPT_V_CONF_CODE.CODE_CODE_IDX is '';
comment on column RPT_V_CONF_CODE.CODE_C_NAME is '';
comment on column RPT_V_CONF_CODE.CODE_E_NAME is '';
comment on column RPT_V_CONF_CODE.CODE_L_NAME is '';
comment on column RPT_V_CONF_CODE.VALID_IS is '';
comment on column RPT_V_CONF_CODE.DISPLAY_NO is '';
comment on column RPT_V_CONF_CODE.CREATOR_ID is '';
comment on column RPT_V_CONF_CODE.CREATE_TIME is '';
comment on column RPT_V_CONF_CODE.UPDATOR_ID is '';
comment on column RPT_V_CONF_CODE.UPDATE_TIME is '';
comment on column RPT_V_CONF_CODE.SERIAL_NO is '';
comment on column RPT_V_CONF_ITEM_RULE_SUMMARY.ENTITY_ID is '';
comment on column RPT_V_CONF_ITEM_RULE_SUMMARY.BOOK_CODE is '';
comment on column RPT_V_CONF_ITEM_RULE_SUMMARY.REPORT_ITEM_ID is '';
comment on column RPT_V_CONF_ITEM_RULE_SUMMARY.REPORT_ITEM_CODE is '';
comment on column RPT_V_CONF_ITEM_RULE_SUMMARY.SUMMARY_EXPR is '';
comment on column RPT_V_CONF_REPORT_ITEM.REPORT_ITEM_ID is '';
comment on column RPT_V_CONF_REPORT_ITEM.SERIAL_NO is '';
comment on column RPT_V_CONF_REPORT_ITEM.UPPER_REPORT_ITEM_ID is '';
comment on column RPT_V_CONF_REPORT_ITEM.REPORT_ITEM_CODE is '';
comment on column RPT_V_CONF_REPORT_ITEM.REPORT_ITEM_C_NAME is '';
comment on column RPT_V_CONF_REPORT_ITEM.REPORT_ITEM_L_NAME is '';
comment on column RPT_V_CONF_REPORT_ITEM.REPORT_ITEM_E_NAME is '';
comment on column RPT_V_CONF_REPORT_ITEM.REMARK is '';
comment on column RPT_V_CONF_REPORT_ITEM.VALID_IS is '';
comment on column RPT_V_CONF_REPORT_ITEM.CREATOR_ID is '';
comment on column RPT_V_CONF_REPORT_ITEM.CREATE_TIME is '';
comment on column RPT_V_CONF_REPORT_ITEM.UPDATOR_ID is '';
comment on column RPT_V_CONF_REPORT_ITEM.UPDATE_TIME is '';
comment on column RPT_V_CONF_REPORT_ITEM.AUDIT_STATE is '';
comment on column RPT_V_CONF_REPORT_ITEM.CHECKED_ID is '';
comment on column RPT_V_CONF_REPORT_ITEM.CHECKED_TIME is '';
comment on column RPT_V_CONF_REPORT_ITEM.CHECKED_MSG is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.UPDATE_TIME is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.AUDIT_STATE is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.CHECKED_ID is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.CHECKED_TIME is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.CHECKED_MSG is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.REPORT_ITEM_RULE_ID is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.SERIAL_NO is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.ENTITY_ID is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.BOOK_CODE is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.REPORT_ITEM_ID is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.REPORT_ITEM_CODE is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.DEAL_TYPE is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.DATA_SOURCE is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.EXPR_TYPE is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.PERIOD_TYPE is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.RULE_EXPR is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.DEAL_LEVEL is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.VALID_IS is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.REMARK is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.CREATOR_ID is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.CREATE_TIME is '';
comment on column RPT_V_CONF_REPORT_ITEM_RULE.UPDATOR_ID is '';


# AtrBussLrcTotService 分片配置优化说明

## 优化背景

原代码中分片数量（PARTITION_SIZE）是硬编码的，这降低了程序的健壮性和灵活性。为了提高系统的可配置性，现将分片数量改为从数据库配置表中动态获取。

## 修改内容

### 1. Java代码修改

#### AtrBussLrcTotService.java

**修改位置1：calcIcp方法**
```java
// 修改前
private void calcIcp() {
    final int PARTITION_SIZE = 20; // 分区数量
    
    for (int partNo = 0; partNo < PARTITION_SIZE; partNo++) {

// 修改后  
private void calcIcp() {
    // 使用从配置表中获取的parts值，提高程序健壮性
    // parts值在AbstractAtrBussLrcService.initEnvParams中通过EcfUtil.getLrcConfParts获取
    
    for (int partNo = 0; partNo < parts; partNo++) {
```

**修改位置2：calculateTransitionEdPremium方法**
```java
// 修改前
// 设置分区数量
final int parts = 20; // 分区数量

// 构建插入临时表所需参数
Map<String, Object> tempDataParams = new HashMap<>(commonParamMap);
tempDataParams.put("parts", parts);

// 分区计算
for (int partNo = 0; partNo < parts; partNo++) {

// 修改后
// 使用从配置表中获取的分区数量
final int partsCount = this.parts;

// 构建插入临时表所需参数
Map<String, Object> tempDataParams = new HashMap<>(commonParamMap);
tempDataParams.put("parts", partsCount);

// 分区计算
for (int partNo = 0; partNo < partsCount; partNo++) {
```

### 2. SQL配置修改

#### AtrBussLrcToCustDao.xml

**修改位置：partitionBaseDataT方法中的pn赋值**
```sql
-- 修改前
-- 添加分区号，用于后续分批处理
trunc(random() * 20) as pn

-- 修改后
-- 添加分区号，用于后续分批处理，使用配置的parts值
trunc(random() * #{parts,jdbcType=INTEGER}) as pn
```

## 配置机制说明

### 1. 配置表结构
分片配置存储在 `atr_conf_partition` 表中：
- `year_month`: 评估期年月
- `business_source_code`: 业务来源代码
- `mode`: 模式（LRC）
- `parts`: 分片数量

### 2. 获取逻辑
```java
// AbstractAtrBussLrcService.initEnvParams方法中
this.parts = EcfUtil.getLrcConfParts(atrBussLrcDao, yearMonth, businessSourceCode);

// EcfUtil.getLrcConfParts方法
public static int getLrcConfParts(AtrBussLrcDao dao, String yearMonth, String businessSourceCode) {
    Integer parts = dao.getConfParts(yearMonth, businessSourceCode);
    if (parts == null) {
        parts = dao.getConfParts(yearMonth, "A");  // 通用配置
    }
    return parts == null ? 20 : parts;  // 默认值20
}
```

### 3. SQL查询逻辑
```sql
SELECT COALESCE(
    (SELECT parts::integer
     FROM atr_conf_partition
     WHERE year_month = #{yearMonth,jdbcType=VARCHAR}
       AND business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
       AND mode = #{mode,jdbcType=VARCHAR}),
    20
) as conf_value
```

## 优化效果

### 1. 提高灵活性
- 可以根据不同的评估期和业务类型配置不同的分片数量
- 无需修改代码即可调整分片策略

### 2. 增强健壮性
- 避免硬编码带来的维护问题
- 统一的配置管理机制

### 3. 性能优化
- 可以根据数据量和系统资源动态调整分片数量
- 支持不同环境使用不同的分片配置

## 配置示例

### 1. 插入配置数据
```sql
-- 为TO业务配置50个分片
INSERT INTO atr_conf_partition (year_month, business_source_code, mode, parts)
VALUES ('202501', 'TO', 'LRC', 50);

-- 通用配置（所有业务类型）
INSERT INTO atr_conf_partition (year_month, business_source_code, mode, parts)
VALUES ('202501', 'A', 'LRC', 30);
```

### 2. 查看当前配置
```sql
SELECT year_month, business_source_code, mode, parts
FROM atr_conf_partition
WHERE mode = 'LRC'
ORDER BY year_month DESC, business_source_code;
```

## 注意事项

### 1. 配置优先级
1. 优先使用具体业务类型的配置
2. 如果没有具体配置，使用通用配置（business_source_code = 'A'）
3. 如果都没有配置，使用默认值20

### 2. 分片数量建议
- 数据量小（<10万条）：建议10-20个分片
- 数据量中等（10-100万条）：建议20-50个分片
- 数据量大（>100万条）：建议50-100个分片

### 3. 系统资源考虑
- 分片数量过多可能导致数据库连接压力
- 分片数量过少可能导致内存占用过大
- 建议根据实际环境进行性能测试后确定最优值

## 兼容性说明

本次修改完全向后兼容：
- 如果配置表中没有相关配置，系统会使用默认值20
- 现有的业务逻辑和计算结果不受影响
- 可以逐步为不同的评估期和业务类型添加配置

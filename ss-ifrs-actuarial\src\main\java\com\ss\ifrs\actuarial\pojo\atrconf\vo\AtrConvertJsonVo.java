package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import java.io.Serializable;

public class AtrConvertJsonVo implements Serializable {

    private Long entityId;

    private String yearMonth;


	/**
	 * 计量类型：全量-A /单个业务线-S
	 */
	private String evaluateType;

    private Long userId;
	
	/**
	 * 自动任务传输字段：任务模式：A-自动
	 */
	private String taskMode;
	
	/**
	 * 自动任务传输字段：任务编码
	 */
	private String taskCode;
	
	
	/**
	 * 自动任务传输字段：重试次数
	 */
	private Long retryOrder;
	
	/**
	 * 自动任务传输字段：业务期间状态
	 */
	private String periodState;
    
    private static final long serialVersionUID = 1L;

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}
	public String getEvaluateType() {
		return evaluateType;
	}
	
	public void setEvaluateType(String evaluateType) {
		this.evaluateType = evaluateType;
	}
	
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
	
	public String getTaskMode() {
		return taskMode;
	}
	
	public void setTaskMode(String taskMode) {
		this.taskMode = taskMode;
	}
	
	public String getTaskCode() {
		return taskCode;
	}
	
	public void setTaskCode(String taskCode) {
		this.taskCode = taskCode;
	}
	
	public Long getRetryOrder() {
		return retryOrder;
	}
	
	public void setRetryOrder(Long retryOrder) {
		this.retryOrder = retryOrder;
	}

	public String getPeriodState() {
		return periodState;
	}

	public void setPeriodState(String periodState) {
		this.periodState = periodState;
	}
}
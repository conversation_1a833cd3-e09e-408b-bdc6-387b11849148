grant SELECT on ATR_CONF_BUSSPERIOD to QTCUS<PERSON>;
grant SELECT on ATR_BUSS_LRC_ACTION to QTCUSER;
grant SELECT on ATR_BUSS_LIC_ACTION to QTCUSER;

grant SELECT on ATR_BUSS_DD_LRC_U to QTCUSER;
grant SELECT on ATR_BUSS_FO_LRC_U to QTCUSER;
grant SELECT on ATR_BUSS_TI_LRC_U to QTCUSER;
grant SELECT on ATR_BUSS_TO_LRC_U to QTCUSER;


grant SELECT on ATR_BUSS_DD_LRC_U_DTL to QTCUSER;
grant SELECT on ATR_BUSS_FO_LRC_U_DTL to QTCUSER;
grant SELECT on ATR_BUSS_TI_LRC_U_DTL to QTCUSER;
grant SELECT on ATR_BUSS_TO_LRC_U_DTL to QTCUSER;

grant SELECT on ATR_BUSS_DD_LIC_G to QTCUSER;
grant SELECT on ATR_BUSS_FO_LIC_G to QTCUSER;
grant SELECT on ATR_BUSS_TI_LIC_G to QTCUSER;
grant SELECT on ATR_BUSS_TO_LIC_G to Q<PERSON>US<PERSON>;

grant SELECT on ATR_BUSS_DD_LIC_G_DTL to QTCUSER;
grant SELECT on ATR_BUSS_FO_LIC_G_DTL to QTCUSER;
grant SELECT on ATR_BUSS_TI_LIC_G_DTL to QTCUSER;
grant SELECT on ATR_BUSS_TO_LIC_G_DTL to QTCUSER;

grant SELECT on ATR_DAP_DD_PREMIUM_PAID to QTCUSER;
grant SELECT on ATR_DAP_FO_PREMIUM_PAID to QTCUSER;
grant SELECT on ATR_DAP_TI_PREMIUM_PAID to QTCUSER;
grant SELECT on ATR_DAP_TO_PREMIUM_PAID to QTCUSER;


grant SELECT on ATR_DAP_DD_CLM_PAID to QTCUSER;
grant SELECT on ATR_DAP_FO_CLM_PAID to QTCUSER;
grant SELECT on ATR_DAP_TI_CLM_PAID to QTCUSER;
grant SELECT on ATR_DAP_TO_CLM_PAID to QTCUSER;


grant SELECT on atr_DAP_DD_PREMIUM to QTCUSER;
grant SELECT on atr_DAP_FO_PREMIUM to QTCUSER;
grant SELECT on atr_DAP_TI_PREMIUM to QTCUSER;
grant SELECT on atr_DAP_TO_PREMIUM to QTCUSER;


grant SELECT on ATR_BUSS_DD_LRC_G to QTCUSER;
grant SELECT on ATR_BUSS_FO_LRC_G to QTCUSER;
grant SELECT on ATR_BUSS_TI_LRC_G to QTCUSER;
grant SELECT on ATR_BUSS_TO_LRC_G to QTCUSER;

grant SELECT on ATR_CONF_MODEL_FACTOR_SD to rptuser;



grant SELECT on ATR_BUSS_BR_POLICY to ACCUSER;
grant SELECT on ATR_DAP_TO_PREMIUM to ACCUSER;
grant SELECT on ATR_CONF_FACTOR_OUTPUT_REF to ACCUSER;
grant SELECT on ATR_DAP_CMUNIT to ACCUSER;
grant SELECT on ATR_BUSS_EVALUATE_DATA_CURRENT to ACCUSER;
grant SELECT on ATR_DAP_TI_PREMIUM to ACCUSER;
grant SELECT on ATR_DAP_FO_PREMIUM to ACCUSER;
grant SELECT on ATR_DAP_DD_PREMIUM to ACCUSER;


grant SELECT on ATR_CONF_QUOTA_DEF_FACT to DMUSER;
grant SELECT on ATR_CONF_QUOTA_DETAIL to DMUSER;
grant SELECT on ATR_CONF_QUOTA_DEF to DMUSER;
grant SELECT on ATR_CONF_QUOTA to DMUSER;



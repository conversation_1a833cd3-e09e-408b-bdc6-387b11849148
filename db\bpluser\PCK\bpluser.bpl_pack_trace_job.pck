CREATE OR REPLACE PACKAGE bpl_pack_trace_job IS

  FUNCTION func_run_task_judge(p_entity_id  NUMBER,
                               p_book_code  VARCHAR2,
                               p_year_month VARCHAR2,
                               p_func_code  VARCHAR2) RETURN VARCHAR2;

  PROCEDURE proc_update_task_log(p_entity_id  IN NUMBER,
                                 p_book_code  IN VARCHAR2,
                                 p_year_month IN VARCHAR2,
                                 p_func_code  IN VARCHAR2,
                                 p_state      IN VARCHAR2);

  PROCEDURE proc_update_task_log(p_entity_id   IN NUMBER,
                                 p_year_month  IN VARCHAR2,
                                 p_book_code   IN VARCHAR2,
                                 p_task_code   IN VARCHAR2,
                                 p_task_mode   IN VARCHAR2,
                                 p_func_code   IN VARCHAR2,
                                 p_task_status IN VARCHAR2);

END bpl_pack_trace_job;
/
CREATE OR REPLACE PACKAGE BODY bpl_pack_trace_job IS

  /***********************************************************************
       NAME         : func_run_task_judge
       DESCRIPTION  : 判断是否允许功能执行任务继续执行【用于手动执行任务的功能pck入口调用】
       PARAM        : in numeric  - p_entity_id  业务单位
       PARAM        : in varchar  - p_book_code  账套，可为空【会计引擎、报表管理的业务期间含账套字段】
       PARAM        : in varchar  - p_year_month  业务年月
       PARAM        : in varchar  - p_func_code  功能编码
       RETURN       : in varchar  - 返回1-允许继续/0-不允许继续
       DATE         :2022-05-19
       AUTHOR       :SRY
  ***********************************************************************/
  FUNCTION func_run_task_judge(p_entity_id  NUMBER,
                               p_book_code  VARCHAR2,
                               p_year_month VARCHAR2,
                               p_func_code  VARCHAR2) RETURN VARCHAR2 IS
  
    v_start_time DATE;
  
  BEGIN
    --会计引擎、报表管理的业务期间含账套字段
    IF p_book_code IS NULL THEN
      -- 获取当前功能日志状态为：1-执行中的功能日志
      SELECT tl.start_time
        INTO v_start_time
        FROM bpluser.bpl_log_pub_task tl
        JOIN bpluser.bpl_qrtz_conf_task_detail td
          ON tl.task_detail_id = td.task_detail_id
       WHERE tl.entity_id = p_entity_id
         AND tl.year_month = p_year_month
         AND td.func_code = p_func_code
         AND tl.task_status = '1'
       ORDER BY tl.create_time DESC;
    
      IF v_start_time IS NULL THEN
        -- 如果不存在1-执行中的功能任务，则更新功能任务日志为1-执行中，并返回判定结果1-允许功能继续执行
        bpluser.bpl_pack_trace_job.proc_update_task_log(p_entity_id, NULL, p_year_month, p_func_code, '1');
        RETURN '1';
      ELSE
        -- 如果存在1-执行中的功能任务，则更新功能任务日志为4-不执行，并返回判定结果0-不允许功能继续执行
        bpluser.bpl_pack_trace_job.proc_update_task_log(p_entity_id, NULL, p_year_month, p_func_code, '4');
        RETURN '0';
      END IF;
    
    ELSE
      -- 获取当前功能日志状态为：1-执行中的功能日志
      SELECT tl.start_time
        INTO v_start_time
        FROM bpluser.bpl_log_pub_task tl
        JOIN bpluser.bpl_qrtz_conf_task_detail td
          ON tl.task_detail_id = td.task_detail_id
       WHERE tl.entity_id = p_entity_id
         AND tl.year_month = p_year_month
         AND tl.book_code = p_book_code --有账套
         AND td.func_code = p_func_code
         AND tl.task_status = '1'
       ORDER BY tl.create_time DESC;
    
      IF v_start_time IS NULL THEN
        -- 如果不存在1-执行中的功能任务，则更新功能任务日志为1-执行中，并返回判定结果1-允许功能继续执行
        bpluser.bpl_pack_trace_job.proc_update_task_log(p_entity_id, p_book_code, p_year_month, p_func_code, '1');
        RETURN '1';
      ELSE
        -- 如果存在1-执行中的功能任务，则更新功能任务日志为4-不执行，并返回判定结果0-不允许功能继续执行
        bpluser.bpl_pack_trace_job.proc_update_task_log(p_entity_id, p_book_code, p_year_month, p_func_code, '4');
        RETURN '0';
      END IF;
    END IF;
  
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(SQLERRM);
  END func_run_task_judge;

  /***********************************************************************
   NAME         : proc_update_task_log
   DESCRIPTION  : 更新功能任务执行日志【用于手动执行任务的功能pck出口调用】
   PARAM        : in numeric  - p_entity_id  业务单位
   PARAM        : in varchar  - p_book_code  账套【会计引擎、报表管理的业务期间含账套字段】，可为空
   PARAM        : in varchar  - p_year_month  业务年月
   PARAM        : in varchar  - p_func_code  功能编码
   PARAM        : in varchar  - p_state  任务状态: 0-待执行、1-执行中、2-成功、3-失败、4-不执行
   DATE         :2021-10-27
   AUTHOR       :SRY
  ***********************************************************************/
  PROCEDURE proc_update_task_log(p_entity_id  IN NUMBER,
                                 p_book_code  IN VARCHAR2,
                                 p_year_month IN VARCHAR2,
                                 p_func_code  IN VARCHAR2,
                                 p_state      IN VARCHAR2) IS
    v_task_detail_id NUMBER(11);
    v_priority_no    NUMBER(8);
    v_rel_dtl_count  NUMBER(8);
    v_count          NUMBER(8);
    v_task_code      VARCHAR2(200);
    rec_detail       bpl_log_pub_task.task_detail_id%TYPE;
  
  BEGIN
    -- 必要参数为空处理
    IF p_entity_id IS NULL
       OR p_year_month IS NULL
       OR p_func_code IS NULL THEN
      RETURN;
    END IF;
  
    -- 分场景处理：账套为空、不为空
    IF p_book_code IS NULL THEN
      -- 获取功能任务日志执行状态为0-待执行，按任务日志创建时间倒序排序的最新功能任务日志
      SELECT COUNT(1)
        INTO v_count
        FROM bpluser.bpl_log_pub_task tl
        JOIN bpluser.bpl_qrtz_conf_task_detail td
          ON tl.task_detail_id = td.task_detail_id
       WHERE td.func_code = p_func_code
         AND tl.entity_id = p_entity_id
         AND tl.year_month = p_year_month
         AND tl.task_status = '0'
       ORDER BY tl.create_time DESC;
    
      IF v_count > 0 THEN
        SELECT t.task_detail_id,
               t.task_code
          INTO v_task_detail_id,
               v_task_code
          FROM (SELECT tl.task_detail_id,
                       tl.task_code
                  FROM bpluser.bpl_log_pub_task tl
                  JOIN bpluser.bpl_qrtz_conf_task_detail td
                    ON tl.task_detail_id = td.task_detail_id
                 WHERE td.func_code = p_func_code
                   AND tl.entity_id = p_entity_id
                   AND tl.year_month = p_year_month
                   AND tl.task_status = '0'
                 ORDER BY tl.create_time DESC) t
         WHERE rownum = 1;
      END IF;
    
      --功能任务开始执行，更新0-待执行的功能日志执行状态为1-执行中、开始时间为当前时间、更新时间
      IF p_state = '1' THEN
        --修改任务状态 执行中，开始时间为当前时间
        UPDATE bpluser.bpl_log_pub_task
           SET start_time  = localtimestamp,
               task_status = p_state,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = v_task_code
           AND task_status = '0'
           AND task_detail_id = v_task_detail_id;
      
        --功能任务执行成功，更新1-执行中的功能任务的执行状态为2-成功、结束时间为当前时间、更新时间
      ELSIF p_state = '2' THEN
        SELECT COUNT(1)
          INTO v_count
          FROM bpluser.bpl_log_pub_task tl
          JOIN bpluser.bpl_qrtz_conf_task_detail td
            ON tl.task_detail_id = td.task_detail_id
         WHERE td.func_code = p_func_code
           AND tl.entity_id = p_entity_id
           AND tl.year_month = p_year_month
           AND tl.task_status = '1'
         ORDER BY tl.create_time DESC;
      
        IF v_count > 0 THEN
          SELECT t.task_detail_id,
                 t.task_code
            INTO v_task_detail_id,
                 v_task_code
            FROM (SELECT tl.task_detail_id,
                         tl.task_code
                    FROM bpluser.bpl_log_pub_task tl
                    JOIN bpluser.bpl_qrtz_conf_task_detail td
                      ON tl.task_detail_id = td.task_detail_id
                   WHERE td.func_code = p_func_code
                     AND tl.entity_id = p_entity_id
                     AND tl.year_month = p_year_month
                     AND tl.task_status = '1'
                   ORDER BY tl.create_time DESC) t
           WHERE rownum = 1;
        END IF;
      
        UPDATE bpluser.bpl_log_pub_task
           SET end_time    = localtimestamp,
               task_status = p_state,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = v_task_code
           AND task_status = '1'
           AND task_detail_id = v_task_detail_id;
      
        --功能任务执行失败[执行功能DB异常]，更新1-执行中的功能任务的执行状态为3-失败、结束时间为当前时间、更新时间、异常发生时间
      ELSIF p_state = '3' THEN
        SELECT COUNT(1)
          INTO v_count
          FROM bpluser.bpl_log_pub_task tl
          JOIN bpluser.bpl_qrtz_conf_task_detail td
            ON tl.task_detail_id = td.task_detail_id
         WHERE td.func_code = p_func_code
           AND tl.entity_id = p_entity_id
           AND tl.year_month = p_year_month
           AND tl.task_status = '1'
         ORDER BY tl.create_time DESC;
      
        IF v_count > 0 THEN
          SELECT t.task_detail_id,
                 t.task_code
            INTO v_task_detail_id,
                 v_task_code
            FROM (SELECT tl.task_detail_id,
                         tl.task_code
                    FROM bpluser.bpl_log_pub_task tl
                    JOIN bpluser.bpl_qrtz_conf_task_detail td
                      ON tl.task_detail_id = td.task_detail_id
                   WHERE td.func_code = p_func_code
                     AND tl.entity_id = p_entity_id
                     AND tl.year_month = p_year_month
                     AND tl.task_status = '1'
                   ORDER BY tl.create_time DESC) t
           WHERE rownum = 1;
        END IF;
      
        UPDATE bpluser.bpl_log_pub_task
           SET end_time    = localtimestamp,
               error_time  = localtimestamp,
               task_status = p_state,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = v_task_code
           AND task_status = '1'
           AND task_detail_id = v_task_detail_id;
      
        -- 获取当前功能的优先级序号，更新有依赖关系的功能日志状态，此时其它功能0-待执行
        SELECT priority_no INTO v_priority_no FROM bpluser.bpl_qrtz_conf_task_detail WHERE task_detail_id = v_task_detail_id;
      
        -- 获取当前功能所属功能组下的其它0-待执行功能任务中有依赖关系，且优先级序号低于(序号越大优先级越低)当前的功能
        FOR rec_detail IN (SELECT tl.task_detail_id
                             FROM bpluser.bpl_log_pub_task tl
                             LEFT JOIN bpluser.bpl_qrtz_conf_task_detail td
                               ON tl.task_detail_id = td.task_detail_id
                             LEFT JOIN bpluser.bpl_qrtz_conf_task ct
                               ON ct.conf_task_id = td.conf_task_id
                            WHERE tl.task_code = v_task_code
                              AND tl.task_status = '0'
                              AND td.relation_task_dtl_id IS NOT NULL
                              AND td.priority_no > v_priority_no
                            ORDER BY td.priority_no) LOOP
        
          -- 先将当前功能ID置为被依赖功能对象，查询依赖它的功能
          SELECT COUNT(1) INTO v_rel_dtl_count FROM bpluser.bpl_qrtz_conf_task_detail WHERE relation_task_dtl_id = v_task_detail_id;
        
          -- 统计依赖它功能的功能数量
          IF v_rel_dtl_count > 0 THEN
            -- 若存在依赖它的功能，则修改其依赖它的功能的执行状态为：4-不执行
            UPDATE bpluser.bpl_log_pub_task
               SET task_status = '4',
                   update_time = localtimestamp
             WHERE entity_id = p_entity_id
               AND year_month = p_year_month
               AND task_code = v_task_code
               AND task_status = '0'
               AND task_detail_id = rec_detail.task_detail_id;
          
            v_task_detail_id := rec_detail.task_detail_id;
          END IF;
        END LOOP;
      
        --功能任务不符合执行条件[存在已经执行的功能任务时，不允许再次执行]，更新执行状态为4-不执行、更新时间
      ELSIF p_state = '4' THEN
        UPDATE bpluser.bpl_log_pub_task
           SET task_status = p_state,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = v_task_code
           AND task_detail_id = v_task_detail_id;
      
        -- 获取当前功能的优先级序号，更新有依赖关系的功能日志状态，此时其它功能0-待执行
        SELECT priority_no INTO v_priority_no FROM bpluser.bpl_qrtz_conf_task_detail WHERE task_detail_id = v_task_detail_id;
      
        -- 获取当前功能所属功能组下的其它0-待执行功能任务中有依赖关系，且优先级序号低于(序号越大优先级越低)当前的功能
        FOR rec_detail IN (SELECT tl.task_detail_id
                             FROM bpluser.bpl_log_pub_task tl
                             LEFT JOIN bpluser.bpl_qrtz_conf_task_detail td
                               ON tl.task_detail_id = td.task_detail_id
                             LEFT JOIN bpluser.bpl_qrtz_conf_task ct
                               ON ct.conf_task_id = td.conf_task_id
                            WHERE tl.task_code = v_task_code
                              AND tl.task_status = '0'
                              AND td.relation_task_dtl_id IS NOT NULL
                              AND td.priority_no > v_priority_no
                            ORDER BY td.priority_no) LOOP
        
          -- 先将当前功能ID置为被依赖功能对象，查询依赖它的功能
          SELECT COUNT(1) INTO v_rel_dtl_count FROM bpluser.bpl_qrtz_conf_task_detail WHERE relation_task_dtl_id = v_task_detail_id;
        
          -- 统计依赖它功能的功能数量
          IF v_rel_dtl_count > 0 THEN
            -- 若存在依赖它的功能，则修改其依赖它的功能的执行状态为：4-不执行
            UPDATE bpluser.bpl_log_pub_task
               SET task_status = '4',
                   update_time = localtimestamp
             WHERE entity_id = p_entity_id
               AND year_month = p_year_month
               AND task_code = v_task_code
               AND task_status = '0'
               AND task_detail_id = rec_detail.task_detail_id;
          
            v_task_detail_id := rec_detail.task_detail_id;
          END IF;
        END LOOP;
      END IF;
    
      --会计引擎、报表管理的业务期间含账套字段
    ELSE
      -- 获取功能任务日志执行状态为0-待执行，按任务日志创建时间倒序排序的最新功能任务日志
      SELECT COUNT(1)
        INTO v_count
        FROM bpluser.bpl_log_pub_task tl
        JOIN bpluser.bpl_qrtz_conf_task_detail td
          ON tl.task_detail_id = td.task_detail_id
       WHERE td.func_code = p_func_code
         AND tl.entity_id = p_entity_id
         AND tl.year_month = p_year_month
         AND tl.book_code = p_book_code --有账套
         AND tl.task_status = '0'
       ORDER BY tl.create_time DESC;
    
      IF v_count > 0 THEN
        SELECT t.task_detail_id,
               t.task_code
          INTO v_task_detail_id,
               v_task_code
          FROM (SELECT tl.task_detail_id,
                       tl.task_code
                  FROM bpluser.bpl_log_pub_task tl
                  JOIN bpluser.bpl_qrtz_conf_task_detail td
                    ON tl.task_detail_id = td.task_detail_id
                 WHERE td.func_code = p_func_code
                   AND tl.entity_id = p_entity_id
                   AND tl.year_month = p_year_month
                   AND tl.book_code = p_book_code --有账套
                   AND tl.task_status = '0'
                 ORDER BY tl.create_time DESC) t
         WHERE rownum = 1;
      END IF;
    
      --功能任务开始执行，更新0-待执行的功能日志执行状态为1-执行中、开始时间为当前时间、更新时间
      IF p_state = '1' THEN
        --修改任务状态 执行中，开始时间为当前时间
        UPDATE bpluser.bpl_log_pub_task
           SET start_time  = localtimestamp,
               task_status = p_state,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND book_code = p_book_code --有账套
           AND task_code = v_task_code
           AND task_status = '0'
           AND task_detail_id = v_task_detail_id;
      
        --功能任务执行成功，更新1-执行中的功能任务的执行状态为2-成功、结束时间为当前时间、更新时间
      ELSIF p_state = '2' THEN
        SELECT COUNT(1)
          INTO v_count
          FROM bpluser.bpl_log_pub_task tl
          JOIN bpluser.bpl_qrtz_conf_task_detail td
            ON tl.task_detail_id = td.task_detail_id
         WHERE td.func_code = p_func_code
           AND tl.entity_id = p_entity_id
           AND tl.year_month = p_year_month
           AND tl.book_code = p_book_code --有账套
           AND tl.task_status = '1'
         ORDER BY tl.create_time DESC;
      
        IF v_count > 0 THEN
          SELECT t.task_detail_id,
                 t.task_code
            INTO v_task_detail_id,
                 v_task_code
            FROM (SELECT tl.task_detail_id,
                         tl.task_code
                    FROM bpluser.bpl_log_pub_task tl
                    JOIN bpluser.bpl_qrtz_conf_task_detail td
                      ON tl.task_detail_id = td.task_detail_id
                   WHERE td.func_code = p_func_code
                     AND tl.entity_id = p_entity_id
                     AND tl.year_month = p_year_month
                     AND tl.book_code = p_book_code --有账套
                     AND tl.task_status = '1'
                   ORDER BY tl.create_time DESC) t
           WHERE rownum = 1;
        END IF;
      
        UPDATE bpluser.bpl_log_pub_task
           SET end_time    = localtimestamp,
               task_status = p_state,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND book_code = p_book_code --有账套
           AND task_code = v_task_code
           AND task_status = '1'
           AND task_detail_id = v_task_detail_id;
      
        --功能任务执行失败[执行功能DB异常]，更新1-执行中的功能任务的执行状态为3-失败、结束时间为当前时间、更新时间、异常发生时间
      ELSIF p_state = '3' THEN
      
        SELECT COUNT(1)
          INTO v_count
          FROM bpluser.bpl_log_pub_task tl
          JOIN bpluser.bpl_qrtz_conf_task_detail td
            ON tl.task_detail_id = td.task_detail_id
         WHERE td.func_code = p_func_code
           AND tl.entity_id = p_entity_id
           AND tl.year_month = p_year_month
           AND tl.book_code = p_book_code --有账套
           AND tl.task_status = '1'
         ORDER BY tl.create_time DESC;
      
        IF v_count > 0 THEN
          SELECT t.task_detail_id,
                 t.task_code
            INTO v_task_detail_id,
                 v_task_code
            FROM (SELECT tl.task_detail_id,
                         tl.task_code
                    FROM bpluser.bpl_log_pub_task tl
                    JOIN bpluser.bpl_qrtz_conf_task_detail td
                      ON tl.task_detail_id = td.task_detail_id
                   WHERE td.func_code = p_func_code
                     AND tl.entity_id = p_entity_id
                     AND tl.year_month = p_year_month
                     AND tl.book_code = p_book_code --有账套
                     AND tl.task_status = '1'
                   ORDER BY tl.create_time DESC) t
           WHERE rownum = 1;
        END IF;
      
        UPDATE bpluser.bpl_log_pub_task
           SET end_time    = localtimestamp,
               error_time  = localtimestamp,
               task_status = p_state,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND book_code = p_book_code --有账套
           AND task_code = v_task_code
           AND task_status = '1'
           AND task_detail_id = v_task_detail_id;
      
        -- 获取当前功能的优先级序号，更新有依赖关系的功能日志状态，此时其它功能0-待执行
        SELECT priority_no INTO v_priority_no FROM bpluser.bpl_qrtz_conf_task_detail WHERE task_detail_id = v_task_detail_id;
        -- 获取当前功能所属功能组下的其它0-待执行功能任务中有依赖关系，且优先级序号低于(序号越大优先级越低)当前的功能
        FOR rec_detail IN (SELECT tl.task_detail_id
                             FROM bpluser.bpl_log_pub_task tl
                             LEFT JOIN bpluser.bpl_qrtz_conf_task_detail td
                               ON tl.task_detail_id = td.task_detail_id
                             LEFT JOIN bpluser.bpl_qrtz_conf_task ct
                               ON ct.conf_task_id = td.conf_task_id
                            WHERE tl.task_code = v_task_code
                              AND tl.task_status = '0'
                              AND td.relation_task_dtl_id IS NOT NULL
                              AND td.priority_no > v_priority_no
                            ORDER BY td.priority_no) LOOP
          -- 先将当前功能ID置为被依赖功能对象，查询依赖它的功能
          SELECT COUNT(1) INTO v_rel_dtl_count FROM bpluser.bpl_qrtz_conf_task_detail WHERE relation_task_dtl_id = v_task_detail_id;
          -- 统计依赖它功能的功能数量
          IF v_rel_dtl_count > 0 THEN
            -- 若存在依赖它的功能，则修改其依赖它的功能的执行状态为：4-不执行
            UPDATE bpluser.bpl_log_pub_task
               SET task_status = '4',
                   update_time = localtimestamp
             WHERE entity_id = p_entity_id
               AND year_month = p_year_month
               AND book_code = p_book_code --有账套
               AND task_code = v_task_code
               AND task_status = '0'
               AND task_detail_id = rec_detail.task_detail_id;
          
            v_task_detail_id := rec_detail.task_detail_id;
          END IF;
        END LOOP;
      
        --功能任务不符合执行条件[存在已经执行的功能任务时，不允许再次执行]，更新执行状态为4-不执行、更新时间
      ELSIF p_state = '4' THEN
        UPDATE bpluser.bpl_log_pub_task
           SET task_status = '4',
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND book_code = p_book_code --有账套
           AND task_code = v_task_code
           AND task_detail_id = v_task_detail_id;
      
        -- 获取当前功能的优先级序号，更新有依赖关系的功能日志状态，此时其它功能0-待执行
        SELECT priority_no INTO v_priority_no FROM bpluser.bpl_qrtz_conf_task_detail WHERE task_detail_id = v_task_detail_id;
        -- 获取当前功能所属功能组下的其它0-待执行功能任务中有依赖关系，且优先级序号低于(序号越大优先级越低)当前的功能
        FOR rec_detail IN (SELECT tl.task_detail_id
                             FROM bpluser.bpl_log_pub_task tl
                             LEFT JOIN bpluser.bpl_qrtz_conf_task_detail td
                               ON tl.task_detail_id = td.task_detail_id
                             LEFT JOIN bpluser.bpl_qrtz_conf_task ct
                               ON ct.conf_task_id = td.conf_task_id
                            WHERE tl.task_code = v_task_code
                              AND tl.task_status = '0'
                              AND td.relation_task_dtl_id IS NOT NULL
                              AND td.priority_no > v_priority_no
                            ORDER BY td.priority_no) LOOP
        
          -- 先将当前功能ID置为被依赖功能对象，查询依赖它的功能
          SELECT COUNT(1) INTO v_rel_dtl_count FROM bpluser.bpl_qrtz_conf_task_detail WHERE relation_task_dtl_id = v_task_detail_id;
        
          -- 统计依赖它功能的功能数量
          IF v_rel_dtl_count > 0 THEN
            -- 若存在依赖它的功能，则修改其依赖它的功能的执行状态为：4-不执行
            UPDATE bpluser.bpl_log_pub_task
               SET task_status = '4',
                   update_time = localtimestamp
             WHERE entity_id = p_entity_id
               AND year_month = p_year_month
               AND book_code = p_book_code --有账套
               AND task_code = v_task_code
               AND task_status = '0'
               AND task_detail_id = rec_detail.task_detail_id;
          
            v_task_detail_id := rec_detail.task_detail_id;
          END IF;
        END LOOP;
      END IF;
    
    END IF;
  
    -- 提交事务
    COMMIT;
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(to_char(SQLCODE) || '::' || substr(SQLERRM, 1, 200));
  END proc_update_task_log;

  /***********************************************************************
     NAME         : proc_update_task_log
     DESCRIPTION  : 更新定时任务功能任务日志【用于手动/自动任务更新日志状态】
     PARAM        : in numeric  - p_entity_id  业务单位
     PARAM        : in varchar  - p_year_month  业务年月
     PARAM        : in varchar  - p_book_code  账套【会计引擎、报表管理的业务期间含账套字段】，可为空
     PARAM        : in varchar  - p_task_code  任务编码
     PARAM        : in varchar  - p_task_mode  任务模式
     PARAM        : in varchar  - p_func_code  功能编码
     PARAM        : in varchar  - p_task_status  任务状态: 0-待执行、1-执行中、2-成功、3-失败、4-不执行
     DATE         : 2022-9-27
     AUTHOR       : YXH
  ***********************************************************************/
  PROCEDURE proc_update_task_log(p_entity_id   IN NUMBER,
                                 p_year_month  IN VARCHAR2,
                                 p_book_code   IN VARCHAR2,
                                 p_task_code   IN VARCHAR2,
                                 p_task_mode   IN VARCHAR2,
                                 p_func_code   IN VARCHAR2,
                                 p_task_status IN VARCHAR2) IS
    --变量声明
    v_log_msg   VARCHAR2(4000); --日志信息
    v_error_msg VARCHAR2(200); --错误信息
  
    v_task_detail_id NUMBER(11);
    v_priority_no    NUMBER(8);
    v_rel_dtl_count  NUMBER(8);
    v_count          NUMBER(8);
    rec_detail       bpl_log_pub_task.task_detail_id%TYPE;
  
  BEGIN
  
    -- 手动任务模式下，必要参数p_entity_id、p_year_month、p_func_code、p_task_status非空处理
    IF p_entity_id IS NULL
       OR p_year_month IS NULL
       OR p_task_code IS NULL
       OR p_task_mode IS NULL
       OR p_func_code IS NULL
       OR p_task_status IS NULL THEN
    
      --抛出异常信息 【中断事务】
      v_error_msg := '参数条件不满足: [' || p_entity_id || ',' || p_year_month || ',' || p_task_code || ',' || p_task_mode || ',' || p_func_code || ',' || p_task_status || ']';
      raise_application_error(-20002, v_error_msg);
    END IF;
  
    -- 分业务场景处理：账套为空、不为空
    IF p_book_code IS NULL THEN
      -- 获取功能任务日志执行状态为0-待执行，按任务日志创建时间倒序排序的最新功能任务日志
      SELECT COUNT(1)
        INTO v_count
        FROM bpluser.bpl_log_pub_task tl
        JOIN bpluser.bpl_qrtz_conf_task_detail td
          ON tl.task_detail_id = td.task_detail_id
       WHERE tl.entity_id = p_entity_id
         AND tl.year_month = p_year_month
         AND tl.task_code = p_task_code
         AND tl.task_mode = p_task_mode
         AND td.func_code = p_func_code
         AND tl.task_status = '0'
       ORDER BY tl.create_time DESC;
    
      IF v_count > 0 THEN
        SELECT t.task_detail_id
          INTO v_task_detail_id
          FROM (SELECT tl.task_detail_id,
                       tl.task_code
                  FROM bpluser.bpl_log_pub_task tl
                  JOIN bpluser.bpl_qrtz_conf_task_detail td
                    ON tl.task_detail_id = td.task_detail_id
                 WHERE tl.entity_id = p_entity_id
                   AND tl.year_month = p_year_month
                   AND tl.task_code = p_task_code
                   AND tl.task_mode = p_task_mode
                   AND td.func_code = p_func_code
                   AND tl.task_status = '0'
                 ORDER BY tl.create_time DESC) t
         WHERE rownum = 1;
      END IF;
    
      --功能任务开始执行，更新0-待执行的功能日志执行状态为1-执行中、开始时间为当前时间、更新时间
      IF p_task_status = '1' THEN
        UPDATE bpluser.bpl_log_pub_task
           SET start_time  = localtimestamp,
               task_status = p_task_status,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = p_task_code
           AND task_code = p_task_mode
           AND task_detail_id = v_task_detail_id
           AND task_status = '0';
      
        --功能任务执行成功，更新1-执行中的功能任务的执行状态为2-成功、结束时间为当前时间、更新时间
      ELSIF p_task_status = '2' THEN
        SELECT COUNT(1)
          INTO v_count
          FROM bpluser.bpl_log_pub_task tl
          JOIN bpluser.bpl_qrtz_conf_task_detail td
            ON tl.task_detail_id = td.task_detail_id
         WHERE tl.entity_id = p_entity_id
           AND tl.year_month = p_year_month
           AND tl.task_code = p_task_code
           AND tl.task_mode = p_task_mode
           AND td.func_code = p_func_code
           AND tl.task_status = '1'
         ORDER BY tl.create_time DESC;
      
        IF v_count > 0 THEN
          SELECT t.task_detail_id
            INTO v_task_detail_id
            FROM (SELECT tl.task_detail_id,
                         tl.task_code
                    FROM bpluser.bpl_log_pub_task tl
                    JOIN bpluser.bpl_qrtz_conf_task_detail td
                      ON tl.task_detail_id = td.task_detail_id
                   WHERE tl.entity_id = p_entity_id
                     AND tl.year_month = p_year_month
                     AND tl.task_code = p_task_code
                     AND tl.task_mode = p_task_mode
                     AND td.func_code = p_func_code
                     AND tl.task_status = '1'
                   ORDER BY tl.create_time DESC) t
           WHERE rownum = 1;
        END IF;
      
        UPDATE bpluser.bpl_log_pub_task
           SET end_time    = localtimestamp,
               task_status = p_task_status,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = p_task_code
           AND task_code = p_task_mode
           AND task_detail_id = v_task_detail_id
           AND task_status = '1';
      
        --功能任务执行失败[执行功能DB异常]，更新1-执行中的功能任务的执行状态为3-失败、结束时间为当前时间、更新时间、异常发生时间
      ELSIF p_task_status = '3' THEN
        SELECT COUNT(1)
          INTO v_count
          FROM bpluser.bpl_log_pub_task tl
          JOIN bpluser.bpl_qrtz_conf_task_detail td
            ON tl.task_detail_id = td.task_detail_id
         WHERE tl.entity_id = p_entity_id
           AND tl.year_month = p_year_month
           AND tl.task_code = p_task_code
           AND tl.task_mode = p_task_mode
           AND td.func_code = p_func_code
           AND tl.task_status = '1'
         ORDER BY tl.create_time DESC;
      
        IF v_count > 0 THEN
          SELECT t.task_detail_id
            INTO v_task_detail_id
            FROM (SELECT tl.task_detail_id,
                         tl.task_code
                    FROM bpluser.bpl_log_pub_task tl
                    JOIN bpluser.bpl_qrtz_conf_task_detail td
                      ON tl.task_detail_id = td.task_detail_id
                   WHERE tl.entity_id = p_entity_id
                     AND tl.year_month = p_year_month
                     AND tl.task_code = p_task_code
                     AND tl.task_mode = p_task_mode
                     AND td.func_code = p_func_code
                     AND tl.task_status = '1'
                   ORDER BY tl.create_time DESC) t
           WHERE rownum = 1;
        END IF;
      
        UPDATE bpluser.bpl_log_pub_task
           SET end_time    = localtimestamp,
               error_time  = localtimestamp,
               task_status = p_task_status,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = p_task_code
           AND task_code = p_task_mode
           AND task_detail_id = v_task_detail_id
           AND task_status = '1';
      
        -- 获取当前功能的优先级序号，更新有依赖关系的功能日志状态，此时其它功能0-待执行
        SELECT priority_no INTO v_priority_no FROM bpluser.bpl_qrtz_conf_task_detail WHERE task_detail_id = v_task_detail_id;
      
        -- 获取当前功能所属功能组下的其它0-待执行功能任务中有依赖关系，且优先级序号低于(序号越大优先级越低)当前的功能
        FOR rec_detail IN (SELECT tl.task_detail_id
                             FROM bpluser.bpl_log_pub_task tl
                             LEFT JOIN bpluser.bpl_qrtz_conf_task_detail td
                               ON tl.task_detail_id = td.task_detail_id
                             LEFT JOIN bpluser.bpl_qrtz_conf_task ct
                               ON ct.conf_task_id = td.conf_task_id
                            WHERE tl.task_code = p_task_code
                              AND tl.task_status = '0'
                              AND td.relation_task_dtl_id IS NOT NULL
                              AND td.priority_no > v_priority_no
                            ORDER BY td.priority_no) LOOP
        
          -- 先将当前功能ID置为被依赖功能对象，查询依赖它的功能
          SELECT COUNT(1) INTO v_rel_dtl_count FROM bpluser.bpl_qrtz_conf_task_detail WHERE relation_task_dtl_id = v_task_detail_id;
        
          -- 统计依赖它功能的功能数量
          IF v_rel_dtl_count > 0 THEN
            -- 若存在依赖它的功能，则修改其依赖它的功能的执行状态为：4-不执行
            UPDATE bpluser.bpl_log_pub_task
               SET task_status = '4',
                   update_time = localtimestamp
             WHERE entity_id = p_entity_id
               AND year_month = p_year_month
               AND task_code = p_task_code
               AND task_code = p_task_mode
               AND task_detail_id = rec_detail.task_detail_id
               AND task_status = '0';
          
            v_task_detail_id := rec_detail.task_detail_id;
          END IF;
        END LOOP;
      
        --功能任务不符合执行条件[存在已经执行的功能任务时，不允许再次执行]，更新执行状态为4-不执行、更新时间
      ELSIF p_task_status = '4' THEN
        UPDATE bpluser.bpl_log_pub_task
           SET task_status = p_task_status,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = p_task_code
           AND task_code = p_task_mode
           AND task_detail_id = v_task_detail_id;
      
        -- 获取当前功能的优先级序号，更新有依赖关系的功能日志状态，此时其它功能0-待执行
        SELECT priority_no INTO v_priority_no FROM bpluser.bpl_qrtz_conf_task_detail WHERE task_detail_id = v_task_detail_id;
      
        -- 获取当前功能所属功能组下的其它0-待执行功能任务中有依赖关系，且优先级序号低于(序号越大优先级越低)当前的功能
        FOR rec_detail IN (SELECT tl.task_detail_id
                             FROM bpluser.bpl_log_pub_task tl
                             LEFT JOIN bpluser.bpl_qrtz_conf_task_detail td
                               ON tl.task_detail_id = td.task_detail_id
                             LEFT JOIN bpluser.bpl_qrtz_conf_task ct
                               ON ct.conf_task_id = td.conf_task_id
                            WHERE tl.task_code = p_task_code
                              AND tl.task_status = '0'
                              AND td.relation_task_dtl_id IS NOT NULL
                              AND td.priority_no > v_priority_no
                            ORDER BY td.priority_no) LOOP
        
          -- 先将当前功能ID置为被依赖功能对象，查询依赖它的功能
          SELECT COUNT(1) INTO v_rel_dtl_count FROM bpluser.bpl_qrtz_conf_task_detail WHERE relation_task_dtl_id = v_task_detail_id;
        
          -- 统计依赖它功能的功能数量
          IF v_rel_dtl_count > 0 THEN
            -- 若存在依赖它的功能，则修改其依赖它的功能的执行状态为：4-不执行
            UPDATE bpluser.bpl_log_pub_task
               SET task_status = '4',
                   update_time = localtimestamp
             WHERE entity_id = p_entity_id
               AND year_month = p_year_month
               AND task_code = p_task_code
               AND task_code = p_task_mode
               AND task_detail_id = rec_detail.task_detail_id
               AND task_status = '0';
          
            v_task_detail_id := rec_detail.task_detail_id;
          END IF;
        END LOOP;
      END IF;
    
      --会计引擎、报表管理的业务期间含账套字段
    ELSE
      -- 获取功能任务日志执行状态为0-待执行，按任务日志创建时间倒序排序的最新功能任务日志
      SELECT COUNT(1)
        INTO v_count
        FROM bpluser.bpl_log_pub_task tl
        JOIN bpluser.bpl_qrtz_conf_task_detail td
          ON tl.task_detail_id = td.task_detail_id
       WHERE tl.entity_id = p_entity_id
         AND tl.year_month = p_year_month
         AND tl.task_code = p_task_code
         AND tl.task_mode = p_task_mode
         AND td.func_code = p_func_code
         AND tl.book_code = p_book_code --有账套
         AND tl.task_status = '0'
       ORDER BY tl.create_time DESC;
    
      IF v_count > 0 THEN
        SELECT t.task_detail_id
          INTO v_task_detail_id
          FROM (SELECT tl.task_detail_id,
                       tl.task_code
                  FROM bpluser.bpl_log_pub_task tl
                  JOIN bpluser.bpl_qrtz_conf_task_detail td
                    ON tl.task_detail_id = td.task_detail_id
                 WHERE tl.entity_id = p_entity_id
                   AND tl.year_month = p_year_month
                   AND tl.task_code = p_task_code
                   AND tl.task_mode = p_task_mode
                   AND td.func_code = p_func_code
                   AND tl.book_code = p_book_code --有账套
                   AND tl.task_status = '0'
                 ORDER BY tl.create_time DESC) t
         WHERE rownum = 1;
      END IF;
    
      --功能任务开始执行，更新0-待执行的功能日志执行状态为1-执行中、开始时间为当前时间、更新时间
      IF p_task_status = '1' THEN
        --修改任务状态 执行中，开始时间为当前时间
        UPDATE bpluser.bpl_log_pub_task
           SET start_time  = localtimestamp,
               task_status = p_task_status,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = p_task_code
           AND task_code = p_task_mode
           AND book_code = p_book_code --有账套
           AND task_detail_id = v_task_detail_id
           AND task_status = '0';
      
        --功能任务执行成功，更新1-执行中的功能任务的执行状态为2-成功、结束时间为当前时间、更新时间
      ELSIF p_task_status = '2' THEN
        SELECT COUNT(1)
          INTO v_count
          FROM bpluser.bpl_log_pub_task tl
          JOIN bpluser.bpl_qrtz_conf_task_detail td
            ON tl.task_detail_id = td.task_detail_id
         WHERE tl.entity_id = p_entity_id
           AND tl.year_month = p_year_month
           AND tl.task_code = p_task_code
           AND tl.task_mode = p_task_mode
           AND td.func_code = p_func_code
           AND tl.book_code = p_book_code --有账套
           AND tl.task_status = '1'
         ORDER BY tl.create_time DESC;
      
        IF v_count > 0 THEN
          SELECT t.task_detail_id
            INTO v_task_detail_id
            FROM (SELECT tl.task_detail_id,
                         tl.task_code
                    FROM bpluser.bpl_log_pub_task tl
                    JOIN bpluser.bpl_qrtz_conf_task_detail td
                      ON tl.task_detail_id = td.task_detail_id
                   WHERE tl.entity_id = p_entity_id
                     AND tl.year_month = p_year_month
                     AND tl.task_code = p_task_code
                     AND tl.task_mode = p_task_mode
                     AND td.func_code = p_func_code
                     AND tl.book_code = p_book_code --有账套
                     AND tl.task_status = '1'
                   ORDER BY tl.create_time DESC) t
           WHERE rownum = 1;
        END IF;
      
        UPDATE bpluser.bpl_log_pub_task
           SET end_time    = localtimestamp,
               task_status = p_task_status,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = p_task_code
           AND task_code = p_task_mode
           AND book_code = p_book_code --有账套
           AND task_detail_id = v_task_detail_id
           AND task_status = '1';
      
        --功能任务执行失败[执行功能DB异常]，更新1-执行中的功能任务的执行状态为3-失败、结束时间为当前时间、更新时间、异常发生时间
      ELSIF p_task_status = '3' THEN
      
        SELECT COUNT(1)
          INTO v_count
          FROM bpluser.bpl_log_pub_task tl
          JOIN bpluser.bpl_qrtz_conf_task_detail td
            ON tl.task_detail_id = td.task_detail_id
         WHERE tl.entity_id = p_entity_id
           AND tl.year_month = p_year_month
           AND tl.task_code = p_task_code
           AND tl.task_mode = p_task_mode
           AND td.func_code = p_func_code
           AND tl.book_code = p_book_code --有账套
           AND tl.task_status = '1'
         ORDER BY tl.create_time DESC;
      
        IF v_count > 0 THEN
          SELECT t.task_detail_id
            INTO v_task_detail_id
            FROM (SELECT tl.task_detail_id,
                         tl.task_code
                    FROM bpluser.bpl_log_pub_task tl
                    JOIN bpluser.bpl_qrtz_conf_task_detail td
                      ON tl.task_detail_id = td.task_detail_id
                   WHERE tl.entity_id = p_entity_id
                     AND tl.year_month = p_year_month
                     AND tl.task_code = p_task_code
                     AND tl.task_mode = p_task_mode
                     AND td.func_code = p_func_code
                     AND tl.book_code = p_book_code --有账套
                     AND tl.task_status = '1'
                   ORDER BY tl.create_time DESC) t
           WHERE rownum = 1;
        END IF;
      
        UPDATE bpluser.bpl_log_pub_task
           SET end_time    = localtimestamp,
               error_time  = localtimestamp,
               task_status = p_task_status,
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = p_task_code
           AND task_code = p_task_mode
           AND book_code = p_book_code --有账套
           AND task_detail_id = v_task_detail_id
           AND task_status = '1';
      
        -- 获取当前功能的优先级序号，更新有依赖关系的功能日志状态，此时其它功能0-待执行
        SELECT priority_no INTO v_priority_no FROM bpluser.bpl_qrtz_conf_task_detail WHERE task_detail_id = v_task_detail_id;
        -- 获取当前功能所属功能组下的其它0-待执行功能任务中有依赖关系，且优先级序号低于(序号越大优先级越低)当前的功能
        FOR rec_detail IN (SELECT tl.task_detail_id
                             FROM bpluser.bpl_log_pub_task tl
                             LEFT JOIN bpluser.bpl_qrtz_conf_task_detail td
                               ON tl.task_detail_id = td.task_detail_id
                             LEFT JOIN bpluser.bpl_qrtz_conf_task ct
                               ON ct.conf_task_id = td.conf_task_id
                            WHERE tl.task_code = p_task_code
                              AND tl.task_status = '0'
                              AND td.relation_task_dtl_id IS NOT NULL
                              AND td.priority_no > v_priority_no
                            ORDER BY td.priority_no) LOOP
        
          -- 先将当前功能ID置为被依赖功能对象，查询依赖它的功能
          SELECT COUNT(1) INTO v_rel_dtl_count FROM bpluser.bpl_qrtz_conf_task_detail WHERE relation_task_dtl_id = v_task_detail_id;
        
          -- 统计依赖它功能的功能数量
          IF v_rel_dtl_count > 0 THEN
            -- 若存在依赖它的功能，则修改其依赖它的功能的执行状态为：4-不执行
            UPDATE bpluser.bpl_log_pub_task
               SET task_status = '4',
                   update_time = localtimestamp
             WHERE entity_id = p_entity_id
               AND year_month = p_year_month
               AND task_code = p_task_code
               AND task_code = p_task_mode
               AND book_code = p_book_code --有账套
               AND task_detail_id = rec_detail.task_detail_id
               AND task_status = '0';
          
            v_task_detail_id := rec_detail.task_detail_id;
          END IF;
        END LOOP;
      
        --功能任务不符合执行条件[存在已经执行的功能任务时，不允许再次执行]，更新执行状态为4-不执行、更新时间
      ELSIF p_task_status = '4' THEN
        UPDATE bpluser.bpl_log_pub_task
           SET task_status = '4',
               update_time = localtimestamp
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND task_code = p_task_code
           AND task_code = p_task_mode
           AND book_code = p_book_code --有账套
           AND task_detail_id = v_task_detail_id;
      
        -- 获取当前功能的优先级序号，更新有依赖关系的功能日志状态，此时其它功能0-待执行
        SELECT priority_no INTO v_priority_no FROM bpluser.bpl_qrtz_conf_task_detail WHERE task_detail_id = v_task_detail_id;
      
        -- 获取当前功能所属功能组下的其它0-待执行功能任务中有依赖关系，且优先级序号低于(序号越大优先级越低)当前的功能
        FOR rec_detail IN (SELECT tl.task_detail_id
                             FROM bpluser.bpl_log_pub_task tl
                             LEFT JOIN bpluser.bpl_qrtz_conf_task_detail td
                               ON tl.task_detail_id = td.task_detail_id
                             LEFT JOIN bpluser.bpl_qrtz_conf_task ct
                               ON ct.conf_task_id = td.conf_task_id
                            WHERE tl.task_code = p_task_code
                              AND tl.task_status = '0'
                              AND td.relation_task_dtl_id IS NOT NULL
                              AND td.priority_no > v_priority_no
                            ORDER BY td.priority_no) LOOP
        
          -- 先将当前功能ID置为被依赖功能对象，查询依赖它的功能
          SELECT COUNT(1) INTO v_rel_dtl_count FROM bpluser.bpl_qrtz_conf_task_detail WHERE relation_task_dtl_id = v_task_detail_id;
        
          -- 统计依赖它功能的功能数量
          IF v_rel_dtl_count > 0 THEN
            -- 若存在依赖它的功能，则修改其依赖它的功能的执行状态为：4-不执行
            UPDATE bpluser.bpl_log_pub_task
               SET task_status = '4',
                   update_time = localtimestamp
             WHERE entity_id = p_entity_id
               AND year_month = p_year_month
               AND task_code = p_task_code
               AND task_code = p_task_mode
               AND book_code = p_book_code --有账套
               AND task_detail_id = rec_detail.task_detail_id
               AND task_status = '0';
          
            v_task_detail_id := rec_detail.task_detail_id;
          END IF;
        END LOOP;
      END IF;
    
    END IF;
  
    -- 提交事务
    COMMIT;
  
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(to_char(SQLCODE) || '::' || substr(SQLERRM, 1, 200));
  END proc_update_task_log;

END bpl_pack_trace_job;
/

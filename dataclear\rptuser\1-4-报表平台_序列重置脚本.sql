--删除序列
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_BUSS_RPT');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_BUSS_RPTHIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_BUSS_RPT_ITEM_DATA');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_BUSS_RPT_ITEM_DATAHIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CODE_CONFIG');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_BUSSPRD');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_BUSSPRD_DTL');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_CODE');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_CODE_HIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_ARTICLE');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_ARTICLEHIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_SUB');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_SUB_HIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_ITEM');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_ITEMHIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_ITEM_RULE');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_ITEM_RULEHIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_TEMPLATE');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_TEMPLATEHIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DAP_ARTICLE_BLC');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DAP_ATC_BLC_HIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DAP_LEDGER_BLC');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DAP_LED_BLC_HIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DUCT_RPT_ITEM_DATA');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DUCT_RPT_ITEM_DATAHIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DUCT_RPT_ITEM_DATA_DTL');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DUCT_RPT_TEMPLATE');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DUCT_RPT_TEMPLATEHIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DUCT_TEMP_DETAIL');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_DUCT_TEMP_DETAILHIS');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_LOG_RPT_ITEM_DRAW_TASK');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_buss_report_qtc_condition');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_fixed_field');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_fixed_fieldhis');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_qtc_model');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_qtc_modelhis');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_dap_qtc_evaluate');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_dap_qtc_evaluatehis');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_BUSS_TRIAL_REPORT_RECORD');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_buss_repot_task');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_buss_repot_task_condition');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_buss_repot_task_item');
call rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_buss_repot_task_template');


--创建序列
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_BUSS_RPT');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_BUSS_RPTHIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_BUSS_RPT_ITEM_DATA');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_BUSS_RPT_ITEM_DATAHIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_BUSSPRD');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_BUSSPRD_DTL');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_CODE');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_CODE_HIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_ARTICLE');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_ARTICLEHIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_SUB');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_SUB_HIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_RPT_ITEM');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_RPT_ITEMHIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_RPT_ITEM_RULE');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_RPT_ITEM_RULEHIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_RPT_TEMPLATE');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_CONF_RPT_TEMPLATEHIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DAP_ARTICLE_BLC');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DAP_ATC_BLC_HIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DAP_LEDGER_BLC');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DAP_LED_BLC_HIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DUCT_RPT_ITEM_DATA');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DUCT_RPT_ITEM_DATAHIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DUCT_RPT_ITEM_DATA_DTL');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DUCT_RPT_TEMPLATE');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DUCT_RPT_TEMPLATEHIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DUCT_TEMP_DETAIL');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_DUCT_TEMP_DETAILHIS');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('RPT_SEQ_LOG_RPT_ITEM_DRAW_TASK');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_buss_report_qtc_condition');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_conf_item_rule_fixed_field');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_conf_item_rule_fixed_fieldhis');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_conf_item_rule_qtc_model');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_conf_item_rule_qtc_modelhis');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_dap_qtc_evaluate');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_dap_qtc_evaluatehis');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_BUSS_TRIAL_REPORT_RECORD');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_buss_repot_task');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_buss_repot_task_condition');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_buss_repot_task_item');
call rptuser.rpt_pack_commonutils.ADD_SEQUENCE('rpt_seq_buss_repot_task_template');


--配置类序列需要根据当前id作为初始值重新创建
/
DECLARE v_count NUMBER(15);
BEGIN
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_BUSSPRD');
  SELECT NVL(max(BUSS_PERIOD_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_BUSSPERIOD;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_BUSSPRD
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';


    rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_BUSSPRD_DTL');
  SELECT NVL(max(PERIOD_DETAIL_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_BUSSPERIOD_DETAIL;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_BUSSPRD_DTL
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1'; 


    rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_CODE');
  SELECT NVL(max(CODE_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_CODE;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_CODE
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';

  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_CODE_HIS');
  SELECT NVL(max(CODE_HIS_ID),0)+1 INTO v_count FROM rptuser.rpt_conf_code_his;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_CODE_HIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';

  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_ARTICLE');
  SELECT NVL(max(RULE_ARTICLE_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_ITEM_RULE_ARTICLE;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_ITEM_RULE_ARTICLE
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_ARTICLEHIS');
  SELECT NVL(max(RULE_ARTICLE_HIS_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_ITEM_RULE_ARTICLEHIS;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_ITEM_RULE_ARTICLEHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';


  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_SUB');
  SELECT NVL(max(REPORT_ITEM_RULE_TERNARY_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_ITEM_RULE_SUB;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_ITEM_RULE_SUB
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_ITEM_RULE_SUB_HIS');
  SELECT NVL(max(REPORT_ITEM_RULE_SUB_HIS_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_ITEM_RULE_SUB_HIS;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_ITEM_RULE_SUB_HIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';


  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_ITEM');
  SELECT NVL(max(REPORT_ITEM_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_REPORT_ITEM;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_RPT_ITEM
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';

  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_ITEMHIS');
  SELECT NVL(max(REPORT_ITEM_HIS_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_REPORT_ITEMHIS;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_RPT_ITEMHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';

  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_ITEM_RULE');
  SELECT NVL(max(REPORT_ITEM_RULE_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_REPORT_ITEM_RULE;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_RPT_ITEM_RULE
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_ITEM_RULEHIS');
  SELECT NVL(max(REPORT_ITEM_RULE_HIS_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_REPORT_ITEM_RULEHIS;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_RPT_ITEM_RULEHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';


  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_TEMPLATE');
  SELECT NVL(max(REPORT_TEMPLATE_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_REPORT_TEMPLATE;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_RPT_TEMPLATE
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('RPT_SEQ_CONF_RPT_TEMPLATEHIS');
  SELECT NVL(max(REPORT_TEMPLATE_HIS_ID),0)+1 INTO v_count FROM rptuser.RPT_CONF_REPORT_TEMPLATEHIS;
  EXECUTE IMMEDIATE 'create sequence RPT_SEQ_CONF_RPT_TEMPLATEHIS
              minvalue 1
              maxvalue 9999999999999999999999999999
              start with '||v_count||'
              increment by 1';

  -- RPT_CONF_ITEM_RULE_QTC_MODEL
  select NVL(max(RULE_QTC_ID) ,0)+1
  into v_count
  from RPT_CONF_ITEM_RULE_QTC_MODEL;
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_qtc_model');

  EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_item_rule_qtc_model
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_count || '
         increment by 1
         ';

  -- RPT_CONF_ITEM_RULE_QTC_MODELHIS
  select NVL(max(RULE_QTC_HIS_ID) ,0)+1
  into v_count
  from RPT_CONF_ITEM_RULE_QTC_MODELHIS;
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_qtc_modelhis');

  EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_item_rule_qtc_modelhis
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_count || '
         increment by 1
         ';

  -- RPT_CONF_ITEM_RULE_FIXED_FIELD
  select COALESCE(max(RULE_FIXED_FIELD_ID) ,0)+1
  into v_count
  from RPT_CONF_ITEM_RULE_FIXED_FIELD;
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_fixed_field');

  EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_item_rule_fixed_field
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_count || '
         increment by 1
         ';

  -- RPT_CONF_ITEM_RULE_FIXED_FIELDHIS
  select COALESCE(max(RULE_FIXED_FIELD_HIS_ID)  ,0)+1
  into v_count
  from RPT_CONF_ITEM_RULE_FIXED_FIELDHIS;
  rptuser.rpt_pack_commonutils.DROP_SEQUENCE('rpt_seq_conf_item_rule_fixed_fieldhis');

  EXECUTE IMMEDIATE  'create sequence rpt_seq_conf_item_rule_fixed_fieldhis
         minvalue 1
         maxvalue 999999999999999999999999999
         start with ' || v_count || '
         increment by 1
         ';

end ;
/
-- 快速数据差异检查SQL
-- 新版本 action_no: 20250729_124218_DC1XT (优化后)
-- 旧版本 action_no: 20250729_095134_LS6VB (优化前)

-- =====================================================
-- 1. 快速总览 - 记录数量对比
-- =====================================================
SELECT 
    '记录数量总览' as check_type,
    table_name,
    old_count,
    new_count,
    (new_count - old_count) as diff_count,
    CASE 
        WHEN old_count = new_count THEN '✓ 一致'
        ELSE '✗ 不一致'
    END as status
FROM (
    SELECT '6维度主表(atr_buss_to_lrc_t_ul_r)' as table_name,
           (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul_r WHERE action_no = '20250729_095134_LS6VB') as old_count,
           (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul_r WHERE action_no = '20250729_124218_DC1XT') as new_count
    UNION ALL
    SELECT '4维度主表(atr_buss_to_lrc_t_ul)' as table_name,
           (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul WHERE action_no = '20250729_095134_LS6VB') as old_count,
           (SELECT COUNT(*) FROM atr_buss_to_lrc_t_ul WHERE action_no = '20250729_124218_DC1XT') as new_count
    UNION ALL
    SELECT 'ICG表(atr_buss_to_lrc_g)' as table_name,
           (SELECT COUNT(*) FROM atr_buss_to_lrc_g WHERE action_no = '20250729_095134_LS6VB') as old_count,
           (SELECT COUNT(*) FROM atr_buss_to_lrc_g WHERE action_no = '20250729_124218_DC1XT') as new_count
) t
ORDER BY table_name;

-- =====================================================
-- 2. 快速总览 - 关键金额对比
-- =====================================================
SELECT 
    '关键金额总览' as check_type,
    metric_name,
    ROUND(old_amount, 2) as old_amount,
    ROUND(new_amount, 2) as new_amount,
    ROUND((new_amount - old_amount), 2) as diff_amount,
    CASE 
        WHEN ABS(new_amount - old_amount) < 0.01 THEN '✓ 一致'
        ELSE '✗ 差异: ' || ROUND(((new_amount - old_amount) * 100.0 / NULLIF(old_amount, 0)), 4) || '%'
    END as status
FROM (
    SELECT '6维度表-总保费' as metric_name,
           (SELECT COALESCE(SUM(premium), 0) FROM atr_buss_to_lrc_t_ul_r WHERE action_no = '20250729_095134_LS6VB') as old_amount,
           (SELECT COALESCE(SUM(premium), 0) FROM atr_buss_to_lrc_t_ul_r WHERE action_no = '20250729_124218_DC1XT') as new_amount
    UNION ALL
    SELECT '6维度表-总手续费' as metric_name,
           (SELECT COALESCE(SUM(net_fee), 0) FROM atr_buss_to_lrc_t_ul_r WHERE action_no = '20250729_095134_LS6VB') as old_amount,
           (SELECT COALESCE(SUM(net_fee), 0) FROM atr_buss_to_lrc_t_ul_r WHERE action_no = '20250729_124218_DC1XT') as new_amount
    UNION ALL
    SELECT '4维度表-总保费' as metric_name,
           (SELECT COALESCE(SUM(total_premium), 0) FROM atr_buss_to_lrc_t_ul WHERE action_no = '20250729_095134_LS6VB') as old_amount,
           (SELECT COALESCE(SUM(total_premium), 0) FROM atr_buss_to_lrc_t_ul WHERE action_no = '20250729_124218_DC1XT') as new_amount
    UNION ALL
    SELECT 'ICG表-总保费' as metric_name,
           (SELECT COALESCE(SUM(total_premium), 0) FROM atr_buss_to_lrc_g WHERE action_no = '20250729_095134_LS6VB') as old_amount,
           (SELECT COALESCE(SUM(total_premium), 0) FROM atr_buss_to_lrc_g WHERE action_no = '20250729_124218_DC1XT') as new_amount
) t
ORDER BY metric_name;

-- =====================================================
-- 3. 重点检查 - riCedingRate分出比例
-- =====================================================
SELECT 
    'riCedingRate检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN ri_ceding_rate IS NULL THEN 1 END) as null_count,
    COUNT(CASE WHEN ri_ceding_rate = 0 THEN 1 END) as zero_count,
    ROUND(MIN(ri_ceding_rate), 6) as min_rate,
    ROUND(MAX(ri_ceding_rate), 6) as max_rate,
    ROUND(AVG(ri_ceding_rate), 6) as avg_rate
FROM atr_buss_to_lrc_t_ul_r 
WHERE action_no = '20250729_124218_DC1XT'
UNION ALL
SELECT 
    'riCedingRate检查(旧版)' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN ri_ceding_rate IS NULL THEN 1 END) as null_count,
    COUNT(CASE WHEN ri_ceding_rate = 0 THEN 1 END) as zero_count,
    ROUND(MIN(ri_ceding_rate), 6) as min_rate,
    ROUND(MAX(ri_ceding_rate), 6) as max_rate,
    ROUND(AVG(ri_ceding_rate), 6) as avg_rate
FROM atr_buss_to_lrc_t_ul_r 
WHERE action_no = '20250729_095134_LS6VB';

-- =====================================================
-- 4. 差异记录快速定位
-- =====================================================

-- 4.1 找出6维度表中有差异的合约
SELECT 
    '6维度表差异合约' as check_type,
    treaty_no,
    old_count,
    new_count,
    old_premium,
    new_premium,
    ROUND((new_premium - old_premium), 2) as premium_diff
FROM (
    SELECT 
        COALESCE(o.treaty_no, n.treaty_no) as treaty_no,
        COALESCE(o.record_count, 0) as old_count,
        COALESCE(n.record_count, 0) as new_count,
        COALESCE(o.total_premium, 0) as old_premium,
        COALESCE(n.total_premium, 0) as new_premium
    FROM (
        SELECT treaty_no, COUNT(*) as record_count, SUM(premium) as total_premium
        FROM atr_buss_to_lrc_t_ul_r 
        WHERE action_no = '20250729_095134_LS6VB'
        GROUP BY treaty_no
    ) o
    FULL OUTER JOIN (
        SELECT treaty_no, COUNT(*) as record_count, SUM(premium) as total_premium
        FROM atr_buss_to_lrc_t_ul_r 
        WHERE action_no = '20250729_124218_DC1XT'
        GROUP BY treaty_no
    ) n ON o.treaty_no = n.treaty_no
) t
WHERE 
    old_count != new_count 
    OR ABS(new_premium - old_premium) > 0.01
ORDER BY ABS(new_premium - old_premium) DESC
LIMIT 10;

-- 4.2 找出4维度表中有差异的合约
SELECT 
    '4维度表差异合约' as check_type,
    treaty_no,
    old_count,
    new_count,
    old_premium,
    new_premium,
    ROUND((new_premium - old_premium), 2) as premium_diff
FROM (
    SELECT 
        COALESCE(o.treaty_no, n.treaty_no) as treaty_no,
        COALESCE(o.record_count, 0) as old_count,
        COALESCE(n.record_count, 0) as new_count,
        COALESCE(o.total_premium, 0) as old_premium,
        COALESCE(n.total_premium, 0) as new_premium
    FROM (
        SELECT treaty_no, COUNT(*) as record_count, SUM(total_premium) as total_premium
        FROM atr_buss_to_lrc_t_ul 
        WHERE action_no = '20250729_095134_LS6VB'
        GROUP BY treaty_no
    ) o
    FULL OUTER JOIN (
        SELECT treaty_no, COUNT(*) as record_count, SUM(total_premium) as total_premium
        FROM atr_buss_to_lrc_t_ul 
        WHERE action_no = '20250729_124218_DC1XT'
        GROUP BY treaty_no
    ) n ON o.treaty_no = n.treaty_no
) t
WHERE 
    old_count != new_count 
    OR ABS(new_premium - old_premium) > 0.01
ORDER BY ABS(new_premium - old_premium) DESC
LIMIT 10;

-- =====================================================
-- 5. 样本数据检查
-- =====================================================

-- 5.1 随机抽取几条记录进行详细对比
WITH sample_keys AS (
    SELECT treaty_no, policy_no, endorse_seq_no, kind_code, sectiono_code, reinsurer_code
    FROM atr_buss_to_lrc_t_ul_r 
    WHERE action_no = '20250729_124218_DC1XT'
    ORDER BY RANDOM()
    LIMIT 5
)
SELECT 
    '样本记录对比' as check_type,
    s.treaty_no,
    s.policy_no,
    s.kind_code,
    ROUND(o.premium, 4) as old_premium,
    ROUND(n.premium, 4) as new_premium,
    ROUND(o.ri_ceding_rate, 6) as old_ri_ceding_rate,
    ROUND(n.ri_ceding_rate, 6) as new_ri_ceding_rate,
    CASE 
        WHEN ABS(COALESCE(o.premium,0) - COALESCE(n.premium,0)) < 0.0001 
         AND ABS(COALESCE(o.ri_ceding_rate,0) - COALESCE(n.ri_ceding_rate,0)) < 0.000001 
        THEN '✓ 一致' 
        ELSE '✗ 差异' 
    END as status
FROM sample_keys s
LEFT JOIN atr_buss_to_lrc_t_ul_r o ON s.treaty_no = o.treaty_no 
    AND s.policy_no = o.policy_no 
    AND s.endorse_seq_no = o.endorse_seq_no 
    AND s.kind_code = o.kind_code
    AND s.sectiono_code = o.sectiono_code
    AND s.reinsurer_code = o.reinsurer_code
    AND o.action_no = '20250729_095134_LS6VB'
LEFT JOIN atr_buss_to_lrc_t_ul_r n ON s.treaty_no = n.treaty_no 
    AND s.policy_no = n.policy_no 
    AND s.endorse_seq_no = n.endorse_seq_no 
    AND s.kind_code = n.kind_code
    AND s.sectiono_code = n.sectiono_code
    AND s.reinsurer_code = n.reinsurer_code
    AND n.action_no = '20250729_124218_DC1XT'
ORDER BY s.treaty_no, s.policy_no;

--清除所有业务期间
TRUNCATE TABLE dm_conf_bussperiod_detail;
TRUNCATE TABLE dm_conf_bussperiod;

declare 
v_year_month varchar2(50) := '202401';
--11、增加业务详细表数据
begin 
  
  insert into dm_conf_bussperiod (BUSS_PERIOD_ID, entity_id, YEAR_MONTH, EXECUTION_STATE, VALID_IS, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME)
values (DM_SEQ_CONF_BUSSPERIOD.Nextval, 1, v_year_month, '0', '1', 1, sysdate, 0, null);

  for cur in (
    select biz_type_id ,'1' as DIRECTION ,'0' as READY_STATE from dm_conf_table where VALID_IS = '1'
      union all 
        select biz_type_id ,'0' as DIRECTION,'0' as READY_STATE from dm_conf_table_output where VALID_IS = '1'
        order by DIRECTION desc ,biz_type_id
) loop 
  insert into dm_conf_bussperiod_detail (PERIOD_DETAIL_ID, BUSS_PERIOD_ID, BIZ_TYPE_ID, TASK_TIME, EXEC_RESULT, DIRECTION, READY_STATE, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME)
values (DM_SEQ_CONF_BUSSPERIOD_DETAIL.Nextval,(select BUSS_PERIOD_ID from dm_conf_bussperiod where year_month = v_year_month),  cur.biz_type_id, null, null, cur.DIRECTION, cur.READY_STATE, 1, sysdate, null, null);

commit;

  end loop;
  
end;
/
call accuser.acc_pack_commonutils_proc_add_table_column('acc_dap_entry_data','backtracking_method','varchar(10)','追溯方法');

call accuser.acc_pack_commonutils_proc_add_table_column('acc_duct_entry_data_success','backtracking_method','varchar(10)','追溯方法');

call accuser.acc_pack_commonutils_proc_add_table_column('acc_duct_entry_data_all','backtracking_method','varchar(10)','追溯方法');



 DROP TABLE accuser.acc_conf_reclassify_mapping;

CREATE TABLE accuser.acc_conf_reclassify_mapping (
	id int8 NOT NULL,
	dr_transfer_account_code varchar(64) NULL,
	dr_transfer_account_id int8 NULL,
	cr_transfer_account_code varchar(64) NULL,
	cr_transfer_account_id int8 NULL,
	transfer_entry_code bpchar(1) NULL,
	reclassify_account_code varchar(64) NULL,
	reclassify_account_id int8 NULL,
	reclassify_entry_code bpchar(1) NULL,
	CONSTRAINT acc_conf_reclassify_mapping_pk PRIMARY KEY (id)
);
ALTER TABLE acc_conf_reclassify_mapping to accuser;


INSERT INTO accuser.acc_conf_reclassify_mapping
(id, dr_transfer_account_code, dr_transfer_account_id, cr_transfer_account_code, cr_transfer_account_id, transfer_entry_code, reclassify_account_code, reclassify_account_id, reclassify_entry_code)
VALUES(1, '************', 1940417, '************', 1940430, NULL, '************', 1940442, NULL);
INSERT INTO accuser.acc_conf_reclassify_mapping
(id, dr_transfer_account_code, dr_transfer_account_id, cr_transfer_account_code, cr_transfer_account_id, transfer_entry_code, reclassify_account_code, reclassify_account_id, reclassify_entry_code)
VALUES(2, '************', 1940419, '************', 1940432, NULL, '************', 1940443, NULL);
INSERT INTO accuser.acc_conf_reclassify_mapping
(id, dr_transfer_account_code, dr_transfer_account_id, cr_transfer_account_code, cr_transfer_account_id, transfer_entry_code, reclassify_account_code, reclassify_account_id, reclassify_entry_code)
VALUES(3, '************', 1940421, '************', 1940433, NULL, '************', 1940228, NULL);
INSERT INTO accuser.acc_conf_reclassify_mapping
(id, dr_transfer_account_code, dr_transfer_account_id, cr_transfer_account_code, cr_transfer_account_id, transfer_entry_code, reclassify_account_code, reclassify_account_id, reclassify_entry_code)
VALUES(4, '************', 1940423, '************', 1940435, NULL, '************', 1940444, NULL);
INSERT INTO accuser.acc_conf_reclassify_mapping
(id, dr_transfer_account_code, dr_transfer_account_id, cr_transfer_account_code, cr_transfer_account_id, transfer_entry_code, reclassify_account_code, reclassify_account_id, reclassify_entry_code)
VALUES(5, '************', 1940425, '************', 1940437, NULL, '************', 1940445, NULL);
INSERT INTO accuser.acc_conf_reclassify_mapping
(id, dr_transfer_account_code, dr_transfer_account_id, cr_transfer_account_code, cr_transfer_account_id, transfer_entry_code, reclassify_account_code, reclassify_account_id, reclassify_entry_code)
VALUES(6, '************', 1940428, '************', 1940440, NULL, '************', 1940438, NULL);
commit;
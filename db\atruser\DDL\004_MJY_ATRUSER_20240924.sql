----------------------------------------------
----- LRC 计算结果明细(单维度，直保&临分分入)
----------------------------------------------
-- sequence
drop sequence if exists atr_seq_buss_dd_lrc_icu_calc_detail;
create sequence atr_seq_buss_dd_lrc_icu_calc_detail;

-- table
drop table if exists atr_buss_dd_lrc_icu_calc_detail cascade;
create table atr_buss_dd_lrc_icu_calc_detail (
                                                 id bigint not null,
                                                 action_no varchar(32) collate "C" not null,
                                                 main_id bigint not null,
                                                 dev_no smallint,
                                                 after_premium_impairment_rate numeric(10,8),
                                                 recv_premium numeric(32,8),
                                                 adj_commission numeric(32,8),
                                                 brokerage_fee numeric(32,8),
                                                 iacf_fee numeric(32,8),
                                                 ed_premium_backup numeric(32,8),
                                                 ed_premium numeric(32,8),
                                                 maintenance_fee numeric(32,8),
                                                 iacf_fee_non_policy numeric(32,8),
                                                 ue_premium numeric(32,8),
                                                 gep_uwq numeric(32,8),
                                                 ultimate_loss numeric(32,8),
                                                 coverage_amount numeric(32,8)
) partition by hash (action_no);

create table atr_buss_dd_lrc_icu_calc_detail_p_0 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 0);
create table atr_buss_dd_lrc_icu_calc_detail_p_1 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 1);
create table atr_buss_dd_lrc_icu_calc_detail_p_2 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 2);
create table atr_buss_dd_lrc_icu_calc_detail_p_3 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 3);
create table atr_buss_dd_lrc_icu_calc_detail_p_4 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 4);
create table atr_buss_dd_lrc_icu_calc_detail_p_5 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 5);
create table atr_buss_dd_lrc_icu_calc_detail_p_6 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 6);
create table atr_buss_dd_lrc_icu_calc_detail_p_7 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 7);
create table atr_buss_dd_lrc_icu_calc_detail_p_8 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 8);
create table atr_buss_dd_lrc_icu_calc_detail_p_9 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 9);
create table atr_buss_dd_lrc_icu_calc_detail_p_10 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 10);
create table atr_buss_dd_lrc_icu_calc_detail_p_11 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 11);
create table atr_buss_dd_lrc_icu_calc_detail_p_12 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 12);
create table atr_buss_dd_lrc_icu_calc_detail_p_13 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 13);
create table atr_buss_dd_lrc_icu_calc_detail_p_14 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 14);
create table atr_buss_dd_lrc_icu_calc_detail_p_15 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 15);
create table atr_buss_dd_lrc_icu_calc_detail_p_16 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 16);
create table atr_buss_dd_lrc_icu_calc_detail_p_17 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 17);
create table atr_buss_dd_lrc_icu_calc_detail_p_18 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 18);
create table atr_buss_dd_lrc_icu_calc_detail_p_19 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 19);
create table atr_buss_dd_lrc_icu_calc_detail_p_20 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 20);
create table atr_buss_dd_lrc_icu_calc_detail_p_21 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 21);
create table atr_buss_dd_lrc_icu_calc_detail_p_22 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 22);
create table atr_buss_dd_lrc_icu_calc_detail_p_23 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 23);
create table atr_buss_dd_lrc_icu_calc_detail_p_24 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 24);
create table atr_buss_dd_lrc_icu_calc_detail_p_25 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 25);
create table atr_buss_dd_lrc_icu_calc_detail_p_26 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 26);
create table atr_buss_dd_lrc_icu_calc_detail_p_27 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 27);
create table atr_buss_dd_lrc_icu_calc_detail_p_28 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 28);
create table atr_buss_dd_lrc_icu_calc_detail_p_29 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 29);
create table atr_buss_dd_lrc_icu_calc_detail_p_30 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 30);
create table atr_buss_dd_lrc_icu_calc_detail_p_31 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 31);
create table atr_buss_dd_lrc_icu_calc_detail_p_32 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 32);
create table atr_buss_dd_lrc_icu_calc_detail_p_33 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 33);
create table atr_buss_dd_lrc_icu_calc_detail_p_34 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 34);
create table atr_buss_dd_lrc_icu_calc_detail_p_35 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 35);
create table atr_buss_dd_lrc_icu_calc_detail_p_36 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 36);
create table atr_buss_dd_lrc_icu_calc_detail_p_37 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 37);
create table atr_buss_dd_lrc_icu_calc_detail_p_38 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 38);
create table atr_buss_dd_lrc_icu_calc_detail_p_39 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 39);
create table atr_buss_dd_lrc_icu_calc_detail_p_40 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 40);
create table atr_buss_dd_lrc_icu_calc_detail_p_41 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 41);
create table atr_buss_dd_lrc_icu_calc_detail_p_42 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 42);
create table atr_buss_dd_lrc_icu_calc_detail_p_43 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 43);
create table atr_buss_dd_lrc_icu_calc_detail_p_44 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 44);
create table atr_buss_dd_lrc_icu_calc_detail_p_45 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 45);
create table atr_buss_dd_lrc_icu_calc_detail_p_46 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 46);
create table atr_buss_dd_lrc_icu_calc_detail_p_47 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 47);
create table atr_buss_dd_lrc_icu_calc_detail_p_48 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 48);
create table atr_buss_dd_lrc_icu_calc_detail_p_49 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 49);
create table atr_buss_dd_lrc_icu_calc_detail_p_50 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 50);
create table atr_buss_dd_lrc_icu_calc_detail_p_51 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 51);
create table atr_buss_dd_lrc_icu_calc_detail_p_52 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 52);
create table atr_buss_dd_lrc_icu_calc_detail_p_53 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 53);
create table atr_buss_dd_lrc_icu_calc_detail_p_54 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 54);
create table atr_buss_dd_lrc_icu_calc_detail_p_55 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 55);
create table atr_buss_dd_lrc_icu_calc_detail_p_56 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 56);
create table atr_buss_dd_lrc_icu_calc_detail_p_57 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 57);
create table atr_buss_dd_lrc_icu_calc_detail_p_58 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 58);
create table atr_buss_dd_lrc_icu_calc_detail_p_59 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 59);
create table atr_buss_dd_lrc_icu_calc_detail_p_60 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 60);
create table atr_buss_dd_lrc_icu_calc_detail_p_61 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 61);
create table atr_buss_dd_lrc_icu_calc_detail_p_62 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 62);
create table atr_buss_dd_lrc_icu_calc_detail_p_63 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 63);
create table atr_buss_dd_lrc_icu_calc_detail_p_64 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 64);
create table atr_buss_dd_lrc_icu_calc_detail_p_65 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 65);
create table atr_buss_dd_lrc_icu_calc_detail_p_66 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 66);
create table atr_buss_dd_lrc_icu_calc_detail_p_67 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 67);
create table atr_buss_dd_lrc_icu_calc_detail_p_68 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 68);
create table atr_buss_dd_lrc_icu_calc_detail_p_69 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 69);
create table atr_buss_dd_lrc_icu_calc_detail_p_70 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 70);
create table atr_buss_dd_lrc_icu_calc_detail_p_71 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 71);
create table atr_buss_dd_lrc_icu_calc_detail_p_72 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 72);
create table atr_buss_dd_lrc_icu_calc_detail_p_73 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 73);
create table atr_buss_dd_lrc_icu_calc_detail_p_74 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 74);
create table atr_buss_dd_lrc_icu_calc_detail_p_75 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 75);
create table atr_buss_dd_lrc_icu_calc_detail_p_76 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 76);
create table atr_buss_dd_lrc_icu_calc_detail_p_77 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 77);
create table atr_buss_dd_lrc_icu_calc_detail_p_78 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 78);
create table atr_buss_dd_lrc_icu_calc_detail_p_79 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 79);
create table atr_buss_dd_lrc_icu_calc_detail_p_80 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 80);
create table atr_buss_dd_lrc_icu_calc_detail_p_81 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 81);
create table atr_buss_dd_lrc_icu_calc_detail_p_82 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 82);
create table atr_buss_dd_lrc_icu_calc_detail_p_83 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 83);
create table atr_buss_dd_lrc_icu_calc_detail_p_84 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 84);
create table atr_buss_dd_lrc_icu_calc_detail_p_85 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 85);
create table atr_buss_dd_lrc_icu_calc_detail_p_86 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 86);
create table atr_buss_dd_lrc_icu_calc_detail_p_87 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 87);
create table atr_buss_dd_lrc_icu_calc_detail_p_88 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 88);
create table atr_buss_dd_lrc_icu_calc_detail_p_89 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 89);
create table atr_buss_dd_lrc_icu_calc_detail_p_90 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 90);
create table atr_buss_dd_lrc_icu_calc_detail_p_91 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 91);
create table atr_buss_dd_lrc_icu_calc_detail_p_92 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 92);
create table atr_buss_dd_lrc_icu_calc_detail_p_93 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 93);
create table atr_buss_dd_lrc_icu_calc_detail_p_94 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 94);
create table atr_buss_dd_lrc_icu_calc_detail_p_95 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 95);
create table atr_buss_dd_lrc_icu_calc_detail_p_96 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 96);
create table atr_buss_dd_lrc_icu_calc_detail_p_97 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 97);
create table atr_buss_dd_lrc_icu_calc_detail_p_98 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 98);
create table atr_buss_dd_lrc_icu_calc_detail_p_99 partition of atr_buss_dd_lrc_icu_calc_detail
    for values with (modulus 100, remainder 99);

create index idx_atr_buss_dd_lrc_icu_calc_detail_id on atr_buss_dd_lrc_icu_calc_detail(id);
create index idx_atr_buss_dd_lrc_icu_calc_detail_main_id on atr_buss_dd_lrc_icu_calc_detail(main_id);

comment on table atr_buss_dd_lrc_icu_calc_detail
  is 'LRC 计算结果明细(单维度，直保&临分分入)';

comment on column atr_buss_dd_lrc_icu_calc_detail.id
  is 'ID';
comment on column atr_buss_dd_lrc_icu_calc_detail.action_no
  is '执行编号';
comment on column atr_buss_dd_lrc_icu_calc_detail.main_id
  is '结果表的ID';
comment on column atr_buss_dd_lrc_icu_calc_detail.dev_no
  is '发展期';
comment on column atr_buss_dd_lrc_icu_calc_detail.after_premium_impairment_rate
  is '公共项/保费减值后比例';
comment on column atr_buss_dd_lrc_icu_calc_detail.recv_premium
  is '预期应收保费/应收保费';
comment on column atr_buss_dd_lrc_icu_calc_detail.adj_commission
  is '调整手续费/调整手续费';
comment on column atr_buss_dd_lrc_icu_calc_detail.brokerage_fee
  is '经纪人费用/经纪人费用';
comment on column atr_buss_dd_lrc_icu_calc_detail.iacf_fee
  is '获取费用/获取费用';
comment on column atr_buss_dd_lrc_icu_calc_detail.ed_premium_backup
  is 'GEP_Persist 已赚保费/已赚保费 BACKUP';
comment on column atr_buss_dd_lrc_icu_calc_detail.ed_premium
  is 'GEP_Persist 已赚保费/已赚保费';
comment on column atr_buss_dd_lrc_icu_calc_detail.maintenance_fee
  is '维持费用/维持费用';
comment on column atr_buss_dd_lrc_icu_calc_detail.iacf_fee_non_policy
  is '非跟单获取费用';
comment on column atr_buss_dd_lrc_icu_calc_detail.ue_premium
  is '未赚保费/未赚保费';
comment on column atr_buss_dd_lrc_icu_calc_detail.gep_uwq
  is 'Claim_byUWQ_AQ_Vert/GEP';
comment on column atr_buss_dd_lrc_icu_calc_detail.ultimate_loss
  is 'Claim_byUWQ_AQ_Vert/Ultimate Loss';
comment on column atr_buss_dd_lrc_icu_calc_detail.coverage_amount
  is 'Coverage_Unit 保额&限额/保额/限额';




----------------------------------------------
----- LRC 计算结果明细(单维度，临分分出)
----------------------------------------------
-- sequence
drop sequence if exists atr_seq_buss_fo_lrc_icu_calc_detail;
create sequence atr_seq_buss_fo_lrc_icu_calc_detail;

-- table
drop table if exists atr_buss_fo_lrc_icu_calc_detail cascade;
create table atr_buss_fo_lrc_icu_calc_detail (
                                                 id bigint not null,
                                                 action_no varchar(32) collate "C" not null,
                                                 main_id bigint not null,
                                                 dev_no smallint,
                                                 after_premium_impairment_rate numeric(10,8),
                                                 recv_premium numeric(32,8),
                                                 adj_commission numeric(32,8),
                                                 brokerage_fee numeric(32,8),
                                                 iacf_fee numeric(32,8),
                                                 ed_premium_backup numeric(32,8),
                                                 ed_premium numeric(32,8),
                                                 maintenance_fee numeric(32,8),
                                                 iacf_fee_non_policy numeric(32,8),
                                                 ue_premium numeric(32,8),
                                                 gep_uwq numeric(32,8),
                                                 ultimate_loss numeric(32,8),
                                                 coverage_amount numeric(32,8)
) partition by hash (action_no);

create table atr_buss_fo_lrc_icu_calc_detail_p_0 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 0);
create table atr_buss_fo_lrc_icu_calc_detail_p_1 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 1);
create table atr_buss_fo_lrc_icu_calc_detail_p_2 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 2);
create table atr_buss_fo_lrc_icu_calc_detail_p_3 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 3);
create table atr_buss_fo_lrc_icu_calc_detail_p_4 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 4);
create table atr_buss_fo_lrc_icu_calc_detail_p_5 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 5);
create table atr_buss_fo_lrc_icu_calc_detail_p_6 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 6);
create table atr_buss_fo_lrc_icu_calc_detail_p_7 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 7);
create table atr_buss_fo_lrc_icu_calc_detail_p_8 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 8);
create table atr_buss_fo_lrc_icu_calc_detail_p_9 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 9);
create table atr_buss_fo_lrc_icu_calc_detail_p_10 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 10);
create table atr_buss_fo_lrc_icu_calc_detail_p_11 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 11);
create table atr_buss_fo_lrc_icu_calc_detail_p_12 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 12);
create table atr_buss_fo_lrc_icu_calc_detail_p_13 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 13);
create table atr_buss_fo_lrc_icu_calc_detail_p_14 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 14);
create table atr_buss_fo_lrc_icu_calc_detail_p_15 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 15);
create table atr_buss_fo_lrc_icu_calc_detail_p_16 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 16);
create table atr_buss_fo_lrc_icu_calc_detail_p_17 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 17);
create table atr_buss_fo_lrc_icu_calc_detail_p_18 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 18);
create table atr_buss_fo_lrc_icu_calc_detail_p_19 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 19);
create table atr_buss_fo_lrc_icu_calc_detail_p_20 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 20);
create table atr_buss_fo_lrc_icu_calc_detail_p_21 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 21);
create table atr_buss_fo_lrc_icu_calc_detail_p_22 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 22);
create table atr_buss_fo_lrc_icu_calc_detail_p_23 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 23);
create table atr_buss_fo_lrc_icu_calc_detail_p_24 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 24);
create table atr_buss_fo_lrc_icu_calc_detail_p_25 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 25);
create table atr_buss_fo_lrc_icu_calc_detail_p_26 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 26);
create table atr_buss_fo_lrc_icu_calc_detail_p_27 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 27);
create table atr_buss_fo_lrc_icu_calc_detail_p_28 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 28);
create table atr_buss_fo_lrc_icu_calc_detail_p_29 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 29);
create table atr_buss_fo_lrc_icu_calc_detail_p_30 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 30);
create table atr_buss_fo_lrc_icu_calc_detail_p_31 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 31);
create table atr_buss_fo_lrc_icu_calc_detail_p_32 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 32);
create table atr_buss_fo_lrc_icu_calc_detail_p_33 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 33);
create table atr_buss_fo_lrc_icu_calc_detail_p_34 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 34);
create table atr_buss_fo_lrc_icu_calc_detail_p_35 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 35);
create table atr_buss_fo_lrc_icu_calc_detail_p_36 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 36);
create table atr_buss_fo_lrc_icu_calc_detail_p_37 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 37);
create table atr_buss_fo_lrc_icu_calc_detail_p_38 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 38);
create table atr_buss_fo_lrc_icu_calc_detail_p_39 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 39);
create table atr_buss_fo_lrc_icu_calc_detail_p_40 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 40);
create table atr_buss_fo_lrc_icu_calc_detail_p_41 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 41);
create table atr_buss_fo_lrc_icu_calc_detail_p_42 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 42);
create table atr_buss_fo_lrc_icu_calc_detail_p_43 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 43);
create table atr_buss_fo_lrc_icu_calc_detail_p_44 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 44);
create table atr_buss_fo_lrc_icu_calc_detail_p_45 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 45);
create table atr_buss_fo_lrc_icu_calc_detail_p_46 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 46);
create table atr_buss_fo_lrc_icu_calc_detail_p_47 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 47);
create table atr_buss_fo_lrc_icu_calc_detail_p_48 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 48);
create table atr_buss_fo_lrc_icu_calc_detail_p_49 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 49);
create table atr_buss_fo_lrc_icu_calc_detail_p_50 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 50);
create table atr_buss_fo_lrc_icu_calc_detail_p_51 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 51);
create table atr_buss_fo_lrc_icu_calc_detail_p_52 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 52);
create table atr_buss_fo_lrc_icu_calc_detail_p_53 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 53);
create table atr_buss_fo_lrc_icu_calc_detail_p_54 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 54);
create table atr_buss_fo_lrc_icu_calc_detail_p_55 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 55);
create table atr_buss_fo_lrc_icu_calc_detail_p_56 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 56);
create table atr_buss_fo_lrc_icu_calc_detail_p_57 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 57);
create table atr_buss_fo_lrc_icu_calc_detail_p_58 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 58);
create table atr_buss_fo_lrc_icu_calc_detail_p_59 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 59);
create table atr_buss_fo_lrc_icu_calc_detail_p_60 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 60);
create table atr_buss_fo_lrc_icu_calc_detail_p_61 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 61);
create table atr_buss_fo_lrc_icu_calc_detail_p_62 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 62);
create table atr_buss_fo_lrc_icu_calc_detail_p_63 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 63);
create table atr_buss_fo_lrc_icu_calc_detail_p_64 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 64);
create table atr_buss_fo_lrc_icu_calc_detail_p_65 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 65);
create table atr_buss_fo_lrc_icu_calc_detail_p_66 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 66);
create table atr_buss_fo_lrc_icu_calc_detail_p_67 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 67);
create table atr_buss_fo_lrc_icu_calc_detail_p_68 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 68);
create table atr_buss_fo_lrc_icu_calc_detail_p_69 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 69);
create table atr_buss_fo_lrc_icu_calc_detail_p_70 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 70);
create table atr_buss_fo_lrc_icu_calc_detail_p_71 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 71);
create table atr_buss_fo_lrc_icu_calc_detail_p_72 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 72);
create table atr_buss_fo_lrc_icu_calc_detail_p_73 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 73);
create table atr_buss_fo_lrc_icu_calc_detail_p_74 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 74);
create table atr_buss_fo_lrc_icu_calc_detail_p_75 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 75);
create table atr_buss_fo_lrc_icu_calc_detail_p_76 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 76);
create table atr_buss_fo_lrc_icu_calc_detail_p_77 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 77);
create table atr_buss_fo_lrc_icu_calc_detail_p_78 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 78);
create table atr_buss_fo_lrc_icu_calc_detail_p_79 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 79);
create table atr_buss_fo_lrc_icu_calc_detail_p_80 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 80);
create table atr_buss_fo_lrc_icu_calc_detail_p_81 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 81);
create table atr_buss_fo_lrc_icu_calc_detail_p_82 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 82);
create table atr_buss_fo_lrc_icu_calc_detail_p_83 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 83);
create table atr_buss_fo_lrc_icu_calc_detail_p_84 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 84);
create table atr_buss_fo_lrc_icu_calc_detail_p_85 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 85);
create table atr_buss_fo_lrc_icu_calc_detail_p_86 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 86);
create table atr_buss_fo_lrc_icu_calc_detail_p_87 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 87);
create table atr_buss_fo_lrc_icu_calc_detail_p_88 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 88);
create table atr_buss_fo_lrc_icu_calc_detail_p_89 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 89);
create table atr_buss_fo_lrc_icu_calc_detail_p_90 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 90);
create table atr_buss_fo_lrc_icu_calc_detail_p_91 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 91);
create table atr_buss_fo_lrc_icu_calc_detail_p_92 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 92);
create table atr_buss_fo_lrc_icu_calc_detail_p_93 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 93);
create table atr_buss_fo_lrc_icu_calc_detail_p_94 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 94);
create table atr_buss_fo_lrc_icu_calc_detail_p_95 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 95);
create table atr_buss_fo_lrc_icu_calc_detail_p_96 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 96);
create table atr_buss_fo_lrc_icu_calc_detail_p_97 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 97);
create table atr_buss_fo_lrc_icu_calc_detail_p_98 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 98);
create table atr_buss_fo_lrc_icu_calc_detail_p_99 partition of atr_buss_fo_lrc_icu_calc_detail
    for values with (modulus 100, remainder 99);

create index idx_atr_buss_fo_lrc_icu_calc_detail_id on atr_buss_fo_lrc_icu_calc_detail(id);
create index idx_atr_buss_fo_lrc_icu_calc_detail_main_id on atr_buss_fo_lrc_icu_calc_detail(main_id);

comment on table atr_buss_fo_lrc_icu_calc_detail
  is 'LRC 计算结果明细(单维度，临分分出)';

comment on column atr_buss_fo_lrc_icu_calc_detail.id
  is 'ID';
comment on column atr_buss_fo_lrc_icu_calc_detail.action_no
  is '执行编号';
comment on column atr_buss_fo_lrc_icu_calc_detail.main_id
  is '结果表的ID';
comment on column atr_buss_fo_lrc_icu_calc_detail.dev_no
  is '发展期';
comment on column atr_buss_fo_lrc_icu_calc_detail.after_premium_impairment_rate
  is '公共项/保费减值后比例';
comment on column atr_buss_fo_lrc_icu_calc_detail.recv_premium
  is '预期应收保费/应收保费';
comment on column atr_buss_fo_lrc_icu_calc_detail.adj_commission
  is '调整手续费/调整手续费';
comment on column atr_buss_fo_lrc_icu_calc_detail.brokerage_fee
  is '经纪人费用/经纪人费用';
comment on column atr_buss_fo_lrc_icu_calc_detail.iacf_fee
  is '获取费用/获取费用';
comment on column atr_buss_fo_lrc_icu_calc_detail.ed_premium_backup
  is 'GEP_Persist 已赚保费/已赚保费 BACKUP';
comment on column atr_buss_fo_lrc_icu_calc_detail.ed_premium
  is 'GEP_Persist 已赚保费/已赚保费';
comment on column atr_buss_fo_lrc_icu_calc_detail.maintenance_fee
  is '维持费用/维持费用';
comment on column atr_buss_fo_lrc_icu_calc_detail.iacf_fee_non_policy
  is '非跟单获取费用';
comment on column atr_buss_fo_lrc_icu_calc_detail.ue_premium
  is '未赚保费/未赚保费';
comment on column atr_buss_fo_lrc_icu_calc_detail.gep_uwq
  is 'Claim_byUWQ_AQ_Vert/GEP';
comment on column atr_buss_fo_lrc_icu_calc_detail.ultimate_loss
  is 'Claim_byUWQ_AQ_Vert/Ultimate Loss';
comment on column atr_buss_fo_lrc_icu_calc_detail.coverage_amount
  is 'Coverage_Unit 保额&限额/保额/限额';




----------------------------------------------
----- 计量平台合同组接口 (直保&临分分入)
----------------------------------------------
-- sequence
drop sequence if exists atr_seq_buss_dd_lrc_qtc_icg;
create sequence atr_seq_buss_dd_lrc_qtc_icg;

-- table
drop table if exists atr_buss_dd_lrc_qtc_icg cascade;
create table atr_buss_dd_lrc_qtc_icg (
                                         id bigint not null,
                                         action_no varchar(32) collate "C" not null,
                                         portfolio_no varchar(64) collate "C" not null,
                                         icg_no varchar(60) collate "C" not null,
                                         evaluate_approach varchar(60) collate "C" not null,
                                         loa_code varchar(20) collate "C" not null,
                                         contract_year_month varchar(6) collate "C" not null,
                                         dev_no smallint not null,
                                         recv_premium numeric(32,8),
                                         ue_premium numeric(32,8),
                                         ed_premium numeric(32,8),
                                         adj_commission numeric(32,8),
                                         brokerage_fee numeric(32,8),
                                         iacf_fee numeric(32,8),
                                         iacf_fee_non_policy numeric(32,8),
                                         maintenance_fee numeric(32,8)
);


alter table atr_buss_dd_lrc_qtc_icg add constraint pk_atr_buss_dd_lrc_qtc_icg primary key (id);
create index idx_atr_buss_dd_lrc_qtc_icg_action on atr_buss_dd_lrc_qtc_icg(action_no);
create index idx_atr_buss_dd_lrc_qtc_icg_icg on atr_buss_dd_lrc_qtc_icg(icg_no);

comment on table atr_buss_dd_lrc_qtc_icg
  is '计量平台合同组接口 (直保&临分分入)';

comment on column atr_buss_dd_lrc_qtc_icg.id
  is 'ID';
comment on column atr_buss_dd_lrc_qtc_icg.action_no
  is '执行编号';
comment on column atr_buss_dd_lrc_qtc_icg.portfolio_no
  is '合同组合号码';
comment on column atr_buss_dd_lrc_qtc_icg.icg_no
  is '合同组号码';
comment on column atr_buss_dd_lrc_qtc_icg.evaluate_approach
  is '评估方法';
comment on column atr_buss_dd_lrc_qtc_icg.loa_code
  is 'LOA编码';
comment on column atr_buss_dd_lrc_qtc_icg.contract_year_month
  is '合同确认年月';
comment on column atr_buss_dd_lrc_qtc_icg.dev_no
  is '发展期';
comment on column atr_buss_dd_lrc_qtc_icg.recv_premium
  is '预期应收保费/应收保费';
comment on column atr_buss_dd_lrc_qtc_icg.ue_premium
  is '未赚保费/未赚保费';
comment on column atr_buss_dd_lrc_qtc_icg.ed_premium
  is 'GEP_Persist 已赚保费/已赚保费';
comment on column atr_buss_dd_lrc_qtc_icg.adj_commission
  is '调整手续费/调整手续费';
comment on column atr_buss_dd_lrc_qtc_icg.brokerage_fee
  is '经纪人费用/经纪人费用';
comment on column atr_buss_dd_lrc_qtc_icg.iacf_fee
  is '获取费用/获取费用';
comment on column atr_buss_dd_lrc_qtc_icg.iacf_fee_non_policy
  is '非跟单获取费用';
comment on column atr_buss_dd_lrc_qtc_icg.maintenance_fee
  is '维持费用/维持费用';


grant select on atr_buss_dd_lrc_qtc_icg to qtcuser;


----------------------------------------------
----- 计量平台合同组接口 (临分分出)
----------------------------------------------
-- sequence
drop sequence if exists atr_seq_buss_fo_lrc_qtc_icg;
create sequence atr_seq_buss_fo_lrc_qtc_icg;

-- table
drop table if exists atr_buss_fo_lrc_qtc_icg cascade;
create table atr_buss_fo_lrc_qtc_icg (
                                         id bigint not null,
                                         action_no varchar(32) collate "C" not null,
                                         portfolio_no varchar(64) collate "C" not null,
                                         icg_no varchar(60) collate "C" not null,
                                         evaluate_approach varchar(60) collate "C" not null,
                                         loa_code varchar(20) collate "C" not null,
                                         contract_year_month varchar(6) collate "C" not null,
                                         dev_no smallint not null,
                                         recv_premium numeric(32,8),
                                         ue_premium numeric(32,8),
                                         ed_premium numeric(32,8),
                                         adj_commission numeric(32,8),
                                         brokerage_fee numeric(32,8),
                                         iacf_fee numeric(32,8),
                                         iacf_fee_non_policy numeric(32,8),
                                         maintenance_fee numeric(32,8)
);


alter table atr_buss_fo_lrc_qtc_icg add constraint pk_atr_buss_fo_lrc_qtc_icg primary key (id);
create index idx_atr_buss_fo_lrc_qtc_icg_action on atr_buss_fo_lrc_qtc_icg(action_no);
create index idx_atr_buss_fo_lrc_qtc_icg_icg on atr_buss_fo_lrc_qtc_icg(icg_no);

comment on table atr_buss_fo_lrc_qtc_icg
  is '计量平台合同组接口 (临分分出)';

comment on column atr_buss_fo_lrc_qtc_icg.id
  is 'ID';
comment on column atr_buss_fo_lrc_qtc_icg.action_no
  is '执行编号';
comment on column atr_buss_fo_lrc_qtc_icg.portfolio_no
  is '合同组合号码';
comment on column atr_buss_fo_lrc_qtc_icg.icg_no
  is '合同组号码';
comment on column atr_buss_fo_lrc_qtc_icg.evaluate_approach
  is '评估方法';
comment on column atr_buss_fo_lrc_qtc_icg.loa_code
  is 'LOA编码';
comment on column atr_buss_fo_lrc_qtc_icg.contract_year_month
  is '合同确认年月';
comment on column atr_buss_fo_lrc_qtc_icg.dev_no
  is '发展期';
comment on column atr_buss_fo_lrc_qtc_icg.recv_premium
  is '预期应收保费/应收保费';
comment on column atr_buss_fo_lrc_qtc_icg.ue_premium
  is '未赚保费/未赚保费';
comment on column atr_buss_fo_lrc_qtc_icg.ed_premium
  is 'GEP_Persist 已赚保费/已赚保费';
comment on column atr_buss_fo_lrc_qtc_icg.adj_commission
  is '调整手续费/调整手续费';
comment on column atr_buss_fo_lrc_qtc_icg.brokerage_fee
  is '经纪人费用/经纪人费用';
comment on column atr_buss_fo_lrc_qtc_icg.iacf_fee
  is '获取费用/获取费用';
comment on column atr_buss_fo_lrc_qtc_icg.iacf_fee_non_policy
  is '非跟单获取费用';
comment on column atr_buss_fo_lrc_qtc_icg.maintenance_fee
  is '维持费用/维持费用';


grant select on atr_buss_fo_lrc_qtc_icg to qtcuser;

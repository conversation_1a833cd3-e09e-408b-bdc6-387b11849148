<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="VcsDirectoryMappings">
    <mapping directory="$PROJECT_DIR$/dataclear" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/db" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-ifrs-actuarial" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-ifrs-datamgr" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-ifrs-expense" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-ifrs-quantification" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-library-job" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-library-mq" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-library-mybatis" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-library-security" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-library-utils" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-platform-admin" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-platform-base" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-platform-common" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-platform-eureka" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-platform-gateway" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-platform-schedule" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-web-vue" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-web-vue-atr" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-web-vue-dm" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/ss-web-vue-exp" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/templatedoc" vcs="Git" />
  </component>
</project>
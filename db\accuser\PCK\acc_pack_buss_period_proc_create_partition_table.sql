CREATE OR REPLACE PROCEDURE accuser.acc_pack_buss_period_proc_create_partition_table(p_entity_id int8, p_book_code text, p_year_month text)
 AS $BODY$
DECLARE
    rec_proc_id  record;
    v_error_line varchar(4000) := ''; -- 错误行号
BEGIN
    -- entity 分区
    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_dap_entry_data_%s PARTITION OF acc_dap_entry_data FOR VALUES IN
            (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_dap_entry_data_%s to bpluser  $list$,
            p_entity_id);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_entry_data_%s PARTITION OF acc_buss_entry_data FOR VALUES IN (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_buss_entry_data_%s to bpluser  $list$,
            p_entity_id);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_entry_data_detail_%s PARTITION OF acc_buss_entry_data_detail FOR VALUES IN (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_buss_entry_data_detail_%s to bpluser  $list$,
            p_entity_id);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_voucher_%s PARTITION OF acc_buss_voucher FOR VALUES IN (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_buss_voucher_%s to bpluser  $list$,
            p_entity_id);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_voucher_detail_%s PARTITION OF acc_buss_voucher_detail FOR VALUES IN (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_buss_voucher_detail_%s to bpluser  $list$,
            p_entity_id);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_all_%s PARTITION OF acc_duct_entry_data_all FOR VALUES IN (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_duct_entry_data_all_%s to bpluser  $list$,
            p_entity_id);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_success_%s PARTITION OF acc_duct_entry_data_success FOR VALUES IN (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_duct_entry_data_success_%s to bpluser  $list$,
            p_entity_id);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_fail_%s PARTITION OF acc_duct_entry_data_fail FOR VALUES IN (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_duct_entry_data_fail_%s to bpluser  $list$,
            p_entity_id);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_detail_%s PARTITION OF acc_duct_voucher_detail FOR VALUES IN (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_duct_voucher_detail_%s to bpluser  $list$,
            p_entity_id);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_article_%s PARTITION OF acc_duct_voucher_article FOR VALUES IN (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_duct_voucher_article_%s to bpluser  $list$,
            p_entity_id);


    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_amount_%s PARTITION OF acc_duct_voucher_amount FOR VALUES IN (%s) PARTITION BY LIST(year_month) $list$,
            p_entity_id, p_entity_id);
    EXECUTE format(
            $list$ grant select on acc_duct_voucher_amount_%s to bpluser  $list$,
            p_entity_id);


    -- yearMonth 分区
    -- dap没有账套
    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_dap_entry_data_%s_%s PARTITION OF acc_dap_entry_data_%s FOR VALUES IN
            (%s) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_dap_entry_data_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_entry_data_%s_%s PARTITION OF acc_buss_entry_data_%s FOR VALUES IN (%s) PARTITION BY LIST(book_code) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_buss_entry_data_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_entry_data_detail_%s_%s PARTITION OF acc_buss_entry_data_detail_%s FOR VALUES IN (%s) PARTITION BY LIST(book_code) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_buss_entry_data_detail_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_voucher_%s_%s PARTITION OF acc_buss_voucher_%s FOR VALUES IN (%s) PARTITION BY LIST(book_code) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_buss_voucher_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_voucher_detail_%s_%s PARTITION OF acc_buss_voucher_detail_%s FOR VALUES IN (%s) PARTITION BY LIST(book_code) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_buss_voucher_detail_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_all_%s_%s PARTITION OF acc_duct_entry_data_all_%s FOR VALUES IN (%s) PARTITION BY LIST(book_code) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_duct_entry_data_all_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_success_%s_%s PARTITION OF acc_duct_entry_data_success_%s FOR VALUES IN (%s) PARTITION BY LIST(book_code) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_duct_entry_data_success_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_fail_%s_%s PARTITION OF acc_duct_entry_data_fail_%s FOR VALUES IN (%s) PARTITION BY LIST(book_code) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_duct_entry_data_fail_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_detail_%s_%s PARTITION OF acc_duct_voucher_detail_%s FOR VALUES IN (%s) PARTITION BY LIST(book_code) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_duct_voucher_detail_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_article_%s_%s PARTITION OF acc_duct_voucher_article_%s FOR VALUES IN (%s) PARTITION BY LIST(book_code) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_duct_voucher_article_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_amount_%s_%s PARTITION OF acc_duct_voucher_amount_%s FOR VALUES IN (%s) PARTITION BY LIST(book_code) $list$,
            p_entity_id, p_year_month, p_entity_id, p_year_month);
    EXECUTE format(
            $list$ grant select on acc_duct_voucher_amount_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month);


    -- book code 分区
    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_entry_data_%s_%s_%s PARTITION OF acc_buss_entry_data_%s_%s FOR
             VALUES IN (%L) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_book_code, p_entity_id, p_year_month, p_book_code);
    EXECUTE format(
            $list$ grant select on acc_buss_entry_data_%s_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month, p_book_code);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_entry_data_detail_%s_%s_%s PARTITION OF acc_buss_entry_data_detail_%s_%s FOR VALUES IN (%L) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_book_code, p_entity_id, p_year_month, p_book_code);
    EXECUTE format(
            $list$ grant select on acc_buss_entry_data_detail_%s_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month, p_book_code);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_voucher_%s_%s_%s PARTITION OF acc_buss_voucher_%s_%s FOR VALUES IN (%L) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_book_code, p_entity_id, p_year_month, p_book_code);
    EXECUTE format(
            $list$ grant select on acc_buss_voucher_%s_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month, p_book_code);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_buss_voucher_detail_%s_%s_%s PARTITION OF acc_buss_voucher_detail_%s_%s FOR VALUES IN (%L) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_book_code, p_entity_id, p_year_month, p_book_code);
    EXECUTE format(
            $list$ grant select on acc_buss_voucher_detail_%s_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month, p_book_code);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_all_%s_%s_%s PARTITION OF
            acc_duct_entry_data_all_%s_%s FOR VALUES IN (%L) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_book_code, p_entity_id, p_year_month, p_book_code);
    EXECUTE format(
            $list$ grant select on acc_duct_entry_data_all_%s_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month, p_book_code);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_success_%s_%s_%s PARTITION OF acc_duct_entry_data_success_%s_%s FOR VALUES IN (%L) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_book_code, p_entity_id, p_year_month, p_book_code);
    EXECUTE format(
            $list$ grant select on acc_duct_entry_data_success_%s_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month, p_book_code);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_fail_%s_%s_%s PARTITION OF acc_duct_entry_data_fail_%s_%s FOR VALUES IN (%L) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_book_code, p_entity_id, p_year_month, p_book_code);
    EXECUTE format(
            $list$ grant select on acc_duct_entry_data_fail_%s_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month, p_book_code);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_detail_%s_%s_%s PARTITION OF acc_duct_voucher_detail_%s_%s FOR VALUES IN (%L) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_book_code, p_entity_id, p_year_month, p_book_code);
    EXECUTE format(
            $list$ grant select on acc_duct_voucher_detail_%s_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month, p_book_code);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_article_%s_%s_%s PARTITION OF acc_duct_voucher_article_%s_%s FOR VALUES IN (%L) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_book_code, p_entity_id, p_year_month, p_book_code);
    EXECUTE format(
            $list$ grant select on acc_duct_voucher_article_%s_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month, p_book_code);

    EXECUTE format(
            $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_amount_%s_%s_%s PARTITION OF acc_duct_voucher_amount_%s_%s FOR VALUES IN (%L) PARTITION BY LIST(proc_id) $list$,
            p_entity_id, p_year_month, p_book_code, p_entity_id, p_year_month, p_book_code);
    EXECUTE format(
            $list$ grant select on acc_duct_voucher_amount_%s_%s_%s to bpluser  $list$,
            p_entity_id, p_year_month, p_book_code);

    -- proc分区
    for rec_proc_id in (select distinct proc_id
                        from bpluser.bms_conf_action_procdef
                        where system_code = 'ACC'
                          and parent_proc_id = (select proc_id
                                                from bpluser.bms_conf_action_procdef
                                                where system_code = 'ACC'
                                                  and proc_code = 'ACC_ACCOUNTENTRY')
													union ALL
													select distinct proc_id
                        from bpluser.bms_conf_action_procdef
                        where system_code = 'ACC'
												and proc_code in ( 'ACC_ACCOUNTENTRY_GAIN_LOSS','ACC_ACCOUNTENTRY_RECLASSIFY'))
        loop
        -- proc分区还会进行hash分区，预防单个节点数据量过多，默认为一个分区

        -- dap没有book_code
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_dap_entry_data_%s_%s_%s PARTITION OF acc_dap_entry_data_%s_%s FOR VALUES IN
            (%s) PARTITION BY hash(entry_data_id)  $list$,
                    p_entity_id, p_year_month, rec_proc_id.proc_id, p_entity_id, p_year_month, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_dap_entry_data_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_dap_entry_data_%s_%s_%s_0 PARTITION OF
                    acc_dap_entry_data_%s_%s_%s  (primary key (entry_data_id) )FOR VALUES WITH (MODULUS 1, REMAINDER 0)  $list$,
                    p_entity_id, p_year_month, rec_proc_id.proc_id, p_entity_id, p_year_month, rec_proc_id.proc_id);


            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_buss_entry_data_%s_%s_%s_%s PARTITION OF acc_buss_entry_data_%s_%s_%s FOR
             VALUES IN (%s) PARTITION BY hash(entry_data_id)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_buss_entry_data_%s_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_buss_entry_data_%s_%s_%s_%s_0 PARTITION OF
                    acc_buss_entry_data_%s_%s_%s_%s  (primary key (entry_data_id,center_code) )FOR VALUES WITH (MODULUS 1, REMAINDER 0)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);

            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_buss_entry_data_detail_%s_%s_%s_%s PARTITION OF acc_buss_entry_data_detail_%s_%s_%s FOR VALUES IN (%s) PARTITION BY hash(buss_entry_dtl_id)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_buss_entry_data_detail_%s_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_buss_entry_data_detail_%s_%s_%s_%s_0 PARTITION OF
                    acc_buss_entry_data_detail_%s_%s_%s_%s (primary key (buss_entry_dtl_id) )FOR VALUES WITH (MODULUS
                    1, REMAINDER 0)   $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);

            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_buss_voucher_%s_%s_%s_%s PARTITION OF acc_buss_voucher_%s_%s_%s FOR VALUES IN (%s) PARTITION BY hash(voucher_id)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_buss_voucher_%s_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_buss_voucher_%s_%s_%s_%s_0 PARTITION OF
                    acc_buss_voucher_%s_%s_%s_%s (primary key (voucher_id) )FOR VALUES WITH (MODULUS 1, REMAINDER 0)
                        $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);

            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_buss_voucher_detail_%s_%s_%s_%s PARTITION OF acc_buss_voucher_detail_%s_%s_%s FOR VALUES IN (%s) PARTITION BY hash(voucher_dtl_id)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_buss_voucher_detail_%s_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_buss_voucher_detail_%s_%s_%s_%s_0 PARTITION OF
                    acc_buss_voucher_detail_%s_%s_%s_%s (primary key (voucher_dtl_id) )FOR VALUES WITH (MODULUS 1, REMAINDER 0)     $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);

            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_all_%s_%s_%s_%s PARTITION OF acc_duct_entry_data_all_%s_%s_%s FOR VALUES IN (%s) PARTITION BY hash(buss_entry_id)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_duct_entry_data_all_%s_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_all_%s_%s_%s_%s_0 PARTITION OF
                    acc_duct_entry_data_all_%s_%s_%s_%s (primary key (buss_entry_id)  )FOR VALUES WITH (MODULUS 1, REMAINDER 0)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);

            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_success_%s_%s_%s_%s PARTITION OF acc_duct_entry_data_success_%s_%s_%s FOR VALUES IN (%s) PARTITION BY hash(buss_entry_id)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_duct_entry_data_success_%s_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_success_%s_%s_%s_%s_0 PARTITION OF
                    acc_duct_entry_data_success_%s_%s_%s_%s (primary key (buss_entry_Id))FOR VALUES WITH (MODULUS 1,
                    REMAINDER 0)   $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);

            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_fail_%s_%s_%s_%s PARTITION OF acc_duct_entry_data_fail_%s_%s_%s FOR VALUES IN (%s) PARTITION BY hash(buss_entry_id)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_duct_entry_data_fail_%s_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_entry_data_fail_%s_%s_%s_%s_0 PARTITION OF
                    acc_duct_entry_data_fail_%s_%s_%s_%s (primary key (buss_entry_Id)) FOR VALUES WITH (MODULUS 1,
                    REMAINDER 0)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);

            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_detail_%s_%s_%s_%s PARTITION OF
                    acc_duct_voucher_detail_%s_%s_%s FOR VALUES IN (%s) PARTITION BY hash(voucher_dtl_id)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_duct_voucher_detail_%s_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_detail_%s_%s_%s_%s_0 PARTITION OF
                    acc_duct_voucher_detail_%s_%s_%s_%s (primary key (voucher_dtl_id) )FOR VALUES WITH (MODULUS 1, REMAINDER 0)   $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);

            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_article_%s_%s_%s_%s PARTITION OF
                    acc_duct_voucher_article_%s_%s_%s FOR VALUES IN (%s) PARTITION BY hash(voucher_dtl_id)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_duct_voucher_article_%s_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_article_%s_%s_%s_%s_0 PARTITION OF
                    acc_duct_voucher_article_%s_%s_%s_%s (primary key (voucher_dtl_id)  )FOR VALUES WITH (MODULUS 1, REMAINDER 0)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);

            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_amount_%s_%s_%s_%s PARTITION OF
                    acc_duct_voucher_amount_%s_%s_%s FOR VALUES IN (%s) PARTITION BY hash(bu_voucher_no)  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ grant select on acc_duct_voucher_amount_%s_%s_%s_%s to bpluser  $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id);
            EXECUTE format(
                    $list$ CREATE TABLE IF NOT EXISTS acc_duct_voucher_amount_%s_%s_%s_%s_0 PARTITION OF
                    acc_duct_voucher_amount_%s_%s_%s_%s (primary key (bu_voucher_no,center_code) )FOR VALUES WITH (MODULUS 1, REMAINDER 0)   $list$,
                    p_entity_id, p_year_month, p_book_code, rec_proc_id.proc_id, p_entity_id, p_year_month, p_book_code,
                    rec_proc_id.proc_id);
        end loop;

EXCEPTION
    WHEN others THEN
        --ROLLBACK;
        get stacked diagnostics v_error_line= PG_EXCEPTION_CONTEXT;
        RAISE exception '%', 'error line:' || v_error_line || '. error:' || SQLERRM;

END ;
$BODY$
  LANGUAGE plpgsql
/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-09-16 14:50:26
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-09-16 14:50:26<br/>
 * Description: 指标配置表<br/>
 * Table Name: ATR_CONF_QUOTA<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrConfQuotaTaskVo implements Serializable {

    @ApiModelProperty(value = "entityId|业务单位", required = true)
    @NotNull(message = "TheBusiness Unit can't be null|业务单位不能为空|業務單位不能為空")
    private Long entityId;


    @ApiModelProperty(value = "model_def_id|计量模型", required = true)
    private String businessSourceCode;


    @ApiModelProperty(value = "quotaClass|指标归类", required = false)
    private String quotaClass;

    private String yearMonth;

    @ApiModelProperty(value = "loa_code|业务线编码", required = true)
    private String loaCode;

    private String validIs;

    private String auditState;

    @ApiModelProperty(value = "creatorId|创建人", required = false)
    private Long creatorId;


    @ApiModelProperty(value = "createTime|创建时间", required = false)
    private Date createTime;

    @ApiModelProperty(value = "dimension|假设颗粒度类型( 合同组合、合同组、保单)", required = false)
    private String dimension;


    @ApiModelProperty(value = "dimension_value|假设颗粒度值", required = true)
    private String dimensionValue;

    private String perYearMonth;

    /**
     * 自动任务传输字段：任务模式：A-自动
     */
    private String taskMode;

    /**
     * 自动任务传输字段：任务编码
     */
    private String taskCode;


    /**
     * 自动任务传输字段：重试次序
     */
    private Long retryOrder;

    private static final long serialVersionUID = 1L;

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getQuotaClass() {
        return quotaClass;
    }

    public void setQuotaClass(String quotaClass) {
        this.quotaClass = quotaClass;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getDimensionValue() {
        return dimensionValue;
    }

    public void setDimensionValue(String dimensionValue) {
        this.dimensionValue = dimensionValue;
    }

    public String getTaskMode() {
        return taskMode;
    }

    public void setTaskMode(String taskMode) {
        this.taskMode = taskMode;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public Long getRetryOrder() {
        return retryOrder;
    }

    public void setRetryOrder(Long retryOrder) {
        this.retryOrder = retryOrder;
    }

    public String getPerYearMonth() {
        return perYearMonth;
    }

    public void setPerYearMonth(String perYearMonth) {
        this.perYearMonth = perYearMonth;
    }
}
---------- drop ---------------
do language plpgsql
$$
    declare
        rec record;
    begin
        for rec in select t.proname, t.prokind from pg_proc t where t.proname ~ '^last_day$'
            loop
                if rec.prokind = 'f' then
                    call atr_pack_commonutils_proc_drop_func(rec.proname::varchar);
                elsif rec.prokind = 'p' then
                    call atr_pack_commonutils_proc_drop_proc(rec.proname::varchar);
                end if;
            end loop;
    end;
$$;



---------- create ---------------
CREATE FUNCTION atruser.last_day(p_date timestamp with time zone) RETURNS timestamp without time zone
    LANGUAGE sql IMMUTABLE
    AS $$
    --------------------------------------------
    -- 模拟oracle的last_day函数  by majingyun
    --------------------------------------------
    select date_trunc('month', (p_date + '1 month')) + '-1 day' +
           (p_date - date_trunc('day', p_date));
$$;


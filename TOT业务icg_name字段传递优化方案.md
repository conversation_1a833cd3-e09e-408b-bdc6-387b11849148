# TOT业务icg_name字段传递优化方案

创建时间：2025-01-29
评估结果：高理解深度 + 模块变更 + 中风险

## 执行计划
1. [阶段1] 分析现状和确定优化方案 - 30分钟
2. [阶段2] 修改SQL查询，添加icg_name字段传递 - 20分钟  
3. [阶段3] 修改Service层数据处理逻辑 - 20分钟
4. [阶段4] 测试验证修改效果 - 10分钟

## 当前状态
正在执行：阶段4 - 测试验证修改效果
进度：90% - 已完成核心修改，正在进行验证

## 已完成
- [✓] 分析AtrBussLrcTotService.java的TOT业务处理逻辑
- [✓] 确认atr_dap_treaty表包含icg_name字段
- [✓] 确认目标表实体类AtrBussToLrcTUl和AtrBussToLrcTUlR都已有icgName字段
- [✓] 分析表之间的关联关系（通过treaty_no字段）
- [✓] 确认数据传递的关键方法和SQL
- [✓] 修改AtrBussLrcToCustDao.xml，在listDapTreaty SQL中添加icg_name字段
- [✓] 修改AtrBussLrcTotService.java，在calcIcp方法中添加icgName字段传递逻辑

## 现状分析

### 数据流向
1. **源表**: `atr_dap_treaty` - 包含 `icg_name` 字段
2. **目标表**: 
   - `atr_buss_to_lrc_t_ul` (4维度表)
   - `atr_buss_to_lrc_t_ul_r` (6维度表)
3. **关联字段**: `treaty_no`

### 关键发现
1. **实体类已就绪**: AtrBussToLrcTUl和AtrBussToLrcTUlR都已有`icgName`字段
2. **AtrDapTreaty实体类已包含icgName字段**
3. **当前SQL缺失**: `listDapTreaty`方法的SQL查询未包含`icg_name`字段
4. **数据传递点**: 在`calcIcp`方法中，通过`treatyConfIdx`获取合约信息并设置到业务对象

### 核心问题
当前`listDapTreaty` SQL查询中缺少`icg_name`字段，导致合约信息中的`icgName`为空，无法传递到目标表。

## 优化方案

### 方案概述
通过修改SQL查询和数据传递逻辑，确保`icg_name`字段能够从`atr_dap_treaty`表正确传递到两个目标表。

### 具体修改点

#### 1. 修改AtrBussLrcToCustDao.xml
**文件**: `ss-ifrs-actuarial\src\main\resources\mapper\postgres\custom\ecf\AtrBussLrcToCustDao.xml`

**修改内容**: 在`listDapTreaty`方法的SQL中添加`icg_name`字段

```sql
-- 修改前（第248-265行）
<select id="listDapTreaty" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrDapTreaty">
    SELECT
        treaty_no                   AS treatyNo,
        sectiono_code               AS sectionoCode,
        reinsurer_code              AS reinsurerCode,
        -- ... 其他字段
        est_premium                 AS estPremium
    FROM atr_dap_treaty
    where year_month = #{yearMonth,jdbcType=VARCHAR}
</select>

-- 修改后
<select id="listDapTreaty" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrDapTreaty">
    SELECT
        treaty_no                   AS treatyNo,
        sectiono_code               AS sectionoCode,
        reinsurer_code              AS reinsurerCode,
        icg_name                    AS icgName,  -- 新增字段
        -- ... 其他字段
        est_premium                 AS estPremium
    FROM atr_dap_treaty
    where year_month = #{yearMonth,jdbcType=VARCHAR}
</select>
```

#### 2. 修改AtrBussLrcTotService.java
**文件**: `ss-ifrs-actuarial\src\main\java\com\ss\ifrs\actuarial\service\impl\AtrBussLrcTotService.java`

**修改内容**: 在`calcIcp`方法中添加`icgName`字段的传递逻辑

```java
// 在calcIcp方法中，约第192行附近，添加icgName的设置
if (treatyInfo.getIcgName() != null) {
    icu.setIcgName(treatyInfo.getIcgName());
}
```

### 数据传递路径
1. `listDapTreaty` SQL查询 → `AtrDapTreaty.icgName`
2. `collectionTreatyConf` → `treatyConfIdx`索引
3. `calcIcp` → 从`treatyInfo`获取`icgName`并设置到`icu`对象
4. `storeRawData` → 插入到`atr_buss_to_lrc_t_ul_r`表
5. `collectIcu` → 汇总到`atr_buss_to_lrc_t_ul`表

## 修改完成总结

### 已实施的修改

#### 1. SQL查询修改 ✅
**文件**: `AtrBussLrcToCustDao.xml` (第251行)
```sql
-- 在listDapTreaty查询中添加了：
icg_name AS icgName,
```

#### 2. Service层数据传递修改 ✅
**文件**: `AtrBussLrcTotService.java` (第199-201行)
```java
// 在calcIcp方法中添加了：
if (treatyInfo.getIcgName() != null) {
    icu.setIcgName(treatyInfo.getIcgName());
}
```

### 数据传递链路验证
1. ✅ `atr_dap_treaty.icg_name` → SQL查询 → `AtrDapTreaty.icgName`
2. ✅ `treatyConfIdx` 索引 → `calcIcp` 方法 → `icu.setIcgName()`
3. ✅ `storeRawData` → `atr_buss_to_lrc_t_ul_r` 表
4. ✅ `collectIcu` → `atr_buss_to_lrc_t_ul` 表

## 风险评估
- **数据一致性风险**: ✅ 低风险 - 使用现有字段和传递机制
- **性能影响**: ✅ 无影响 - 仅增加一个字段查询
- **向后兼容性**: ✅ 完全兼容 - 实体类字段已存在

## 验证建议
1. 运行TOT业务计算，检查目标表中icg_name字段是否正确填充
2. 验证数据与atr_dap_treaty表中的icg_name保持一致
3. 确认现有TOT业务功能正常运行

## 优化效果
通过此次优化，`atr_dap_treaty` 表中的 `icg_name` 字段现在能够正确传递到：
- `atr_buss_to_lrc_t_ul` 表（4维度汇总表）
- `atr_buss_to_lrc_t_ul_r` 表（6维度详细表）

这将提升TOT业务数据的完整性和可追溯性。

alter sequence atruser.atr_seq_dap_dd_premium restart with 1;
alter sequence atruser.atr_seq_dap_fo_premium restart with 1;
alter sequence atruser.atr_seq_dap_ti_premium restart with 1;
alter sequence atruser.atr_seq_dap_to_premium restart with 1;
alter sequence atruser.atr_seq_dap_to_premium_recovered restart with 1;
alter sequence atruser.atr_seq_dap_dd_payment_plan restart with 1;
alter sequence atruser.atr_seq_dap_dd_premium_paid restart with 1;
alter sequence atruser.atr_seq_dap_fo_premium_paid restart with 1;
alter sequence atruser.atr_seq_dap_ti_premium_paid restart with 1;
alter sequence atruser.atr_seq_dap_to_premium_paid restart with 1;
alter sequence atruser.atr_seq_dap_dd_claim_recv restart with 1;
alter sequence atruser.atr_seq_dap_fo_claim_recv restart with 1;
alter sequence atruser.atr_seq_dap_ti_claim_recv restart with 1;
alter sequence atruser.atr_seq_dap_to_claim_recv restart with 1;
alter sequence atruser.atr_seq_dap_dd_claim_paid restart with 1;
alter sequence atruser.atr_seq_dap_fo_claim_paid restart with 1;
alter sequence atruser.atr_seq_dap_ti_claim_paid restart with 1;
alter sequence atruser.atr_seq_dap_to_claim_paid restart with 1;
alter sequence atruser.atr_seq_dap_dd_icg_claim_amount restart with 1;
alter sequence atruser.atr_seq_dap_fo_icg_claim_amount restart with 1;
alter sequence atruser.atr_seq_dap_ti_icg_claim_amount restart with 1;
alter sequence atruser.atr_seq_dap_to_icg_claim_amount restart with 1;
alter sequence atruser.atr_seq_dap_dd_icg_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_dap_fo_icg_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_dap_ti_icg_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_dap_to_icg_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_buss_quota_def restart with 1;
alter sequence atruser.atr_seq_buss_quota_value restart with 1;
alter sequence atruser.atr_seq_buss_dd_lic_icg_calc restart with 1;
alter sequence atruser.atr_seq_buss_fo_lic_icg_calc restart with 1;
alter sequence atruser.atr_seq_buss_ti_lic_icg_calc restart with 1;
alter sequence atruser.atr_seq_buss_to_lic_icg_calc restart with 1;
alter sequence atruser.atr_seq_buss_dd_lic_icg_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_fo_lic_icg_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_ti_lic_icg_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_to_lic_icg_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_dd_lic_icg_calc_accident_ym restart with 1;
alter sequence atruser.atr_seq_buss_fo_lic_icg_calc_accident_ym restart with 1;
alter sequence atruser.atr_seq_buss_ti_lic_icg_calc_accident_ym restart with 1;
alter sequence atruser.atr_seq_buss_to_lic_icg_calc_accident_ym restart with 1;
alter sequence atruser.atr_seq_buss_dd_lic_icg_calc_accident_ym_detail restart with 1;
alter sequence atruser.atr_seq_buss_fo_lic_icg_calc_accident_ym_detail restart with 1;
alter sequence atruser.atr_seq_buss_ti_lic_icg_calc_accident_ym_detail restart with 1;
alter sequence atruser.atr_seq_buss_to_lic_icg_calc_accident_ym_detail restart with 1;
alter sequence atruser.atr_seq_buss_dd_lrc_icg_calc restart with 1;
alter sequence atruser.atr_seq_buss_fo_lrc_icg_calc restart with 1;
alter sequence atruser.atr_seq_buss_ti_lrc_icg_calc restart with 1;
alter sequence atruser.atr_seq_buss_to_lrc_icg_calc restart with 1;
alter sequence atruser.atr_seq_buss_dd_lrc_icu_calc restart with 1;
alter sequence atruser.atr_seq_buss_fo_lrc_icu_calc restart with 1;
alter sequence atruser.atr_seq_buss_ti_lrc_icu_calc restart with 1;
alter sequence atruser.atr_seq_buss_to_lrc_icu_calc restart with 1;
alter sequence atruser.atr_seq_buss_dd_lrc_icg_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_fo_lrc_icg_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_ti_lrc_icg_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_to_lrc_icg_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_dd_lrc_icu_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_fo_lrc_icu_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_ti_lrc_icu_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_to_lrc_icu_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_ti_lrc_icu_ul_calc restart with 1;
alter sequence atruser.atr_seq_buss_to_lrc_icu_ul_calc restart with 1;
alter sequence atruser.atr_seq_buss_to_lrc_icu_rep_calc restart with 1;
alter sequence atruser.atr_seq_buss_ti_lrc_icu_ul_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_to_lrc_icu_ul_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_to_lrc_icu_rep_calc_detail restart with 1;
alter sequence atruser.atr_seq_buss_to_lrc_icu_recv restart with 1;
alter sequence atruser.atr_seq_buss_to_lrc_icu_recv_detail restart with 1;
alter sequence atruser.atr_seq_buss_ecf_dap_action restart with 1;
alter sequence atruser.atr_seq_buss_lic_action restart with 1;
alter sequence atruser.atr_seq_buss_lrc_action restart with 1;
alter sequence atruser.atr_seq_log_ecf_dap restart with 1;
alter sequence atruser.atr_seq_log_lic_trace restart with 1;
alter sequence atruser.atr_seq_log_lrc_trace restart with 1;
alter sequence atruser.atr_seq_dap_dd_icp_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_buss_dd_lic_icp_risk_ratio restart with 1;
alter sequence atruser.atr_seq_buss_dd_lic_icg_risk_ratio restart with 1;
alter sequence atruser.atr_seq_buss_dd_lic_icg_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_dap_fo_icp_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_buss_fo_lic_icp_risk_ratio restart with 1;
alter sequence atruser.atr_seq_buss_fo_lic_icg_risk_ratio restart with 1;
alter sequence atruser.atr_seq_buss_fo_lic_icg_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_dap_ti_icp_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_buss_ti_lic_icp_risk_ratio restart with 1;
alter sequence atruser.atr_seq_buss_ti_lic_icg_risk_ratio restart with 1;
alter sequence atruser.atr_seq_buss_ti_lic_icg_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_dap_to_icp_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_buss_to_lic_icp_risk_ratio restart with 1;
alter sequence atruser.atr_seq_buss_to_lic_icg_risk_ratio restart with 1;
alter sequence atruser.atr_seq_buss_to_lic_icg_claim_accident_amount restart with 1;
alter sequence atruser.atr_seq_buss_dd_lrc_qtc_icg restart with 1;
alter sequence atruser.atr_seq_buss_fo_lrc_qtc_icg restart with 1;
alter sequence atruser.atr_seq_action_no restart with 1;


truncate table atruser.atr_dap_dd_premium;
truncate table atruser.atr_dap_fo_premium;
truncate table atruser.atr_dap_ti_premium;
truncate table atruser.atr_dap_to_premium;
truncate table atruser.atr_dap_to_premium_recovered;
truncate table atruser.atr_dap_dd_payment_plan;
truncate table atruser.atr_dap_dd_premium_paid;
truncate table atruser.atr_dap_fo_premium_paid;
truncate table atruser.atr_dap_ti_premium_paid;
truncate table atruser.atr_dap_to_premium_paid;
truncate table atruser.atr_dap_dd_claim_recv;
truncate table atruser.atr_dap_fo_claim_recv;
truncate table atruser.atr_dap_ti_claim_recv;
truncate table atruser.atr_dap_to_claim_recv;
truncate table atruser.atr_dap_dd_claim_paid;
truncate table atruser.atr_dap_fo_claim_paid;
truncate table atruser.atr_dap_ti_claim_paid;
truncate table atruser.atr_dap_to_claim_paid;
truncate table atruser.atr_dap_dd_icg_claim_amount;
truncate table atruser.atr_dap_fo_icg_claim_amount;
truncate table atruser.atr_dap_ti_icg_claim_amount;
truncate table atruser.atr_dap_to_icg_claim_amount;
truncate table atruser.atr_dap_dd_icg_claim_accident_amount;
truncate table atruser.atr_dap_fo_icg_claim_accident_amount;
truncate table atruser.atr_dap_ti_icg_claim_accident_amount;
truncate table atruser.atr_dap_to_icg_claim_accident_amount;
truncate table atruser.atr_buss_quota_def;
truncate table atruser.atr_buss_quota_value;
truncate table atruser.atr_buss_dd_lic_icg_calc;
truncate table atruser.atr_buss_fo_lic_icg_calc;
truncate table atruser.atr_buss_ti_lic_icg_calc;
truncate table atruser.atr_buss_to_lic_icg_calc;
truncate table atruser.atr_buss_dd_lic_icg_calc_detail;
truncate table atruser.atr_buss_fo_lic_icg_calc_detail;
truncate table atruser.atr_buss_ti_lic_icg_calc_detail;
truncate table atruser.atr_buss_to_lic_icg_calc_detail;
truncate table atruser.atr_buss_dd_lic_icg_calc_accident_ym;
truncate table atruser.atr_buss_fo_lic_icg_calc_accident_ym;
truncate table atruser.atr_buss_ti_lic_icg_calc_accident_ym;
truncate table atruser.atr_buss_to_lic_icg_calc_accident_ym;
truncate table atruser.atr_buss_dd_lic_icg_calc_accident_ym_detail;
truncate table atruser.atr_buss_fo_lic_icg_calc_accident_ym_detail;
truncate table atruser.atr_buss_ti_lic_icg_calc_accident_ym_detail;
truncate table atruser.atr_buss_to_lic_icg_calc_accident_ym_detail;
truncate table atruser.atr_buss_dd_lrc_icg_calc;
truncate table atruser.atr_buss_fo_lrc_icg_calc;
truncate table atruser.atr_buss_ti_lrc_icg_calc;
truncate table atruser.atr_buss_to_lrc_icg_calc;
truncate table atruser.atr_buss_dd_lrc_icu_calc;
truncate table atruser.atr_buss_fo_lrc_icu_calc;
truncate table atruser.atr_buss_ti_lrc_icu_calc;
truncate table atruser.atr_buss_to_lrc_icu_calc;
truncate table atruser.atr_buss_dd_lrc_icg_calc_detail;
truncate table atruser.atr_buss_fo_lrc_icg_calc_detail;
truncate table atruser.atr_buss_ti_lrc_icg_calc_detail;
truncate table atruser.atr_buss_to_lrc_icg_calc_detail;
truncate table atruser.atr_buss_dd_lrc_icu_calc_detail;
truncate table atruser.atr_buss_fo_lrc_icu_calc_detail;
truncate table atruser.atr_buss_ti_lrc_icu_calc_detail;
truncate table atruser.atr_buss_to_lrc_icu_calc_detail;
truncate table atruser.atr_buss_ti_lrc_icu_ul_calc;
truncate table atruser.atr_buss_to_lrc_icu_ul_calc;
truncate table atruser.atr_buss_to_lrc_icu_rep_calc;
truncate table atruser.atr_buss_ti_lrc_icu_ul_calc_detail;
truncate table atruser.atr_buss_to_lrc_icu_ul_calc_detail;
truncate table atruser.atr_buss_to_lrc_icu_rep_calc_detail;
truncate table atruser.atr_buss_to_lrc_icu_recv;
truncate table atruser.atr_buss_to_lrc_icu_recv_detail;
truncate table atruser.atr_buss_ecf_dap_action;
truncate table atruser.atr_buss_lic_action;
truncate table atruser.atr_buss_lrc_action;
truncate table atruser.atr_log_ecf_dap;
truncate table atruser.atr_log_lic_trace;
truncate table atruser.atr_log_lrc_trace;
truncate table atruser.atr_dap_dd_icp_claim_accident_amount;
truncate table atruser.atr_buss_dd_lic_icp_risk_ratio;
truncate table atruser.atr_buss_dd_lic_icg_risk_ratio;
truncate table atruser.atr_buss_dd_lic_icg_claim_accident_amount;
truncate table atruser.atr_dap_fo_icp_claim_accident_amount;
truncate table atruser.atr_buss_fo_lic_icp_risk_ratio;
truncate table atruser.atr_buss_fo_lic_icg_risk_ratio;
truncate table atruser.atr_buss_fo_lic_icg_claim_accident_amount;
truncate table atruser.atr_dap_ti_icp_claim_accident_amount;
truncate table atruser.atr_buss_ti_lic_icp_risk_ratio;
truncate table atruser.atr_buss_ti_lic_icg_risk_ratio;
truncate table atruser.atr_buss_ti_lic_icg_claim_accident_amount;
truncate table atruser.atr_dap_to_icp_claim_accident_amount;
truncate table atruser.atr_buss_to_lic_icp_risk_ratio;
truncate table atruser.atr_buss_to_lic_icg_risk_ratio;
truncate table atruser.atr_buss_to_lic_icg_claim_accident_amount;
truncate table atruser.atr_temp_ecf_quota_param;
truncate table atruser.atr_conf_max_no;
truncate table atruser.atr_temp_lrc_icg_dev_coverage_amount;
truncate table atruser.atr_buss_dd_lrc_qtc_icg;
truncate table atruser.atr_buss_fo_lrc_qtc_icg;
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-ifrs-datamgr/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-ifrs-quantification/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-library-job/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-library-job/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-library-mq/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-library-mybatis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-library-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-library-utils/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-platform-admin/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-platform-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-platform-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-platform-eureka/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-platform-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ss-platform-schedule/src/main/java" charset="UTF-8" />
  </component>
</project>
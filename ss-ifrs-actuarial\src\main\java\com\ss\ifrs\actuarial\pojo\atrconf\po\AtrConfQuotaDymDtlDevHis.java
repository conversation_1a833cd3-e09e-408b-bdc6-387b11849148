/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-20 10:16:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-20 10:16:00<br/>
 * Description: 假设值事故年月的发展期明细表轨迹 （基于事故年月）<br/>
 * Table Name: ATR_CONF_QUOTA_DYM_DTL_DEVHIS<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "假设值事故年月的发展期明细表轨迹 （基于事故年月）")
public class AtrConfQuotaDymDtlDevHis implements Serializable {
    /**
     * Database column: ATR_CONF_QUOTA_DYM_DTL_DEVHIS.DEV_HIS_ID
     * Database remarks: null
     */
    private Long devHisId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DTL_DEVHIS.DEV_ID
     * Database remarks: 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private Long devId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DTL_DEVHIS.QUOTA_DETAIL_ID
     * Database remarks: 明细表ID
     */
    @ApiModelProperty(value = "明细表ID", required = false)
    private Long quotaDetailId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DTL_DEVHIS.DEV_PERIOD
     * Database remarks: 发展期
     */
    @ApiModelProperty(value = "发展期", required = false)
    private Long devPeriod;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DTL_DEVHIS.QUOTA_VALUE
     * Database remarks: 指标值
     */
    @ApiModelProperty(value = "指标值", required = false)
    private String quotaValue;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DTL_DEVHIS.CREATOR_ID
     * Database remarks: 创建人
     */
    @ApiModelProperty(value = "创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DTL_DEVHIS.CREATE_TIME
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DTL_DEVHIS.UPDATOR_ID
     * Database remarks: 最后修改人
     */
    @ApiModelProperty(value = "最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DTL_DEVHIS.UPDATE_TIME
     * Database remarks: 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间", required = false)
    private Date updateTime;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DTL_DEVHIS.SERIAL_NO
     * Database remarks: 版本号与主表一致
     */
    @ApiModelProperty(value = "版本号与主表一致", required = false)
    private Integer serialNo;

    private static final long serialVersionUID = 1L;

    public Long getDevHisId() {
        return devHisId;
    }

    public void setDevHisId(Long devHisId) {
        this.devHisId = devHisId;
    }

    public Long getDevId() {
        return devId;
    }

    public void setDevId(Long devId) {
        this.devId = devId;
    }

    public Long getQuotaDetailId() {
        return quotaDetailId;
    }

    public void setQuotaDetailId(Long quotaDetailId) {
        this.quotaDetailId = quotaDetailId;
    }

    public Long getDevPeriod() {
        return devPeriod;
    }

    public void setDevPeriod(Long devPeriod) {
        this.devPeriod = devPeriod;
    }

    public String getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(String quotaValue) {
        this.quotaValue = quotaValue;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }
}
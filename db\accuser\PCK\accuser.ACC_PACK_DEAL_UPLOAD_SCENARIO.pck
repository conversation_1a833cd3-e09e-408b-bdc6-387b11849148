CREATE OR REPLACE PACKAGE ACC_PACK_DEAL_UPLOAD_SCENARIO IS
    PROCEDURE clear_data;
    PROCEDURE process_data(p_batch_no IN VARCHAR2);
END ACC_PACK_DEAL_UPLOAD_SCENARIO;
/
CREATE OR R<PERSON>LACE PACKAGE BODY ACC_PACK_DEAL_UPLOAD_SCENARIO IS


    /***********************************************************************
       NAME         : clear_data
       DESCRIPTION  : 清除临时表数据
       DATE         : 2023-07-07
       AUTHOR       : XZX
      ***********************************************************************/
    PROCEDURE clear_data IS

      BEGIN
        delete from acc_temp_conf_entryrule_detail;
        delete from acc_temp_conf_entryrule;
        delete from ACC_TEMP_CONF_SCENARIO_MODELREF;
        delete from ACC_TEMP_CONF_SCENARIO;
     end clear_data;


    /***********************************************************************
       NAME         : process_data
       DESCRIPTION  : 将场景配置上传到临时表的数据，同步到正式表（临时表没有id，
                        关联关系靠临时表中的场景编码关联。插入正式表需要转换成id关联）。
                        以及记录到轨迹表
       DATE         : 2023-07-07
       AUTHOR       : XZX
      ***********************************************************************/
     PROCEDURE process_data(p_batch_no IN VARCHAR2) IS

       v_qtp_proc_id   NUMBER(11);
       v_cs_proc_id    NUMBER(11);

      BEGIN
       -- 更新condition
        v_qtp_proc_id := bpluser.bpl_pack_common.func_get_procid('ACC_ACCOUNTENTRY_QTP_BCR');
        v_cs_proc_id  := bpluser.bpl_pack_common.func_get_procid('ACC_ACCOUNTENTRY_EXP_BCR');

        for rec_tmp  in (select SCENARIO_CODE from acc_temp_conf_scenario where batch_no = p_batch_no) loop
            UPDATE acc_temp_conf_scenario CS
            SET CS.SCENARIO_CONDITION =
                (SELECT LISTAGG((CASE
                              WHEN UPPER(C.MODEL_COLUMN_CODE) = 'TREATY_TYPE_CODE' THEN
                                CASE WHEN B.CODE_CODE = '00' THEN
                                  '(CASE WHEN B.TREATY_TYPE_CODE is NOT NULL THEN ''00'' ELSE B.TREATY_TYPE_CODE end)'
                                ELSE
                                  '(CASE WHEN B.TREATY_TYPE_CODE is NULL THEN ''00'' ELSE B.TREATY_TYPE_CODE end)'
                                END
                              WHEN UPPER(C.MODEL_COLUMN_CODE) = 'PROC_ID' THEN
                               '( CASE B.PROC_ID WHEN ' || v_cs_proc_id || ' THEN ''02'' WHEN ' || v_qtp_proc_id ||
                               ' THEN ''03'' ELSE ''01'' END )'
                              WHEN UPPER(C.MODEL_COLUMN_CODE) = 'RI_DIRECTION_CODE' THEN
                               '( CASE WHEN B.RI_DIRECTION_CODE is NULL THEN ''D'' ELSE B.RI_DIRECTION_CODE END )'
                              WHEN UPPER(C.MODEL_COLUMN_CODE) = 'POLICY_TYPE_CODE' THEN
                               '( CASE WHEN B.POLICY_TYPE_CODE is NULL THEN ''D'' ELSE B.POLICY_TYPE_CODE END )'
                              WHEN UPPER(C.MODEL_COLUMN_CODE) = 'RI_ARRANGEMENT_CODE' THEN
                               '( CASE WHEN B.RI_ARRANGEMENT_CODE is NULL THEN ''D'' ELSE B.RI_ARRANGEMENT_CODE END )'
                              WHEN UPPER(C.MODEL_COLUMN_CODE) = 'PAYMENT_TYPE_CODE' AND B.CODE_CODE != 'D' THEN --佣金支付-销数方式置为D
                               '( CASE WHEN B.EXTEND_COLUMN2 is NULL AND B.EXTEND_COLUMN2 != ''G'' AND B.POSTING_TYPE_CODE IN (''02'') AND EXPENSES_TYPE_CODE LIKE ''2%'' THEN ''D'' ELSE B.EXTEND_COLUMN2 END )'
                              WHEN UPPER(C.MODEL_COLUMN_CODE) = 'PAYMENT_TYPE_CODE' AND B.CODE_CODE = 'D' THEN --佣金支付-销数方式置为D
                               '( CASE WHEN B.EXTEND_COLUMN2 is NOT NULL AND B.EXTEND_COLUMN2 != ''G'' AND B.POSTING_TYPE_CODE IN (''02'') AND EXPENSES_TYPE_CODE LIKE ''2%'' THEN ''D'' ELSE B.EXTEND_COLUMN2 END )'
                              when upper(c.MODEL_COLUMN_CODE) = 'PROFIT_LOSS_CODE' and b.code_code = 'D' then
                               '(case when B.EXTEND_COLUMN8 is not null then ''D'' else  COALESCE(B.EXTEND_COLUMN8,''D'') end )'
                              when upper(c.MODEL_COLUMN_CODE) = 'ADVANCE_IND' and b.code_code in( '2','0') then
                               '(case when EXPENSES_TYPE_CODE = ''201B'' then ''2'' else (case when COALESCE(B.EXTEND_COLUMN1,''2'') in (''0'',''2'')  then '''|| b.code_code || ''' else  COALESCE(B.EXTEND_COLUMN1,''2'') end) end)'
                              ELSE
                               'b.' ||
                               COALESCE(M.MAPPING_COLUMN, C.MODEL_COLUMN_CODE)
                            END) || ' = ''' || B.CODE_CODE || '''',
                            ' and ') WITHIN GROUP(ORDER BY B.DISPLAY_NO)

                   FROM acc_temp_conf_scenario_modelref B
                   LEFT JOIN ACC_CONF_MODEL_CODE C
                     ON B.MODEL_CODE_ID = C.MODEL_CODE_ID
                   LEFT JOIN BPLUSER.BBS_CONF_MODEL_MAPPING M
                     ON C.MODEL_COLUMN_CODE = M.SOURCE_COLUMN
                    AND M.SOURCE_TABLE = 'ACC_DAP_ENTRY_DATA'
                    AND M.MAPPING_TABLE = 'ACC_DAP_ENTRY_DATA'
                    AND M.VALID_IS = '1'
                  WHERE B.SCENARIO_CODE = rec_tmp.SCENARIO_CODE
                    AND B.VALID_IS = '1')
          WHERE CS.SCENARIO_CODE = rec_tmp.SCENARIO_CODE;
        END LOOP;

       -- 插入或更新ACC_CONF_SCENARIO表
       merge into ACC_CONF_SCENARIO sc
       using (select scTmp.SCENARIO_CODE
            ,scTmp.SCENARIO_C_NAME
            ,scTmp.SCENARIO_L_NAME
            ,scTmp.SCENARIO_E_NAME
            ,scTmp.SERIAL_NO
            ,scTmp.SCENARIO_CONDITION
            ,scTmp.REMARK
            ,scTmp.VALID_IS
            ,scTmp.AUDIT_STATE
            ,scTmp.CHECKED_ID
            ,scTmp.CHECKED_TIME
            ,scTmp.CHECKED_MSG
            ,scTmp.CREATOR_ID
            ,scTmp.CREATE_TIME
            ,scTmp.UPDATOR_ID
            ,scTmp.UPDATE_TIME
            from ACC_TEMP_CONF_SCENARIO scTmp where scTmp.batch_no = p_batch_no) tmp
       on (sc.SCENARIO_CODE = tmp.SCENARIO_CODE)
       when matched then
       update set sc.SCENARIO_C_NAME = tmp.SCENARIO_C_NAME
            ,sc.SCENARIO_L_NAME = tmp.SCENARIO_L_NAME
            ,sc.SCENARIO_E_NAME = tmp.SCENARIO_E_NAME
            ,sc.SERIAL_NO = case when sc.SERIAL_NO is null then 2 else sc.SERIAL_NO +1 end
            ,sc.SCENARIO_CONDITION = tmp.SCENARIO_CONDITION
            ,sc.REMARK = tmp.REMARK
            ,sc.VALID_IS = 1
            ,sc.AUDIT_STATE = 1
            ,sc.CHECKED_TIME = tmp.CREATE_TIME
            ,sc.CHECKED_ID = tmp.CREATOR_ID
            ,sc.CHECKED_MSG = ''
            ,sc.UPDATOR_ID = tmp.CREATOR_ID
            ,sc.UPDATE_TIME = tmp.CREATE_TIME
        when not matched then
        insert (SCENARIO_ID
            ,SCENARIO_CODE
            ,SCENARIO_C_NAME
            ,SCENARIO_L_NAME
            ,SCENARIO_E_NAME
            ,SERIAL_NO
            ,SCENARIO_CONDITION
            ,REMARK
            ,VALID_IS
            ,AUDIT_STATE
            ,CHECKED_ID
            ,CHECKED_TIME
            ,CHECKED_MSG
            ,CREATOR_ID
            ,CREATE_TIME
            ,UPDATOR_ID
            ,UPDATE_TIME
            )
        values (acc_seq_configscene.NEXTVAL
            ,tmp.SCENARIO_CODE
            ,tmp.SCENARIO_C_NAME
            ,tmp.SCENARIO_L_NAME
            ,tmp.SCENARIO_E_NAME
            ,tmp.SERIAL_NO
            ,tmp.SCENARIO_CONDITION
            ,tmp.REMARK
            ,tmp.VALID_IS
            ,tmp.AUDIT_STATE
            ,tmp.CHECKED_ID
            ,tmp.CHECKED_TIME
            ,tmp.CHECKED_MSG
            ,tmp.CREATOR_ID
            ,tmp.CREATE_TIME
            ,tmp.UPDATOR_ID
            ,tmp.UPDATE_TIME
            );

        -- 查找ACC_CONF_SCENARIO的id，更新至acc_temp_conf_scenario
        update ACC_TEMP_CONF_SCENARIO tmp
            set tmp.SCENARIO_ID =
            (select sc.SCENARIO_ID from ACC_CONF_SCENARIO sc where sc.SCENARIO_CODE = tmp.SCENARIO_CODE) where tmp.batch_no = p_batch_no;



        -- 记录acc_conf_scenariohis轨迹表
        insert into acc_conf_scenariohis (SCENARIO_HIS_ID
            ,SCENARIO_ID
            ,SCENARIO_CODE
            ,SCENARIO_C_NAME
            ,SCENARIO_L_NAME
            ,SCENARIO_E_NAME
            ,SERIAL_NO
            ,REMARK
            ,VALID_IS
            ,AUDIT_STATE
            ,CHECKED_ID
            ,CHECKED_TIME
            ,CHECKED_MSG
            ,CREATOR_ID
            ,CREATE_TIME
            ,OPER_ID
            ,OPER_TIME
            ,OPER_TYPE
            )
        select acc_seq_conf_scenehis.nextval
            ,sc.SCENARIO_ID
            ,sc.SCENARIO_CODE
            ,sc.SCENARIO_C_NAME
            ,sc.SCENARIO_L_NAME
            ,sc.SCENARIO_E_NAME
            ,sc.SERIAL_NO
            ,sc.REMARK
            ,sc.VALID_IS
            ,sc.AUDIT_STATE
            ,sc.CHECKED_ID
            ,sc.CHECKED_TIME
            ,sc.CHECKED_MSG
            ,sc.CREATOR_ID
            ,sc.CREATE_TIME
            ,sc.UPDATOR_ID
            ,sc.UPDATE_TIME
            -- 根据版本号判断是新增还是更新
            ,case when sc.SERIAL_NO<=1 then '1' else '2' end
        from ACC_CONF_SCENARIO sc
        where exists(select 1 from ACC_TEMP_CONF_SCENARIO tmp where sc.SCENARIO_ID = tmp.SCENARIO_ID and tmp.batch_no = p_batch_no);


        -- 将ACC_CONF_SCENARIO 的id 和acc_temp_conf_scenario_modelref的数据关联
        update ACC_TEMP_CONF_SCENARIO_MODELREF tmp
            set tmp.SCENARIO_ID =
            (select sc.SCENARIO_ID from ACC_TEMP_CONF_SCENARIO sc where sc.SCENARIO_CODE = tmp.SCENARIO_CODE and sc.batch_no = p_batch_no);

        -- 插入ACC_CONF_SCENARIO_MODELREF
        -- 先删除，再插入，轨迹表记录为审核
        delete from ACC_CONF_SCENARIO_MODELREF modelref
            where exists (select 1 from ACC_TEMP_CONF_SCENARIO tmp where modelref.SCENARIO_ID = tmp.SCENARIO_ID and tmp.batch_no = p_batch_no);

        insert into ACC_CONF_SCENARIO_MODELREF(SCENARIO_MODELREF_ID
            ,SCENARIO_ID
            ,MODEL_CODE_ID
            ,CODE_CODE
            ,SERIAL_NO
            ,DISPLAY_NO
            ,VALID_IS
            ,AUDIT_STATE
            ,CHECKED_ID
            ,CHECKED_TIME
            ,CHECKED_MSG
            ,CREATOR_ID
            ,CREATE_TIME
            ,UPDATOR_ID
            ,UPDATE_TIME
            )
        select acc_seq_confscenefactorref.nextval
            ,tmp.SCENARIO_ID
            ,tmp.MODEL_CODE_ID
            ,tmp.CODE_CODE
            ,tmp.SERIAL_NO
            ,tmp.DISPLAY_NO
            ,tmp.VALID_IS
            ,tmp.AUDIT_STATE
            ,tmp.CHECKED_ID
            ,tmp.CHECKED_TIME
            ,tmp.CHECKED_MSG
            ,tmp.CREATOR_ID
            ,tmp.CREATE_TIME
            ,tmp.UPDATOR_ID
            ,tmp.UPDATE_TIME
        from ACC_TEMP_CONF_SCENARIO_MODELREF tmp where tmp.batch_no = p_batch_no;

        -- 记录acc_conf_scenario_modelrefhis轨迹表
        insert into acc_conf_scenario_modelrefhis(SCENARIO_MODELREF_HIS_ID
            ,SCENARIO_MODELREF_ID
            ,SCENARIO_ID
            ,MODEL_CODE_ID
            ,CODE_CODE
            ,SERIAL_NO
            ,DISPLAY_NO
            ,VALID_IS
            ,AUDIT_STATE
            ,CHECKED_ID
            ,CHECKED_TIME
            ,CHECKED_MSG
            ,CREATOR_ID
            ,CREATE_TIME
            ,UPDATOR_ID
            ,UPDATE_TIME
            ,OPER_TYPE
            ,OPER_ID
            ,OPER_TIME
            )
        select acc_seq_confscenefactorrefhis.nextval
            ,model.SCENARIO_MODELREF_ID
            ,model.SCENARIO_ID
            ,model.MODEL_CODE_ID
            ,model.CODE_CODE
            ,model.SERIAL_NO
            ,model.DISPLAY_NO
            ,model.VALID_IS
            ,model.AUDIT_STATE
            ,model.CHECKED_ID
            ,model.CHECKED_TIME
            ,model.CHECKED_MSG
            ,model.CREATOR_ID
            ,model.CREATE_TIME
            ,model.UPDATOR_ID
            ,model.UPDATE_TIME
            ,'4'
            ,model.UPDATOR_ID
            ,model.UPDATE_TIME
        from ACC_CONF_SCENARIO_MODELREF model
            where exists (select 1 from ACC_TEMP_CONF_SCENARIO sc where model.SCENARIO_ID = model.SCENARIO_ID and sc.batch_no = p_batch_no);


        -- 插入acc_conf_entryrule表
        -- 更新sceniro id
        update acc_temp_conf_entryrule rule
            set rule.SCENARIO_ID =
            (select sc.SCENARIO_ID from ACC_CONF_SCENARIO sc where sc.SCENARIO_CODE = rule.SCENARIO_CODE ) where rule.batch_no = p_batch_no;

        merge into acc_conf_entryrule rule
        using (select ENTITY_ID
                ,BOOK_CODE
                ,SCENARIO_ID
                ,ACCOUNT_ID_CR
                ,ACCOUNT_ID_DR
                ,EFFECTIVE_DATE
                ,EXPIRE_DATE
                ,VALID_IS
                ,CHECKED_TIME
                ,CHECKED_ID
                ,AUDIT_STATE
                ,CHECKED_MSG
                ,CREATE_TIME
                ,CREATOR_ID
                ,UPDATE_TIME
                ,UPDATOR_ID
                ,SERIAL_NO
            from acc_temp_conf_entryrule t where t.batch_no = p_batch_no)tmp
        on (rule.SCENARIO_ID=tmp.SCENARIO_ID
            and rule.ENTITY_ID = tmp.ENTITY_ID
            and rule.BOOK_CODE = tmp.BOOK_CODE)
        when matched then
        update set ACCOUNT_ID_CR = tmp.ACCOUNT_ID_CR
            ,ACCOUNT_ID_DR = tmp.ACCOUNT_ID_DR
            ,VALID_IS = tmp.VALID_IS
            ,CHECKED_TIME = tmp.UPDATE_TIME
            ,CHECKED_ID = tmp.UPDATOR_ID
            ,AUDIT_STATE = tmp.AUDIT_STATE
            ,CHECKED_MSG = tmp.CHECKED_MSG
            ,UPDATE_TIME = tmp.UPDATE_TIME
            ,UPDATOR_ID = tmp.UPDATOR_ID
            ,SERIAL_NO = case when rule.SERIAL_NO is null then 2 else rule.SERIAL_NO + 1 end
        when not matched then
        insert (ENTRY_RULE_ID
            ,ENTITY_ID
            ,BOOK_CODE
            ,SCENARIO_ID
            ,ACCOUNT_ID_CR
            ,ACCOUNT_ID_DR
            ,EFFECTIVE_DATE
            ,EXPIRE_DATE
            ,VALID_IS
            ,CHECKED_TIME
            ,CHECKED_ID
            ,AUDIT_STATE
            ,CHECKED_MSG
            ,CREATE_TIME
            ,CREATOR_ID
            ,UPDATE_TIME
            ,UPDATOR_ID
            ,SERIAL_NO
            )
        values(acc_seq_entryrule.nextval
                ,tmp.ENTITY_ID
                ,tmp.BOOK_CODE
                ,tmp.SCENARIO_ID
                ,tmp.ACCOUNT_ID_CR
                ,tmp.ACCOUNT_ID_DR
                ,tmp.EFFECTIVE_DATE
                ,tmp.EXPIRE_DATE
                ,tmp.VALID_IS
                ,tmp.CHECKED_TIME
                ,tmp.CHECKED_ID
                ,tmp.AUDIT_STATE
                ,tmp.CHECKED_MSG
                ,tmp.CREATE_TIME
                ,tmp.CREATOR_ID
                ,tmp.UPDATE_TIME
                ,tmp.UPDATOR_ID
                ,tmp.SERIAL_NO
            );
        -- 更新id
        update acc_temp_conf_entryrule tmp
            set ENTRY_RULE_ID =
            (select rule.ENTRY_RULE_ID from acc_conf_entryrule rule
                where rule.SCENARIO_ID=tmp.SCENARIO_ID
                    and rule.ENTITY_ID = tmp.ENTITY_ID
                    and rule.BOOK_CODE = tmp.BOOK_CODE) where tmp.batch_no = p_batch_no;
        -- 记录轨迹表
        insert into acc_conf_entryrulehis(ENTRY_RULE_HIS_ID
            ,ENTRY_RULE_ID
            ,ENTITY_ID
            ,BOOK_CODE
            ,SCENARIO_ID
            ,ACCOUNT_ID_CR
            ,ACCOUNT_ID_DR
            ,EFFECTIVE_DATE
            ,EXPIRE_DATE
            ,VALID_IS
            ,CHECKED_TIME
            ,CHECKED_ID
            ,AUDIT_STATE
            ,CHECKED_MSG
            ,OPER_TYPE
            ,OPER_ID
            ,OPER_TIME
            ,CREATE_TIME
            ,CREATOR_ID
            ,UPDATE_TIME
            ,UPDATOR_ID
            ,SERIAL_NO
            )
        select acc_seq_entryrulehis.nextval
            ,rule.ENTRY_RULE_ID
            ,rule.ENTITY_ID
            ,rule.BOOK_CODE
            ,rule.SCENARIO_ID
            ,rule.ACCOUNT_ID_CR
            ,rule.ACCOUNT_ID_DR
            ,rule.EFFECTIVE_DATE
            ,rule.EXPIRE_DATE
            ,rule.VALID_IS
            ,rule.CHECKED_TIME
            ,rule.CHECKED_ID
            ,rule.AUDIT_STATE
            ,rule.CHECKED_MSG
            ,case when rule.SERIAL_NO <= 1 then 1 else 2 end
            ,rule.UPDATOR_ID
            ,rule.UPDATE_TIME
            ,rule.CREATE_TIME
            ,rule.CREATOR_ID
            ,rule.UPDATE_TIME
            ,rule.UPDATOR_ID
            ,rule.SERIAL_NO
            from acc_conf_entryrule rule
                where exists
                    (select 1 from acc_temp_conf_entryrule tmp where tmp.ENTRY_RULE_ID = rule.ENTRY_RULE_ID and tmp.batch_no = p_batch_no ) ;


        -- 用ENTRY_RULE_ID 关联acc_conf_entryrule和acc_conf_entryrule_detail
        update acc_temp_conf_entryrule_detail detail
            set ENTRY_RULE_ID =
                (select rule.ENTRY_RULE_ID from acc_temp_conf_entryrule rule where rule.SCENARIO_CODE = detail.SCENARIO_CODE and rule.batch_no = p_batch_no);

        -- 清除acc_conf_entryrule_detail原来的数据
        delete from acc_conf_entryrule_detail detail where exists
            (select 1 from acc_temp_conf_entryrule rule where rule.ENTRY_RULE_ID = detail.ENTRY_RULE_ID and rule.batch_no = p_batch_no);

        -- 插入acc_conf_entryrule_detail
        insert into acc_conf_entryrule_detail(ENTRY_RULE_DTL_ID
            ,ENTRY_RULE_ID
            ,DISPLAY_NO
            ,ACCOUNT_ID_CR
            ,ACCOUNT_ID_DR
            ,SERIAL_NO
            ,CREATE_TIME
            ,CREATOR_ID
            ,UPDATE_TIME
            ,UPDATOR_ID
            )
        select acc_seq_entryrule_detail.nextval
            ,tmp.ENTRY_RULE_ID
            ,tmp.DISPLAY_NO
            ,tmp.ACCOUNT_ID_CR
            ,tmp.ACCOUNT_ID_DR
            ,tmp.SERIAL_NO
            ,tmp.CREATE_TIME
            ,tmp.CREATOR_ID
            ,tmp.UPDATE_TIME
            ,tmp.UPDATOR_ID
        from acc_temp_conf_entryrule_detail tmp where tmp.batch_no = p_batch_no;

        -- 插入轨迹表
        insert into ACC_CONF_ENTRYRULE_DETAIL_HIS(ENTRY_RULE_DTL_HIS_ID
            ,ENTRY_RULE_DTL_ID
            ,ENTRY_RULE_ID
            ,DISPLAY_NO
            ,ACCOUNT_ID_CR
            ,ACCOUNT_ID_DR
            ,SERIAL_NO
            ,CREATE_TIME
            ,CREATOR_ID
            ,UPDATE_TIME
            ,UPDATOR_ID
            )
        select acc_seq_entryrule_detail_his.nextval
            ,detail.ENTRY_RULE_DTL_ID
            ,detail.ENTRY_RULE_ID
            ,detail.DISPLAY_NO
            ,detail.ACCOUNT_ID_CR
            ,detail.ACCOUNT_ID_DR
            ,detail.SERIAL_NO
            ,detail.CREATE_TIME
            ,detail.CREATOR_ID
            ,detail.UPDATE_TIME
            ,detail.UPDATOR_ID
        from acc_conf_entryrule_detail detail where exists
             (select 1 from acc_temp_conf_entryrule rule where rule.ENTRY_RULE_ID = detail.ENTRY_RULE_ID and rule.batch_no = p_batch_no);

    EXCEPTION
    WHEN others THEN
     dbms_output.put_line(dbms_utility.format_error_backtrace());
     raise_application_error(-20004,SQLERRM);


    end process_data;
end ACC_PACK_DEAL_UPLOAD_SCENARIO;
/

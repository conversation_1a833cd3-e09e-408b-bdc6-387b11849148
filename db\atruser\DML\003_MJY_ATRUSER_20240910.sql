


--
-- 自动任务参数  start
delete from atruser.atr_conf_code t where exists (
    select * from atruser.atr_conf_code uc
    where uc.code_code = 'IBNR_CALC_JOB_PARAM' and uc.code_id = t.upper_code_id
);

delete from atruser.atr_conf_code t where t.code_code = 'IBNR_CALC_JOB_PARAM';



INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name,
                                   code_l_name, code_e_name, display_no, valid_is,
                                   creator_id, create_time, updator_id, update_time)
VALUES ( nextval('atr_seq_conf_code') , 0, 'IBNR_CALC_JOB_PARAM',
         'IBNR 计算自动任务的参数',
         'The parameters of the automatic task for IBNR calculation',
         'The parameters of the automatic task for IBNR calculation',
         1, '1', 1,  clock_timestamp() , 1,  clock_timestamp() );


INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name,
                                   code_l_name, code_e_name, display_no, valid_is,
                                   creator_id, create_time, updator_id, update_time)
VALUES ( nextval('atr_seq_conf_code') ,
         (select code_id from atr_conf_code where code_code = 'IBNR_CALC_JOB_PARAM'),
         'EXTRACTION_ZONES',
         '36',
         '36',
         '36', (select count(*) + 1 from atr_conf_code t, atr_conf_code uc
                where uc.code_code = 'IBNR_CALC_JOB_PARAM' and t.upper_code_id = uc.code_id),
         '1', 1,  clock_timestamp() , 1,  clock_timestamp() );


INSERT INTO atruser.atr_conf_code (code_id, upper_code_id, code_code, code_c_name,
                                   code_l_name, code_e_name, display_no, valid_is,
                                   creator_id, create_time, updator_id, update_time)
VALUES ( nextval('atr_seq_conf_code') ,
         (select code_id from atr_conf_code where code_code = 'IBNR_CALC_JOB_PARAM'),
         'CONFIRM_METHOD',
         'BF',
         'BF',
         'BF', (select count(*) + 1 from atr_conf_code t, atr_conf_code uc
                where uc.code_code = 'IBNR_CALC_JOB_PARAM' and t.upper_code_id = uc.code_id),
         '1', 1,  clock_timestamp() , 1,  clock_timestamp() );

-- 自动任务参数  end

-- 修改模型
delete from atr_conf_bussperiod_detail;
delete from atr_conf_bussperiod;

delete from atr_conf_table where biz_code = 'ATR_IBNR_CALC';

INSERT INTO atr_conf_table (biz_type_id, biz_code, type_e_name, type_l_name, type_c_name, direction,
                            valid_is, display_no, create_time,  system_code)
VALUES (19, 'ATR_IBNR_CALC', 'IBNR Calculation',
        'IBNR 計算', 'IBNR 计算', '0', '1', 0, clock_timestamp(), 'ATR');

update atr_conf_table t set direction = '2', biz_code = 'BUSS_ACTION' where type_e_name = 'Expected Cash Flows';

commit;
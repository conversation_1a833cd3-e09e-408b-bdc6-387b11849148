# AtrBussLrcTotService 内存优化总结

## 优化背景
原有的 `collectionPrePolicyInfoDetailed()` 方法将所有上期详细数据（包含sectionoCode和reinsurerCode的6维度数据）一次性加载到内存中的 `preToLrcUlsDetailed` 索引，在大数据量情况下会导致OOM问题。

## 优化方案
参考 `AtrBussLrcDdService` 中的批次处理方案，将内存索引改为分片+批量查询的方式，避免大量IO操作。

## 核心改动

### 1. 数据库层面
- **利用现有分片表**: 使用 `atr_temp_to_t_lrc_u` 表的 `pn` 字段进行分片
- **新增DAO方法**:
  - `getPartBasePolicyData()`: 获取指定分片的基础保单数据
  - `getPreDetailedDataByBatch()`: 批量获取历史详细数据

### 2. Service层面
- **添加分片配置**: `PARTITION_SIZE = 50`
- **重构计算流程**:
  - 修改 `calcIcp()` 为分片批次处理
  - 新增 `getPartPolicyData()` 获取分片数据
  - 新增 `getPreDetailedDataForBatch()` 批量获取历史数据
- **优化内存使用**:
  - 删除 `preToLrcUlsDetailed` 内存索引
  - 删除 `treatyPolicyList` 内存列表
  - 使用批量查询+内存映射的方式

### 3. 核心逻辑变更
**原逻辑**:
```java
// 一次性加载所有数据到内存
List<AtrBussToLrcTUlR> atrBussToLrcUSES = atrBussLrcToDao.listBasePolicyT();
atrBussToLrcUSES.forEach(item -> treatyPolicyList.add(item));

List<AtrBussToLrcTUlR> atrBussToLrcTUlRs = atrBussLrcToDao.listPreBussToLrcTUlRDetailed(commonParamMap);
atrBussToLrcTUlRs.forEach(item -> preToLrcUlsDetailed.add(detailedKey, item));

// 逐条处理
treatyPolicyList.forEach(this::calcIcp);
```

**新逻辑**:
```java
// 分片批次处理
for (int partNo = 0; partNo < PARTITION_SIZE; partNo++) {
    // 获取当前分片的基础数据
    List<AtrBussToLrcTUlR> partPolicyList = getPartPolicyData(paramMap);

    // 批量获取这批数据对应的历史详细数据
    Map<String, AtrBussToLrcTUlR> preDetailedDataMap = getPreDetailedDataForBatch(partPolicyList);

    // 处理当前分片的数据
    partPolicyList.forEach(icu -> calcIcp(icu, preDetailedDataMap));
}
```

## 性能优化效果

### 内存使用
- **优化前**: 所有历史详细数据常驻内存，数据量大时易OOM
- **优化后**: 只在临时表中存储，内存使用大幅降低

### 查询性能
- **优化前**: 内存查询，速度快但占用内存大
- **优化后**: 批量SQL查询，减少IO次数，内存友好

### 数据处理方式
- **优化前**: 全量加载 → 内存索引 → 逐条处理
- **优化后**: 分片处理 → 批量查询 → 内存映射 → 批次处理

## 业务逻辑保证
1. **6维度匹配**: 保持原有的6维度键匹配逻辑不变
2. **数据完整性**: 通过SQL查询确保数据的准确性
3. **计算逻辑**: `calcIcp()` 方法的业务计算逻辑完全不变

## 风险控制
1. **SQL性能**: 通过在临时表上建立适当索引来保证查询性能
2. **数据一致性**: 使用事务控制确保分片操作的原子性
3. **错误处理**: 添加完善的异常处理和日志记录

## 部署注意事项
1. **利用现有表**: 使用现有的 `atr_temp_to_t_lrc_u` 分片表，无需创建新表
2. **索引优化**: 确保历史数据表 `atr_buss_to_lrc_t_ul_r` 有适当的索引
3. **监控指标**: 关注内存使用情况和批量查询性能

## 建议的索引
```sql
-- 为历史数据表创建复合索引以优化批量查询性能
CREATE INDEX idx_buss_to_lrc_t_ul_r_key ON atr_buss_to_lrc_t_ul_r
(year_month, action_no, treaty_no, policy_no, endorse_seq_no, kind_code, sectiono_code, reinsurer_code);

-- 确保分片表有分区索引
CREATE INDEX idx_temp_to_t_lrc_u_pn ON atr_temp_to_t_lrc_u (pn);
```

## 后续优化建议
1. **缓存机制**: 可考虑为频繁查询的数据添加本地缓存
2. **批量查询**: 如果发现单条查询性能不佳，可改为批量查询+本地匹配
3. **分区表**: 考虑使用PostgreSQL的分区表特性进一步优化
4. **监控告警**: 添加内存使用和查询性能的监控告警

## 测试验证
建议进行以下测试：
1. **功能测试**: 验证计算结果与优化前完全一致
2. **性能测试**: 对比优化前后的内存使用和执行时间
3. **压力测试**: 在大数据量场景下验证不再出现OOM
4. **并发测试**: 验证多线程环境下的数据一致性

create or replace package acc_pack_buss_period is

  FUNCTION func_period_detail(p_entity_id IN NUMBER, p_book_code IN VARCHAR2,
                              p_year_month IN VARCHAR2, p_state IN VARCHAR2) RETURN NUMBER;
  PROCEDURE proc_period_execution(p_entity_id IN NUMBER, p_book_code IN VARCHAR2, p_state IN VARCHAR2);

end acc_pack_buss_period;
/
create or replace package body acc_pack_buss_period is

  FUNCTION func_period_detail(p_entity_id IN NUMBER, p_book_code IN VARCHAR2,
                              p_year_month IN VARCHAR2, p_state IN VARCHAR2) RETURN NUMBER IS
pragma autonomous_transaction;
    v_count NUMBER(8);
  BEGIN

  --新增业务期间明细
  IF p_state = '0' THEN
    INSERT INTO acc_conf_accountperiod_detail
      (period_detail_id,
       period_id,
       biz_type_id,
       system_code,
       --direction,
       ready_state,
       creator_id,
       create_time)
    SELECT acc_seq_conf_acc_prd_dtl.NEXTVAL,
           bp.period_id,
           ct.biz_type_id,
           system_code,
           --direction, --输入
           '0' ready_state,
           bp.creator_id,
           LOCALTIMESTAMP create_time
    FROM acc_conf_table ct
    LEFT JOIN acc_conf_accountperiod bp
      ON bp.entity_id = p_entity_id
     AND bp.book_code = p_book_code
     AND bp.year_month = p_year_month
    WHERE ct.valid_is = '1'
    ;
    COMMIT;
    RETURN 1;

  --业务期间己准备，所有输入数据己准备
  ELSIF p_state = '1' THEN
    SELECT COUNT(period_detail_id) INTO v_count
      FROM acc_conf_accountperiod_detail bpd
      LEFT JOIN acc_conf_accountperiod bp
        ON bp.period_id = bpd.period_id
      LEFT JOIN acc_conf_table ct
      ON ct.biz_type_id = bpd.biz_type_id
     WHERE ct.direction = '1'
       AND bpd.ready_state = '0'
       AND bp.entity_id = p_entity_id
       AND bp.book_code = p_book_code
       AND bp.year_month = p_year_month;

    --无待准备数据
    IF v_count = 0 THEN
      RETURN 1;
    ELSE
      RETURN 0;
    END IF;

  --业务期间处理中，所有输入数据己准备
  ELSIF p_state = '2' THEN
    SELECT COUNT(period_detail_id) INTO v_count
      FROM acc_conf_accountperiod_detail bpd
      LEFT JOIN acc_conf_accountperiod bp
        ON bp.period_id = bpd.period_id
      LEFT JOIN acc_conf_table ct
        ON ct.biz_type_id = bpd.biz_type_id
     WHERE ct.direction = '1'
       AND bpd.ready_state = '0'
       AND bp.entity_id = p_entity_id
       AND bp.book_code = p_book_code
       AND bp.year_month = p_year_month;

    --无待准备数据
    IF v_count = 0 THEN
      RETURN 1;
    ELSE
      RETURN 0;
    END IF;

  --业务期间已完成，所有输出数据己准备
  ELSIF p_state = '3' THEN
    SELECT COUNT(period_detail_id) INTO v_count
      FROM acc_conf_accountperiod_detail bpd
      LEFT JOIN acc_conf_accountperiod bp
        ON bp.period_id = bpd.period_id
      LEFT JOIN acc_conf_table ct
        ON ct.biz_type_id = bpd.biz_type_id
     WHERE ct.direction = '0'
       AND bpd.ready_state = '0'
       AND bp.entity_id = p_entity_id
       AND bp.book_code = p_book_code
       AND bp.year_month = p_year_month;

    --无待准备数据
    IF v_count = 0 THEN
      RETURN 1;
    ELSE
      RETURN 0;
    END IF;
  END IF;

  RETURN 0;

  EXCEPTION
  WHEN others THEN
    ROLLBACK;
    dbms_output.put_line(to_char(SQLCODE) || '::'|| SUBSTR(SQLERRM, 1, 200));
    RETURN 0;
  END func_period_detail;

  PROCEDURE proc_period_execution(p_entity_id IN NUMBER, p_book_code IN VARCHAR2, p_state IN VARCHAR2) IS

  v_year_month VARCHAR2(10);
  v_next_year_month VARCHAR2(10);
  v_detail_reday_state NUMBER(1);
 BEGIN

  --新增业务期间
  IF p_state = '0' THEN
    --检验是否存在准备中的业务期间(有且仅有一个在准备中的业务期间)
    SELECT min(year_month) INTO v_year_month
      FROM acc_conf_accountperiod
     WHERE entity_id = p_entity_id
       AND book_code = p_book_code
       AND execution_state = '0';

    IF v_year_month IS NULL THEN
      --增加一个新的业务期间
      SELECT to_char((to_date(year_month,'yyyymm') + interval '1' month),'YYYYMM') INTO v_next_year_month
        FROM acc_conf_accountperiod
       WHERE entity_id = p_entity_id
        AND book_code = p_book_code
       ORDER BY year_month DESC
       FETCH NEXT 1 rows ONLY;

      --初始数据按当前时间处理
      IF v_next_year_month IS NULL THEN
        v_next_year_month := to_char(LOCALTIMESTAMP,'YYYYMM');
      END IF;

      INSERT INTO acc_conf_accountperiod
        (period_id,
         entity_id,
         book_code,
         year_month,
         currency_code,
         execution_state,
         valid_is,
         audit_state,
         creator_id,
         create_time
        )
      VALUES
       (acc_seq_acc_conf_accountperiod.nextval,
        p_entity_id,
        p_book_code,
        v_next_year_month,
        coalesce((select p.currency_code from acc_conf_accountperiod p where p.entity_id = p_entity_id AND book_code = p_book_code AND rownum = 1),'HKD'),--todo
        '0',
        '1',
        '1',
        '1',
        LOCALTIMESTAMP
        );

      --TODO 增加准备中的输入输出明细数据
      v_detail_reday_state := acc_pack_buss_period.func_period_detail(p_entity_id, p_book_code, v_next_year_month, p_state);

    END IF;
  --业务期间己准备
  ELSIF p_state = '1' THEN
    --检验是否存在准备中业务期间
    SELECT min(year_month) INTO v_year_month
      FROM acc_conf_accountperiod
     WHERE entity_id = p_entity_id
       AND book_code = p_book_code
       AND execution_state = '0';

    IF v_year_month IS NOT NULL THEN

      --验证输入明细数据状态是否己准备
      v_detail_reday_state := acc_pack_buss_period.func_period_detail(p_entity_id, p_book_code, v_year_month, p_state);
      IF v_detail_reday_state=1 THEN
        --修改当前业务期间状态为己准备
        UPDATE acc_conf_accountperiod
          SET execution_state = '1',
              updator_id = 1,
              update_time = LOCALTIMESTAMP
        WHERE entity_id = p_entity_id
          AND book_code = p_book_code
          AND year_month = v_year_month;

        --增加下个准备中业务期间
        acc_pack_buss_period.proc_period_execution(p_entity_id, p_book_code, '0');

      END IF;
    END IF;

    --确保有下个处理中业务期间
    acc_pack_buss_period.proc_period_execution(p_entity_id, p_book_code, '2');

  --业务期间处理中
  ELSIF p_state = '2' THEN

    --循环游标， 已准备的期间会自动调整为处理中
   /* FOR rec_period IN ( SELECT year_month
      FROM acc_conf_accountperiod
      WHERE entity_id = p_entity_id
        AND book_code = p_book_code
        AND execution_state = '1'
      ORDER BY year_month) LOOP

         --验证输出明细数据状态是否己准备完成，验证成功后执行以下逻辑
        v_detail_reday_state := acc_pack_buss_period.func_period_detail(p_entity_id, p_book_code, rec_period.year_month, p_state);
        IF v_detail_reday_state=1 THEN

          --修改当前业务期间状态为处理中
          UPDATE acc_conf_accountperiod
            SET execution_state = '2',
                updator_id = 1,
                update_time = LOCALTIMESTAMP
          WHERE entity_id = p_entity_id
            AND book_code = p_book_code
            AND year_month = rec_period.year_month;
        END IF;

    END LOOP;*/

    
    --检查在处理中的业务期间
    SELECT min(year_month) INTO v_year_month
    FROM acc_conf_accountperiod
    WHERE entity_id = p_entity_id
      AND book_code = p_book_code
      AND execution_state = '2' ;

    --确保无在处理中的业务期间
    IF v_year_month IS NULL THEN
      SELECT min(year_month) INTO v_next_year_month
      FROM acc_conf_accountperiod
      WHERE entity_id = p_entity_id
        AND book_code = p_book_code
        AND execution_state = '1';

      --存在下一个己准备的业务期间,开始处理
      IF v_next_year_month IS NOT NULL THEN

        --验证输出明细数据状态是否己准备完成，验证成功后执行以下逻辑
        v_detail_reday_state := acc_pack_buss_period.func_period_detail(p_entity_id, p_book_code, v_next_year_month, p_state);
        IF v_detail_reday_state=1 THEN

          --修改当前业务期间状态为处理中
          UPDATE acc_conf_accountperiod
            SET execution_state = '2',
                updator_id = 1,
                update_time = LOCALTIMESTAMP
          WHERE entity_id = p_entity_id
            AND book_code = p_book_code
            AND year_month = v_next_year_month;
        END IF;
      END IF;
    END IF;
    
  --业务期间已完成
  ELSIF p_state = '3' THEN
    SELECT min(year_month) INTO v_year_month
    FROM acc_conf_accountperiod
    WHERE entity_id = p_entity_id
      AND book_code = p_book_code
      AND execution_state = '2';

    IF v_year_month IS NOT NULL THEN

      --验证输出明细数据状态是否己完成，验证成功后执行以下逻辑
      v_detail_reday_state := acc_pack_buss_period.func_period_detail(p_entity_id, p_book_code, v_year_month, p_state);
      IF v_detail_reday_state=1 THEN

        --修改当前业务期间状态为已完成
        UPDATE acc_conf_accountperiod
          SET execution_state = '3',
              updator_id = 1,
              update_time = LOCALTIMESTAMP
        WHERE entity_id = p_entity_id
          AND book_code = p_book_code
          AND year_month = v_year_month;

        --下一个业务期间
        v_next_year_month := to_char((to_date(v_year_month,'yyyymm') + interval '1' month),'YYYYMM');

        --检验下一个业务期间是否存在
        SELECT min(year_month) INTO v_year_month
        FROM acc_conf_accountperiod
        WHERE entity_id = p_entity_id
          AND book_code = p_book_code
          AND year_month = v_next_year_month;

        IF v_year_month IS NULL THEN
          --增加下个准备中业务期间
          acc_pack_buss_period.proc_period_execution(p_entity_id, p_book_code, '0');
        ELSE
          --修改下个业务期间状态为处理中
          acc_pack_buss_period.proc_period_execution(p_entity_id, p_book_code, '2');
        END IF;
      END IF;
    END IF;
  END IF;

  EXCEPTION
  WHEN others THEN
    --ROLLBACK;
    --RAISE EXCEPTION '(%)', SQLERRM;
    dbms_output.put_line(to_char(SQLCODE) || '::'|| SUBSTR(SQLERRM, 1, 200));
  END  proc_period_execution;

end acc_pack_buss_period;
/

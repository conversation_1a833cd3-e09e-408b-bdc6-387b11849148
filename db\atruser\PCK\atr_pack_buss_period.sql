---------- drop ---------------
do language plpgsql
$$
    declare
        rec record;
    begin
        for rec in select t.proname, t.prokind from pg_proc t where t.proname ~ '^atr_pack_buss_period_.*'
            loop
                if rec.prokind = 'f' then
                    call atr_pack_commonutils_proc_drop_func(rec.proname::varchar);
                elsif rec.prokind = 'p' then
                    call atr_pack_commonutils_proc_drop_proc(rec.proname::varchar);
                end if;
            end loop;
    end;
$$;



---------- create ---------------
CREATE FUNCTION atruser.atr_pack_buss_period_func_period_detail(p_entity_id bigint, p_year_month text, p_state text) RETURNS bigint
    LANGUAGE plpgsql
    AS $$
DECLARE


    v_count bigint;

BEGIN

    --新增业务期间明细
    IF p_state = '0' THEN


        RETURN 1;

        --业务期间己准备，所有输入数据己准备
    ELSIF p_state = '1' THEN
        SELECT COUNT(period_detail_id)
        INTO STRICT v_count
        FROM atr_conf_bussperiod_detail bpd
                 LEFT JOIN atr_conf_bussperiod bp
                           ON bp.buss_period_id = bpd.buss_period_id
                 LEFT JOIN atr_conf_table ct
                           ON ct.biz_type_id = bpd.biz_type_id
        WHERE ct.direction = '0'
          AND bpd.ready_state = '0'
          AND bp.entity_id = p_entity_id
          AND bp.year_month = p_year_month;

        --无待准备数据
        IF v_count = 0 THEN
            RETURN 1;
        ELSE
            RETURN 0;
        END IF;

        --业务期间处理中，所有输入数据己准备
    ELSIF p_state = '2' THEN
        SELECT COUNT(period_detail_id)
        INTO STRICT v_count
        FROM atr_conf_bussperiod_detail bpd
                 LEFT JOIN atr_conf_bussperiod bp
                           ON bp.buss_period_id = bpd.buss_period_id
                 LEFT JOIN atr_conf_table ct
                           ON ct.biz_type_id = bpd.biz_type_id
        WHERE ct.direction = '1'
          AND bpd.ready_state = '0'
          AND bp.entity_id = p_entity_id
          AND bp.year_month = p_year_month;

        --无待准备数据
        IF v_count = 0 THEN
            RETURN 1;
        ELSE
            RETURN 0;
        END IF;

        --业务期间已完成，所有输出数据己准备
    ELSIF p_state = '3' THEN
        SELECT COUNT(period_detail_id)
        INTO STRICT v_count
        FROM atr_conf_bussperiod_detail bpd
                 LEFT JOIN atr_conf_bussperiod bp
                           ON bp.buss_period_id = bpd.buss_period_id
                 LEFT JOIN atr_conf_table ct
                           ON ct.biz_type_id = bpd.biz_type_id
        WHERE ct.direction = '2'
          AND bpd.ready_state = '0'
          AND bp.entity_id = p_entity_id
          AND bp.year_month = p_year_month;

        --无待准备数据
        IF v_count = 0 THEN
            RETURN 1;
        ELSE
            RETURN 0;
        END IF;
    END IF;

    RETURN 0;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '%', SQLERRM;
--       RAISE NOTICE '%', dbms_utility.format_error_stack;
        RETURN 0;
END;

$$;

CREATE PROCEDURE atruser.atr_pack_buss_period_proc_period_execution(IN p_entity_id bigint, IN p_state text)
    LANGUAGE plpgsql
    AS $$
DECLARE


    v_year_month         varchar(200);
    v_next_year_month    varchar(200);
    v_detail_reday_state bigint;
    v_count              bigint;

BEGIN

    --新增业务期间
    IF p_state = '0' THEN
        --检验是否存在准备中的业务期间(有且仅有一个在准备中的业务期间)
        SELECT COUNT(year_month)
        INTO STRICT v_count
        FROM atr_conf_bussperiod
        WHERE entity_id = p_entity_id
          AND period_state = '0';

        IF v_count = 0 THEN
            --增加一个新的业务期间
            SELECT to_char(to_date(year_month, 'yyyymm') + '1 month'::interval,
                           'YYYYMM')
            INTO STRICT v_next_year_month
            FROM (SELECT *
                  from atr_conf_bussperiod
                  WHERE entity_id = p_entity_id
                  ORDER BY year_month DESC) alias2
            LIMIT 1;


            --初始数据按当前时间处理
            IF v_next_year_month IS NULL THEN
                v_next_year_month := to_char(clock_timestamp(), 'YYYYMM');
            END IF;

            INSERT INTO atr_conf_bussperiod(buss_period_id,
                                            ENTITY_ID,
                                            year_month,
                                            period_state,
                                            valid_is,
                                            creator_id,
                                            create_time)
            VALUES (nextval('atr_seq_conf_bussperiod'),
                    p_entity_id,
                    v_next_year_month,
                    '0',
                    '1',
                    '1',
                    clock_timestamp());

            --TODO 增加准备中的输入输出明细数据
            --select atr_pack_buss_period_func_period_detail(p_entity_id, v_next_year_month, p_state)
            --into v_detail_reday_state from dual;
            INSERT INTO atr_conf_bussperiod_detail(period_detail_id,
                                                   buss_period_id,
                                                   biz_type_id,
                                                   biz_code,
                                                   ready_state,
                                                   creator_id,
                                                   create_time)
            SELECT nextval('atr_seq_conf_bussperiod_detail'),
                   a.*
            FROM (SELECT bp.buss_period_id,
                         ct.biz_type_id,
                         ct.biz_code,
                         '0'               ready_state,
                         bp.creator_id,
                         clock_timestamp() create_time
                  FROM atr_conf_table ct
                           LEFT JOIN atr_conf_bussperiod bp
                                     ON bp.entity_id = p_entity_id
                                         AND bp.year_month = v_next_year_month
                  WHERE ct.valid_is = '1') a;

        END IF;
        --业务期间己准备
    ELSIF p_state = '1' THEN
        --检验是否存在准备中业务期间
        SELECT count(*)
        into v_count
        FROM atr_conf_bussperiod
        WHERE entity_id = p_entity_id
          AND period_state = '0';

        IF v_count > 0 THEN
            SELECT tt.year_month
            INTO STRICT v_year_month
            FROM (SELECT year_month
                  FROM atr_conf_bussperiod
                  WHERE entity_id = p_entity_id
                    AND period_state = '0'
                  ORDER BY year_month) tt
            LIMIT 1;

            --验证输入明细数据状态是否己准备
            SELECT atr_pack_buss_period_func_period_detail
                   (p_entity_id, v_year_month, p_state)
            INTO STRICT v_detail_reday_state;

            IF v_detail_reday_state = 1 THEN
                --修改当前业务期间状态为己准备
                UPDATE atr_conf_bussperiod
                SET period_state = '1',
                    updator_id   = 1,
                    update_time  = clock_timestamp()
                WHERE entity_id = p_entity_id
                  AND year_month = v_year_month;

                --增加下个准备中业务期间
                CALL atr_pack_buss_period_proc_period_execution(p_entity_id, '0');

            END IF;
        END IF;

        --业务期间处理中
    ELSIF p_state = '2' THEN
        --检查在处理中的业务期间
        SELECT COUNT(tt2.year_month)
        INTO STRICT v_count
        FROM (SELECT year_month
              FROM atr_conf_bussperiod
              WHERE entity_id = p_entity_id
                AND period_state = '2'
              ORDER BY year_month) tt2
        LIMIT 1;


        --确保无在处理中的业务期间
        IF v_count = 0 THEN
            select year_month
            into strict v_next_year_month
            from atr_conf_bussperiod
            where entity_id = p_entity_id
              and period_state = '1'
            order by year_month
            limit 1;

            --存在下一个己准备的业务期间,开始处理
            IF v_next_year_month IS NOT NULL THEN

                --验证输出明细数据状态是否己准备完成，验证成功后执行以下逻辑
                SELECT atr_pack_buss_period_func_period_detail
                       (p_entity_id, v_next_year_month, p_state)
                INTO STRICT v_detail_reday_state;
                IF v_detail_reday_state = 1 THEN

                    --修改当前业务期间状态为处理中
                    UPDATE atr_conf_bussperiod
                    SET period_state = '2',
                        updator_id   = 1,
                        update_time  = clock_timestamp()
                    WHERE entity_id = p_entity_id
                      AND year_month = v_next_year_month;
                END IF;
            END IF;
        END IF;

        --业务期间已完成
    ELSIF p_state = '3' THEN
        SELECT tt4.year_month
        INTO STRICT v_year_month
        FROM (SELECT year_month
              FROM atr_conf_bussperiod
              WHERE entity_id = p_entity_id
                AND period_state = '2'
              ORDER BY year_month) tt4
        LIMIT 1;

        IF v_year_month IS NOT NULL THEN

            --验证输出明细数据状态是否己完成，验证成功后执行以下逻辑
            SELECT atr_pack_buss_period_func_period_detail(p_entity_id, v_year_month, p_state)
            INTO STRICT v_detail_reday_state;
            IF v_detail_reday_state = 1 THEN

                --修改当前业务期间状态为已完成
                UPDATE atr_conf_bussperiod
                SET period_state = '3',
                    updator_id   = 1,
                    update_time  = clock_timestamp()
                WHERE entity_id = p_entity_id
                  AND year_month = v_year_month;

                --下一个业务期间
                v_next_year_month := to_char(to_date(v_year_month, 'yyyymm') + '1 month'::interval, 'YYYYMM');

                --检验下一个业务期间是否存在
                SELECT max(year_month)
                INTO STRICT v_year_month
                FROM atr_conf_bussperiod
                WHERE entity_id = p_entity_id
                  AND year_month = v_next_year_month;

                IF v_year_month IS NULL THEN
                    --增加下个准备中业务期间
                    CALL atr_pack_buss_period_proc_period_execution(p_entity_id, '0');
                ELSE
                    --修改下个业务期间状态为处理中
                    CALL atr_pack_buss_period_proc_period_execution(p_entity_id, '2');
                END IF;
            END IF;
        END IF;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        --ROLLBACK;
        RAISE NOTICE '%', SQLERRM;
--       RAISE NOTICE '%', dbms_utility.format_error_stack;
END;

$$;


--精算自动任务
DELETE FROM
	bpl_qrtz_conf_task_detail T 
WHERE
	EXISTS ( SELECT 1 FROM bpl_qrtz_conf_task qct WHERE task_group = 'ATR' AND task_c_name = '精算自动任务' AND T.conf_task_id = qct.conf_task_id ) 
	AND func_code IN ( 'BUSS_IBNR_CALC', 'BUSS_IBNR_CFM' );
	
	
INSERT INTO bpluser.bpl_qrtz_conf_task_detail (
	task_detail_id,
	conf_task_id,
	func_code,
	func_c_name,
	func_l_name,
	func_e_name,
	task_type,
	METHOD,
	serialized_param,
	proc_name,
	param,
	frequency,
	valid_is,
	create_time,
	update_time,
	creator_id,
	updator_id,
	async_is,
	relation_task_dtl_id,
	priority_no,
	retry_count,
	retry_strategy 
)
VALUES
	(
		nextval( 'bpl_seq_qrtz_conf_task' ),
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'ATR' AND task_c_name = '精算自动任务' ),
		'BUSS_IBNR_CALC',
		'IBNR分摊计算',
		'IBNR分摊计算',
		'IBNR allocation calculation',
		'M',
		'atrBussIbnrSecAllocApi.iBnrSecondaryAllocationCalc',
		'javax.servlet.http.HttpServletRequest,com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo',
		'',
		NULL,
		'M',
		'1',
		'2024-11-07 10:38:08.257',
		NULL,
		1,
		NULL,
		'0',
		( SELECT task_detail_id FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'BUSS_LRC_BECF_CFM' ),
		3,
		1,
		'Fixed' 
	);
	
INSERT INTO bpluser.bpl_qrtz_conf_task_detail (
	task_detail_id,
	conf_task_id,
	func_code,
	func_c_name,
	func_l_name,
	func_e_name,
	task_type,
	METHOD,
	serialized_param,
	proc_name,
	param,
	frequency,
	valid_is,
	create_time,
	update_time,
	creator_id,
	updator_id,
	async_is,
	relation_task_dtl_id,
	priority_no,
	retry_count,
	retry_strategy 
)
VALUES
	(
		nextval( 'bpl_seq_qrtz_conf_task' ),
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'ATR' AND task_c_name = '精算自动任务' ),
		'BUSS_IBNR_CFM',
		'IBNR分摊确认',
		'IBNR分摊确认',
		'IBNR allocation confirmation',
		'M',
		'atrBussIbnrSecAllocApi.confirmAlloc',
		'javax.servlet.http.HttpServletRequest,com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo',
		'',
		NULL,
		'M',
		'1',
		'2024-11-07 10:38:08.257',
		NULL,
		1,
		NULL,
		'0',
		( SELECT task_detail_id FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'BUSS_IBNR_CALC' ),
		4,
		1,
		'Fixed' 
	); 
	
	
UPDATE bpl_qrtz_conf_task_detail 
SET relation_task_dtl_id = ( SELECT task_detail_id FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'BUSS_IBNR_CFM' ),
priority_no = 5 
WHERE
	func_code = 'BUSS_LIC_BECF_CALC' 
	AND EXISTS ( SELECT 1 FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'BUSS_IBNR_CFM' );
	
		
UPDATE bpl_qrtz_conf_task_detail 
SET priority_no = 6 
WHERE func_code = 'BUSS_LIC_BECF_CFM' 
	AND EXISTS ( SELECT 1 FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'BUSS_LIC_BECF_CALC' );
	
	
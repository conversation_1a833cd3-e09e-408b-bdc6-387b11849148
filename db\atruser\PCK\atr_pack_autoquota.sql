---------- drop ---------------
do language plpgsql
$$
    declare
        rec record;
    begin
        for rec in select t.proname, t.prokind from pg_proc t where t.proname ~ '^atr_pack_autoquota_.*'
            loop
                if rec.prokind = 'f' then
                    call atr_pack_commonutils_proc_drop_func(rec.proname::varchar);
                elsif rec.prokind = 'p' then
                    call atr_pack_commonutils_proc_drop_proc(rec.proname::varchar);
                end if;
            end loop;
    end;
$$;



---------- create ---------------
CREATE PROCEDURE atruser.atr_pack_autoquota_proc_approve_action(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    v_period_ym   varchar(6);
    v_year_months varchar(6)[];
    v_year_month  varchar(6);
    rec           record;
begin

    select coalesce(max(t.year_month), '000000')
    into v_period_ym
    from atr_conf_bussperiod t
    where execution_state = '3';

    select string_to_array(a.appli_ev_yearmonths, ',')
    into v_year_months
    from atr_buss_autoquota_action a
    where a.action_no = p_action_no;

    for i in 1..array_length(v_year_months, 1)
        loop
            v_year_month := v_year_months[i];

            if v_year_month > v_period_ym then
                insert into atr_buss_autoquota_mapping (id, action_no, entity_id, business_source_code,
                                                        evaluate_approach, model_def_id,
                                                        loa_code, year_month, quota_def_id,
                                                        quota_code, quota_class, quota_value, oper_id, oper_time)
                select nextval('atr_seq_buss_autoquota_mapping'),
                       p_action_no,
                       a.entity_id,
                       v.business_source_code,
                       loa.evaluate_approach,
                       (select mp.model_def_id
                        from qtcuser.qtc_conf_model_plan mp
                        where mp.entity_id = loa.entity_id
                          and mp.evaluate_approach = loa.evaluate_approach
                          and mp.business_model || mp.business_direction = v.business_source_code) as model_def_id,
                       v.loa_code,
                       v_year_month,
                       d.quota_def_id,
                       v.quota_code,
                       d.quota_class,
                       v.quota_value,
                       a.oper_id,
                       clock_timestamp()
                from atr_buss_autoquota_action a,
                     atr_buss_autoquota_value v,
                     qtcuser.qtc_conf_quota_def d,
                     bpluser.bbs_conf_loa loa
                where a.action_no = p_action_no
                  and a.action_no = v.action_no
                  and v.quota_code = d.quota_code
                  and loa.entity_id = a.entity_id
                  and loa.loa_code = v.loa_code
                  and case
                          when v.business_source_code in ('DD', 'FO') then
                              loa.business_model || loa.business_direction = 'DD'
                          else
                              loa.business_model || loa.business_direction = v.business_source_code
                    end
                  and d.audit_state = '1'
                  and d.valid_is = '1'
                  and a.approval_state <> '1'
                  and d.quota_class = 'Q'
                order by v.quota_code, v.business_source_code, v.loa_code;

                insert into atr_buss_autoquota_mapping (id, action_no, entity_id, business_source_code,
                                                        loa_code, year_month, quota_def_id,
                                                        quota_code, quota_class, quota_value, oper_id, oper_time)
                select nextval('atr_seq_buss_autoquota_mapping'),
                       p_action_no,
                       a.entity_id,
                       v.business_source_code,
                       v.loa_code,
                       v_year_month,
                       d.quota_def_id,
                       v.quota_code,
                       d.quota_class,
                       v.quota_value,
                       a.oper_id,
                       clock_timestamp()
                from atr_buss_autoquota_action a,
                     atr_buss_autoquota_value v,
                     atr_conf_quota_def d
                where a.action_no = p_action_no
                  and a.action_no = v.action_no
                  and v.quota_code = d.quota_code
                  and d.audit_state = '1'
                  and d.valid_is = '1'
                  and a.approval_state <> '1'
                  and d.quota_class in ('A', 'E')
                order by v.quota_code, v.business_source_code, v.loa_code;

                for rec in (select *
                            from atr_buss_autoquota_mapping t
                            where t.action_no = p_action_no
                              and t.quota_value <> 0
                            order by t.id)
                    loop
                        if rec.quota_class = 'Q' then

                            merge into qtcuser.qtc_conf_quota t
                            using (select 1) x
                            on (t.quota_def_id = rec.quota_def_id
                                and t.model_def_id = rec.model_def_id
                                and t.dimension = 'G'
                                and t.dimension_value = rec.loa_code
                                and t.quota_class = rec.quota_class
                                and t.year_month = v_year_month)
                            when matched then
                                update
                                set quota_value = rec.quota_value,
                                    updator_id  = rec.oper_id,
                                    update_time = clock_timestamp()
                            when not matched then
                                insert (quota_id, entity_id, model_def_id, quota_class,
                                        dimension, dimension_value, year_month, quota_def_id,
                                        quota_value, serial_no, valid_is, audit_state, checked_msg,
                                        checked_id, checked_time, creator_id, create_time, updator_id, update_time,
                                        loa_code)
                                values (nextval('qtcuser.qtc_seq_conf_quota'), rec.entity_id, rec.model_def_id,
                                        rec.quota_class, 'G', rec.loa_code, v_year_month, rec.quota_def_id,
                                        rec.quota_value,
                                        1, '1', '1', default, default, default, rec.oper_id, clock_timestamp(),
                                        rec.oper_id,
                                        clock_timestamp(), rec.loa_code);

                        else

                            merge into atr_conf_quota t
                            using (select 1) x
                            on (t.quota_def_id = rec.quota_def_id
                                and t.business_source_code = rec.business_source_code
                                and t.dimension = 'G'
                                and t.dimension_value = rec.loa_code
                                and t.quota_class = rec.quota_class
                                and t.year_month = v_year_month)
                            when matched then
                                update
                                set quota_value = rec.quota_value,
                                    updator_id  = rec.oper_id,
                                    update_time = clock_timestamp()
                            when not matched then
                                insert (quota_id, entity_id, business_source_code, quota_class,
                                        dimension, dimension_value, year_month, quota_def_id,
                                        quota_value, serial_no, valid_is, audit_state, checked_msg,
                                        checked_id, checked_time, creator_id, create_time, updator_id, update_time,
                                        loa_code)
                                values (nextval('atr_seq_conf_quota'), rec.entity_id, rec.business_source_code,
                                        rec.quota_class, 'G', rec.loa_code, v_year_month, rec.quota_def_id,
                                        rec.quota_value,
                                        1, '1', '1', default, default, default, rec.oper_id, clock_timestamp(),
                                        rec.oper_id,
                                        clock_timestamp(), rec.loa_code);

                        end if;

                    end loop;


            end if;

        end loop;

    commit;
end
$$;

CREATE PROCEDURE atruser.atr_pack_autoquota_proc_log(IN p_action_no character varying, IN p_mark character varying)
    LANGUAGE plpgsql
    AS $$

begin

    insert into atr_log_autoquota (id, action_no, create_time, mark)
    values (nextval('atr_seq_log_autoquota'), p_action_no, clock_timestamp(), p_mark);

    commit;

end
$$;

CREATE PROCEDURE atruser.atr_pack_autoquota_proc_calc(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    rec           record;
    rec2          record;
    v_quota_codes varchar(20)[];
    v_quota_code  varchar(20);
    v_start_year  varchar(4);
    v_end_year    varchar(4);
begin
    select * into rec from atr_buss_autoquota_action t where t.action_no = p_action_no;

    v_start_year := to_char(rec.deadline - (rec.draw_interval_quantity - 1) * '1 year'::interval, 'yyyy');
    v_end_year := to_char(rec.deadline, 'yyyy');

    call atr_pack_autoquota_proc_log(p_action_no, 'init-buss_value');

    -- 计算业务值初始化
    insert into atr_buss_autoquota_buss_value (id, action_no, business_source_code, loa_code, buss_year,
                                               offset_years, end_year_is)
    select nextval('atr_seq_buss_autoquota_buss_value')               as id,
           p_action_no,
           b.business_source_code,
           b.loa_code,
           y.buss_year,
           y.buss_year::int - v_end_year::int                         as offset_years,
           (case when y.buss_year = v_end_year then '1' else '0' end) as end_year_is
    from (select distinct substr(year_month, 1, 4) as buss_year from atr_conf_year_month) y,
         (select distinct x.business_source_code, x.loa_code
          from (select business_source_code, loa_code
                from atr_dap_autoquota_unit_premium
                where action_no = p_action_no
                union
                select business_source_code, loa_code
                from atr_dap_autoquota_unit_settled
                where action_no = p_action_no
                union
                select business_source_code, loa_code
                from atr_dap_autoquota_unit_os
                where action_no = p_action_no
                union
                select business_source_code, loa_code
                from atr_dap_autoquota_account
                where action_no = p_action_no) x) b
    where y.buss_year >= v_start_year
      and y.buss_year <= v_end_year
    order by b.business_source_code, b.loa_code, y.buss_year desc;

    call atr_pack_autoquota_proc_log(p_action_no, 'set-buss_value');

    update atr_buss_autoquota_buss_value t
    set gross_premium       = coalesce((select sum(u.gross_premium)
                                        from atr_dap_autoquota_unit_premium u
                                        where u.action_no = t.action_no
                                          and u.business_source_code = t.business_source_code
                                          and u.loa_code = t.loa_code
                                          and u.buss_year = t.buss_year), 0),
        net_premium         = coalesce((select sum(coalesce(u.gross_premium, 0)
            - coalesce(u.commission_amount, 0)
            - coalesce(u.discount_amount, 0))
                                        from atr_dap_autoquota_unit_premium u
                                        where u.action_no = t.action_no
                                          and u.business_source_code = t.business_source_code
                                          and u.loa_code = t.loa_code
                                          and u.buss_year = t.buss_year), 0),
        settled_claim       = coalesce((select sum(u.settled_claim)
                                        from atr_dap_autoquota_unit_settled u
                                        where u.action_no = t.action_no
                                          and u.business_source_code = t.business_source_code
                                          and u.loa_code = t.loa_code
                                          and u.buss_year = t.buss_year), 0),
        settled_expense     = coalesce((select sum(u.settled_expense)
                                        from atr_dap_autoquota_unit_settled u
                                        where u.action_no = t.action_no
                                          and u.business_source_code = t.business_source_code
                                          and u.loa_code = t.loa_code
                                          and u.buss_year = t.buss_year), 0),
        os_claim            = coalesce((select sum(u.os_claim)
                                        from atr_dap_autoquota_unit_os u
                                        where u.action_no = t.action_no
                                          and u.business_source_code = t.business_source_code
                                          and u.loa_code = t.loa_code
                                          and u.buss_year = t.buss_year), 0),
        os_expense          = coalesce((select sum(u.os_expense)
                                        from atr_dap_autoquota_unit_os u
                                        where u.action_no = t.action_no
                                          and u.business_source_code = t.business_source_code
                                          and u.loa_code = t.loa_code
                                          and u.buss_year = t.buss_year), 0),
        non_acq_expense     = coalesce((
                                           case t.business_source_code
                                               when 'DD' then
                                                   (select sum(a.amount)
                                                    from atr_dap_autoquota_account a
                                                    where a.business_source_code = t.business_source_code
                                                      and a.loa_code = t.loa_code
                                                      and substr(a.year_month, 1, 4) = t.buss_year
                                                      and a.upper_account_code = 'NonAcqExpense'
                                                      and a.action_no = p_action_no)
                                               end
                                           ), 0),
        maintenance_expense = coalesce((
                                           case t.business_source_code
                                               when 'DD' then
                                                   (select sum(a.amount)
                                                    from atr_dap_autoquota_account a
                                                    where a.business_source_code = t.business_source_code
                                                      and a.loa_code = t.loa_code
                                                      and substr(a.year_month, 1, 4) = t.buss_year
                                                      and a.upper_account_code = 'MaintenanceExpense'
                                                      and a.action_no = p_action_no)
                                               end
                                           ), 0)
    where t.action_no = p_action_no;

    call atr_pack_autoquota_proc_log(p_action_no, 'calc-quota');

    v_quota_codes := string_to_array(rec.appli_quota_codes, ',');

    for i in 1..array_length(v_quota_codes, 1)
        loop
            v_quota_code := v_quota_codes[i];

            for rec2 in (select a.business_source_code,
                                a.loa_code,
                                sum(
                                        (
                                            case
                                                when v_quota_code = 'BE013' then
                                                    -- EXPECTED LOSS RATIO (BE013)
                                                    (
                                                        case
                                                            when a.net_premium = 0 then 0
                                                            else
                                                                -(case
                                                                      when a.end_year_is = '0' then
                                                                          a.settled_claim
                                                                      else
                                                                          a.settled_claim + a.os_claim
                                                                    end) / a.net_premium
                                                            end
                                                        )
                                                when v_quota_code = 'BE010' then
                                                    -- ULAE O/S  BE010
                                                    (
                                                        case
                                                            when a.os_claim = 0 then
                                                                0
                                                            else
                                                                a.os_expense / a.os_claim
                                                            end
                                                        )
                                                when v_quota_code = 'QR012' then
                                                    -- ULAE IBNR   QR012
                                                    (
                                                        case
                                                            when a.settled_claim = 0 then
                                                                0
                                                            else
                                                                a.settled_expense / a.settled_claim
                                                            end
                                                        )
                                                when v_quota_code = 'QR002' then
                                                    -- Non-policy Insurance Acquisition Expense Rate (QR002)
                                                    (case
                                                         when a.gross_premium = 0 then
                                                             0
                                                         else
                                                             a.non_acq_expense / a.gross_premium
                                                        end)
                                                when v_quota_code = 'QR003' then
                                                    -- Expected Maintenance Expense Rate (QR003)
                                                    (case
                                                         when a.gross_premium = 0 then
                                                             0
                                                         else
                                                             a.maintenance_expense / a.gross_premium
                                                        end)
                                                end
                                            ) * (
                                            case
                                                when exists (select 1
                                                             from atr_buss_autoquota_weight wt
                                                             where wt.action_no = p_action_no
                                                               and wt.business_source_code = a.business_source_code
                                                               and wt.loa_code = a.loa_code
                                                               and wt.weight_value <> 0) then
                                                    coalesce((select wt.weight_value
                                                              from atr_buss_autoquota_weight wt
                                                              where wt.action_no = p_action_no
                                                                and wt.business_source_code = a.business_source_code
                                                                and wt.loa_code = a.loa_code
                                                                and wt.offset_years = a.offset_years),
                                                             0)
                                                else
                                                    -- 如果没有设置权重， 则平均
                                                    (
                                                        case
                                                            when a.offset_years = 0 then
                                                                -- 尾差
                                                                1 -
                                                                round(1.0 / rec.draw_interval_quantity, 8) *
                                                                (rec.draw_interval_quantity - 1)
                                                            else
                                                                round(1.0 / rec.draw_interval_quantity, 8)
                                                            end
                                                        )
                                                end
                                            )
                                ) quota_value
                         from atr_buss_autoquota_buss_value a
                         where a.action_no = p_action_no
                         group by a.business_source_code, a.loa_code
                         order by a.business_source_code, a.loa_code)
                loop

                    if rec2.quota_value is not null then
                        insert into atr_buss_autoquota_value (id, action_no, business_source_code,
                                                              loa_code, quota_code, quota_value)
                        values (nextval('atr_seq_buss_autoquota_value'), p_action_no, rec2.business_source_code,
                                rec2.loa_code, v_quota_code, rec2.quota_value);

                    end if;

                end loop;
        end loop;

end;
$$;

CREATE PROCEDURE atruser.atr_pack_autoquota_proc_delete_action(IN p_id bigint)
    LANGUAGE plpgsql
    AS $$
declare
    v_action_no varchar(100);
begin

    select t.action_no into v_action_no from atr_buss_autoquota_action t where t.id = p_id;

    delete from atr_buss_autoquota_value t where t.action_no = v_action_no;
    delete from atr_buss_autoquota_buss_value t where t.action_no = v_action_no;
    delete from atr_buss_autoquota_weight t where t.action_no = v_action_no;
    delete from atr_buss_autoquota_mapping t where t.action_no = v_action_no;
    delete from atr_buss_autoquota_action t where t.action_no = v_action_no;

end;
$$;

CREATE PROCEDURE atruser.atr_pack_autoquota_proc_fetch_data(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    rec                           record;
    v_business_source_codes       varchar(2)[];
    v_business_source_code        varchar(2);
    v_start_year_bom              date;
    v_start_year_month            varchar(6);
    v_end_year_month              varchar(6);
    v_nextday_of_deadline         date;
    v_base_currency               varchar(3);
    v_code_id_settled_claim_af    numeric(11);
    v_code_id_settled_claim_bf    numeric(11);
    v_code_id_settled_expense_af  numeric(11);
    v_code_id_settled_expense_bf  numeric(11);
    v_code_id_os_claim_af         numeric(11);
    v_code_id_os_claim_bf         numeric(11);
    v_code_id_os_expense_af       numeric(11);
    v_code_id_os_expense_bf       numeric(11);
    v_code_id_non_acq_expense     numeric(11);
    v_code_id_maintenance_expense numeric(11);
begin

    select t.code_id into v_code_id_settled_claim_af from atr_conf_code t where t.code_code = 'SettledClaimAF';
    select t.code_id into v_code_id_settled_claim_bf from atr_conf_code t where t.code_code = 'SettledClaimBF';
    select t.code_id into v_code_id_settled_expense_af from atr_conf_code t where t.code_code = 'SettledExpenseAF';
    select t.code_id into v_code_id_settled_expense_bf from atr_conf_code t where t.code_code = 'SettledExpenseBF';
    select t.code_id into v_code_id_os_claim_af from atr_conf_code t where t.code_code = 'OsClaimAF';
    select t.code_id into v_code_id_os_claim_bf from atr_conf_code t where t.code_code = 'OsClaimBF';
    select t.code_id into v_code_id_os_expense_af from atr_conf_code t where t.code_code = 'OsExpenseAF';
    select t.code_id into v_code_id_os_expense_bf from atr_conf_code t where t.code_code = 'OsExpenseBF';
    select t.code_id into v_code_id_non_acq_expense from atr_conf_code t where t.code_code = 'NonAcqExpense';
    select t.code_id into v_code_id_maintenance_expense from atr_conf_code t where t.code_code = 'MaintenanceExpense';

    select * into rec from atr_buss_autoquota_action t where t.action_no = p_action_no;

    select t.currency_code
    into strict v_base_currency
    from bpluser.bbs_conf_account_set t
    where t.entity_id = rec.entity_id;

    if v_base_currency is null then
        raise '没有配置本位币';
    end if;

    v_nextday_of_deadline := date_trunc('day', rec.deadline + '1 day'::interval);
    v_start_year_bom :=
            date_trunc('year', rec.deadline - (rec.draw_interval_quantity - 1) * '1 year'::interval);
    v_start_year_month := to_char(v_start_year_bom, 'yyyymm');
    v_end_year_month := to_char(rec.deadline, 'yyyymm');

    if length(rec.business_source_code) > 0 then
        v_business_source_codes := string_to_array(rec.business_source_code, ',');
    else
        v_business_source_codes := string_to_array('DD,FO,TO', ',');
    end if;


    for i in 1 .. array_length(v_business_source_codes, 1)
        loop
            v_business_source_code := v_business_source_codes[i];

            if v_business_source_code = 'DD' then
                call atr_pack_autoquota_proc_log(p_action_no, 'fetch-premium-DD');

                -- 保费
                insert into atr_dap_autoquota_unit_premium (id, action_no, business_source_code, policy_no,
                                                            endorse_seq_no, loa_code,
                                                            portfolio_no, icg_no, buss_year,
                                                            effective_date, expiry_date, uw_approval_date,
                                                            gross_premium, commission_amount, discount_amount)

                select nextval('atr_seq_dap_autoquota_unit_premium') as id,
                       p_action_no,
                       v_business_source_code,
                       t.policy_no,
                       t.endorse_seq_no,
                       c.loa_code,
                       c.portfolio_no,
                       c.icg_no,
                       to_char(t.effective_date::date, 'yyyy')       as buss_year,
                       t.effective_date::date                        as effective_date,
                       t.expiry_date::date                           as expiry_date,
                       t.approval_date::date                         as uw_approval_date,
                       (select sum(p.premium)
                        from dmuser.dm_policy_premium p
                        where p.entity_id = rec.entity_id
                          and t.policy_no = p.policy_no
                          and t.endorse_seq_no = p.endorse_seq_no)   as gross_premium,
                       (select sum(p.commission_amount)
                        from dmuser.dm_policy_premium p
                        where p.entity_id = rec.entity_id
                          and t.policy_no = p.policy_no
                          and t.endorse_seq_no = p.endorse_seq_no)   as commission_amount,
                       (select sum(p.discount_amount * 1)
                        from dmuser.dm_policy_premium p
                        where p.entity_id = rec.entity_id
                          and t.policy_no = p.policy_no
                          and t.endorse_seq_no = p.endorse_seq_no)   as discount_amount
                from dmuser.dm_policy_main t,
                     dmuser.dm_buss_cmunit_direct c
                where t.effective_date >= v_start_year_bom
                  and t.effective_date < v_nextday_of_deadline
                  and (rec.loa_code is null or rec.loa_code = '' or c.loa_code = rec.loa_code)
                  and t.entity_id = rec.entity_id
                  and t.entity_id = c.entity_id
                  and t.policy_no = c.policy_no
                  and c.year_month is not null
                group by t.policy_no,
                         t.endorse_seq_no,
                         c.loa_code,
                         c.portfolio_no,
                         c.icg_no,
                         to_char(t.effective_date, 'yyyy'),
                         t.effective_date::date,
                         t.expiry_date::date,
                         t.approval_date::date
                order by t.policy_no, t.endorse_seq_no, t.effective_date::date;

                call atr_pack_autoquota_proc_log(p_action_no, 'fetch-settled-DD');

                -- 已决
                insert into atr_dap_autoquota_unit_settled (id, action_no, business_source_code, policy_no,
                                                            loa_code, portfolio_no, icg_no, buss_year,
                                                            claim_approval_date, settled_claim, settled_expense)
                select nextval('atr_seq_dap_autoquota_unit_settled') as id,
                       p_action_no,
                       v_business_source_code,
                       t.policy_no,
                       c.loa_code,
                       c.portfolio_no,
                       c.icg_no,
                       to_char(t.approval_date::date, 'yyyy')        as buss_year,
                       t.approval_date::date                         as claim_approval_date,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_settled_claim_bf
                                              and co.code_code = t.expenses_type_code) then t.amount
                           end)                                      as settled_claim,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_settled_expense_bf
                                              and co.code_code = t.expenses_type_code) then t.amount
                           end)                                      as settled_expense
                from dmuser.dm_claim_loss_detail t,
                     dmuser.dm_buss_cmunit_direct c
                where t.approval_date >= v_start_year_bom
                  and t.approval_date < v_nextday_of_deadline
                  and (rec.loa_code is null or rec.loa_code = '' or c.loa_code = rec.loa_code)
                  and t.entity_id = rec.entity_id
                  and t.entity_id = c.entity_id
                  and t.policy_no = c.policy_no
                  and c.year_month is not null
                group by t.policy_no,
                         c.loa_code,
                         c.portfolio_no,
                         c.icg_no,
                         t.approval_date::date
                order by t.policy_no, t.approval_date::date;

                call atr_pack_autoquota_proc_log(p_action_no, 'fetch-os-DD');

                -- 未决
                insert into atr_dap_autoquota_unit_os (id, action_no, business_source_code, policy_no,
                                                       loa_code, portfolio_no, icg_no,
                                                       buss_year, os_year_month, os_claim, os_expense)
                select nextval('atr_seq_dap_autoquota_unit_os') as id,
                       p_action_no,
                       v_business_source_code,
                       t.policy_no,
                       c.loa_code,
                       c.portfolio_no,
                       c.icg_no,
                       substr(t.year || t.month, 1, 4)          as buss_year,
                       t.year || t.month                        as os_year_month,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_os_claim_bf
                                              and co.code_code = t.expenses_type_code) then t.outstanding_amount
                           end)                                 as os_claim,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_os_expense_bf
                                              and co.code_code = t.expenses_type_code) then t.outstanding_amount
                           end)                                 as os_expense
                from dmuser.dm_claim_outstanding t,
                     dmuser.dm_buss_cmunit_direct c
                where t.year || lpad(t.month, 2, '0') >= v_start_year_month
                  and t.year || lpad(t.month, 2, '0') <= v_end_year_month
                  and (t.month = '12' or t.year || lpad(t.month, 2, '0') = v_end_year_month)
                  and t.business_type_code = 'BF'
                  and (rec.loa_code is null or rec.loa_code = '' or c.loa_code = rec.loa_code)
                  and t.entity_id = rec.entity_id
                  and t.entity_id = c.entity_id
                  and t.policy_no = c.policy_no
                  and c.year_month is not null
                group by t.policy_no,
                         c.loa_code,
                         c.portfolio_no,
                         c.icg_no,
                         t.year || t.month
                order by t.policy_no, t.year || t.month;

            elsif v_business_source_code = 'FO' then
                call atr_pack_autoquota_proc_log(p_action_no, 'fetch-premium-FO');

                -- 保费
                insert into atr_dap_autoquota_unit_premium (id, action_no, business_source_code,
                                                            ri_policy_no, treaty_no, loa_code,
                                                            portfolio_no, icg_no, buss_year,
                                                            gl_posting_date, gross_premium, commission_amount)
                select nextval('atr_seq_dap_autoquota_unit_premium')             as id,
                       p_action_no,
                       v_business_source_code,
                       t.ri_policy_no,
                       d.treaty_no,
                       cm.loa_code,
                       cm.portfolio_no,
                       cm.icg_no,
                       to_char(d.gl_posting_date::date, 'yyyy')                  as buss_year,
                       d.gl_posting_date::date                                   as gl_posting_date,
                       sum(case when mp.fee_class = 'Premium' then d.amount end) as gross_premium,
                       sum(case when mp.fee_class = 'Comm' then d.amount end)    as commission_amount
                from dmuser.dm_buss_cmunit_fac_outwards cm,
                     dmuser.dm_reins_outward t,
                     dmuser.dm_reins_outward_detail d,
                     bpluser.bbs_conf_fee_type_mapping mp
                where cm.entity_id = t.entity_id
                  and cm.fac_no = t.ri_policy_no
                  and cm.year_month is not null
                  and (rec.loa_code is null or rec.loa_code = '' or rec.loa_code = cm.loa_code)
                  and t.entity_id = d.entity_id
                  and t.ri_policy_no = d.ri_policy_no
                  and t.ri_endorse_seq_no = d.ri_endorse_seq_no
                  and mp.business_source_code = 'FO'
                  and d.expenses_type_code = mp.expenses_type_code
                  and mp.fee_class in ('Premium', 'Comm')
                  and t.entity_id = rec.entity_id
                  and d.gl_posting_date >= v_start_year_bom
                  and d.gl_posting_date < v_nextday_of_deadline
                group by t.ri_policy_no,
                         d.treaty_no,
                         cm.loa_code,
                         cm.portfolio_no,
                         cm.icg_no,
                         d.gl_posting_date::date
                order by t.ri_policy_no,
                         d.treaty_no,
                         d.gl_posting_date::date;

                call atr_pack_autoquota_proc_log(p_action_no, 'fetch-settled-FO');

                -- 已决
                insert into atr_dap_autoquota_unit_settled (id, action_no, business_source_code,
                                                            ri_policy_no,
                                                            treaty_no, loa_code, portfolio_no, icg_no,
                                                            buss_year, statement_approval_date,
                                                            settled_claim, settled_expense)
                select nextval('atr_seq_dap_autoquota_unit_settled') as id,
                       p_action_no,
                       v_business_source_code,
                       t.ri_policy_no,
                       d.treaty_no,
                       cm.loa_code,
                       cm.portfolio_no,
                       cm.icg_no,
                       to_char(d.gl_posting_date::date, 'yyyy')      as buss_year,
                       d.gl_posting_date::date                       as gl_posting_date,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_settled_claim_af
                                              and co.code_code = d.expenses_type_code) then d.amount
                           end)                                      as settled_claim,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_settled_expense_af
                                              and co.code_code = d.expenses_type_code) then d.amount
                           end)                                      as settled_expense
                from dmuser.dm_buss_cmunit_fac_outwards cm,
                     dmuser.dm_reins_outward t,
                     dmuser.dm_reins_outward_detail d,
                     bpluser.bbs_conf_fee_type_mapping mp
                where cm.entity_id = t.entity_id
                  and cm.fac_no = t.ri_policy_no
                  and cm.year_month is not null
                  and (rec.loa_code is null or rec.loa_code = '' or rec.loa_code = cm.loa_code)
                  and t.entity_id = d.entity_id
                  and t.ri_policy_no = d.ri_policy_no
                  and t.ri_endorse_seq_no = d.ri_endorse_seq_no
                  and mp.business_source_code = 'FO'
                  and d.expenses_type_code = mp.expenses_type_code
                  and mp.fee_class in ('Claim')
                  and t.entity_id = rec.entity_id
                  and d.gl_posting_date >= v_start_year_bom
                  and d.gl_posting_date < v_nextday_of_deadline
                group by t.ri_policy_no,
                         d.treaty_no,
                         cm.loa_code,
                         cm.portfolio_no,
                         cm.icg_no,
                         d.gl_posting_date::date
                order by t.ri_policy_no,
                         d.treaty_no,
                         d.gl_posting_date::date;

                call atr_pack_autoquota_proc_log(p_action_no, 'fetch-os-FO');

                -- 未决
                insert into atr_dap_autoquota_unit_os (id, action_no, business_source_code, ri_policy_no,
                                                       treaty_no, loa_code, portfolio_no, icg_no,
                                                       buss_year, os_year_month, os_claim, os_expense)
                select nextval('atr_seq_dap_autoquota_unit_os') as id,
                       p_action_no,
                       v_business_source_code,
                       t.ri_policy_no,
                       t.reference_no                           as treaty_no,
                       c.loa_code,
                       c.portfolio_no,
                       c.icg_no,
                       substr(t.year || t.month, 1, 4)          as buss_year,
                       t.year || t.month                        as os_year_month,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_os_claim_af
                                              and co.code_code = t.expenses_type_code) then t.outstanding_amount
                           end)                                 as os_claim,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_os_expense_af
                                              and co.code_code = t.expenses_type_code) then t.outstanding_amount
                           end)                                 as os_expense
                from dmuser.dm_claim_outstanding t,
                     dmuser.dm_buss_cmunit_fac_outwards c
                where t.year || lpad(t.month, 2, '0') >= v_start_year_month
                  and t.year || lpad(t.month, 2, '0') <= v_end_year_month
                  and (t.month = '12' or t.year || lpad(t.month, 2, '0') = v_end_year_month)
                  and t.business_type_code = 'AF'
                  and (rec.loa_code is null or rec.loa_code = '' or c.loa_code = rec.loa_code)
                  and t.entity_id = rec.entity_id
                  and t.entity_id = c.entity_id
                  and t.ri_policy_no = c.fac_no
                  and c.year_month is not null
                group by t.ri_policy_no,
                         t.reference_no,
                         c.loa_code,
                         c.portfolio_no,
                         c.icg_no,
                         t.year || t.month
                order by t.ri_policy_no, t.reference_no, t.year || t.month;

            elsif v_business_source_code = 'TO' then
                call atr_pack_autoquota_proc_log(p_action_no, 'fetch-premium-TO');

                -- 保费
                insert into atr_dap_autoquota_unit_premium (id, action_no, business_source_code,
                                                            ri_policy_no, treaty_no, loa_code,
                                                            portfolio_no, icg_no, buss_year,
                                                            gl_posting_date, gross_premium, commission_amount)
                select nextval('atr_seq_dap_autoquota_unit_premium')             as id,
                       p_action_no,
                       v_business_source_code,
                       t.ri_policy_no,
                       d.treaty_no,
                       cm.loa_code,
                       cm.portfolio_no,
                       cm.icg_no,
                       to_char(d.gl_posting_date::date, 'yyyy')                  as buss_year,
                       d.gl_posting_date::date                                   as gl_posting_date,
                       sum(case when mp.fee_class = 'Premium' then d.amount end) as gross_premium,
                       sum(case when mp.fee_class = 'Comm' then d.amount end)    as commission_amount
                from dmuser.dm_buss_cmunit_treaty cm,
                     dmuser.dm_reins_outward t,
                     dmuser.dm_reins_outward_detail d,
                     bpluser.bbs_conf_fee_type_mapping mp
                where cm.entity_id = t.entity_id
                  and cm.treaty_no = d.treaty_no
                  and cm.ri_direction_code = 'O'
                  and cm.year_month is not null
                  and (rec.loa_code is null or rec.loa_code = '' or rec.loa_code = cm.loa_code)
                  and t.entity_id = d.entity_id
                  and t.ri_policy_no = d.ri_policy_no
                  and t.ri_endorse_seq_no = d.ri_endorse_seq_no
                  and mp.business_source_code = 'TO'
                  and d.expenses_type_code = mp.expenses_type_code
                  and mp.fee_class in ('Premium', 'Comm')
                  and t.entity_id = rec.entity_id
                  and d.gl_posting_date >= v_start_year_bom
                  and d.gl_posting_date < v_nextday_of_deadline
                group by t.ri_policy_no,
                         d.treaty_no,
                         cm.loa_code,
                         cm.portfolio_no,
                         cm.icg_no,
                         d.gl_posting_date::date
                order by t.ri_policy_no,
                         d.treaty_no,
                         d.gl_posting_date::date;

                call atr_pack_autoquota_proc_log(p_action_no, 'fetch-settled-TO');

                -- 已决
                insert into atr_dap_autoquota_unit_settled (id, action_no, business_source_code, ri_policy_no,
                                                            treaty_no, loa_code, portfolio_no, icg_no,
                                                            buss_year,
                                                            gl_posting_date, settled_claim, settled_expense)
                select nextval('atr_seq_dap_autoquota_unit_settled') as id,
                       p_action_no,
                       v_business_source_code,
                       t.ri_policy_no,
                       d.treaty_no,
                       cm.loa_code,
                       cm.portfolio_no,
                       cm.icg_no,
                       to_char(d.gl_posting_date::date, 'yyyy')      as buss_year,
                       d.gl_posting_date::date                       as gl_posting_date,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_settled_claim_af
                                              and co.code_code = d.expenses_type_code) then d.amount
                           end)                                      as settled_claim,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_settled_expense_af
                                              and co.code_code = d.expenses_type_code) then d.amount
                           end)                                      as settled_expense
                from dmuser.dm_buss_cmunit_treaty cm,
                     dmuser.dm_reins_outward t,
                     dmuser.dm_reins_outward_detail d,
                     bpluser.bbs_conf_fee_type_mapping mp
                where cm.entity_id = t.entity_id
                  and cm.treaty_no = d.treaty_no
                  and cm.year_month is not null
                  and (rec.loa_code is null or rec.loa_code = '' or rec.loa_code = cm.loa_code)
                  and t.entity_id = d.entity_id
                  and t.ri_policy_no = d.ri_policy_no
                  and t.ri_endorse_seq_no = d.ri_endorse_seq_no
                  and mp.business_source_code = 'TO'
                  and d.expenses_type_code = mp.expenses_type_code
                  and mp.fee_class in ('Claim')
                  and t.entity_id = rec.entity_id
                  and d.gl_posting_date >= v_start_year_bom
                  and d.gl_posting_date < v_nextday_of_deadline
                group by t.ri_policy_no,
                         d.treaty_no,
                         cm.loa_code,
                         cm.portfolio_no,
                         cm.icg_no,
                         d.gl_posting_date::date
                order by t.ri_policy_no,
                         d.treaty_no,
                         d.gl_posting_date::date;

                call atr_pack_autoquota_proc_log(p_action_no, 'fetch-os-TO');

                -- 未决
                insert into atr_dap_autoquota_unit_os (id, action_no, business_source_code, ri_policy_no,
                                                       treaty_no, loa_code, portfolio_no, icg_no,
                                                       buss_year, os_year_month, os_claim, os_expense)
                select nextval('atr_seq_dap_autoquota_unit_os') as id,
                       p_action_no,
                       v_business_source_code,
                       t.ri_policy_no,
                       t.reference_no                           as treaty_no,
                       c.loa_code,
                       c.portfolio_no,
                       c.icg_no,
                       substr(t.year || t.month, 1, 4)          as buss_year,
                       t.year || t.month                        as os_year_month,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_os_claim_af
                                              and co.code_code = t.expenses_type_code) then t.outstanding_amount
                           end)                                 as os_claim,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code co
                                            where co.upper_code_id = v_code_id_os_expense_af
                                              and co.code_code = t.expenses_type_code) then t.outstanding_amount
                           end)                                 as os_expense
                from dmuser.dm_claim_outstanding t,
                     dmuser.dm_buss_cmunit_treaty c
                where t.year || lpad(t.month, 2, '0') >= v_start_year_month
                  and t.year || lpad(t.month, 2, '0') <= v_end_year_month
                  and (t.month = '12' or t.year || lpad(t.month, 2, '0') = v_end_year_month)
                  and t.business_type_code = 'AF'
                  and (rec.loa_code is null or rec.loa_code = '' or c.loa_code = rec.loa_code)
                  and t.entity_id = rec.entity_id
                  and t.entity_id = c.entity_id
                  and t.reference_no = c.treaty_no
                  and c.ri_direction_code = 'O'
                  and c.year_month is not null
                group by t.ri_policy_no,
                         t.reference_no,
                         c.loa_code,
                         c.portfolio_no,
                         c.icg_no,
                         t.year || t.month
                order by t.ri_policy_no, t.reference_no, t.year || t.month;


            end if;

        end loop;

    call atr_pack_autoquota_proc_log(p_action_no, 'fetch-account');

    -- 科目
    for i in 1 .. array_length(v_business_source_codes, 1)
        loop
            v_business_source_code := v_business_source_codes[i];

            if v_business_source_code = 'DD' then

                insert into atr_dap_autoquota_account (id, action_no, business_source_code,
                                                       loa_code, year_month, account_code, upper_account_code,
                                                       currency_code, amount)
                select nextval('atr_seq_dap_autoquota_account') as id,
                       p_action_no,
                       v_business_source_code,
                       co.code_e_name                           as loa_code,
                       t.year_month,
                       t.account_code,
                       uc.code_code                             as upper_account_code,
                       t.currency_code,
                       t.closing_balance                        as amount
                from dmuser.dm_fin_ledger_balance t,
                     atr_conf_code co,
                     atr_conf_code uc
                where t.entity_id = rec.entity_id
                  and t.account_code = co.code_code
                  and co.upper_code_id = uc.code_id
                  and uc.code_id in (v_code_id_non_acq_expense, v_code_id_maintenance_expense)
                  and (rec.loa_code is null or rec.loa_code = '' or rec.loa_code = co.code_e_name)
                  and t.year_month >= v_start_year_month
                  and t.year_month < v_end_year_month
                  and (t.year_month like '%12' or t.year_month = v_end_year_month);

            end if;


        end loop;

    commit;

end ;
$$;

CREATE PROCEDURE atruser.atr_pack_autoquota_proc_do_action(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
begin

    call atr_pack_autoquota_proc_log(p_action_no, 'start');

    call atr_pack_autoquota_proc_fetch_data(p_action_no);
    call atr_pack_autoquota_proc_calc(p_action_no);

    update atr_buss_autoquota_action t
    set draw_state    = '2',
        draw_end_time = clock_timestamp()
    where t.action_no = p_action_no;

    commit;

    call atr_pack_autoquota_proc_log(p_action_no, 'end');
end;
$$;


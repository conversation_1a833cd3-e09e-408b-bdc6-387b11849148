DECLARE

  --固定参数
  v_entity_id NUMERIC(11, 0) := 1;
  v_book_code VARCHAR2(10) := 'BookI17';
  v_user_id   NUMERIC(11, 0) := 1;


  year_month_begin VARCHAR2(10) := '202201'; --将作为初始业务期间
  year_month_end   VARCHAR2(10) := '202312';
  v_year_month     VARCHAR2(10);


BEGIN

  --dbms_output.put_line('===开始清理数据===');


  IF year_month_begin > year_month_end THEN
    --dbms_output.put_line('开始业务期间不能大于结束业务期间，请重新配置！');
    --dbms_output.put_line('===结束清理数据===');
    RETURN;
  END IF;


  --当前处理业务期间赋值
  v_year_month := year_month_begin;

  WHILE v_year_month <= year_month_end LOOP
  
    -- 调试打印信息
    --dbms_output.put_line('**当前处理业务期间: ' || v_year_month);
  
  
    --清除业务期间详情
    DELETE FROM rptuser.rpt_conf_bussperiod_detail
     WHERE period_detail_id IN (SELECT bpd.period_detail_id
                                  FROM rptuser.rpt_conf_bussperiod_detail bpd
                                  LEFT JOIN rptuser.rpt_conf_bussperiod p
                                    ON bpd.buss_period_id = p.buss_period_id
                                  LEFT JOIN rptuser.rpt_conf_table t
                                    ON bpd.biz_type_id = t.biz_type_id
                                  LEFT JOIN bpluser.bbs_entity c
                                    ON p.entity_id = c.entity_id
                                 WHERE 1 = 1
                                   AND p.entity_id = v_entity_id
                                   AND p.year_month = v_year_month
                                   AND p.book_code = v_book_code
                                --and t.direction = '0' --0-输出，1-输入
                                --AND p.execution_state = '2' --0-准备中；1-已准备；2-处理中；3-已完成；4-重开
                                --AND T.biz_code = 'atr_DRAW_AND_GENERATE_BUSS_REPORT'
                                );
  
  
    --清除业务期间
    DELETE FROM rptuser.rpt_conf_bussperiod t
     WHERE t.entity_id = v_entity_id
       AND t.book_code = v_book_code
       AND t.year_month = v_year_month;
  
    --===清理业务表数据===
    --业务报表
    DELETE FROM rptuser.rpt_buss_report t
     WHERE t.entity_id = v_entity_id
       AND t.book_code = v_book_code
       AND t.year_month = v_year_month;
  
    --业务报表轨迹 
    DELETE FROM rptuser.rpt_buss_reporthis t
     WHERE t.entity_id = v_entity_id
       AND t.book_code = v_book_code
       AND t.year_month = v_year_month;
  
    --列项提数结果表
    DELETE FROM rptuser.rpt_buss_report_item_data t
     WHERE t.entity_id = v_entity_id
       AND t.book_code = v_book_code
       AND t.year_month = v_year_month;
  
    --列项提数结果表轨迹   
    DELETE FROM rptuser.rpt_buss_report_item_datahis t
     WHERE t.entity_id = v_entity_id
       AND t.book_code = v_book_code
       AND t.year_month = v_year_month;
  
    --科目余额及专项余额数据接入相关表
    --清除专项余额数据   
    DELETE FROM rptuser.rpt_dap_article_balance b
     WHERE b.entity_id = v_entity_id
       AND b.book_code = v_book_code
       AND b.year_month = v_year_month;
  
    --清除专项余额数据轨迹   
    DELETE FROM rptuser.rpt_dap_article_bal_his b
     WHERE b.entity_id = v_entity_id
       AND b.book_code = v_book_code
       AND b.year_month = v_year_month;
  
    --清除科目余额数据   
    DELETE FROM rptuser.rpt_dap_ledger_balance b
     WHERE b.entity_id = v_entity_id
       AND b.book_code = v_book_code
       AND b.year_month = v_year_month;
  
    --清除科目余额数据轨迹  
    DELETE FROM rptuser.rpt_dap_ledger_balancehis b
     WHERE b.entity_id = v_entity_id
       AND b.book_code = v_book_code
       AND b.year_month = v_year_month;
  
  
    --列项提数相关过程表
    --清除列项提数过程子表数据
    DELETE FROM rptuser.rpt_duct_report_item_data_sub t
     WHERE t.report_item_data_id IN (SELECT t1.report_item_data_id
                                       FROM rptuser.rpt_duct_report_item_data t1
                                      WHERE t1.entity_id = v_entity_id
                                        AND t1.book_code = v_book_code
                                        AND t1.year_month = v_year_month);
    --清除列项提数过程表数据                 
    DELETE FROM rptuser.rpt_duct_report_item_data t
     WHERE t.entity_id = v_entity_id
       AND t.book_code = v_book_code
       AND t.year_month = v_year_month;
  
    --清除列项提数过程表轨迹数据
    DELETE FROM rptuser.rpt_duct_report_item_datahis t
     WHERE t.entity_id = v_entity_id
       AND t.book_code = v_book_code
       AND t.year_month = v_year_month;
  
  
    --报表模板解析相关过程表
    --清除报表模板解析数据
    DELETE FROM rptuser.rpt_duct_report_template t
     WHERE t.entity_id = v_entity_id
       AND t.book_code = v_book_code
       AND t.year_month = v_year_month;
  
    --清除报表模板解析轨迹数据  
    DELETE FROM rptuser.rpt_duct_report_templatehis t
     WHERE t.entity_id = v_entity_id
       AND t.book_code = v_book_code
       AND t.year_month = v_year_month;
  
    --清除报表模板解析子表数据
    DELETE FROM rptuser.rpt_duct_report_temp_detail t
     WHERE t.report_template_duct_id IN (SELECT t1.report_template_duct_id
                                           FROM rptuser.rpt_duct_report_template t1
                                          WHERE t1.entity_id = v_entity_id
                                            AND t1.book_code = v_book_code
                                            AND t1.year_month = v_year_month);
  
    --清除报表模板解析子表轨迹数据                    
    DELETE FROM rptuser.rpt_duct_report_temp_detailhis t
     WHERE t.report_template_duct_id IN (SELECT t1.report_template_duct_id
                                           FROM rptuser.rpt_duct_report_template t1
                                          WHERE t1.entity_id = v_entity_id
                                            AND t1.book_code = v_book_code
                                            AND t1.year_month = v_year_month);
  
    --列项提数日志表
    DELETE FROM rptuser.rpt_log_report_item_draw_task
     WHERE entity_id = v_entity_id
       AND book_code = v_book_code
       AND year_month = v_year_month;


    -- 维度数据结果表
    delete from rpt_buss_dimension_data
    WHERE entity_id = v_entity_id
      AND book_code = v_book_code
      AND year_month = v_year_month;
    -- 维度数据结果轨迹表
    delete from rpt_buss_dimension_datahis
    WHERE entity_id = v_entity_id
      AND book_code = v_book_code
      AND year_month = v_year_month;
    -- 维度数据过程表
    delete from rpt_duct_dimension_data
    WHERE entity_id = v_entity_id
      AND book_code = v_book_code
      AND year_month = v_year_month;
    -- 维度数据过程明细表
    delete from rpt_duct_dimension_data_sub
    WHERE entity_id = v_entity_id
      AND book_code = v_book_code
      AND year_month = v_year_month;
  
    -- 跳转下一个业务期间
    v_year_month := to_char(add_months(to_date(v_year_month, 'yyyymm'), 1), 'YYYYMM');
  
  END LOOP;

  --插入初始化业务期间及业务详情数据

  --业务期间配置
  INSERT INTO rptuser.rpt_conf_bussperiod
    (buss_period_id,
     entity_id,
     book_code,
     year_month,
     execution_state,
     valid_is,
     creator_id,
     create_time,
     updator_id,
     update_time)
  VALUES
    (rptuser.rpt_seq_conf_bussprd.nextval,
     v_entity_id,
     v_book_code,
     year_month_begin,
     '0', --执行状态：0-准备中，1-已准备，2-处理中，3-已完成
     '1',
     v_user_id,
     SYSDATE,
     NULL,
     NULL);

  --提交事务
  COMMIT;

  --1、循环xxx_conf_period
  FOR cur_period IN (SELECT buss_period_id,
                            execution_state
                       FROM rptuser.rpt_conf_bussperiod
                      WHERE valid_is = '1'
                        AND entity_id = v_entity_id
                        AND book_code = v_book_code
                        AND year_month = year_month_begin) LOOP
    --2、循环xxx_conf_table
    FOR cur_table IN (SELECT biz_type_id,
                             biz_code,
                             direction,
                             system_code
                        FROM rptuser.rpt_conf_table) LOOP
    
    
      --插入xxx_conf_bussperiod_detail
      INSERT INTO rptuser.rpt_conf_bussperiod_detail
        (period_detail_id,
         buss_period_id,
         biz_type_id,
         task_time,
         exec_result,
         ready_state,
         creator_id,
         create_time,
         updator_id,
         update_time)
      VALUES
        (rptuser.rpt_seq_conf_bussprd_dtl.nextval,
         cur_period.buss_period_id,
         cur_table.biz_type_id,
         NULL,
         NULL,
         (CASE WHEN cur_period.execution_state = '1' OR cur_period.execution_state = '3' THEN '1' ELSE '0' END), --业务期间：1-已准备中或3-已完成，则准备状态为1-已准备，其它情况为0-准备中
         v_user_id,
         SYSDATE,
         NULL,
         NULL);
    
      --提交事务
      COMMIT;
    END LOOP;
  END LOOP;

  --dbms_output.put_line('===结束清理数据===');

  --提交事务
  COMMIT;

EXCEPTION
  WHEN OTHERS THEN
    --dbms_output.put_line('**SQLERRM: ' || SQLERRM);
    --dbms_output.put_line('**Error line: ' || dbms_utility.format_error_backtrace());
    ROLLBACK;
END;

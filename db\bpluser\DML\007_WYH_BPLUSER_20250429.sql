--计量数据推送任务配置
DELETE 
FROM
	bpl_qrtz_conf_task_detail T 
WHERE
	EXISTS ( SELECT 1 FROM bpl_qrtz_conf_task qct WHERE task_group = 'QTC' AND task_c_name = '计量数据推送' AND T.conf_task_id = qct.conf_task_id );
DELETE 
FROM
	bpl_qrtz_conf_task 
WHERE
	task_group = 'QTC' 
	AND task_c_name = '计量数据推送';
INSERT INTO bpluser.bpl_qrtz_conf_task (
	conf_task_id,
	task_c_name,
	task_l_name,
	task_e_name,
	url,
	param,
	frequency,
	valid_is,
	create_time,
	update_time,
	creator_id,
	updator_id,
	repeat_is,
	task_group,
	relation_task_id,
	retry_count,
	retry_strategy,
	async_is 
)
VALUES
	(
		nextval( 'bpl_seq_qrtz_conf_task' ),
		'计量数据推送',
		'计量數據推送',
		'QTC Data Push To Atr',
		'http://ss-ifrs-quantification/job/push/data',
		NULL,
		'M',
		'1',
		'2024-11-07 10:39:39',
		'2024-11-07 11:00:48.585',
		1,
		1,
		'1',
		'QTC',
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'QTC' AND task_c_name = '计量自动任务' ),
		1,
		'Fixed',
		'0' 
	);
INSERT INTO bpluser.bpl_qrtz_conf_task_detail (
	task_detail_id,
	conf_task_id,
	func_code,
	func_c_name,
	func_l_name,
	func_e_name,
	task_type,
	METHOD,
	serialized_param,
	proc_name,
	param,
	frequency,
	valid_is,
	create_time,
	update_time,
	creator_id,
	updator_id,
	async_is,
	relation_task_dtl_id,
	priority_no,
	retry_count,
	retry_strategy 
)
VALUES
	(
		nextval( 'bpl_seq_qrtz_conf_task' ),
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'QTC' AND task_c_name = '计量数据推送' ),
		'QTC_DATA_PUSH_TO_ACC',
		'计量数据推送会计',
		'计量數據推送会计',
		'Data Push To Acc',
		'M',
		'qtcBussDataPullApi.syncAcc',
		'javax.servlet.http.HttpServletRequest,com.ss.ifrs.quantification.pojo.conf.vo.QtcConfBussPeriodVo',
		'',
		NULL,
		'M',
		'1',
		'2024-11-07 10:38:08.257',
		NULL,
		1,
		NULL,
		'0',
		NULL,
		1,
		1,
		'Fixed' 
	);
DELETE 
FROM
	bpl_qrtz_job_details T 
WHERE
	job_group = 'QTC' 
	AND job_name = 'QTC_DATA_PUSH';
INSERT INTO bpluser.bpl_qrtz_job_details (
	sched_name,
	job_name,
	job_group,
	description,
	job_class_name,
	is_durable,
	is_nonconcurrent,
	is_update_data,
	requests_recovery,
	job_data,
	url,
	create_time,
	job_c_name,
	job_l_name,
	entity_id,
	proc_id,
	job_e_name,
	creator_id,
	period_buss_code,
	display_no,
	original_is,
	job_frequency,
	priority,
	conf_task_id 
)
VALUES
	(
		'DefaultQuartzScheduler',
		'QTC_DATA_PUSH',
		'QTC',
		NULL,
		'com.ss.platform.schedule.work.TaskJob',
		't',
		'f',
		't',
		'f',
		E'#\\012#Mon Sep 26 14:28:28 CST 2022\\012entityId=1\\012url=http\\\\://ss-ifrs-quantification/job/push/data\\012jobFrequency=M\\012cookie=\\012cron=0 0 1 26 * ?\\012param=\\012funcType=1\\012userCode=U001' :: BYTEA,
		'http://ss-ifrs-quantification/job/push/data',
		NULL,
		'计量推送数据',
		'计量推送数据',
		1,
		NULL,
		'Qtc Push Data',
		NULL,
		'QTC_PUSH_DATA',
		NULL,
		'1',
		'M',
		5,
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'QTC' AND task_c_name = '计量数据推送' ) 
	);
--计量数据推送任务配置
DELETE 
FROM
	bpl_qrtz_conf_task_detail T 
WHERE
	EXISTS ( SELECT 1 FROM bpl_qrtz_conf_task qct WHERE task_group = 'QTC' AND task_c_name = '输入数据检查' AND T.conf_task_id = qct.conf_task_id );
DELETE 
FROM
	bpl_qrtz_conf_task 
WHERE
	task_group = 'QTC' 
	AND task_c_name = '输入数据检查';
INSERT INTO bpluser.bpl_qrtz_conf_task (
	conf_task_id,
	task_c_name,
	task_l_name,
	task_e_name,
	url,
	param,
	frequency,
	valid_is,
	create_time,
	update_time,
	creator_id,
	updator_id,
	repeat_is,
	task_group,
	relation_task_id,
	retry_count,
	retry_strategy,
	async_is 
)
VALUES
	(
		nextval( 'bpl_seq_qrtz_conf_task' ),
		'输入数据检查',
		'輸入數據檢查',
		'Input Data Check',
		'http://ss-ifrs-quantification/job/pull/check',
		NULL,
		'M',
		'1',
		'2024-11-07 10:39:39',
		'2024-11-07 11:00:48.585',
		1,
		1,
		'1',
		'QTC',
		NULL,
		1,
		'Fixed',
		'0' 
	);
INSERT INTO bpluser.bpl_qrtz_conf_task_detail (
	task_detail_id,
	conf_task_id,
	func_code,
	func_c_name,
	func_l_name,
	func_e_name,
	task_type,
	METHOD,
	serialized_param,
	proc_name,
	param,
	frequency,
	valid_is,
	create_time,
	update_time,
	creator_id,
	updator_id,
	async_is,
	relation_task_dtl_id,
	priority_no,
	retry_count,
	retry_strategy 
)
VALUES
	(
		nextval( 'bpl_seq_qrtz_conf_task' ),
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'QTC' AND task_c_name = '输入数据检查' ),
		'ATR_PULL_STATS_CHECK',
		'精算平台输入数据检查',
		'精算平台輸入數據檢查',
		'Atr Input Data Check',
		'M',
		'qtcBussDataPullApi.atrPeriodStateCheck',
		'javax.servlet.http.HttpServletRequest,com.ss.ifrs.quantification.pojo.conf.vo.QtcConfBussPeriodVo',
		'',
		NULL,
		'M',
		'1',
		'2024-11-07 10:38:08.257',
		NULL,
		1,
		NULL,
		'0',
		NULL,
		1,
		1,
		'Fixed' 
	);
DELETE 
FROM
	bpl_qrtz_job_details T 
WHERE
	job_group = 'QTC' 
	AND job_name = 'QTC_PULL_DATA_CHECK';
INSERT INTO bpluser.bpl_qrtz_job_details (
	sched_name,
	job_name,
	job_group,
	description,
	job_class_name,
	is_durable,
	is_nonconcurrent,
	is_update_data,
	requests_recovery,
	job_data,
	url,
	create_time,
	job_c_name,
	job_l_name,
	entity_id,
	proc_id,
	job_e_name,
	creator_id,
	period_buss_code,
	display_no,
	original_is,
	job_frequency,
	priority,
	conf_task_id 
)
VALUES
	(
		'DefaultQuartzScheduler',
		'QTC_PULL_DATA_CHECK',
		'QTC',
		NULL,
		'com.ss.platform.schedule.work.TaskJob',
		't',
		'f',
		't',
		'f',
		E'#\\012#Mon Sep 26 14:28:28 CST 2022\\012entityId=1\\012url=http\\\\://ss-ifrs-quantification/job/pull/check\\012jobFrequency=M\\012cookie=\\012cron=0 0 1 26 * ?\\012param=\\012funcType=1\\012userCode=U001' :: BYTEA,
		'http://ss-ifrs-quantification/job/pull/check',
		NULL,
		'输入数据检查',
		'輸入數據檢查',
		1,
		NULL,
		'Input Data Check',
		NULL,
		'QTC_PULL_DATA_CHECK',
		NULL,
		'1',
		'M',
		1,
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'QTC' AND task_c_name = '输入数据检查' ) 
	);
DELETE 
FROM
	BPL_qRTZ_CRON_TRIGGERS T 
WHERE
	EXISTS ( SELECT 1 FROM BPL_qRTZ_TRIGGERS d WHERE d.job_name = 'QTC_SYNC_DATA' AND d.TRIGGER_NAME = T.TRIGGER_NAME );
DELETE 
FROM
	BPL_qRTZ_TRIGGERS 
WHERE
	job_name = 'QTC_SYNC_DATA';
UPDATE BPL_QRTZ_CONF_TASK T 
SET RELATION_TASK_ID = ( SELECT CONF_TASK_ID FROM BPL_QRTZ_CONF_TASK WHERE TASK_GROUP = 'QTC' AND TASK_C_NAME = '输入数据检查' ) 
WHERE
	TASK_GROUP = 'QTC' 
	AND TASK_C_NAME = '计量自动任务';
-------------------------------------------------------------------------------
--计量自动任务
SELECT
	* 
FROM
	bpl_qrtz_conf_task_detail T 
WHERE
	EXISTS ( SELECT 1 FROM bpl_qrtz_conf_task qct WHERE task_group = 'QTC' AND task_c_name = '计量自动任务' AND T.conf_task_id = qct.conf_task_id ) 
ORDER BY
	priority_no;
SELECT
	* 
FROM
	bpl_qrtz_conf_task 
WHERE
	task_group = 'QTC' 
	AND task_c_name = '计量自动任务';
DELETE 
FROM
	bpl_qrtz_conf_task_detail T 
WHERE
	EXISTS ( SELECT 1 FROM bpl_qrtz_conf_task qct WHERE task_group = 'QTC' AND task_c_name = '计量自动任务' AND T.conf_task_id = qct.conf_task_id ) 
	AND func_code IN ( 'DAP_DATA_CHECK', 'EVALUATE_DATA_CHECK', 'BUSS_ALLOC', 'ALLOC_DATA_CHECK' );
INSERT INTO bpluser.bpl_qrtz_conf_task_detail (
	task_detail_id,
	conf_task_id,
	func_code,
	func_c_name,
	func_l_name,
	func_e_name,
	task_type,
	METHOD,
	serialized_param,
	proc_name,
	param,
	frequency,
	valid_is,
	create_time,
	update_time,
	creator_id,
	updator_id,
	async_is,
	relation_task_dtl_id,
	priority_no,
	retry_count,
	retry_strategy 
)
VALUES
	(
		nextval( 'bpl_seq_qrtz_conf_task' ),
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'QTC' AND task_c_name = '计量自动任务' ),
		'DAP_DATA_CHECK',
		'数据检查',
		'数据检查',
		'Data Check',
		'M',
		'qtcDomainEvaluateApi.dapDataCheck',
		'javax.servlet.http.HttpServletRequest,com.ss.ifrs.quantification.pojo.qtcbuss.evaluate.vo.QtcBussEvaluateMainVo',
		'',
		NULL,
		'M',
		'1',
		'2024-11-07 10:38:08.257',
		NULL,
		1,
		NULL,
		'0',
		NULL,
		1,
		1,
		'Fixed' 
	);
INSERT INTO bpluser.bpl_qrtz_conf_task_detail (
	task_detail_id,
	conf_task_id,
	func_code,
	func_c_name,
	func_l_name,
	func_e_name,
	task_type,
	METHOD,
	serialized_param,
	proc_name,
	param,
	frequency,
	valid_is,
	create_time,
	update_time,
	creator_id,
	updator_id,
	async_is,
	relation_task_dtl_id,
	priority_no,
	retry_count,
	retry_strategy 
)
VALUES
	(
		nextval( 'bpl_seq_qrtz_conf_task' ),
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'QTC' AND task_c_name = '计量自动任务' ),
		'EVALUATE_DATA_CHECK',
		'计量数据校验',
		'计量数据校验',
		'Evaluate Data Check',
		'M',
		'qtcDomainEvaluateApi.evaluateDataCheck',
		'javax.servlet.http.HttpServletRequest,com.ss.ifrs.quantification.pojo.qtcbuss.evaluate.vo.QtcBussEvaluateMainVo',
		'',
		NULL,
		'M',
		'1',
		'2024-11-07 10:38:08.257',
		NULL,
		1,
		NULL,
		'0',
		( SELECT task_detail_id FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'BUSS_EVALUATE' ),
		3,
		1,
		'Fixed' 
	);
INSERT INTO bpluser.bpl_qrtz_conf_task_detail (
	task_detail_id,
	conf_task_id,
	func_code,
	func_c_name,
	func_l_name,
	func_e_name,
	task_type,
	METHOD,
	serialized_param,
	proc_name,
	param,
	frequency,
	valid_is,
	create_time,
	update_time,
	creator_id,
	updator_id,
	async_is,
	relation_task_dtl_id,
	priority_no,
	retry_count,
	retry_strategy 
)
VALUES
	(
		nextval( 'bpl_seq_qrtz_conf_task' ),
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'QTC' AND task_c_name = '计量自动任务' ),
		'BUSS_ALLOC',
		'计量分摊',
		'计量分摊',
		'Evaluate Allocate',
		'M',
		'qtcDomainEvaluateApi.allocAll',
		'javax.servlet.http.HttpServletRequest,com.ss.ifrs.quantification.pojo.qtcbuss.evaluate.vo.QtcBussEvaluateMainVo',
		'',
		NULL,
		'M',
		'1',
		'2024-11-07 10:38:08.257',
		NULL,
		1,
		NULL,
		'0',
		( SELECT task_detail_id FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'EVALUATE_DATA_CHECK' ),
		4,
		1,
		'Fixed' 
	);
INSERT INTO bpluser.bpl_qrtz_conf_task_detail (
	task_detail_id,
	conf_task_id,
	func_code,
	func_c_name,
	func_l_name,
	func_e_name,
	task_type,
	METHOD,
	serialized_param,
	proc_name,
	param,
	frequency,
	valid_is,
	create_time,
	update_time,
	creator_id,
	updator_id,
	async_is,
	relation_task_dtl_id,
	priority_no,
	retry_count,
	retry_strategy 
)
VALUES
	(
		nextval( 'bpl_seq_qrtz_conf_task' ),
		( SELECT conf_task_id FROM bpl_qrtz_conf_task WHERE task_group = 'QTC' AND task_c_name = '计量自动任务' ),
		'ALLOC_DATA_CHECK',
		'计量分摊校验',
		'计量分摊校验',
		'Evaluate Allocate Check',
		'M',
		'qtcDomainEvaluateApi.allocDataCheck',
		'javax.servlet.http.HttpServletRequest,com.ss.ifrs.quantification.pojo.qtcbuss.evaluate.vo.QtcBussEvaluateMainVo',
		'',
		NULL,
		'M',
		'1',
		'2024-11-07 10:38:08.257',
		NULL,
		1,
		NULL,
		'0',
		( SELECT task_detail_id FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'BUSS_ALLOC' ),
		5,
		1,
		'Fixed' 
	);
UPDATE bpl_qrtz_conf_task_detail 
SET relation_task_dtl_id = ( SELECT task_detail_id FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'DAP_DATA_CHECK' ),
priority_no = 2 
WHERE
	func_code = 'BUSS_EVALUATE' 
	AND EXISTS ( SELECT 1 FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'DAP_DATA_CHECK' );
UPDATE bpl_qrtz_conf_task_detail 
SET relation_task_dtl_id = ( SELECT task_detail_id FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'ALLOC_DATA_CHECK' ),
priority_no = 6 
WHERE
	func_code = 'BUSS_EVALUATE_CFM' 
	AND EXISTS ( SELECT 1 FROM bpl_qrtz_conf_task_detail dd WHERE dd.func_code = 'ALLOC_DATA_CHECK' );
	
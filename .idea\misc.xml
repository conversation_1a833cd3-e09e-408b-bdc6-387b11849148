<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/ss-library-mq/pom.xml" />
        <option value="$PROJECT_DIR$/ss-library-job/pom.xml" />
        <option value="$PROJECT_DIR$/ss-library-utils/pom.xml" />
        <option value="$PROJECT_DIR$/ss-platform-base/pom.xml" />
        <option value="$PROJECT_DIR$/ss-ifrs-actuarial/pom.xml" />
        <option value="$PROJECT_DIR$/ss-platform-admin/pom.xml" />
        <option value="$PROJECT_DIR$/ss-library-mybatis/pom.xml" />
        <option value="$PROJECT_DIR$/ss-platform-common/pom.xml" />
        <option value="$PROJECT_DIR$/ss-platform-eureka/pom.xml" />
        <option value="$PROJECT_DIR$/ss-library-security/pom.xml" />
        <option value="$PROJECT_DIR$/ss-platform-gateway/pom.xml" />
        <option value="$PROJECT_DIR$/ss-platform-schedule/pom.xml" />
        <option value="$PROJECT_DIR$/ss-ifrs-datamgr/pom.xml" />
        <option value="$PROJECT_DIR$/ss-ifrs-quantification/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" default="true" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>
---------- drop ---------------
do language plpgsql
$$
    declare
        rec record;
    begin
        for rec in select t.proname, t.prokind from pg_proc t where t.proname ~ '^random_str$'
            loop
                if rec.prokind = 'f' then
                    call atr_pack_commonutils_proc_drop_func(rec.proname::varchar);
                elsif rec.prokind = 'p' then
                    call atr_pack_commonutils_proc_drop_proc(rec.proname::varchar);
                end if;
            end loop;
    end;
$$;



---------- create ---------------
CREATE FUNCTION atruser.random_str(p_len integer) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
-----------------------------------------------------------
-- 返回指定长度的随机字符串， 字符串由 0-9A-Z 构成  by majingyun
-----------------------------------------------------------
declare
v_ran smallint;
  v_str varchar(1000) := '';
begin
for i in 1..p_len loop
      v_ran := floor(random() * 36);
      if v_ran < 10 then
        v_str := v_str || v_ran;
else
        v_str := v_str || chr(v_ran + 55);
end if;
end loop;

return v_str;
end;
$$;


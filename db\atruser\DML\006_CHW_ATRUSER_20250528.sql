delete from atr_conf_becf_output_def;
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (3, 'DD', 'ed_premium', '已赚保费现金流', '已赚保费现金流', '已赚保费现金流', 'edPremium', 3, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (34, 'TO', 'ed_premium', '已赚保费现金流', '已赚保费现金流', '已赚保费现金流', 'edPremium', 6, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (4, 'DD', 'ed_net_fee', '已赚净额结算', '已赚净额结算', '已赚净额结算', 'edNetFee', 4, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (41, 'DD', 'ra', '预期非金融风险调整', '预期非金融风险调整', '预期非金融风险调整', 'ra', 19, 'G', '1', 1, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (37, 'TO', 'ed_net_fee', '增量已赚净额结算手续费现金流', '增量已赚净额结算手续费现金流', '增量已赚净额结算手续费现金流', 'edNetFee', 7, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (36, 'TO', 'net_fee', '净额结算手续费现金流', '净额结算手续费现金流', '净额结算手续费现金流', 'netFeeCf', 9, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (38, 'DD', 'mt_fee', '预期维持费用现金流', '预期维持费用现金流', '预期维持费用现金流', 'mtFee', 16, 'G', '1', 1, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (35, 'TO', 'recv_premium', '保费现金流', '保费现金流', '保费现金流', 'premiumCf', 8, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (39, 'DD', 'claim', '预期赔付现金流', '预期赔付现金流', '预期赔付现金流', 'claim', 17, 'G', '1', 1, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (7, 'DD', 'ed_iaehc_out', '已赚非跟单获取费用-对外', '已赚非跟单获取费用-对外', '已赚非跟单获取费用-对外', 'edIaehcOut', 7, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (42, 'FO', 'ed_premium', '已赚保费现金流', '已赚保费现金流', '已赚保费现金流', 'edPremium', 6, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (8, 'DD', 'recv_premium', '保费现金流', '保费现金流', '保费现金流', 'premium', 8, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (9, 'DD', 'net_fee', '净额结算手续费', '净额结算手续费', '净额结算手续费', 'netFee', 9, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (43, 'FO', 'ed_net_fee', '增量已赚净额结算手续费现金流', '增量已赚净额结算手续费现金流', '增量已赚净额结算手续费现金流', 'edNetFee', 7, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (13, 'DD', 'iaehc_out', '非跟单获取费用-对外', '非跟单获取费用-对外', '非跟单获取费用-对外', 'iaehcOut', 14, 'U', '1', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (12, 'DD', 'iaehc_in', '非跟单获取费用-对内', '非跟单获取费用-对内', '非跟单获取费用-对内', 'iaehcIn', 13, 'U', '1', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (11, 'DD', 'iacf', '跟单获取费用', '跟单获取费用', '跟单获取费用', 'iacf', 12, 'U', '1', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (14, 'DD', 'lapse', '预期退保现金流', '预期退保现金流', '预期退保现金流', 'lapse', 15, 'G', '1', 1, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (40, 'DD', 'ulae', '预期间接理赔费用', '预期间接理赔费用', '预期间接理赔费用', 'ulae', 18, 'G', '1', 1, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (44, 'FO', 'recv_premium', '保费现金流', '保费现金流', '保费现金流', 'recvPremium', 8, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (45, 'FO', 'net_fee', '净额结算手续费现金流', '净额结算手续费现金流', '净额结算手续费现金流', 'netFee', 9, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (46, 'TI', 'ed_premium', '已赚保费现金流', '已赚保费现金流', '已赚保费现金流', 'edPremium', 3, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (47, 'TI', 'ed_net_fee', '已赚净额结算', '已赚净额结算', '已赚净额结算', 'edNetFee', 4, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (50, 'TI', 'ed_iaehc_out', '已赚非跟单获取费用-对外', '已赚非跟单获取费用-对外', '已赚非跟单获取费用-对外', 'edIaehcOut', 7, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (51, 'TI', 'recv_premium', '保费现金流', '保费现金流', '保费现金流', 'premium', 8, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (52, 'TI', 'net_fee', '净额结算手续费', '净额结算手续费', '净额结算手续费', 'netFee', 9, 'U', '1', null, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (54, 'TI', 'iacf', '跟单获取费用', '跟单获取费用', '跟单获取费用', 'iacf', 12, 'U', '1', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (55, 'TI', 'iaehc_in', '非跟单获取费用-对内', '非跟单获取费用-对内', '非跟单获取费用-对内', 'iaehcIn', 13, 'U', '1', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (56, 'TI', 'iaehc_out', '非跟单获取费用-对外', '非跟单获取费用-对外', '非跟单获取费用-对外', 'iaehcOut', 14, 'U', '1', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (57, 'TI', 'lapse', '预期退保现金流', '预期退保现金流', '预期退保现金流', 'lapse', 15, 'G', '1', 1, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (48, 'TI', 'ed_iacf', '已赚跟单获取费用', '已赚跟单获取费用', '已赚跟单获取费用', 'edIacf', 5, 'U', '1', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (49, 'TI', 'ed_iaehc_in', '已赚非跟单获取费用-对内', '已赚非跟单获取费用现金流-对内', '已赚非跟单获取费用现金流-对内', 'edIaehcIn', 6, 'U', '1', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (10, 'DD', 'bad_debt', '减值现金流', '减值现金流', '减值现金流', 'badDebt', 10, 'U', '0', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (5, 'DD', 'ed_iacf', '已赚跟单获取费用', '已赚跟单获取费用', '已赚跟单获取费用', 'edIacf', 5, 'U', '1', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (6, 'DD', 'ed_iaehc_in', '已赚非跟单获取费用-对内', '已赚非跟单获取费用现金流-对内', '已赚非跟单获取费用现金流-对内', 'edIaehcIn', 6, 'U', '1', 0, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (58, 'TI', 'mt_fee', '预期维持费用现金流', '预期维持费用现金流', '预期维持费用现金流', 'mtFee', 16, 'G', '1', 1, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (59, 'TI', 'claim', '预期赔付现金流', '预期赔付现金流', '预期赔付现金流', 'claim', 17, 'G', '1', 1, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (60, 'TI', 'ulae', '预期间接理赔费用', '预期间接理赔费用', '预期间接理赔费用', 'ulae', 18, 'G', '1', 1, null);
INSERT INTO atruser.atr_conf_becf_output_def (becf_out_id, business_source_code, out_code, out_c_name, out_e_name, out_l_name, remark, display_no, dimension, type, start_dev_no, end_dev_no) VALUES (61, 'TI', 'ra', '预期非金融风险调整', '预期非金融风险调整', '预期非金融风险调整', 'ra', 19, 'G', '1', 1, null);

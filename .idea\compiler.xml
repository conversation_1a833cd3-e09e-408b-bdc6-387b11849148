<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <wildcardResourcePatterns>
      <entry name="!?*.java" />
      <entry name="!?*.form" />
      <entry name="!?*.class" />
      <entry name="!?*.groovy" />
      <entry name="!?*.scala" />
      <entry name="!?*.flex" />
      <entry name="!?*.kt" />
      <entry name="!?*.clj" />
      <entry name="!?*.aj" />
      <entry name="!?*.xml" />
    </wildcardResourcePatterns>
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="ss-platform-schedule" />
        <module name="ss-platform-base" />
        <module name="ss-ifrs-actuarial" />
        <module name="ss-library-mq" />
        <module name="ss-platform-gateway" />
        <module name="ss-ifrs-quantification" />
        <module name="ss-platform-common" />
        <module name="ss-ifrs-datamgr" />
        <module name="ss-library-job" />
        <module name="ss-platform-admin" />
        <module name="ss-library-mybatis" />
        <module name="ss-library-security" />
        <module name="ss-library-utils" />
        <module name="ss-platform-eureka" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="ss-ifrs-actuarial" options="-parameters" />
      <module name="ss-ifrs-datamgr" options="-parameters" />
      <module name="ss-ifrs-quantification" options="-parameters" />
      <module name="ss-library-job" options="-parameters" />
      <module name="ss-library-mq" options="-parameters" />
      <module name="ss-library-mybatis" options="-parameters" />
      <module name="ss-library-security" options="-parameters" />
      <module name="ss-library-utils" options="-parameters" />
      <module name="ss-platform-admin" options="-parameters" />
      <module name="ss-platform-base" options="-parameters" />
      <module name="ss-platform-common" options="-parameters" />
      <module name="ss-platform-eureka" options="-parameters" />
      <module name="ss-platform-gateway" options="-parameters" />
      <module name="ss-platform-schedule" options="-parameters" />
    </option>
  </component>
</project>
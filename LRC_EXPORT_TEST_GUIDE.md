# LRC导出优化测试指南

## 测试目标
验证LRC导出功能的流式优化是否有效解决OOM问题，并确保功能正常。

## 测试环境准备

### 1. JVM参数设置
为了更容易触发OOM，可以设置较小的堆内存：
```bash
-Xms512m -Xmx1024m -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/
```

### 2. 数据库配置
确保数据库连接池支持长连接：
```properties
# 连接超时时间（毫秒）
spring.datasource.hikari.connection-timeout=60000
# 查询超时时间（秒）
spring.datasource.hikari.validation-timeout=30
# 最大连接生命周期（毫秒）
spring.datasource.hikari.max-lifetime=1800000
```

## 测试用例

### 测试用例1：功能验证测试
**目标**：验证流式导出功能正常
**步骤**：
1. 准备小量测试数据（1000条以内）
2. 调用becfDownload接口
3. 验证导出文件格式和内容正确
4. 检查日志中是否有"开始流式导出LRC数据"信息

**预期结果**：
- 导出成功，文件格式正确
- 日志显示使用了流式导出
- 内存使用平稳

### 测试用例2：大数据量测试
**目标**：验证大数据量下不会OOM
**步骤**：
1. 准备大量测试数据（10万条以上）
2. 监控JVM内存使用情况
3. 调用becfDownload接口
4. 观察内存使用变化

**预期结果**：
- 导出成功完成
- 内存使用保持稳定，不会持续增长
- 没有OOM异常

### 测试用例3：并发测试
**目标**：验证并发导出的稳定性
**步骤**：
1. 同时启动3-5个导出任务
2. 监控系统资源使用
3. 验证所有任务都能正常完成

**预期结果**：
- 所有导出任务都能成功完成
- 系统负载在可接受范围内
- 没有资源竞争问题

### 测试用例4：异常处理测试
**目标**：验证异常情况下的资源清理
**步骤**：
1. 在导出过程中模拟数据库连接中断
2. 在导出过程中模拟磁盘空间不足
3. 验证资源是否正确释放

**预期结果**：
- 异常被正确捕获和处理
- 数据库连接被正确释放
- 临时文件被清理

## 性能对比测试

### 测试数据准备
```sql
-- 创建测试数据（根据实际表结构调整）
INSERT INTO atr_buss_dd_lrc_icu_calc (action_no, year_month, portfolio_no, icg_no, ...)
SELECT 
    'TEST_ACTION_' || generate_series(1, 100000),
    '202312',
    'PORT_' || (random() * 1000)::int,
    'ICG_' || (random() * 100)::int,
    ...
FROM generate_series(1, 100000);
```

### 性能指标监控
1. **内存使用**：
   - 堆内存使用峰值
   - GC频率和耗时
   - 内存增长趋势

2. **执行时间**：
   - 总导出时间
   - 数据库查询时间
   - 文件写入时间

3. **系统资源**：
   - CPU使用率
   - 磁盘I/O
   - 网络I/O

## 监控命令

### JVM内存监控
```bash
# 查看JVM内存使用
jstat -gc <pid> 5s

# 查看堆内存详情
jmap -heap <pid>

# 生成内存快照
jmap -dump:format=b,file=heap.hprof <pid>
```

### 系统资源监控
```bash
# 监控CPU和内存
top -p <pid>

# 监控磁盘I/O
iostat -x 1

# 监控网络
netstat -i
```

## 测试结果记录模板

### 测试环境
- JVM版本：
- 堆内存设置：
- 数据库版本：
- 测试数据量：

### 优化前结果
- 导出时间：
- 内存峰值：
- 是否OOM：
- GC次数：

### 优化后结果
- 导出时间：
- 内存峰值：
- 是否OOM：
- GC次数：

### 性能提升
- 内存使用减少：%
- 导出时间变化：%
- 稳定性提升：是/否

## 回归测试检查清单

- [ ] 所有业务类型（DD、FO、TI、TO、TX）导出正常
- [ ] 导出文件格式和内容正确
- [ ] 多sheet导出功能正常
- [ ] ZIP打包功能正常
- [ ] 错误处理和日志记录正常
- [ ] 并发导出不冲突
- [ ] 资源清理正确
- [ ] 性能符合预期

## 注意事项

1. **测试数据清理**：测试完成后及时清理测试数据
2. **生产环境谨慎**：在生产环境测试前先在测试环境充分验证
3. **监控告警**：设置适当的监控告警，及时发现问题
4. **回滚准备**：准备回滚方案，以防出现问题

## 故障排查

### 常见问题
1. **内存仍然增长**：检查ResultHandler是否正确关闭
2. **导出失败**：检查数据库连接和模板文件
3. **文件损坏**：检查磁盘空间和权限
4. **性能下降**：检查数据库索引和查询优化

### 日志关键字
- "开始流式导出LRC数据"
- "完成LRC数据流式导出"
- "CommonResultHandler write to excel"
- "使用了过时的downloadData方法"

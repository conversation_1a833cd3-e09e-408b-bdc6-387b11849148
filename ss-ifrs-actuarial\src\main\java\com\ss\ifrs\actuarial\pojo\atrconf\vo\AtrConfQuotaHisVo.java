/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-11-23 14:53:02
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.ss.platform.core.annotation.SsTranslateCode;
import com.ss.platform.core.constant.SystemConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-11-23 14:53:02<br/>
 * Description: 指标配置轨迹表<br/>
 * Table Name: ATR_CONF_QUOTAHIS<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "指标配置轨迹表")
public class AtrConfQuotaHisVo implements Serializable {
    /**
     * Database column: ATR_CONF_QUOTAHIS.QUOTAHIS_ID
     * Database remarks: quotaHisId|主键
     */
    @ApiModelProperty(value = "quotaHisId|主键", required = true)
    private Long quotaHisId;

    /**
     * Database column: ATR_CONF_QUOTAHIS.QUOTA_ID
     * Database remarks: quotaId|主键
     */
    @ApiModelProperty(value = "quotaId|主键", required = true)
    private Long quotaId;

    /**
     * Database column: ATR_CONF_QUOTAHIS.CENTER_ID
     * Database remarks: entityId|业务单位
     */
    @ApiModelProperty(value = "entityId|业务单位", required = true)
    private Long entityId;

    /**
     * Database column: ATR_CONF_QUOTAHIS.MODEL_DEF_ID
     * Database remarks: model_def_id|计量模型
     */
    @ApiModelProperty(value = "model_def_id|计量模型", required = true)
    private String businessSourceCode;

    /**
     * Database column: bbs_conf_quota.quota_class
     * Database remarks: quotaClass|指标分类
     */
    @ApiModelProperty(value = "quotaClass|指标归类", required = false)
    @SsTranslateCode(context = SystemConstant.AtrIdentity.APP_CONTEXT,codeCodeIdx = "QuotaClass")
    private String quotaClass;

    /**
     * Database column: ATR_CONF_QUOTAHIS.dimension
     * Database remarks: null
     */
    private String dimension;

    /**
     * Database column: ATR_CONF_QUOTAHIS.dimension_value
     * Database remarks: null
     */
    private String dimensionValue;

    /**
     * Database column: ATR_CONF_QUOTAHIS.QUOTA_DEF_ID
     * Database remarks: quotaDefId|指标定义
     */
    @ApiModelProperty(value = "quotaDefId|指标定义", required = true)
    private Long quotaDefId;

    /**
     * Database column: ATR_CONF_QUOTAHIS.QUOTA_VALUE
     * Database remarks: quotaValue|指标值
     */
    @ApiModelProperty(value = "quotaValue|指标值", required = false)
    private String quotaValue;

    /**
     * Database column: ATR_CONF_QUOTAHIS.SERIAL_NO
     * Database remarks: SERIAL_NO|版本号
     */
    @ApiModelProperty(value = "SERIAL_NO|版本号", required = false)
    private Integer serialNo;

    /**
     * Database column: ATR_CONF_QUOTAHIS.VALID_IS
     * Database remarks: validIs|是否有效
     */
    @ApiModelProperty(value = "validIs|是否有效", required = true)
    private String validIs;

    /**
     * Database column: ATR_CONF_QUOTAHIS.AUDIT_STATE
     * Database remarks: auditState|审核状态
     */
    @ApiModelProperty(value = "auditState|审核状态", required = false)
    private String auditState;

    /**
     * Database column: ATR_CONF_QUOTAHIS.CHECKED_MSG
     * Database remarks: checkedMsg|审核意见
     */
    @ApiModelProperty(value = "checkedMsg|审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: ATR_CONF_QUOTAHIS.CHECKED_ID
     * Database remarks: checkedId|审核热
     */
    @ApiModelProperty(value = "checkedId|审核热", required = false)
    private Long checkedId;

    /**
     * Database column: ATR_CONF_QUOTAHIS.CHECKED_TIME
     * Database remarks: checkedTime|审核时间
     */
    @ApiModelProperty(value = "checkedTime|审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: ATR_CONF_QUOTAHIS.CREATOR_ID
     * Database remarks: creatorId|创建人
     */
    @ApiModelProperty(value = "creatorId|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_CONF_QUOTAHIS.CREATE_TIME
     * Database remarks: createTime|创建时间
     */
    @ApiModelProperty(value = "createTime|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_CONF_QUOTAHIS.UPDATOR_ID
     * Database remarks: updatorId|最后修改人
     */
    @ApiModelProperty(value = "updatorId|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_CONF_QUOTAHIS.UPDATE_TIME
     * Database remarks: updateTime|最后修改时间
     */
    @ApiModelProperty(value = "updateTime|最后修改时间", required = false)
    private Date updateTime;

    /**
     * Database column: ATR_CONF_QUOTAHIS.OPER_TYPE
     * Database remarks: operType|操作类型
     */
    @ApiModelProperty(value = "operType|操作类型", required = false)
    private String operType;

    /**
     * Database column: ATR_CONF_QUOTAHIS.OPER_ID
     * Database remarks: operId|操作人
     */
    @ApiModelProperty(value = "operId|操作人", required = false)
    private Long operId;

    /**
     * Database column: ATR_CONF_QUOTAHIS.OPER_TIME
     * Database remarks: operTime|操作时间
     */
    @ApiModelProperty(value = "operTime|操作时间", required = false)
    private Date operTime;

    private static final long serialVersionUID = 1L;

    public Long getQuotaHisId() {
        return quotaHisId;
    }

    public void setQuotaHisId(Long quotaHisId) {
        this.quotaHisId = quotaHisId;
    }

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getQuotaClass() {
        return quotaClass;
    }

    public void setQuotaClass(String quotaClass) {
        this.quotaClass = quotaClass;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getDimensionValue() {
        return dimensionValue;
    }

    public void setDimensionValue(String dimensionValue) {
        this.dimensionValue = dimensionValue;
    }

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    public String getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(String quotaValue) {
        this.quotaValue = quotaValue;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperType() {
        return operType;
    }

    public void setOperType(String operType) {
        this.operType = operType;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }
}
delete
from BPL_SAA_ROLE_MENU_TASK
where menu_task_id = (select MENU_TASK_ID
                      from BPL_SAA_MENU_TASK
                      where menu_id =
                            (select menu_id from BPL_SAA_MENU  where SYSTEM_CODE = 'ACC'
                                     and MENU_LEVEL = '3'
                                     and MENU_C_NAME = '报表预览'));


DELETE
from BPL_SAA_MENU_TASK
where menu_id = (select menu_id from BPL_SAA_MENU  where SYSTEM_CODE = 'ACC'
                                     and MENU_LEVEL = '3'
                                     and MENU_C_NAME = '报表预览');

delete from BPL_SAA_MENU where SYSTEM_CODE = 'ACC'
                                     and MENU_LEVEL = '3'
                                     and MENU_C_NAME = '报表预览';

INSERT INTO BPLUSER.BPL_SAA_MENU (MENU_ID, UPPER_ID, MENU_LEVEL, SYSTEM_CODE, MENU_C_NAME, MENU_L_NAME, MENU_E_NAME,
                                  TARGET_ROUTER, ACTION_URL, TASK_CODE, TYPE, CODE, ICON, DISPLAY_NO, VALID_IND, REMARK,
                                  CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME, MAPPING_URL)
VALUES (nextval('BPL_SEQ_SAA_MENU'), (select MENU_ID
                                   from BPL_SAA_MENU
                                   where SYSTEM_CODE = 'ACC'
                                     and MENU_LEVEL = '2'
                                     and MENU_C_NAME = '总账结果查询'), 3, 'ACC',
        '报表预览',
        '報表預覽',
        'Report Preview',
        '/acc/generalLedger/report_preview_app',
        '/reporting/confReportTemplate', null, 'M', null, null, 7, '1', null, 1, localtimestamp,
        1, localtimestamp, null);


INSERT INTO BPLUSER.BPL_SAA_MENU_TASK (MENU_TASK_ID, MENU_ID, TASK_ID, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME,
                                       TASK_TYPE)
VALUES (nextval('BPL_SEQ_SAA_MENU_TASK'), ( select MENU_ID from BPL_SAA_MENU where SYSTEM_CODE = 'ACC'
                                     and MENU_LEVEL = '3'
                                     and MENU_C_NAME = '报表预览'), 2, null, null, null, null, null);



GRANT EXECUTE ON bpluser.bpl_pack_common TO expuser,accuser,dmuser,rptuser,atruser,qtcuser;

grant SELECT  on bpluser.BBS_V_ACCOUNT to expuser,accuser,dmuser,rptuser,atruser,qtcuser;
grant SELECT  on bpluser.BBS_ACCOUNT to expuser,accuser,dmuser,rptuser,atruser,qtcuser;
grant SELECT  on bpluser.BPL_SAA_USER to expuser,accuser,dmuser,rptuser,atruser,qtcuser;

grant select on bbs_entity to expuser,accuser,dmuser,rptuser,atruser,qtcuser;
grant select on bpluser.bpl_v_conf_code to expuser,accuser,dmuser,rptuser,atruser,qtcuser;
grant select on bpluser.bpl_conf_code to expuser,accuser,dmuser,rptuser,atruser,qtcuser;
grant select on bpluser.BPL_ACT_RE_PROCDEF to expuser,accuser,dmuser,r<PERSON><PERSON>,atruser,qtcuser;
grant select on bpluser.BPL_lOG_ACTION to expuser,accuser,dmuser,rptuser,atruser,qtcuser;
grant select on bpluser.bbs_conf_risk  to accuser;




grant select, insert, update on BPL_SAA_OPEN_PATH to QTCUSER;
grant select on bbs_conf_loa to QTCUSER;
grant select on bbs_entity to QTCUSER;


GRANT EXECUTE ON bpluser.bpl_pack_action_log TO expuser,accuser,dmuser,rptuser,atruser,qtcuser;


grant SELECT  on BBS_RISK_CLASS to ATRUSER;
grant SELECT  on BPL_SAA_USER to ATRUSER;
grant SELECT  on bbs_entity to ATRUSER;
grant SELECT on bpl_v_conf_code to QTCUSER;
grant EXECUTE on BPL_PACK_CHECK to ATRUSER;
grant EXECUTE on BPL_PACK_TRACE_JOB to ATRUSER;
grant EXECUTE on BPL_PACK_ACTION_LOG to ATRUSER;
grant SELECT on BMS_SQL_OPER_LOG to ATRUSER;
grant INSERT on BMS_SQL_OPER_LOG to ATRUSER;
grant UPDATE on BPL_SAA_OPEN_PATH to ATRUSER;
grant SELECT on BPL_SAA_OPEN_PATH to ATRUSER;
grant INSERT on BPL_SAA_OPEN_PATH to ATRUSER;
grant SELECT on BBS_CONF_LOA to ATRUSER;
grant UPDATE on BBS_CONF_LOA to ATRUSER;
grant SELECT on BBS_CONF_REINS_FLOAT_CHARGEHIS to ATRUSER;
grant SELECT on BPL_CONF_ADAPT to ATRUSER;
grant SELECT on BPL_LOG_ACTION to ATRUSER;
grant SELECT on BBS_CONF_REINS_FLOAT_CHARGE to ATRUSER;
grant SELECT on BBS_CONF_REGULAR_RULE to ATRUSER;
grant SELECT on BBS_CONF_LOA_TIC to ATRUSER;
grant SELECT on BBS_CONF_ACCOUNT_YEAR_MONTH to ATRUSER;
grant SELECT on BBS_CONF_RISK_MAPPINGHIS to ATRUSER;
grant SELECT on BBS_CONF_RISK_CLASS to ATRUSER;
grant SELECT on BBS_CONF_BASE_TREATY to ATRUSER;
grant SELECT on BMS_SEQ_SQL_OPEL_LOG to ATRUSER;
grant SELECT on BBS_CONF_CURRENCYRATE to ATRUSER;
grant SELECT on BBS_CONF_RISK_MAPPING to ATRUSER;
grant SELECT on BBS_V_ACCOUNT to ATRUSER;
grant SELECT on BBS_CONF_LOA_TICHIS to ATRUSER;
grant SELECT on BPL_LOG_ACTION_DETAIL to ATRUSER;
grant SELECT on BPL_CONF_CHECKRULE to ATRUSER;
grant SELECT on BBS_CONF_CURRENCY to ATRUSER;
grant SELECT on bbs_conf_fee_type_mapping to ATRUSER;
grant SELECT on bbs_conf_risk to ATRUSER;
grant SELECT on bbs_conf_product to ATRUSER;
grant SELECT on bpl_act_re_procdef to ATRUSER;
grant SELECT on bbs_conf_loa_detail to ATRUSER;
GRANT EXECUTE ON bpluser.bpl_pack_common TO atruser;


grant UPDATE on BPL_SAA_OPEN_PATH to QTCUSER;
grant SELECT on BPL_SAA_OPEN_PATH to QTCUSER;
grant SELECT on BBS_V_ACCOUNT to QTCUSER;
grant SELECT on BPL_SAA_USER to QTCUSER;
grant SELECT on BBS_ENTITY to QTCUSER;
grant SELECT on BBS_CONF_LOA to QTCUSER;
grant SELECT on bbs_conf_loa_detail to ATRUSER;
grant INSERT on BPL_SAA_OPEN_PATH to QTCUSER;
grant EXECUTE on BPL_PACK_COMMON to QTCUSER;
grant EXECUTE on BPL_PACK_ACTION_LOG to QTCUSER;
grant SELECT on BMS_SQL_OPER_LOG to QTCUSER;
grant SELECT on BPL_LOG_ACTION to QTCUSER;
grant SELECT on BPL_LOG_ACTION_DETAIL to QTCUSER;
grant SELECT on BPL_CONF_CHECKRULE to QTCUSER;
grant SELECT on bbs_conf_regular_rule to QTCUSER;
grant SELECT on BPL_ACT_RE_PROCDEF to QTCUSER;
grant SELECT on bpl_v_conf_code to QTCUSER;



grant SELECT on BBS_CONF_LOA_TICHIS to rptuser;
grant ALTER on BMS_SEQ_SQL_OPEL_LOG to rptuser;
grant SELECT on BMS_SEQ_SQL_OPEL_LOG to rptuser;
grant SELECT on BBS_CONF_LOA_TIC to rptuser;
grant INSERT on BPL_SAA_OPEN_PATH to rptuser;
grant SELECT on BPL_SAA_OPEN_PATH to rptuser;
grant UPDATE on BPL_SAA_OPEN_PATH to rptuser;
grant EXECUTE on BPL_PACK_TRACE_JOB to rptuser;
grant SELECT on BBS_CONF_ACCOUNT_YEAR_MONTH to rptuser;
grant SELECT on BMS_SQL_OPER_LOG to rptuser;
grant INSERT on BMS_SQL_OPER_LOG to rptuser;


grant INDEX on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant SELECT on BPL_SEQ_LOG_ACTION to ACCUSER;
grant SELECT on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant UPDATE on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant REFERENCES on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant ON COMMIT REFRESH on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant QUERY REWRITE on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant DEBUG on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant FLASHBACK on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant SELECT on BBS_CONF_CURRENCYRATE to ACCUSER;
grant SELECT on BPL_QRTZ_CONF_TASK to ACCUSER;
grant SELECT on BBS_CONF_LOA_TICHIS to ACCUSER;
grant SELECT on BBS_CONF_LOA_DETAIL to ACCUSER;
grant SELECT on BBS_CONF_LOA_TIC to ACCUSER;
grant SELECT on BPL_QRTZ_CONF_TASK_DETAIL to ACCUSER;
grant SELECT on BBS_CONF_BASE_TREATY to ACCUSER;
grant SELECT on BBS_CONF_PRODUCT to ACCUSER;
grant SELECT on BPL_QRTZ_SCHEDULE_JOB_LOG to ACCUSER;
grant SELECT on BBS_CONF_MODEL_MAPPING to ACCUSER;
grant ALTER on BPL_LOG_ACTION to ACCUSER;
grant DELETE on BPL_LOG_ACTION to ACCUSER;
grant INDEX on BPL_LOG_ACTION to ACCUSER;
grant INSERT on BPL_LOG_ACTION to ACCUSER;
grant SELECT on BPL_LOG_ACTION to ACCUSER;
grant UPDATE on BPL_LOG_ACTION to ACCUSER;
grant REFERENCES on BPL_LOG_ACTION to ACCUSER;
grant ON COMMIT REFRESH on BPL_LOG_ACTION to ACCUSER;
grant QUERY REWRITE on BPL_LOG_ACTION to ACCUSER;
grant DEBUG on BPL_LOG_ACTION to ACCUSER;
grant FLASHBACK on BPL_LOG_ACTION to ACCUSER;
grant SELECT on BPL_LOG_PUB_TASK to ACCUSER;
grant SELECT on BBS_CONF_LOA to ACCUSER;
grant SELECT on BPL_CONF_CHECKRULE to ACCUSER;
grant SELECT on BPL_CONF_ADAPT to ACCUSER;
grant SELECT on BBS_CONF_ACCOUNT_JS to ACCUSER;
grant SELECT on BBS_CONF_RISK_MAPPING to ACCUSER;
grant SELECT on BBS_CONF_CURRENCY to ACCUSER;
grant INSERT on BPL_SAA_OPEN_PATH to ACCUSER;
grant SELECT on BPL_SAA_OPEN_PATH to ACCUSER;
grant UPDATE on BPL_SAA_OPEN_PATH to ACCUSER;
grant EXECUTE on BPL_PACK_ACTION_LOG to ACCUSER;
grant EXECUTE on BPL_PACK_COMMON to ACCUSER;
grant EXECUTE on BPL_PACK_TRACE_JOB to ACCUSER;
grant EXECUTE on BPL_PACK_CHECK to ACCUSER;
grant SELECT on BBS_CONF_ACCOUNT_YEAR_MONTH to ACCUSER;
grant INSERT on BMS_SQL_OPER_LOG to ACCUSER;
grant SELECT on BMS_SQL_OPER_LOG to ACCUSER;
grant SELECT on BMS_SEQ_SQL_OPEL_LOG to ACCUSER;
grant SELECT on BBS_V_ACCOUNT to ACCUSER;
grant SELECT on BBS_CONF_ACCOUNT_RESERVE to ACCUSER;
grant DELETE on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant ALTER on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant SELECT on BBS_CONF_ACCOUNT_MAPPING to ACCUSER;
grant INSERT on BPL_LOG_ACTION_DETAIL to ACCUSER;


grant SELECT on BBS_CONF_REINS_RISK to DMUSER;
grant SELECT on BBS_CONF_BASE_TREATY to DMUSER;
grant SELECT on BBS_CONF_REINS_REINSURER to DMUSER;
grant SELECT on BPL_LOG_ACTION to DMUSER;
grant UPDATE on BPL_LOG_ACTION to DMUSER;
grant SELECT on BBS_CONF_RISK to DMUSER;
grant SELECT on BBS_CONF_TREATY_ERR to DMUSER;
grant SELECT on BBS_CONF_REINS_FLOAT_CHARGEHIS to DMUSER;
grant SELECT on BPL_SAA_USER to DMUSER;
grant SELECT on BPL_CONF_CHECKRULE to DMUSER;
grant SELECT on BBS_RISK_CLASS to DMUSER;
grant SELECT on BPL_CONF_ADAPT to DMUSER;
grant SELECT on BPL_SAA_BASIC_SYSTEM to DMUSER;
grant SELECT on BBS_CONF_RISK_CLASS to DMUSER;
grant SELECT on BBS_CONF_FEE_TYPE_MAPPING to DMUSER;
grant SELECT on BBS_CONF_RISK_MAPPING to DMUSER;
grant SELECT on BBS_CONF_REINS_FLOAT_CHARGE to DMUSER;
grant SELECT on BBS_CONF_TREATY_CLASS to DMUSER;
grant SELECT on BBS_CONF_TREATY to DMUSER;
grant INSERT on BPL_SAA_OPEN_PATH to DMUSER;
grant SELECT on BPL_SAA_OPEN_PATH to DMUSER;
grant UPDATE on BPL_SAA_OPEN_PATH to DMUSER;
grant EXECUTE on BPL_PACK_ACTION_LOG to DMUSER;
grant EXECUTE on BPL_PACK_COMMON to DMUSER;
grant EXECUTE on BPL_PACK_DATA_SYNC to DMUSER;
grant EXECUTE on BPL_PACK_TRACE_JOB to DMUSER;
grant EXECUTE on BPL_PACK_CHECK to DMUSER;
grant SELECT on BPL_V_CONF_CODE to DMUSER;
grant SELECT on BBS_CONF_ACCOUNT_YEAR_MONTH to DMUSER;
grant INSERT on BMS_SQL_OPER_LOG to DMUSER;
grant SELECT on BMS_SQL_OPER_LOG to DMUSER;
grant SELECT on BMS_SEQ_SQL_OPEL_LOG to DMUSER;
grant SELECT on BBS_V_ACCOUNT to DMUSER;
grant SELECT on BBS_CONF_LOA_TIC to DMUSER;
grant SELECT on BBS_CONF_LOA_DETAIL to DMUSER;
grant SELECT on BBS_CONF_LOA_TICHIS to DMUSER;
grant SELECT on BBS_CONF_RISK_MAPPINGHIS to DMUSER;
grant SELECT on BPL_LOG_ACTION_DETAIL to DMUSER;

grant SELECT on BPL_CONF_CHECKRULE to EXPUSER;
grant EXECUTE on BPL_PACK_CHECK to EXPUSER;
grant EXECUTE on BPL_PACK_TRACE_JOB to EXPUSER;
grant EXECUTE on BPL_PACK_ACTION_LOG to EXPUSER;
grant SELECT on BMS_SQL_OPER_LOG to EXPUSER;
grant INSERT on BMS_SQL_OPER_LOG to EXPUSER;
grant UPDATE on BPL_SAA_OPEN_PATH to EXPUSER;
grant SELECT on BPL_SAA_OPEN_PATH to EXPUSER;
grant INSERT on BPL_SAA_OPEN_PATH to EXPUSER;
grant SELECT on BPL_CONF_ADAPT to EXPUSER;
grant REFERENCES on BPL_LOG_ACTION to EXPUSER;
grant INSERT on BPL_LOG_ACTION to EXPUSER;
grant SELECT on BMS_SEQ_SQL_OPEL_LOG to EXPUSER;
grant SELECT on BBS_CONF_CURRENCYRATE to EXPUSER;
grant SELECT on BBS_CONF_LOA_TICHIS to EXPUSER;
grant SELECT on BPL_LOG_ACTION_DETAIL to EXPUSER;
grant DELETE on BPL_LOG_ACTION_DETAIL to EXPUSER;
grant INSERT on BPL_LOG_ACTION_DETAIL to EXPUSER;
grant SELECT on BBS_V_ACCOUNT to EXPUSER;
grant SELECT on BBS_CONF_MODEL_MAPPING to EXPUSER;
grant SELECT on BBS_CONF_ACCOUNT_YEAR_MONTH to EXPUSER;
grant SELECT on BBS_CONF_LOA_TIC to EXPUSER;
grant SELECT on BPL_LOG_ACTION to EXPUSER;
grant ALTER on BPL_LOG_ACTION to EXPUSER;
grant DELETE on BPL_LOG_ACTION to EXPUSER;


grant SELECT on BPL_SEQ_LOG_ACTION to ACCUSER;
grant SELECT on BBS_CONF_LOA_TICHIS to ACCUSER;
grant SELECT on BBS_CONF_LOA_TICHIS to ATRUSER;
grant SELECT on BBS_CONF_LOA_TICHIS to DMUSER;
grant SELECT on BBS_CONF_LOA_TICHIS to EXPUSER;
grant SELECT on BBS_CONF_LOA_TICHIS to RPTUSER;
grant SELECT on BBS_CONF_REINS_REINSURER to DMUSER;
grant SELECT on BBS_CONF_TREATY_ERR to DMUSER;
grant SELECT on BBS_CONF_CURRENCYRATE to ACCUSER;
grant SELECT on BBS_CONF_CURRENCYRATE to EXPUSER;
grant SELECT on BBS_CONF_CURRENCYRATE to ATRUSER;
grant SELECT on BBS_CONF_LOA to ACCUSER;
grant UPDATE on BBS_CONF_LOA to ATRUSER;
grant SELECT on BBS_CONF_LOA to ATRUSER;
grant SELECT on BBS_CONF_FEE_TYPE_MAPPING to DMUSER;
grant SELECT on BBS_CONF_REINS_FLOAT_CHARGE to ATRUSER;
grant SELECT on BBS_CONF_REINS_FLOAT_CHARGE to DMUSER;
grant SELECT on BBS_CONF_RISK_MAPPINGHIS to ATRUSER;
grant SELECT on BBS_CONF_RISK_MAPPINGHIS to DMUSER;
grant SELECT on BBS_CONF_RISK to DMUSER;
grant SELECT on BPL_QRTZ_CONF_TASK_DETAIL to ACCUSER;
grant SELECT on BBS_CONF_CURRENCY to ATRUSER;
grant SELECT on BBS_CONF_CURRENCY to ACCUSER;
grant SELECT on BBS_CONF_REINS_FLOAT_CHARGEHIS to ATRUSER;
grant SELECT on BBS_CONF_REINS_FLOAT_CHARGEHIS to DMUSER;
grant SELECT on BBS_CONF_REGULAR_RULE to ATRUSER;
grant SELECT on BBS_RISK_CLASS to ATRUSER;
grant SELECT on BBS_RISK_CLASS to DMUSER;
grant SELECT on BBS_CONF_ACCOUNT_MAPPING to ACCUSER;
grant SELECT on BBS_CONF_LOA_TIC to ACCUSER;
grant SELECT on BBS_CONF_LOA_TIC to ATRUSER;
grant SELECT on BBS_CONF_LOA_TIC to DMUSER;
grant SELECT on BBS_CONF_LOA_TIC to EXPUSER;
grant SELECT on BBS_CONF_LOA_TIC to RPTUSER;
grant SELECT on BPL_LOG_PUB_TASK to ACCUSER;
grant SELECT on BBS_CONF_RISK_CLASS to DMUSER;
grant SELECT on BBS_CONF_RISK_CLASS to ATRUSER;
grant SELECT on BBS_CONF_REINS_RISK to DMUSER;
grant SELECT on BPL_CONF_ADAPT to DMUSER;
grant SELECT on BPL_CONF_ADAPT to EXPUSER;
grant SELECT on BPL_CONF_ADAPT to ATRUSER;
grant SELECT on BPL_CONF_ADAPT to ACCUSER;
grant SELECT on BBS_CONF_ACCOUNT_JS to ACCUSER;
grant SELECT on BPL_SAA_BASIC_SYSTEM to DMUSER;
grant SELECT on BBS_CONF_TREATY to DMUSER;
grant SELECT on BBS_CONF_PRODUCT to ACCUSER;
grant SELECT on BPL_QRTZ_SCHEDULE_JOB_LOG to ACCUSER;
grant SELECT on BBS_CONF_RISK_MAPPING to ATRUSER;
grant SELECT on BBS_CONF_RISK_MAPPING to DMUSER;
grant SELECT on BPL_QRTZ_CONF_TASK to ACCUSER;
grant SELECT on BBS_CONF_LOA_DETAIL to DMUSER;
grant SELECT on BBS_CONF_LOA_DETAIL to ACCUSER;
grant SELECT on BBS_CONF_TREATY_CLASS to DMUSER;
grant SELECT on BPL_SAA_USER to DMUSER;
grant INSERT on BPL_SAA_OPEN_PATH to DMUSER;
grant SELECT on BPL_SAA_OPEN_PATH to DMUSER;
grant UPDATE on BPL_SAA_OPEN_PATH to DMUSER;
grant INSERT on BPL_SAA_OPEN_PATH to ACCUSER;
grant SELECT on BPL_SAA_OPEN_PATH to ACCUSER;
grant UPDATE on BPL_SAA_OPEN_PATH to ACCUSER;
grant INSERT on BPL_SAA_OPEN_PATH to ATRUSER;
grant SELECT on BPL_SAA_OPEN_PATH to ATRUSER;
grant UPDATE on BPL_SAA_OPEN_PATH to ATRUSER;
grant INSERT on BPL_SAA_OPEN_PATH to EXPUSER;
grant SELECT on BPL_SAA_OPEN_PATH to EXPUSER;
grant UPDATE on BPL_SAA_OPEN_PATH to EXPUSER;
grant INSERT on BPL_SAA_OPEN_PATH to RPTUSER;
grant SELECT on BPL_SAA_OPEN_PATH to RPTUSER;
grant UPDATE on BPL_SAA_OPEN_PATH to RPTUSER;
grant SELECT on BBS_CONF_BASE_TREATY to DMUSER;
grant SELECT on BBS_CONF_BASE_TREATY to ACCUSER;
grant SELECT on BBS_CONF_BASE_TREATY to ATRUSER;
grant SELECT on BPL_CONF_CHECKRULE to ATRUSER;
grant SELECT on BPL_CONF_CHECKRULE to DMUSER;
grant SELECT on BPL_CONF_CHECKRULE to EXPUSER;
grant SELECT on BPL_CONF_CHECKRULE to ACCUSER;
grant SELECT on BPL_LOG_ACTION to ATRUSER;
grant SELECT on BPL_LOG_ACTION to DMUSER;
grant SELECT on BPL_LOG_ACTION to EXPUSER;
grant ALTER on BPL_LOG_ACTION to ACCUSER;
grant DELETE on BPL_LOG_ACTION to ACCUSER;
grant INDEX on BPL_LOG_ACTION to ACCUSER;
grant INSERT on BPL_LOG_ACTION to ACCUSER;
grant SELECT on BPL_LOG_ACTION to ACCUSER;
grant UPDATE on BPL_LOG_ACTION to ACCUSER;
grant REFERENCES on BPL_LOG_ACTION to ACCUSER;
grant ON COMMIT REFRESH on BPL_LOG_ACTION to ACCUSER;
grant QUERY REWRITE on BPL_LOG_ACTION to ACCUSER;
grant DEBUG on BPL_LOG_ACTION to ACCUSER;
grant FLASHBACK on BPL_LOG_ACTION to ACCUSER;
grant SELECT on BPL_LOG_ACTION_DETAIL to ATRUSER;
grant SELECT on BPL_LOG_ACTION_DETAIL to DMUSER;
grant SELECT on BPL_LOG_ACTION_DETAIL to EXPUSER;
grant ALTER on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant DELETE on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant INDEX on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant INSERT on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant SELECT on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant UPDATE on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant REFERENCES on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant ON COMMIT REFRESH on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant QUERY REWRITE on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant DEBUG on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant FLASHBACK on BPL_LOG_ACTION_DETAIL to ACCUSER;
grant SELECT on BBS_CONF_MODEL_MAPPING to ACCUSER;
grant SELECT on BBS_CONF_MODEL_MAPPING to EXPUSER;
grant EXECUTE on BPL_PACK_COMMON to ACCUSER;
grant EXECUTE on BPL_PACK_TRACE_JOB to DMUSER;
grant EXECUTE on BPL_PACK_TRACE_JOB to ACCUSER;
grant EXECUTE on BPL_PACK_TRACE_JOB to ATRUSER;
grant EXECUTE on BPL_PACK_TRACE_JOB to EXPUSER;
grant EXECUTE on BPL_PACK_TRACE_JOB to RPTUSER;
grant EXECUTE on BPL_PACK_DATA_SYNC to DMUSER;
grant EXECUTE on BPL_PACK_ACTION_LOG to ATRUSER;
grant EXECUTE on BPL_PACK_ACTION_LOG to DMUSER;
grant EXECUTE on BPL_PACK_ACTION_LOG to EXPUSER;
grant EXECUTE on BPL_PACK_ACTION_LOG to ACCUSER;
grant EXECUTE on BPL_PACK_CHECK to ATRUSER;
grant EXECUTE on BPL_PACK_CHECK to DMUSER;
grant EXECUTE on BPL_PACK_CHECK to EXPUSER;
grant EXECUTE on BPL_PACK_CHECK to ACCUSER;
grant SELECT on BPL_V_CONF_CODE to DMUSER;
grant SELECT on BBS_V_ACCOUNT to EXPUSER;
grant SELECT on BBS_V_ACCOUNT to ACCUSER;
grant SELECT on BBS_V_ACCOUNT to DMUSER;
grant SELECT on BBS_V_ACCOUNT to RPTUSER;
grant SELECT on BBS_V_ACCOUNT to ATRUSER;
grant EXECUTE on BPL_PACK_COMMON to RPTUSER;
grant SELECT on BBS_CONF_ACCOUNT_RESERVE to ACCUSER;
grant SELECT on BBS_CONF_RISK_MAPPING to ACCUSER;
grant SELECT on BBS_CONF_ACCOUNT_YEAR_MONTH to ACCUSER;
grant SELECT on BBS_CONF_ACCOUNT_YEAR_MONTH to ATRUSER;
grant SELECT on BBS_CONF_ACCOUNT_YEAR_MONTH to DMUSER;
grant SELECT on BBS_CONF_ACCOUNT_YEAR_MONTH to EXPUSER;
grant SELECT on BBS_CONF_ACCOUNT_YEAR_MONTH to RPTUSER;
grant SELECT on BMS_SQL_OPER_LOG to RPTUSER;
grant SELECT on BMS_SEQ_SQL_OPEL_LOG to RPTUSER;
grant ALTER on BMS_SEQ_SQL_OPEL_LOG to RPTUSER;
grant INSERT on BMS_SQL_OPER_LOG to RPTUSER;
grant SELECT on BMS_SEQ_SQL_OPEL_LOG to ATRUSER;
grant INSERT on BMS_SQL_OPER_LOG to ATRUSER;
grant SELECT on BMS_SQL_OPER_LOG to ATRUSER;
grant SELECT on BMS_SEQ_SQL_OPEL_LOG to DMUSER;
grant INSERT on BMS_SQL_OPER_LOG to DMUSER;
grant SELECT on BMS_SQL_OPER_LOG to DMUSER;
grant SELECT on BMS_SEQ_SQL_OPEL_LOG to EXPUSER;
grant INSERT on BMS_SQL_OPER_LOG to EXPUSER;
grant SELECT on BMS_SQL_OPER_LOG to EXPUSER;
grant SELECT on BMS_SEQ_SQL_OPEL_LOG to ACCUSER;
grant INSERT on BMS_SQL_OPER_LOG to ACCUSER;
grant SELECT on BMS_SQL_OPER_LOG to ACCUSER;
grant UPDATE on BPL_LOG_ACTION to DMUSER;
grant ALTER on BPL_LOG_ACTION to EXPUSER;
grant DELETE on BPL_LOG_ACTION to EXPUSER;
grant INSERT on BPL_LOG_ACTION to EXPUSER;
grant REFERENCES on BPL_LOG_ACTION to EXPUSER;
grant DELETE on BPL_LOG_ACTION_DETAIL to EXPUSER;
grant INSERT on BPL_LOG_ACTION_DETAIL to EXPUSER;
grant INSERT on BPL_SAA_OPEN_PATH to QTCUSER;
grant SELECT on BPL_SAA_OPEN_PATH to QTCUSER;
grant UPDATE on BPL_SAA_OPEN_PATH to QTCUSER;
grant SELECT on BBS_CONF_LOA to QTCUSER;
grant SELECT on BBS_ENTITY to QTCUSER;
grant EXECUTE on BPL_PACK_COMMON to EXPUSER;
grant EXECUTE on BPL_PACK_COMMON to DMUSER;
grant EXECUTE on BPL_PACK_COMMON to ATRUSER;
grant EXECUTE on BPL_PACK_COMMON to QTCUSER;
grant SELECT on BBS_V_ACCOUNT to QTCUSER;



  
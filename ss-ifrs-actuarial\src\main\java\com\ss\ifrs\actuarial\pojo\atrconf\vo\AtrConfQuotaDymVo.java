/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-13 15:48:14
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-13 15:48:14<br/>
 * Description: 假设值主表 （基于事故年月）<br/>
 * Table Name: ATR_CONF_QUOTA_DYM<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "假设值主表 （基于事故年月）")
public class AtrConfQuotaDymVo implements Serializable {
    /**
     * Database column: ATR_CONF_QUOTA_DYM.QUOTA_ID
     * Database remarks: 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private Long quotaId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.CENTER_ID
     * Database remarks: 业务单位
     */
    @ApiModelProperty(value = "业务单位", required = true)
    private Long entityId;

    @ApiModelProperty(value = "year_month|评估期", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.LOA_CODE
     * Database remarks: LOA编码
     */
    @ApiModelProperty(value = "LOA编码", required = true)
    private String loaCode;


    /**
     * Database column: ATR_CONF_QUOTA_DYM.DIMENSION_VALUE
     * Database remarks: 假设维度值
     */
    @ApiModelProperty(value = "假设维度值", required = true)
    private String dimensionValue;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.QUOTA_DEF_ID
     * Database remarks: 指标ID
     */
    @ApiModelProperty(value = "指标ID", required = true)
    private Long quotaDefId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.SERIAL_NO
     * Database remarks: 版本号
     */
    @ApiModelProperty(value = "版本号", required = false)
    private Integer serialNo;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.VALID_IS
     * Database remarks: 是否有效
     */
    @ApiModelProperty(value = "是否有效", required = true)
    private String validIs;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.AUDIT_STATE
     * Database remarks: 审核状态
     */
    @ApiModelProperty(value = "审核状态", required = false)
    private String auditState;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.CHECKED_MSG
     * Database remarks: 审核意见
     */
    @ApiModelProperty(value = "审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.CHECKED_ID
     * Database remarks: 审核人
     */
    @ApiModelProperty(value = "审核人", required = false)
    private Long checkedId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.CHECKED_TIME
     * Database remarks: 审核时间
     */
    @ApiModelProperty(value = "审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.CREATOR_ID
     * Database remarks: 创建人
     */
    @ApiModelProperty(value = "创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.CREATE_TIME
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.UPDATOR_ID
     * Database remarks: 最后修改人
     */
    @ApiModelProperty(value = "最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM.UPDATE_TIME
     * Database remarks: 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间", required = false)
    private Date updateTime;

    /* 以下为业务数据传输字段 */
    private String entityCode;

    private String entityEName;

    private String entityCName;

    private String entityLName;

    private String creatorName;

    private String updatorName;

    private String quotaCode;

    private String loaCName;
    private String loaLName;
    private String loaEName;

    private String businessSourceCode;

    private String dimension;

    private String quotaType;

    private String perYearMonth;

    private List<AtrConfQuotaDymDetailVo> confQuotaDymDetailVoList;

    private static final long serialVersionUID = 1L;

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getDimensionValue() {
        return dimensionValue;
    }

    public void setDimensionValue(String dimensionValue) {
        this.dimensionValue = dimensionValue;
    }

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public List<AtrConfQuotaDymDetailVo> getConfQuotaDymDetailVoList() {
        return confQuotaDymDetailVoList;
    }

    public void setConfQuotaDymDetailVoList(List<AtrConfQuotaDymDetailVo> confQuotaDymDetailVoList) {
        this.confQuotaDymDetailVoList = confQuotaDymDetailVoList;
    }

    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public String getLoaCName() {
        return loaCName;
    }

    public void setLoaCName(String loaCName) {
        this.loaCName = loaCName;
    }

    public String getLoaLName() {
        return loaLName;
    }

    public void setLoaLName(String loaLName) {
        this.loaLName = loaLName;
    }

    public String getLoaEName() {
        return loaEName;
    }

    public void setLoaEName(String loaEName) {
        this.loaEName = loaEName;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getQuotaType() {
        return quotaType;
    }

    public void setQuotaType(String quotaType) {
        this.quotaType = quotaType;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getPerYearMonth() {
        return perYearMonth;
    }

    public void setPerYearMonth(String perYearMonth) {
        this.perYearMonth = perYearMonth;
    }
}
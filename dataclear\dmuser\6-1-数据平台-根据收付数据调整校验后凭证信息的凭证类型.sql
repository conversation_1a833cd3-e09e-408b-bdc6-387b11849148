--存在收付数据的类型为FI的凭证信息中凭证类型修改为BU
update dm_fin_voucher t set remark = 'FI', posting_type_code = 'BU' where t.posting_type_code = 'FI' and substr(t.task_code,8,6) >= '202101' and substr(t.task_code,8,6) <= '202103' and
    exists (select 1 from dm_acc_payment x where x.bu_voucher_no = t.voucher_no and substr(x.task_code,8,6) = substr(t.task_code,8,6))
--不存在收付数据的类型为BU的凭证信息中凭证类型修改为FI
update dm_fin_voucher T set remark = 'BU', posting_type_code = 'FI' where t.posting_type_code = 'BU' and substr(t.task_code,8,6) >= '202101' and substr(t.task_code,8,6) <= '202103' and
    not exists (select 1 from dm_acc_payment x where x.bu_voucher_no = t.voucher_no and substr(x.task_code,8,6) = substr(t.task_code,8,6))
create or replace package acc_pack_data_sync is

    --获取计量数据因子的结果值
    FUNCTION func_get_atr_factor_vlaue(p_factorCode IN VARCHAR2, p_evaluateMainId in VARCHAR2,p_icgNo in VARCHAR2, p_factorType  in VARCHAR2) RETURN NUMBER;

    FUNCTION func_get_atr_profit_loss(P_MODEL_ID       IN VARCHAR2,
                                      P_EVALUATEMAINID IN VARCHAR2,
                                      P_ICGNO          IN VARCHAR2,
                                      P_FACTORTYPE     IN VARCHAR2)
        RETURN VARCHAR2;

    FUNCTION func_get_atr_business(p_type IN VARCHAR2,p_businessModel IN VARCHAR2, p_business_no IN VARCHAR2) RETURN VARCHAR2;

    PROCEDURE proc_add_actuarial(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER);

    PROCEDURE proc_add_expense_allocation(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER);
    PROCEDURE proc_add_accpayment(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER);
    PROCEDURE proc_add_ledger_balance(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER);
    PROCEDURE proc_add_article_balance(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER);
    PROCEDURE proc_add_acc_voucher(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER);
    --获取海内/外标识
    FUNCTION  func_get_offshore(p_type IN VARCHAR2, p_business_no IN VARCHAR2, p_business_direction IN VARCHAR2) RETURN VARCHAR2;

    --获取海内/外标识
    FUNCTION  func_get_offshore2(p_type IN VARCHAR2, p_buins_type IN VARCHAR2, p_business_no IN VARCHAR2, p_business_direction IN VARCHAR2) RETURN VARCHAR2;

    --获取评估方法
    FUNCTION  func_get_evaluate_approach(p_ri_arrangement_code IN VARCHAR2,p_business_no IN VARCHAR2, p_business_direction IN VARCHAR2, p_risk_code IN VARCHAR2) RETURN VARCHAR2;
    --获取业务信息
    FUNCTION  func_get_business_msg(p_payment_id IN number,p_type IN VARCHAR2) RETURN VARCHAR2;
    --获取时间
    FUNCTION  func_get_date_time(p_business_no IN VARCHAR2, p_buins_type IN VARCHAR2, p_ri_arrangement_code IN VARCHAR2, p_business_direction IN VARCHAR2, p_type IN VARCHAR2) RETURN TIMESTAMP;

end acc_pack_data_sync;
/
create or replace package body acc_pack_data_sync is


    /***********************************************************************
    NAME :func_get_factor_vlaue
    DESCRIPTION :获取计量数据因子的结果值
    AUTHOR :wuyh
    BUSINESS RULE : p_factorCode 因子虚拟字段， p_evaluateMainId：计量结果主键 ，p_icgNo：合同组号 ，p_factorType：因子类型 （Lic，Lrc）
    ***********************************************************************/
    FUNCTION func_get_atr_factor_vlaue(p_factorCode IN VARCHAR2, p_evaluateMainId in VARCHAR2,p_icgNo in VARCHAR2, p_factorType  in VARCHAR2)
        RETURN NUMBER IS
        v_sql              VARCHAR2(4000);   --执行 sql脚本
        v_result           NUMBER(16,2);
    BEGIN
        v_sql:= 'select sum(' || p_factorCode || ') from QTCUSER.QTC_BUSS_EVALUATE_RESULT Tr '
            || ' WHERE tr.evaluate_main_id='|| p_evaluateMainId || ' AND Tr.icg_no= ''' || p_icgNo||'''' || ' AND Tr.var1= ''' || p_factorType||'''';
        begin
            EXECUTE immediate v_sql
                INTO v_result;
        EXCEPTION
            WHEN OTHERS THEN
                dbms_output.put_line('**SQLSTATE: '||to_char(SQLCODE)||'; **SQLERRM: '||SUBSTR(SQLERRM, 1, 200)||',【脚本解析失败，请检查脚本Sql：'||v_sql||'】');
                RETURN null;
        end;
        --返回脚本结果
        RETURN v_result;
    end func_get_atr_factor_vlaue;


    /***********************************************************************
    NAME :func_get_atr_profit_loss
    DESCRIPTION :获取计量合同组盈亏信息
    BUSINESS RULE : P_MODEL_ID： 1-模型id
                    P_EVALUATEMAINID：业务主键id
                    P_ICGNO ： 合同组
                    P_FACTORTYPE：因子类型
    ***********************************************************************/
    FUNCTION func_get_atr_profit_loss(P_MODEL_ID       IN VARCHAR2,
                                      P_EVALUATEMAINID IN VARCHAR2,
                                      P_ICGNO          IN VARCHAR2,
                                      P_FACTORTYPE     IN VARCHAR2) RETURN VARCHAR2 IS

        v_result           VARCHAR2(1);
        v_profit_loss      NUMBER;
    BEGIN
        --再保前BBA
        IF p_model_id = 1 THEN
            SELECT NUM54 INTO v_profit_loss
            FROM QTCUSER.QTC_BUSS_EVALUATE_RESULT TR
            WHERE TR.EVALUATE_MAIN_ID = P_EVALUATEMAINID
              AND TR.ICG_NO = P_ICGNO
              AND tr.var1=P_FACTORTYPE;
            --再保前PAA
        ELSIF p_model_id = 3 THEN
            SELECT NUM12 INTO v_profit_loss
            FROM QTCUSER.QTC_BUSS_EVALUATE_RESULT TR
            WHERE TR.EVALUATE_MAIN_ID = P_EVALUATEMAINID
              AND TR.ICG_NO = P_ICGNO
              AND tr.var1=P_FACTORTYPE;
        END IF;

        --转化成盈亏枚举值
        IF v_profit_loss = 1 THEN
            v_result := 'P';
        ELSIF v_profit_loss = 2 THEN
            v_result := 'L';
        ELSE
            v_result := 'D';
        END IF;
        RETURN v_result;
    EXCEPTION
        WHEN OTHERS THEN
            dbms_output.put_line('[EXCEPTION]FUNC_GET_ATR_PROFIT_LOSS：' || to_char(SQLCODE) || ','|| SUBSTR(SQLERRM, 1, 200));
            RETURN '';
    END FUNC_GET_ATR_PROFIT_LOSS;


    /***********************************************************************
    NAME :func_get_BUSINESS_SOURCE_CODE
    DESCRIPTION :获取业务类型或业务方向
    DATE :2022-10-31
    AUTHOR :CHENJUNFENG
    BUSINESS RULE : p_type： 1-获取业务类型，2-获取业务方向
                    p_businessModel：业务模型
                    p_business_no ： 业务号
    ***********************************************************************/
    FUNCTION func_get_atr_business(p_type IN VARCHAR2,p_businessModel IN VARCHAR2, p_business_no IN VARCHAR2) RETURN VARCHAR2 IS

        v_BUSINESS_SOURCE_CODE varchar2(2);
    BEGIN
        IF p_type = '1' THEN
            if p_businessModel = 'TI' then
                v_BUSINESS_SOURCE_CODE :='TB';
            elsif p_businessModel = 'TO' then
                v_BUSINESS_SOURCE_CODE :='TB';
            elsif p_businessModel = 'FO' then
                v_BUSINESS_SOURCE_CODE :='FB';
            elsif p_businessModel = 'DD' then
                SELECT MAX(t.BUSINESS_SOURCE_CODE) INTO v_BUSINESS_SOURCE_CODE FROM atruser.atr_dap_dd_premium t
                WHERE icg_no = p_business_no AND rownum=1;
            end if;
        ELSIF p_type = '2' THEN
            if p_businessModel = 'TI' then
                v_BUSINESS_SOURCE_CODE :='I';
            elsif p_businessModel = 'TO' then
                v_BUSINESS_SOURCE_CODE :='O';
            elsif p_businessModel = 'FO' then
                v_BUSINESS_SOURCE_CODE :='O';
            elsif p_businessModel = 'DD' then
                SELECT  MAX(CASE WHEN t.BUSINESS_SOURCE_CODE = 'FB' THEN 'I' ELSE 'D' END ) INTO v_BUSINESS_SOURCE_CODE FROM atruser.atr_dap_dd_premium t
                WHERE icg_no = p_business_no AND rownum=1;
            end if;
        ELSIF p_type = '3' THEN
            if p_businessModel = 'TI' then
                RETURN 'T';
            elsif p_businessModel = 'TO' then
                RETURN 'T';
            elsif p_businessModel = 'FO' then
                RETURN 'F';
            elsif p_businessModel = 'DD' then
                RETURN 'D';
            end if;
        END IF;
        RETURN v_BUSINESS_SOURCE_CODE;
    EXCEPTION
        WHEN OTHERS THEN
            dbms_output.put_line('[EXCEPTION]func_get_BUSINESS_SOURCE_CODE：' || to_char(SQLCODE) || ','|| SUBSTR(SQLERRM, 1, 200));
            RETURN '';
    END func_get_atr_business;



    /***********************************************************************
     DESCRIPTION : 同步计量结果数据（By 会计引擎）
     DATE :2022-03-10
     AUTHOR :cjf
    ***********************************************************************/
    PROCEDURE proc_add_actuarial(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER) IS

        v_data_status VARCHAR2(500);
        v_period_detail_id NUMBER(11);
        v_period_detail_status VARCHAR2(500);
        v_account_posting_qtp  NUMBER(11);
        v_error_msg       VARCHAR2(4000);
    BEGIN
        --验证源数据是否已准备
        SELECT execution_state INTO v_data_status
        FROM atruser.atr_conf_bussperiod
        WHERE entity_id = p_entity_id
          AND year_month = p_yearmonth
          AND valid_is = '1';

        --查询当前业务期间详情id, 验证当前数据是否为准备中
        select bpd.period_detail_id,bpd.ready_state into v_period_detail_id,v_period_detail_status
        from acc_conf_accountperiod_detail bpd
                 left join acc_conf_accountperiod p
                           on bpd.period_id = p.period_id
                 left join acc_conf_table t
                           on bpd.biz_type_id = t.biz_type_id
        where p.entity_id = p_entity_id
          and p.book_code = p_bookcode
          and p.year_month = p_yearmonth
          and t.biz_code ='DAP_ATR_ENTRY_DATA';

        dbms_output.put_line('源数据准备状态:'||v_data_status||',业务期间详情id:'||v_period_detail_id||',当前数据准备状态'||v_period_detail_status);

        IF v_data_status != '3' THEN
            v_error_msg := '现行科目余额源数据数据平台期间状态不是已完成为:' || '，不执行同步：[' || p_entity_id || ']-[' || p_bookcode || ']-[' || p_yearmonth || ']';
            --往外层抛出异常信息
            raise_application_error(-20002, v_error_msg);
        END IF;

        IF v_data_status = '3' and v_period_detail_status = '0' THEN

            select proc_id into v_account_posting_qtp from bpl_act_re_procdef where proc_code = 'ACC_ACCOUNTENTRY_QTP_BCR';

            --先清空当前业务年月计量数据
            DELETE FROM ACC_DAP_ENTRY_DATA B
            WHERE B.entity_id = p_entity_id
              AND B.YEAR_MONTH = P_YEARMONTH
              AND PROC_ID =
                  (SELECT PROC_ID
                   FROM BPL_ACT_RE_PROCDEF
                   WHERE PROC_CODE = 'ACC_ACCOUNTENTRY_QTP_BCR');

            --写入数据
            INSERT INTO ACC_DAP_ENTRY_DATA
            (entry_data_id,--entry_data_id|主键;
             entity_id,
             year_month,
             posting_type_code,
             proc_id,
             currency_code,
             amount,
             BUSINESS_SOURCE_CODE,
             RI_ARRANGEMENT_CODE,
             RI_DIRECTION_CODE,
             TREATY_TYPE_CODE,
             portfolio_no,
             icg_no,
             input_date,
             extend_column4,
             extend_column3,
             EVALUATE_APPROACH,--评估方法
             extend_column6,   --业务线
             extend_column7,
             OFFSHORE_IS,
             BU_VOUCHER_NO,
             extend_column8,
             business_id
            )
            SELECT acc_seq_dap_entry_data.nextval as entry_data_id,
                   tr.entity_id,
                   tr.year_month,
                   posting_type_code, --posting_type_code
                   proc_id, -- 入账处理[计量]流程节点id proc_id
                   currency_code,
                   tr.amount,
                   BUSINESS_SOURCE_CODE, --与因子表，再转换，按照图片值显示 ，直接因子表的值会不会跟截图图片不一致？3
                   RI_ARRANGEMENT_CODE, --与因子表，再转换，按照图片值显示 ,因子表哪个字段，还是直接'D'？4
                   RI_DIRECTION_CODE,
                   '00' as tty_type,
                   tr.portfolio_no as portfolio_no,
                   tr.icg_no as icg_no,
                   sysdate as input_date, --input_date|入机时间，默认sysdate;
                   tr.factor_code,
                   tr.unit_no,
                   evaluate_approach,
                   extend_column6,
                   factor_type,
                   OFFSHORE_IS,
                   BU_VOUCHER_NO,
                   extend_column8,
                   business_id
            from (SELECT m.entity_id,
                         m.year_month,
                         '04' as posting_type_code,
                         v_account_posting_qtp as proc_id,
                         m.currency_code,
                         acc_pack_data_sync.func_get_atr_factor_vlaue(T3.TAB_COLUMN,m.evaluate_main_id,t2.icg_no, T2.Var1)
                             *nvl(T3.CAL_SIGN,1) as amount,
                         acc_pack_data_sync.func_get_atr_business('1', m.BUSINESS_MODEL || m.business_direction,t2.icg_no) as BUSINESS_SOURCE_CODE,--分出业务类型不区分
                         (case when m.business_direction = 'O' then m.BUSINESS_MODEL else 'D' end) as RI_ARRANGEMENT_CODE,--'D' as RI_ARRANGEMENT_CODE, --计量分保安排
                         acc_pack_data_sync.func_get_atr_business('2', m.BUSINESS_MODEL || m.business_direction,t2.icg_no) as RI_DIRECTION_CODE,
                         t2.Portfolio_No,
                         t2.icg_no,
                         T3.VALUE_REF AS factor_code,
                         t2.unit_no,
                         cl.Evaluate_Approach  AS Evaluate_Approach,
                         m.loa_code AS extend_column6,
                         T2.Var1 AS factor_type,
                         func_get_offshore2('2', (case WHEN m.BUSINESS_MODEL  in ('D','F') then '1' when  m.BUSINESS_MODEL in ('T') then '2' else '' end ), t2.icg_no, m.business_direction) AS OFFSHORE_IS,
                         t2.icg_no||T2.Var1 AS BU_VOUCHER_NO,--20230719 计量按合同组+计量类型生成凭证
                         (CASE WHEN T2.Var1 = 'Lrc' THEN
                                   acc_pack_data_sync.FUNC_GET_ATR_PROFIT_LOSS(m.model_def_id,m.evaluate_main_id,t2.icg_no, T2.Var1)
                               ELSE '' END ) AS extend_column8,
                         T2.EVALUATE_RESULT_ID AS business_id
                  FROM QTCUSER.QTC_BUSS_EVALUATE_MAIN M
                           LEFT JOIN bpluser.bbs_conf_loa cl
             ON (CASE WHEN m.business_model||m.business_direction = 'FO' THEN 'DD' ELSE m.business_model||m.business_direction END) = cl.business_model||cl.BUSINESS_DIRECTION 
                and M.Loa_Code = cl.loa_code
                and cl.entity_id = m.entity_id
                           LEFT JOIN QTCUSER.QTC_BUSS_EVALUATE_RESULT T2
                                     ON M.EVALUATE_MAIN_ID = T2.EVALUATE_MAIN_ID
                           LEFT JOIN QTCUSER.qtc_conf_factor_output_ref T3
                                     ON m.model_def_id = T3.Model_Def_Id
                                         AND T2.Var1=T3.FACTOR_TYPE
                                         AND T3.account_flag = '1'
                  WHERE m.entity_id = p_entity_id
                    AND m.YEAR_MONTH = p_yearmonth
                    AND m.CONFIRM_IS = '1'
                    AND T2.Icg_No IS NOT NULL
                 ) tr
            WHERE tr.amount <> 0;

            --修改acc_conf_accountperiod_detail为已准备状态
            update acc_conf_accountperiod_detail set ready_state='1',exec_result='success',task_time=sysdate where period_detail_id = v_period_detail_id;

            --提交事务
            COMMIT;
            --同步业务期间执行状态
            acc_pack_buss_period.proc_period_execution(p_entity_id, p_bookcode, '1');
            dbms_output.put_line('同步业务期间执行状态完成');
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            --修改rpt_conf_bussperiod_detail为失败状态
            UPDATE acc_conf_accountperiod_detail
            SET ready_state = '0',
                exec_result = 'failed',
                task_time   = SYSDATE
            WHERE period_detail_id = v_period_detail_id;
            --提交事务
            COMMIT;
            --抛出自定义异常信息 【会中断事务】
            v_error_msg := substr('会计同步计量结果发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);

            --往外层抛出异常信息
            raise_application_error(-20003, v_error_msg);
    END proc_add_actuarial;


    /***********************************************************************
    DESCRIPTION : 同步合同组分摊数据（By 费用分摊）
    DATE :2021-12-29
    AUTHOR :wyh
    ***********************************************************************/
    PROCEDURE proc_add_expense_allocation(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER) IS

        v_data_status          VARCHAR2(100);
        v_period_detail_id     NUMBER(11);
        v_period_detail_status VARCHAR2(100);

        v_insert_sql VARCHAR(1000);
        v_select_sql VARCHAR(1000);
        v_snyc_sql   VARCHAR(30000);
        v_exp_dimension VARCHAR(1);
        v_count      NUMBER(11);
        v_error_msg  VARCHAR(4000);
        v_tran_ym   VARCHAR(6);
    BEGIN

        --验证源数据是否已准备
        SELECT execution_state INTO v_data_status
        FROM expuser.exp_conf_bussperiod
        WHERE entity_id = p_entity_id
          AND year_month = p_yearmonth
          AND valid_is = '1';
        --查询当前业务期间详情id, 验证当前数据是否为准备中
        select bpd.period_detail_id,bpd.ready_state into v_period_detail_id,v_period_detail_status
        from acc_conf_accountperiod_detail bpd
                 left join acc_conf_accountperiod p
                           on bpd.period_id = p.period_id
                 left join acc_conf_table t
                           on bpd.biz_type_id = t.biz_type_id
        where p.entity_id = p_entity_id
          and p.book_code = p_bookcode
          and p.year_month = p_yearmonth
          --and t.biz_code ='DAP_ENTRY_DATA_ALLOCATION';
          and t.biz_code ='DAP_EXP_ENTRY_DATA';

        SELECT COUNT(T.CODE_CODE) INTO v_count
        FROM BPLUSER.BPL_V_CONF_CODE T
        WHERE UPPER_CODE_ID =
              (SELECT T.CODE_ID
               FROM BPLUSER.BPL_V_CONF_CODE T
               WHERE T.CODE_CODE_IDX = 'ExpAllocationDimension')
          AND VALID_IS = '1'
          AND ROWNUM = 1;

        IF v_count > 0 THEN
            SELECT T.CODE_CODE
            INTO V_EXP_DIMENSION
            FROM BPLUSER.BPL_V_CONF_CODE T
            WHERE UPPER_CODE_ID =
                  (SELECT T.CODE_ID
                   FROM BPLUSER.BPL_V_CONF_CODE T
                   WHERE T.CODE_CODE_IDX = 'ExpAllocationDimension')
              AND VALID_IS = '1'
              AND ROWNUM = 1;
        ELSE
            V_EXP_DIMENSION := '1';
        END IF;

        --判断是否是过渡期当月 ********
        begin
            SELECT t.code_code into v_tran_ym
            FROM acc_conf_code t
            where t.upper_code_id =
                  (SELECT a.code_id
                   FROM acc_v_conf_code a
                   where a.code_code_idx = 'TransitionPeriod')
              and t.valid_is = '1';

            --过渡期特殊处理
            if v_tran_ym is not null and p_yearmonth < v_tran_ym then
                update acc_conf_accountperiod_detail t set t.ready_state = '1',t.task_time = sysdate where t.period_detail_id = v_period_detail_id;
                acc_pack_buss_period.proc_period_execution(p_entity_id, p_bookcode, '1');
                return ;
            end if;

        EXCEPTION
            WHEN OTHERS THEN
                --抛出异常提示信息
                --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
                --RAISE EXCEPTION '[EXCEPTION]测试异常：%, %',v_text1,v_text2;
                dbms_output.put_line('[EXCEPTION]proc_add_expense_allocation TransitionPeriod：' ||
                                     to_char(SQLCODE) || ';' ||
                                     substr(SQLERRM, 1, 200));

        end;



        dbms_output.put_line('源数据准备状态:'||v_data_status||',业务期间详情id:'||v_period_detail_id||',当前数据准备状态'||v_period_detail_status);

        select ','||listagg(source_column,',') within group(order by source_column ), ','||listagg(mapping_column,',') within group(order by mapping_column )
        into v_select_sql,v_insert_sql
        from bpluser.bbs_conf_model_mapping
        WHERE source_table='EXP_BUSS_ALLOCATION'
          and mapping_table = 'ACC_DAP_ENTRY_DATA';

        IF v_data_status != '3' THEN
            v_error_msg := '费用分摊期间状态不是已完成为:' || '，不执行同步：[' || p_entity_id || ']-[' || p_bookcode || ']-[' || p_yearmonth || ']';
            --往外层抛出异常信息
            raise_application_error(-20002, v_error_msg);
        END IF;

        IF v_data_status = '3' and v_period_detail_status = '0' THEN
            --先清空当前业务年月数据
            DELETE FROM ACC_DAP_ENTRY_DATA b
            where b.entity_id = p_entity_id
              and b.year_month = p_yearmonth
              and proc_id in (select proc_id from bpl_act_re_procdef
                              where proc_code = 'ACC_ACCOUNTENTRY_EXP_BCR');
            IF V_EXP_DIMENSION = '1' THEN
                --写入数据
                v_snyc_sql := 'INSERT INTO ACC_DAP_ENTRY_DATA
      (ENTRY_DATA_ID, -- id|主键
       entity_id, -- entity_id|核算单位ID
       posting_type_code, -- PostType| 05-费用分摊
       YEAR_MONTH,
       PROC_ID, -- PROC_ID|数据归属节点  BPL_ACT_RE_PROCDEF
       EXPENSES_TYPE_CODE, -- EXPENSES_TYPE_CODE_Code|费用类型编码
       currency_code, -- Cuurency|币别
       AMOUNT, -- Amount|金额
       RISK_CODE, -- Risk_Code|险种编码
       DEPT_ID, -- DepId|部门ID
       DEPT_CODE,
       BUSINESS_SOURCE_CODE,--BUSINESS_SOURCE_CODE|业务类型 BPL_CODE = BUSINESS_SOURCE_CODEDB直接业务 FB临汾业务 CB理赔业务 TB合约业务;
       POLICY_TYPE_CODE,--POLICY_TYPE_CODE|保单类型 POLICY_TYPE_CODE  N新单  E 批单 C 退保  R 续保;
       RI_ARRANGEMENT_CODE,--RI_ARRANGEMENT_CODE|业务类型编码  RI_ARRANGEMENT_CODE I分入业务 S自营业务 D不区分;
       RI_DIRECTION_CODE,--RI_DIRECTION_CODE|分入分出标志[I分入 O分出];
       TREATY_TYPE_CODE,
       PORTFOLIO_NO, -- ContractGroupNo|合同组编号
       ICG_NO, -- ContractNo|合同编号
       extend_column3,
       INPUT_DATE,
       BU_VOUCHER_NO,
       business_id,
       extend_column6,
       TASK_CODE'  ||v_insert_sql ||'
      )
      SELECT ACC_SEQ_DAP_ENTRY_DATA.NEXTVAL,
       entity_id,
       posting_type_code,
       YEAR_MONTH,
       PROC_ID,
       EXPENSES_TYPE_CODE,
       currency_code,
       CURRENT_AMOUNT,
       RISK_CODE,
       BENF_entity_id,
       DEPT_CODE,
       BUSINESS_SOURCE_CODE,
       POLICY_TYPE_CODE,
       RI_ARRANGEMENT_CODE,
       RI_DIRECTION_CODE,
       TREATY_TYPE_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       CMUNIT_NO,
       LOCALTIMESTAMP,
       BU_VOUCHER_NO,
       business_id,
       extend_column6,
       TASK_CODE,
       EXP_EXPENSES_TYPE_CODE
      FROM (SELECT
       exp.entity_id, -- entity_id|核算部门
       ''05'' as posting_type_code,
       exp.YEAR_MONTH,
       (select proc_id
          from bpluser.bpl_act_re_procdef
         where proc_code = ''ACC_ACCOUNTENTRY_EXP_BCR'') as PROC_ID, -- 入账处理[费用分摊]流程节点id
       AC.EXPENSES_TYPE_CODE,
       exp.currency_code,
       exp.current_amount,
       exp.RISK_CODE,
       exp.BENF_entity_id, --benf_entity_id|收益部门
       bc.COMPANY_CODE as DEPT_CODE,
       (CASE WHEN exp.BUSINESS_SOURCE_CODE =''CB'' THEN NULL ELSE exp.BUSINESS_SOURCE_CODE END) BUSINESS_SOURCE_CODE,
      ''D'' as POLICY_TYPE_CODE,
      ''D'' as RI_ARRANGEMENT_CODE,
      ''D'' as RI_DIRECTION_CODE,
      ''00'' as TREATY_TYPE_CODE,
       exp.account_id as account_id,
       exp.PORTFOLIO_NO,
       exp.ICG_NO,
       exp.CMUNIT_NO,
       LOCALTIMESTAMP,
       coalesce(exp.icg_NO,exp.year_month) as BU_VOUCHER_NO,
       exp.ALLOCATION_ID as business_id,
       cd.loa_code as extend_column6,
       exp.TASK_CODE' ||   v_select_sql ||'
      FROM expuser.exp_buss_allocation exp
      LEFT JOIN EXPUSER.EXP_CONF_ACCOUNT AC
       ON exp.account_id = AC.account_id
      LEFT JOIN bpluser.bpl_company bc
       ON exp.BENF_entity_id = bc.COMPANY_ID
      LEFT JOIN (SELECT Portfolio_No,loa_code  FROM  dmuser.dm_buss_cmunit_direct cd  GROUP BY  Portfolio_No,loa_code
         union all
         SELECT Portfolio_No,loa_code  FROM  dmuser.dm_buss_cmunit_fac_outwards cf  GROUP BY  Portfolio_No,loa_code
         union all
         SELECT Portfolio_No,loa_code  FROM  dmuser.dm_buss_cmunit_fac_outwards cf  GROUP BY  Portfolio_No,loa_code ) cd ON EXP.Portfolio_No = cd.portfolio_no
      WHERE exp.entity_id = ' || p_entity_id ||'
       and exp.year_month = ''' || p_yearmonth || '''' ||'
       and exp.current_amount <> 0
       )';
            ELSIF V_EXP_DIMENSION = '2' THEN
                --写入数据
                v_snyc_sql := 'INSERT INTO ACC_DAP_ENTRY_DATA
      (ENTRY_DATA_ID, -- id|主键
       entity_id, -- entity_id|核算单位ID
       posting_type_code, -- PostType| 05-费用分摊
       YEAR_MONTH,
       PROC_ID, -- PROC_ID|数据归属节点  BPL_ACT_RE_PROCDEF
       EXPENSES_TYPE_CODE, -- EXPENSES_TYPE_CODE_Code|费用类型编码
       currency_code, -- Cuurency|币别
       AMOUNT, -- Amount|金额
       RISK_CODE, -- Risk_Code|险种编码
       DEPT_ID, -- DepId|部门ID
       DEPT_CODE,
       BUSINESS_SOURCE_CODE,--BUSINESS_SOURCE_CODE|业务类型 BPL_CODE = BUSINESS_SOURCE_CODEDB直接业务 FB临汾业务 CB理赔业务 TB合约业务;
       POLICY_TYPE_CODE,--POLICY_TYPE_CODE|保单类型 POLICY_TYPE_CODE  N新单  E 批单 C 退保  R 续保;
       RI_ARRANGEMENT_CODE,--RI_ARRANGEMENT_CODE|业务类型编码  RI_ARRANGEMENT_CODE I分入业务 S自营业务 D不区分;
       RI_DIRECTION_CODE,--RI_DIRECTION_CODE|分入分出标志[I分入 O分出];
       TREATY_TYPE_CODE,
       PORTFOLIO_NO, -- ContractGroupNo|合同组编号
       ICG_NO, -- ContractNo|合同编号
       INPUT_DATE,
       BU_VOUCHER_NO,
       business_id,
       extend_column6,
       TASK_CODE'  ||v_insert_sql ||'
      )
      SELECT ACC_SEQ_DAP_ENTRY_DATA.NEXTVAL,
       entity_id,
       posting_type_code,
       YEAR_MONTH,
       PROC_ID,
       EXPENSES_TYPE_CODE,
       currency_code,
       CURRENT_AMOUNT,
       RISK_CODE,
       BENF_entity_id,
       DEPT_CODE,
       BUSINESS_SOURCE_CODE,
       POLICY_TYPE_CODE,
       RI_ARRANGEMENT_CODE,
       RI_DIRECTION_CODE,
       TREATY_TYPE_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       LOCALTIMESTAMP,
       BU_VOUCHER_NO,
       business_id,
       extend_column6,
       TASK_CODE,
       EXP_EXPENSES_TYPE_CODE
      FROM (SELECT
       exp.entity_id, -- entity_id|核算部门
       ''05'' as posting_type_code,
       exp.YEAR_MONTH,
       (select proc_id
          from bpluser.bpl_act_re_procdef
         where proc_code = ''ACC_ACCOUNTENTRY_EXP_BCR'') as PROC_ID, -- 入账处理[费用分摊]流程节点id
       AC.EXPENSES_TYPE_CODE,
       exp.currency_code,
       exp.current_amount,
       exp.RISK_CODE,
       exp.BENF_entity_id, --benf_entity_id|收益部门
       bc.COMPANY_CODE as DEPT_CODE,
       (CASE WHEN BUSINESS_SOURCE_CODE =''CB'' THEN NULL ELSE BUSINESS_SOURCE_CODE END) BUSINESS_SOURCE_CODE,
      ''D'' as POLICY_TYPE_CODE,
      ''D'' as RI_ARRANGEMENT_CODE,
      ''D'' as RI_DIRECTION_CODE,
      ''00'' as TREATY_TYPE_CODE,
       exp.account_id as account_id,
       exp.PORTFOLIO_NO,
       exp.ICG_NO,
       LOCALTIMESTAMP,
       coalesce(exp.icg_NO,exp.year_month) as BU_VOUCHER_NO,
       exp.ALLOCATION_ID as business_id,
       cd.loa_code as extend_column6,
       exp.TASK_CODE' ||   v_select_sql ||'
      FROM expuser.exp_buss_allocation_icg exp
      LEFT JOIN EXPUSER.EXP_CONF_ACCOUNT AC
       ON exp.account_id = AC.account_id
      LEFT JOIN bpluser.bpl_company bc
       ON exp.BENF_entity_id = bc.COMPANY_ID
      LEFT JOIN (SELECT Portfolio_No,loa_code  FROM  dmuser.dm_buss_cmunit_direct cd  GROUP BY  Portfolio_No,loa_code
         union all
         SELECT Portfolio_No,loa_code  FROM  dmuser.dm_buss_cmunit_fac_outwards cf  GROUP BY  Portfolio_No,loa_code
         union all
         SELECT Portfolio_No,loa_code  FROM  dmuser.dm_buss_cmunit_fac_outwards cf  GROUP BY  Portfolio_No,loa_code ) cd ON EXP.Portfolio_No = cd.portfolio_no
      WHERE exp.entity_id = ' || p_entity_id ||'
       and exp.year_month = ''' || p_yearmonth || '''' ||'
       and exp.current_amount <> 0
       )';

            ELSE
                --写入数据
                v_snyc_sql := 'INSERT INTO ACC_DAP_ENTRY_DATA
      (ENTRY_DATA_ID, -- id|主键
       entity_id, -- entity_id|核算单位ID
       posting_type_code, -- PostType| 05-费用分摊
       YEAR_MONTH,
       PROC_ID, -- PROC_ID|数据归属节点  BPL_ACT_RE_PROCDEF
       EXPENSES_TYPE_CODE, -- EXPENSES_TYPE_CODE_Code|费用类型编码
       currency_code, -- Cuurency|币别
       AMOUNT, -- Amount|金额
       RISK_CODE, -- Risk_Code|险种编码
       DEPT_ID, -- DepId|部门ID
       DEPT_CODE,
       BUSINESS_SOURCE_CODE,--BUSINESS_SOURCE_CODE|业务类型 BPL_CODE = BUSINESS_SOURCE_CODEDB直接业务 FB临汾业务 CB理赔业务 TB合约业务;
       POLICY_TYPE_CODE,--POLICY_TYPE_CODE|保单类型 POLICY_TYPE_CODE  N新单  E 批单 C 退保  R 续保;
       RI_ARRANGEMENT_CODE,--RI_ARRANGEMENT_CODE|业务类型编码  RI_ARRANGEMENT_CODE I分入业务 S自营业务 D不区分;
       RI_DIRECTION_CODE,--RI_DIRECTION_CODE|分入分出标志[I分入 O分出];
       TREATY_TYPE_CODE,
       PORTFOLIO_NO, -- ContractNo|合同编号
       INPUT_DATE,
       BU_VOUCHER_NO,
       business_id,
       extend_column6,
       TASK_CODE'  ||v_insert_sql ||'
      )
      SELECT ACC_SEQ_DAP_ENTRY_DATA.NEXTVAL,
       entity_id,
       posting_type_code,
       YEAR_MONTH,
       PROC_ID,
       EXPENSES_TYPE_CODE,
       currency_code,
       CURRENT_AMOUNT,
       RISK_CODE,
       BENF_entity_id,
       DEPT_CODE,
       BUSINESS_SOURCE_CODE,
       POLICY_TYPE_CODE,
       RI_ARRANGEMENT_CODE,
       RI_DIRECTION_CODE,
       TREATY_TYPE_CODE,
       PORTFOLIO_NO,
       LOCALTIMESTAMP,
       BU_VOUCHER_NO,
       business_id,
       extend_column6,
       TASK_CODE,
       EXP_EXPENSES_TYPE_CODE
      FROM (SELECT
       exp.entity_id, -- entity_id|核算部门
       ''05'' as posting_type_code,
       exp.YEAR_MONTH,
       (select proc_id
          from bpluser.bpl_act_re_procdef
         where proc_code = ''ACC_ACCOUNTENTRY_EXP_BCR'') as PROC_ID, -- 入账处理[费用分摊]流程节点id
       AC.EXPENSES_TYPE_CODE,
       exp.currency_code,
       exp.current_amount,
       exp.RISK_CODE,
       exp.BENF_entity_id, --benf_entity_id|收益部门
       bc.COMPANY_CODE as DEPT_CODE,
       (CASE WHEN exp.BUSINESS_SOURCE_CODE =''CB'' THEN NULL ELSE exp.BUSINESS_SOURCE_CODE END) BUSINESS_SOURCE_CODE,
      ''D'' as POLICY_TYPE_CODE,
      ''D'' as RI_ARRANGEMENT_CODE,
      ''D'' as RI_DIRECTION_CODE,
      ''00'' as TREATY_TYPE_CODE,
       exp.account_id as account_id,
       exp.PORTFOLIO_NO,
       LOCALTIMESTAMP,
       coalesce(exp.PORTFOLIO_NO,exp.year_month) as BU_VOUCHER_NO,
       exp.ALLOCATION_ID as business_id,
       cd.loa_code as extend_column6,
       exp.TASK_CODE' ||   v_select_sql ||'
      FROM expuser.exp_buss_allocation_port exp
      LEFT JOIN EXPUSER.EXP_CONF_ACCOUNT AC
       ON exp.account_id = AC.account_id
      LEFT JOIN bpluser.bpl_company bc
       ON exp.BENF_entity_id = bc.COMPANY_ID
      LEFT JOIN (SELECT Portfolio_No,loa_code  FROM  dmuser.dm_buss_cmunit_direct cd  GROUP BY  Portfolio_No,loa_code
         union all
         SELECT Portfolio_No,loa_code  FROM  dmuser.dm_buss_cmunit_fac_outwards cf  GROUP BY  Portfolio_No,loa_code
         union all
         SELECT Portfolio_No,loa_code  FROM  dmuser.dm_buss_cmunit_fac_outwards cf  GROUP BY  Portfolio_No,loa_code ) cd ON EXP.Portfolio_No = cd.portfolio_no
      WHERE exp.entity_id = ' || p_entity_id ||'
       and exp.year_month = ''' || p_yearmonth || '''' ||'
       and exp.current_amount <> 0
       )';
            END IF;

            EXECUTE immediate v_snyc_sql ;

            --修改acc_conf_accountperiod_detail为已准备状态
            update acc_conf_accountperiod_detail set ready_state='1',exec_result='success',task_time=sysdate where period_detail_id = v_period_detail_id;

            --提交事务
            COMMIT;
            --同步业务期间执行状态
            acc_pack_buss_period.proc_period_execution(p_entity_id, p_bookcode, '1');
            dbms_output.put_line('同步分摊数据业务期间执行状态完成');
        END IF;

    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            dbms_output.put_line(to_char(SQLCODE) || '::'|| SUBSTR(SQLERRM, 1, 200));
            --抛出自定义异常信息 【会中断事务】
            v_error_msg := substr('会计同步分摊数据发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);

            --往外层抛出异常信息
            raise_application_error(-20003, v_error_msg);
    END proc_add_expense_allocation;

    PROCEDURE proc_add_accpayment(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER) IS

        v_data_status           VARCHAR2(100);
        v_period_detail_id      NUMBER(11);
        v_period_detail_status  VARCHAR2(100);
        v_insert_sql VARCHAR(1000);
        v_select_sql VARCHAR(1000);
        v_snyc_sql   VARCHAR(30000);
        v_dm_period_count       NUMBER(11);
        v_end_count             NUMBER(11) := 0;
        v_error_msg  VARCHAR(4000);
        v_task_code  VARCHAR(1000);
        v_tran_ym   VARCHAR(6);
        v_dm_status           VARCHAR2(100);
    BEGIN
        --验证源数据是否已准备
        SELECT count(execution_state),max(execution_state)  INTO v_dm_period_count,v_dm_status
        FROM dmuser.dm_conf_bussperiod
        WHERE entity_id = p_entity_id
          AND year_month = p_yearmonth
          AND valid_is = '1';

        --查询当前业务期间详情id, 验证当前数据是否为准备中
        select bpd.period_detail_id,bpd.ready_state into v_period_detail_id,v_period_detail_status
        from acc_conf_accountperiod_detail bpd
                 left join acc_conf_accountperiod p
                           on bpd.period_id = p.period_id
                 left join acc_conf_table t
                           on bpd.biz_type_id = t.biz_type_id
        where p.entity_id = p_entity_id
          and p.book_code = p_bookcode
          and p.year_month = p_yearmonth
          and t.biz_code ='DAP_DM_ENTRY_DATA';

        --判断是否是过渡期当月 ********
        begin
            SELECT t.code_code into v_tran_ym
            FROM acc_conf_code t
            where t.upper_code_id =
                  (SELECT a.code_id
                   FROM acc_v_conf_code a
                   where a.code_code_idx = 'TransitionPeriod')
              and t.valid_is = '1';

        EXCEPTION
            WHEN OTHERS THEN
                --抛出异常提示信息
                --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
                --RAISE EXCEPTION '[EXCEPTION]测试异常：%, %',v_text1,v_text2;
                dbms_output.put_line('[EXCEPTION]proc_add_accpayment TransitionPeriod：' ||
                                     to_char(SQLCODE) || ';' ||
                                     substr(SQLERRM, 1, 200));
        end;

        --过渡期特殊处理
        /*if v_tran_ym is not null and p_yearmonth < v_tran_ym then
          update acc_conf_accountperiod_detail t set t.ready_state = '1',t.task_time = sysdate where t.period_detail_id = v_period_detail_id;

         return ;
        end if;*/

        --RAISE NOTICE '源数据准备状态:%,业务期间详情id:%,当前数据准备状态%',v_data_status,v_period_detail_id,v_period_detail_status;
        dbms_output.put_line('源数据准备状态:'||v_data_status||',业务期间详情id:'||v_period_detail_id||',当前数据准备状态'||v_period_detail_status);
        IF v_dm_period_count > 0 and v_period_detail_status = '0' THEN

            v_task_code :=bpluser.bpl_pack_common.func_get_taskcode('ACC', 'A','PY-Sync');

            SELECT COALESCE((',py.'||listagg(source_column,',') within group(order by source_column )),''),
                   COALESCE((','||listagg(mapping_column,',') within group(order by mapping_column)),'')
            into v_select_sql,v_insert_sql
            FROM bpluser.bbs_conf_model_mapping
            WHERE source_table='DM_ACC_PAYMENT'
              and mapping_table = 'ACC_DAP_ENTRY_DATA';


            --写入待入账数据表 ETL
            v_snyc_sql := 'INSERT INTO ACC_DAP_ENTRY_DATA
          (entry_data_id, -- id|主键
           entity_id, -- entity_id|核算单位ID
           YEAR_MONTH,
           posting_type_code, -- PostType|入账类型(03预收/预付 02实收/实付 01应收/应付)
           PROC_ID, -- PROC_ID|数据归属节点  BPL_ACT_RE_PROCDEF
           EXPENSES_TYPE_CODE, -- FeeTyp|费用类型编码
           currency_code, -- Cuurency|币别
           AMOUNT, -- Amount|金额
           RISK_CODE, -- Risk_Code|险种编码
           DEPT_ID, -- Dept_Id|部门ID
           DEPT_CODE, -- Dept_CODE|部门编码
           POLICY_NO,    -- POLICY_NO|保单号码
           ENDORSE_NO,    -- ENDORSE_NO|批单号码
           ENDORSE_SEQ_NO,
           RI_POLICY_NO,
           CLAIM_LOSS_NO,    -- CLAIM_LOSS_NO|理赔号码
           RI_STATEMENT_NO,    -- RI_STATEMENT_NO|账单号码[再保的业务号码]
           EST_PAYMENT_SEQ_NO,     --缴费次数
           BUSINESS_SOURCE_CODE, -- BUSINESS_SOURCE_CODE|业务类型
           POLICY_TYPE_CODE,
           RI_ARRANGEMENT_CODE,   --業務來源（分保类型比如：D-不区分，F-临分，T-比例合约，X-非比例合约
           RI_DIRECTION_CODE,    -- RI_DIRECTION_CODE|分入分出标志
           TREATY_TYPE_CODE,        --合约类型
           TREATY_CODE,     -- Treaty_Code|合约编码
           TREATY_YEAR,     -- TREATY_YEAR|合约年份
           INPUT_DATE,      -- INPUT_DATE|入机时间，默认sysdate
           BU_VOUCHER_NO,      -- PAYMENT_NO|凭证号码
           BU_VOUCHER_DATE,    -- BU_VOUCHER_DATE|凭证日期
           CASHFLOW_ARTICLE,         --CASHFLOW_ARTICLE|现金流专项
           account_entry_code,    -- account_entry_code|现金科目借贷方向 D-借 C-贷
           account_id, -- account_id|现金科目,存放BPL_CONFIGACCOUNTCODE.account_id 需要进行转换
           PORTFOLIO_NO,
           ICG_NO,
           extend_column3, -- 计量单元编号
           business_id,--DM_acc_payment.id
           extend_column12, -- 暂时用作同步数据标识，每次结束后会清空
           EVALUATE_APPROACH,--评估方法
           OFFSHORE_IS,--是否海外业务
           extend_column11,--同步任务号 ******** 为了初始化预收标识和销数方式的值
           ACCIDENT_DATE_TIME, --出险时间 ********
           TASK_CODE'  ||v_insert_sql ||'
          )
         SELECT /*+INDEX(PY DM_IDX_ACC_PAYMENT_VOUCHER_DATE_YM )*/
           acc_seq_dap_entry_data.nextval,
           py.entity_id, -- entity_id|核算部门
           ''' || p_yearmonth || '''' || 'as yearmonth, --会计期间
           m.posting_type_code,
           M.PROC_ID,
           py.EXPENSES_TYPE_CODE, -- EXPENSES_TYPE_CODE|费用类型
           py.currency_code, -- currency_code|币别
           py.AMOUNT, -- amount|金额
           py.RISK_CODE, -- risk_code|险种
           py.DEPT_ID, -- benf_entity_id|收益部门
           py.DEPT_CODE,
           py.POLICY_NO,
           py.ENDORSE_NO,
           py.endorse_seq_no,
           py.RI_POLICY_NO,
           py.CLAIM_LOSS_NO,
           py.RI_STATEMENT_NO,
           py.EST_PAYMENT_SEQ_NO,
           py.BUSINESS_SOURCE_CODE,
           py.POLICY_TYPE_CODE,
           py.RI_ARRANGEMENT_CODE,
           py.RI_DIRECTION_CODE,
           py.TREATY_TYPE_CODE,
           substr(py.treaty_no,1,length(py.treaty_no)-4) as TREATY_CODE,
           substr(py.treaty_no,length(py.treaty_no)-3) as TREATY_YEAR,
           LOCALTIMESTAMP,
           py.BU_VOUCHER_NO,
           py.BU_VOUCHER_DATE,
           py.CASHFLOW_ARTICLE,
           py.account_entry_code,
           py.account_id,  -- account_id|科目
           '''',--COALESCE(db_cm.portfolio_no, fob_cm.portfolio_no,tb_cm.portfolio_no) as portfolio_no,
           '''',--COALESCE(db_cm.icg_no, fob_cm.icg_no,tb_cm.icg_no) as icg_no,
           '''',--COALESCE(db_cm.cmunit_no, fob_cm.cmunit_no,tb_cm.cmunit_no) as cmunit_no,
           py.id,
           ''1'',
           acc_pack_data_sync.func_get_evaluate_approach(acc_pack_data_sync.func_get_business_msg(py.id,''1'') ,acc_pack_data_sync.func_get_business_msg(py.id,''2''),py.RI_DIRECTION_CODE,py.risk_code),
           acc_pack_data_sync.func_get_offshore((case when py.BUSINESS_SOURCE_CODE in (''DB'',''FB'') then ''1'' when  py.BUSINESS_SOURCE_CODE in (''TB'') then ''2'' else '''' end ),(case when py.BUSINESS_SOURCE_CODE in (''DB'',''FB'') then py.policy_no when  py.BUSINESS_SOURCE_CODE in (''TB'') then  py.treaty_no else '''' end ),py.RI_DIRECTION_CODE),
           '''||v_task_code||
                          ''', acc_pack_data_sync.func_get_date_time((case when py.BUSINESS_SOURCE_CODE in (''DB'',''FB'') then py.CLAIM_LOSS_NO else py.RI_STATEMENT_NO end), py.BUSINESS_SOURCE_CODE, py.RI_ARRANGEMENT_CODE, py.RI_DIRECTION_CODE, ''1'') '||
                          ',py.task_code'||   v_select_sql ||'
         FROM dmuser.DM_acc_payment py
         left join acc_conf_entry_type_mapping m
           on py.entry_type_code = m.entry_type_code
          and py.entity_id = m.entity_id
      WHERE to_char(BU_VOUCHER_DATE,''yyyymm'') = ''' || p_yearmonth || '''
        AND py.entity_id = ' || p_entity_id ||'
        and not exists(SELECT /*+INDEX(FS ODS_IDX_ACC_PAYMENT_VOUCHER_NO )*/ FS.ID FROM dmuser.ODS_acc_payment fs where fs.BU_VOUCHER_NO = py.BU_VOUCHER_NO and fs.task_status !=''6'' )
        and not exists ( select /*+INDEX(A IDX_ACC_DAP_ENTRY_DATA_BUSINESS_ID )*/ A.BUSINESS_ID from ACC_DAP_ENTRY_DATA a where a.business_id = py.id AND A.entity_id = PY.entity_id and a.year_month = to_char(BU_VOUCHER_DATE,''yyyymm'')  )
        and py.draw_type = ''2''
        and exists ( select /*+INDEX(B UNQ_DM_SRC_DATA_PUSH_SIGNAL_BUSS )*/ B.DATA_PUSH_SIGNAL_ID from dmuser.ODS_data_push_signal b where substr(b.task_code,1,15) = substr(py.task_code,1,15) and upper(b.push_model)in( ''ACC_PAYMENT'',''FIN_PAYMENT'')
         and b.year_month = to_char(BU_VOUCHER_DATE,''yyyymm'') and  b.task_status =''3'' )';

            --dbms_output.put_line('v_snyc_sql '||v_snyc_sql);
            EXECUTE immediate v_snyc_sql ;

            --更新信号表状态
            /*update dmuser.ODS_data_push_signal t set task_status = '3',deal_time = sysdate where t.push_model = 'acc_payment'
            and t.year_month = p_yearmonth and t.task_code in (SELECT a.task_code FROM ACC_DAP_ENTRY_DATA a where a.extend_column12 = '1' and a.year_month = p_yearmonth
            and a.entity_id = p_entity_id );
            --清空标识
            update ACC_DAP_ENTRY_DATA t set extend_column12 = null where t.extend_column12 = '1' and t.year_month = p_yearmonth and t.entity_id = p_entity_id;
    */
            --写入待入账数据表 非ETL
            v_snyc_sql := '';
            v_snyc_sql := 'INSERT INTO ACC_DAP_ENTRY_DATA
          (entry_data_id, -- id|主键
           entity_id, -- entity_id|核算单位ID
           YEAR_MONTH,
           posting_type_code, -- PostType|入账类型(03预收/预付 02实收/实付 01应收/应付)
           PROC_ID, -- PROC_ID|数据归属节点  BPL_ACT_RE_PROCDEF
           EXPENSES_TYPE_CODE, -- FeeTyp|费用类型编码
           currency_code, -- Cuurency|币别
           AMOUNT, -- Amount|金额
           RISK_CODE, -- Risk_Code|险种编码
           DEPT_ID, -- Dept_Id|部门ID
           DEPT_CODE,
           POLICY_NO,    -- POLICY_NO|保单号码
           ENDORSE_NO,    -- ENDORSE_NO|批单号码
           ENDORSE_SEQ_NO,
           RI_POLICY_NO,
           CLAIM_LOSS_NO,    -- CLAIM_LOSS_NO|理赔号码
           RI_STATEMENT_NO,    -- RI_STATEMENT_NO|账单号码[再保的业务号码]
           EST_PAYMENT_SEQ_NO,     --缴费次数
           BUSINESS_SOURCE_CODE, -- BUSINESS_SOURCE_CODE|业务类型
           POLICY_TYPE_CODE,
           RI_ARRANGEMENT_CODE,   --業務來源（分保类型比如：D-不区分，F-临分，T-比例合约，X-非比例合约
           RI_DIRECTION_CODE,    -- RI_DIRECTION_CODE|分入分出标志
           TREATY_TYPE_CODE,        --合约类型
           TREATY_CODE,     -- Treaty_Code|合约编码
           TREATY_YEAR,     -- TREATY_YEAR|合约年份
           INPUT_DATE,      -- INPUT_DATE|入机时间，默认sysdate
           BU_VOUCHER_NO,      -- PAYMENT_NO|凭证号码
           BU_VOUCHER_DATE,    -- BU_VOUCHER_DATE|凭证日期
           CASHFLOW_ARTICLE,         --CASHFLOW_ARTICLE|现金流专项
           account_entry_code,    -- account_entry_code|现金科目借贷方向 D-借 C-贷
           account_id, -- account_id|现金科目,存放BPL_CONFIGACCOUNTCODE.account_id 需要进行转换
           PORTFOLIO_NO,
           ICG_NO,
           extend_column3, -- 计量单元编号
           business_id,--DM_acc_payment.id
           extend_column12, -- 暂时用作同步数据标识，每次结束后会清空
           EVALUATE_APPROACH,--评估方法
           OFFSHORE_IS,
           extend_column11,--同步任务号 ******** 为了初始化预收标识和销数方式的值
           ACCIDENT_DATE_TIME, --出险时间 ********
           TASK_CODE'  ||v_insert_sql ||'
          )
         SELECT
           acc_seq_dap_entry_data.nextval,
           py.entity_id, -- entity_id|核算部门
           ''' || p_yearmonth || '''' || 'as yearmonth, --会计期间
           m.posting_type_code,
           M.PROC_ID,
           py.EXPENSES_TYPE_CODE, -- EXPENSES_TYPE_CODE|费用类型
           py.currency_code, -- currency_code|币别
           py.AMOUNT, -- amount|金额
           py.RISK_CODE, -- risk_code|险种
           py.DEPT_ID, -- benf_entity_id|收益部门
           py.DEPT_CODE,
           py.POLICY_NO,
           py.ENDORSE_NO,
           py.endorse_seq_no,
           py.RI_POLICY_NO,
           py.CLAIM_LOSS_NO,
           py.RI_STATEMENT_NO,
           py.EST_PAYMENT_SEQ_NO,
           py.BUSINESS_SOURCE_CODE,
           py.POLICY_TYPE_CODE,
           py.RI_ARRANGEMENT_CODE,
           py.RI_DIRECTION_CODE,
           py.TREATY_TYPE_CODE,
           substr(py.treaty_no,1,length(py.treaty_no)-4) as TREATY_CODE,
           substr(py.treaty_no,length(py.treaty_no)-3) as TREATY_YEAR,
           LOCALTIMESTAMP,
           py.BU_VOUCHER_NO,
           py.BU_VOUCHER_DATE,
           py.CASHFLOW_ARTICLE,
           py.account_entry_code,
           py.account_id,  -- account_id|科目
           '''',--COALESCE(db_cm.portfolio_no, fob_cm.portfolio_no,tb_cm.portfolio_no) as portfolio_no,
           '''',--COALESCE(db_cm.icg_no, fob_cm.icg_no,tb_cm.icg_no) as icg_no,
           '''',--COALESCE(db_cm.cmunit_no, fob_cm.cmunit_no,tb_cm.cmunit_no) as cmunit_no,
           py.id,
           ''1'',
           acc_pack_data_sync.func_get_evaluate_approach(acc_pack_data_sync.func_get_business_msg(py.id,''1'') ,acc_pack_data_sync.func_get_business_msg(py.id,''2''),py.RI_DIRECTION_CODE,py.risk_code),
           acc_pack_data_sync.func_get_offshore((case when py.BUSINESS_SOURCE_CODE in (''DB'',''FB'') then ''1'' when  py.BUSINESS_SOURCE_CODE in (''TB'') then ''2'' else '''' end ),(case when py.BUSINESS_SOURCE_CODE in (''DB'',''FB'') then py.policy_no when  py.BUSINESS_SOURCE_CODE in (''TB'') then  py.treaty_no else '''' end ),py.RI_DIRECTION_CODE),
           '''||v_task_code||
                          ''', acc_pack_data_sync.func_get_date_time((case when py.BUSINESS_SOURCE_CODE in (''DB'',''FB'') then py.CLAIM_LOSS_NO else py.RI_STATEMENT_NO end), py.BUSINESS_SOURCE_CODE, py.RI_ARRANGEMENT_CODE, py.RI_DIRECTION_CODE, ''1'') '||
                          ',py.task_code'||   v_select_sql ||'
         FROM dmuser.DM_acc_payment py
         left join acc_conf_entry_type_mapping m
           on py.entry_type_code = m.entry_type_code
          and py.entity_id = m.entity_id
      WHERE py.entity_id = ' || p_entity_id ||'
        and to_char(BU_VOUCHER_DATE,''yyyymm'') = ''' || p_yearmonth || '''
        and not exists ( select 1 from ACC_DAP_ENTRY_DATA a where a.business_id = py.id AND A.ENTITY_ID = PY.ENTITY_ID and a.year_month = to_char(BU_VOUCHER_DATE,''yyyymm'') AND A.posting_type_code IN (''01'',''02'',''03'',''04'')  )
        and not exists(SELECT 1 FROM dmuser.ODS_acc_payment fs where fs.BU_VOUCHER_NO = py.BU_VOUCHER_NO and fs.task_status !=''6'' AND fs.TASK_CODE = PY.TASK_CODE)
        and py.draw_type != ''2'' ';

            --dbms_output.put_line('v_snyc_sql '||v_snyc_sql);
            EXECUTE immediate v_snyc_sql ;

            --初始化不要字段值
            update acc_dap_entry_data t
            set t.extend_column1 = coalesce(t.extend_column1,'2'),
                t.extend_column2 =coalesce(t.extend_column2,'D')
            where t.year_month = p_yearmonth
              and t.entity_id = p_entity_id
              and t.extend_column11 = v_task_code
              and t.proc_id in (
                select proc_id
                from bpluser.bpl_act_re_procdef
                where proc_code in( 'ACC_ACCOUNTENTRY_UW_ACR','ACC_ACCOUNTENTRY_COMP_STL_ACR')
            );
            --评估方法：直保应收应付、预收保费、预付佣金置空，只保留实收实付、结转的评估方法 ********
            --实际现金流levies在不同计量模型下的入账调整,挂账和实收都需要打标签 ********
            --所有类型都需要模型 ********
            /* update acc_dap_entry_data t
                set t.evaluate_approach = (case when t.posting_type_code = '01' and t.EXPENSES_TYPE_CODE not in('ECIL','MIB','ECIIB','GTFC','IIA','ECILC','MIBC','ECIIBC','GTFCC','ECILP','MIBP','ECIIBP','GTFCP','IAC') then '' when t.posting_type_code = '02' and t.extend_column1 = '1' then '' else t.evaluate_approach end)
              where t.year_month = p_yearmonth
                and t.entity_id = p_entity_id
                and t.extend_column11 = v_task_code
                and t.proc_id in (
                 select proc_id
                         from bpluser.bpl_act_re_procdef
                        where proc_code = 'ACC_ACCOUNTENTRY_UW_ACR'
                ) ;*/

            --更新当年/往年标识 赔款/费用用费用区分
            update acc_dap_entry_data t set current_previous_is = ( (case
                                                                         when to_char(t.accident_date_time, 'YYYY') <
                                                                              to_char(t.bu_voucher_date, 'YYYY')  then
                                                                             '2'
                                                                         else
                                                                             '1'
                end)) where t.accident_date_time is not null;

            --根据任务号： 天是00代表每一个月同步一次，或者年月日等于业务期间的最后一天，当月数据同结束；调整业务期间状态
            SELECT count(1) into v_end_count FROM acc_dap_entry_data t where t.year_month = p_yearmonth and t.entity_id = p_entity_id and (substr(t.task_code,14,2) = '00' or substr(t.task_code,8,8) = to_char(add_months(to_date(p_yearmonth,'YYYYMM'),1) -1,'YYYYMMDD'));
            if v_end_count > 0 or v_dm_status = '3' then
                --修改acc_conf_accountperiod_detail为已准备状态
                update acc_conf_accountperiod_detail set ready_state='1',exec_result='success',task_time=sysdate where period_detail_id = v_period_detail_id;

                --同步业务期间执行状态
                acc_pack_buss_period.proc_period_execution(p_entity_id, p_bookcode, '1');
                dbms_output.put_line('同步业务期间执行状态完成');
                --处理收付数据计量信息同步
                acc_pack_voucher.proc_entry_deal_cmunit(p_entity_id, p_bookcode,p_yearmonth,p_userid);
            else
                update acc_conf_accountperiod_detail set task_time=sysdate where period_detail_id = v_period_detail_id;
            end if;
            --提交事务
            COMMIT;
        END if;
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            if v_period_detail_id is not null then
                update acc_conf_accountperiod_detail set task_time=sysdate where period_detail_id = v_period_detail_id;
            end if;
            dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
            dbms_output.put_line('**插入数据异常，请检查 SQLERRM:' || to_char(SQLCODE) || '::'|| SUBSTR(SQLERRM, 1, 200));
            --抛出自定义异常信息 【会中断事务】
            v_error_msg := substr('会计同步收付数据发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);

            --往外层抛出异常信息
            raise_application_error(-20003, v_error_msg);
    END proc_add_accpayment;

    PROCEDURE proc_add_ledger_balance(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER) IS

        v_data_status       VARCHAR2(100);
        v_period_detail_id    NUMBER(11);
        v_period_detail_status  VARCHAR2(100);
        v_error_msg  VARCHAR(4000);
        v_tran_ym   VARCHAR(6);
    BEGIN
        --验证源数据是否已准备
        SELECT execution_state INTO v_data_status
        FROM dmuser.dm_conf_bussperiod
        WHERE entity_id = p_entity_id
          AND year_month = p_yearmonth
          AND valid_is = '1';
        --查询当前业务期间详情id, 验证当前数据是否为准备中
        select bpd.period_detail_id,bpd.ready_state into v_period_detail_id,v_period_detail_status
        from acc_conf_accountperiod_detail bpd
                 left join acc_conf_accountperiod p
                           on bpd.period_id = p.period_id
                 left join acc_conf_table t
                           on bpd.biz_type_id = t.biz_type_id
        where p.entity_id = p_entity_id and p.year_month = p_yearmonth and t.biz_code ='EXT_DM_LEDGER_BALANCE' and p.book_code = p_bookcode;
        --RAISE NOTICE '源数据准备状态:%,业务期间详情id:%,当前数据准备状态%',v_data_status,v_period_detail_id,v_period_detail_status;
        dbms_output.put_line('源数据准备状态:'||v_data_status||',业务期间详情id:'||v_period_detail_id||',当前数据准备状态'||v_period_detail_status);

        IF v_data_status != '3' THEN
            v_error_msg := '数据平台期间状态不是已完成为:' || '，不执行同步：[' || p_entity_id || ']-[' || p_bookcode || ']-[' || p_yearmonth || ']';
            --往外层抛出异常信息
            raise_application_error(-20002, v_error_msg);
        END IF;

        --判断是否是过渡期当月 ********
        begin
            SELECT t.code_code into v_tran_ym
            FROM acc_conf_code t
            where t.upper_code_id =
                  (SELECT a.code_id
                   FROM acc_v_conf_code a
                   where a.code_code_idx = 'TransitionPeriod')
              and t.valid_is = '1';

            --过渡期特殊处理
            --if v_tran_ym is not null and p_yearmonth < v_tran_ym then
            if p_yearmonth <to_char(add_months(to_date(v_tran_ym,'YYYYMM'),-1),'YYYYMM') then
                update acc_conf_accountperiod_detail t set t.ready_state = '1',t.task_time = sysdate where t.period_detail_id = v_period_detail_id;
                acc_pack_buss_period.proc_period_execution(p_entity_id, p_bookcode, '1');
                return ;
            end if;

        EXCEPTION
            WHEN OTHERS THEN
                --抛出异常提示信息
                --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
                --RAISE EXCEPTION '[EXCEPTION]测试异常：%, %',v_text1,v_text2;
                dbms_output.put_line('[EXCEPTION]proc_add_ledger_balance TransitionPeriod：' ||
                                     to_char(SQLCODE) || ';' ||
                                     substr(SQLERRM, 1, 200));
        end;

        IF v_data_status = '3' and v_period_detail_status = '0' THEN
            --先清空当前业务年月数据
            delete from acc_ext_ledger_balance b where  b.entity_id = p_entity_id and b.year_month = p_yearmonth;
            -- TODO
            INSERT INTO accuser.acc_ext_ledger_balance (
                ledger_balance_id,
                entity_id,
                book_code,
                year_month,
                root_account_id,
                account_id,
                currency_code,
                currency_cu_code,
                debit_amount,
                debit_amount_cu,
                credit_amount,
                credit_amount_cu,
                debit_amount_quarter,
                debit_amount_quarter_cu,
                credit_amount_quarter,
                credit_amount_quarter_cu,
                debit_amount_year,
                debit_amount_year_cu,
                credit_amount_year,
                credit_amount_year_cu,
                opening_balance,
                closing_balance,
                opening_balance_cu,
                closing_balance_cu,
                create_time,
                creator_id
            )
            SELECT
                acc_seq_ext_ledger_balance.nextval,
                b.entity_id,
                b.book_code,
                year_month,
                root_account_id,
                account_id,
                currency_code,
                currency_cu_code,
                debit_amount,
                debit_amount_cu,
                credit_amount,
                credit_amount_cu,
                debit_amount_quarter,
                debit_amount_quarter_cu,
                credit_amount_quarter,
                credit_amount_quarter_cu,
                debit_amount_year,
                debit_amount_year_cu,
                credit_amount_year,
                credit_amount_year_cu,
                opening_balance,
                closing_balance,
                opening_balance_cu,
                closing_balance_cu,
                sysdate, -- 创建时间
                p_userid --creator_id
            FROM dmuser.DM_fin_ledger_balance b
            WHERE b.entity_id = p_entity_id --and b.year_month = p_yearmonth
              and (case when substr(b.year_month,5,2) = 'JS' then substr(b.year_month,1,4)||'12' else b.year_month end) = p_yearmonth
              AND NOT EXISTS(select 1 from acc_ext_ledger_balance T where b.entity_id = t.entity_id and b.book_code= t.book_code and
                    b.year_month = t.year_month and b.account_id = t.account_id);
            --修改acc_conf_accountperiod_detail为已准备状态
            update acc_conf_accountperiod_detail set ready_state='1',exec_result='success',task_time=sysdate where period_detail_id = v_period_detail_id;

            --提交事务
            COMMIT;
            --同步业务期间执行状态
            acc_pack_buss_period.proc_period_execution(p_entity_id, p_bookcode, '1');
        END IF;


    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
            dbms_output.put_line('**插入数据异常，请检查 SQLERRM:' || to_char(SQLCODE) || '::'|| SUBSTR(SQLERRM, 1, 200));
            --抛出自定义异常信息 【会中断事务】
            v_error_msg := substr('会计同步总账余额数据发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);

            --往外层抛出异常信息
            raise_application_error(-20003, v_error_msg);
    END proc_add_ledger_balance;

    PROCEDURE proc_add_article_balance(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER) IS

        v_data_status       VARCHAR2(100);
        v_period_detail_id    NUMBER(11);
        v_period_detail_status  VARCHAR2(100);
        v_error_msg  VARCHAR(4000);
        v_tran_ym    VARCHAR(6);
    BEGIN
        --验证源数据是否已准备
        SELECT execution_state INTO v_data_status
        FROM dmuser.dm_conf_bussperiod
        WHERE entity_id = p_entity_id
          AND year_month = p_yearmonth
          AND valid_is = '1';
        --查询当前业务期间详情id, 验证当前数据是否为准备中
        select bpd.period_detail_id,bpd.ready_state into v_period_detail_id,v_period_detail_status
        from acc_conf_accountperiod_detail bpd
                 left join acc_conf_accountperiod p
                           on bpd.period_id = p.period_id
                 left join acc_conf_table t
                           on bpd.biz_type_id = t.biz_type_id
        where p.entity_id = p_entity_id and p.year_month = p_yearmonth  and p.book_code = p_bookcode and t.biz_code ='EXT_DM_ARTICLE_BALANCE';
        --RAISE NOTICE '源数据准备状态:%,业务期间详情id:%,当前数据准备状态%',v_data_status,v_period_detail_id,v_period_detail_status;
        dbms_output.put_line('源数据准备状态:'||v_data_status||',业务期间详情id:'||v_period_detail_id||',当前数据准备状态'||v_period_detail_status);

        IF v_data_status != '3' THEN
            v_error_msg := '数据平台期间状态不是已完成为:' || '，不执行同步：[' || p_entity_id || ']-[' || p_bookcode || ']-[' || p_yearmonth || ']';
            --往外层抛出异常信息
            raise_application_error(-20002, v_error_msg);
        END IF;

        --判断是否是过渡期当月 ********
        begin
            SELECT t.code_code into v_tran_ym
            FROM acc_conf_code t
            where t.upper_code_id =
                  (SELECT a.code_id
                   FROM acc_v_conf_code a
                   where a.code_code_idx = 'TransitionPeriod')
              and t.valid_is = '1';

            --过渡期特殊处理
            --if v_tran_ym is not null and p_yearmonth < v_tran_ym then
            if p_yearmonth <to_char(add_months(to_date(v_tran_ym,'YYYYMM'),-1),'YYYYMM') then
                update acc_conf_accountperiod_detail t set t.ready_state = '1',t.task_time = sysdate where t.period_detail_id = v_period_detail_id;
                acc_pack_buss_period.proc_period_execution(p_entity_id, p_bookcode, '1');
                return ;
            end if;
        EXCEPTION
            WHEN OTHERS THEN
                --抛出异常提示信息
                --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
                --RAISE EXCEPTION '[EXCEPTION]测试异常：%, %',v_text1,v_text2;
                dbms_output.put_line('[EXCEPTION]proc_add_article_balance TransitionPeriod：' ||
                                     to_char(SQLCODE) || ';' ||
                                     substr(SQLERRM, 1, 200));
        end;

        IF v_data_status = '3' and v_period_detail_status = '0' THEN
            --先清空当前业务年月数据
            delete from acc_ext_article_balance b where  b.entity_id = p_entity_id and b.year_month = p_yearmonth;
            -- TODO
            --写入科目专项余额数据表
            INSERT INTO accuser.acc_ext_article_balance (
                article_id,
                entity_id,
                book_code,
                year_month,
                root_account_id,
                account_id,
                article1,
                article2,
                article3,
                article4,
                article5,
                article6,
                article7,
                article8,
                article9,
                article10,
                article11,
                article12,
                article13,
                article14,
                article15,
                article16,
                article17,
                currency_code,
                currency_cu_code,
                debit_amount,
                debit_amount_cu,
                credit_amount,
                credit_amount_cu,
                debit_amount_quarter,
                debit_amount_quarter_cu,
                credit_amount_quarter,
                credit_amount_quarter_cu,
                debit_amount_year,
                debit_amount_year_cu,
                credit_amount_year,
                credit_amount_year_cu,
                opening_balance,
                closing_balance,
                opening_balance_cu,
                closing_balance_cu,
                create_time,
                creator_id
            ) SELECT
                  acc_seq_ext_article_balance.nextval,
                  entity_id,
                  book_code,
                  year_month,
                  root_account_id,
                  account_id,
                  article1,
                  article2,
                  article3,
                  article4,
                  article5,
                  article6,
                  article7,
                  article8,
                  article9,
                  article10,
                  article11,
                  article12,
                  article13,
                  article14,
                  article15,
                  article16,
                  article17,
                  currency_code,
                  currency_cu_code,
                  debit_amount,
                  debit_amount_cu,
                  credit_amount,
                  credit_amount_cu,
                  debit_amount_quarter,
                  debit_amount_quarter_cu,
                  credit_amount_quarter,
                  credit_amount_quarter_cu,
                  debit_amount_year,
                  debit_amount_year_cu,
                  credit_amount_year,
                  credit_amount_year_cu,
                  opening_balance,
                  closing_balance,
                  opening_balance_cu,
                  closing_balance_cu,
                  LOCALTIMESTAMP, -- 创建时间
                  p_userid --creator_id
            FROM dmuser.DM_fin_article_balance b
            WHERE b.entity_id = p_entity_id --and b.year_month = p_yearmonth ;
              and (case when substr(b.year_month,5,2) = 'JS' then substr(b.year_month,1,4)||'12' else b.year_month end) = p_yearmonth ;

            --修改acc_conf_accountperiod_detail为已准备状态
            update acc_conf_accountperiod_detail set ready_state='1',exec_result='success',task_time=LOCALTIMESTAMP where period_detail_id = v_period_detail_id;

            --提交事务
            COMMIT;
            --同步业务期间执行状态
            accuser.acc_pack_buss_period.proc_period_execution(p_entity_id, p_bookcode, '1');


        END IF;

        begin
            if v_tran_ym = p_yearmonth then
                accuser.acc_pack_annual.proc_annual_trans_period(p_entity_id ,
                                                                 'BookI4' ,
                                                                 p_yearmonth,
                                                                 p_userid);
            end if;

        exception
            when others then
                dbms_output.put_line('**过渡期初始化期初失败: '|| SUBSTR(SQLERRM, 1, 200));
        end;

    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
            dbms_output.put_line('**插入数据异常，请检查 SQLERRM:' || to_char(SQLCODE) || '::'|| SUBSTR(SQLERRM, 1, 200));
            --抛出自定义异常信息 【会中断事务】
            v_error_msg := substr('会计同步专项余额数据发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);

            --往外层抛出异常信息
            raise_application_error(-20003, v_error_msg);
    END proc_add_article_balance;

    PROCEDURE proc_add_acc_voucher(p_entity_id IN NUMBER, p_yearmonth IN VARCHAR2, p_bookcode IN VARCHAR2, p_userid IN NUMBER) IS

        v_data_status       VARCHAR2(100);
        v_period_detail_id    NUMBER(11);
        v_period_detail_status  VARCHAR2(100);
        v_error_msg  VARCHAR(4000);
        v_tran_ym   VARCHAR(6);
    BEGIN
        --验证源数据是否已准备
        SELECT execution_state INTO v_data_status
        FROM dmuser.dm_conf_bussperiod
        WHERE entity_id = p_entity_id
          AND year_month = p_yearmonth
          AND valid_is = '1';
        --查询当前业务期间详情id, 验证当前数据是否为准备中
        select bpd.period_detail_id,bpd.ready_state into v_period_detail_id,v_period_detail_status
        from acc_conf_accountperiod_detail bpd
                 left join acc_conf_accountperiod p
                           on bpd.period_id = p.period_id
                 left join acc_conf_table t
                           on bpd.biz_type_id = t.biz_type_id
        where p.entity_id = p_entity_id and p.year_month = p_yearmonth and t.biz_code ='EXT_DM_VOUCHER';
        --RAISE NOTICE '源数据准备状态:%,业务期间详情id:%,当前数据准备状态%',v_data_status,v_period_detail_id,v_period_detail_status;
        dbms_output.put_line('源数据准备状态:'||v_data_status||',业务期间详情id:'||v_period_detail_id||',当前数据准备状态'||v_period_detail_status);

        IF v_data_status != '3' THEN
            v_error_msg := '数据平台期间状态不是已完成为:' || '，不执行同步：[' || p_entity_id || ']-[' || p_bookcode || ']-[' || p_yearmonth || ']';
            --往外层抛出异常信息
            raise_application_error(-20002, v_error_msg);
        END IF;

        --判断是否是过渡期当月 ********
        begin
            SELECT t.code_code into v_tran_ym
            FROM acc_conf_code t
            where t.upper_code_id =
                  (SELECT a.code_id
                   FROM acc_v_conf_code a
                   where a.code_code_idx = 'TransitionPeriod')
              and t.valid_is = '1';

            if p_yearmonth < v_tran_ym then
                update acc_conf_accountperiod_detail t set t.ready_state = '1',t.task_time = sysdate where t.period_detail_id = v_period_detail_id;
                acc_pack_buss_period.proc_period_execution(p_entity_id, p_bookcode, '1');
                return ;
            end if;

        EXCEPTION
            WHEN OTHERS THEN
                --抛出异常提示信息
                --get stacked diagnostics v_text1= MESSAGE_TEXT,v_text2 = PG_EXCEPTION_CONTEXT;
                --RAISE EXCEPTION '[EXCEPTION]测试异常：%, %',v_text1,v_text2;
                dbms_output.put_line('[EXCEPTION]proc_entry_data_check：' ||
                                     to_char(SQLCODE) || ';' ||
                                     substr(SQLERRM, 1, 200));
        end;

        IF v_data_status = '3' and v_period_detail_status = '0' THEN

            delete from acc_ext_voucher_detail b
            WHERE exists(select 1 from acc_ext_voucher a WHERE a.entity_id = p_entity_id and a.year_month = p_yearmonth and b.voucher_no = a.voucher_no);

            delete from acc_ext_voucher b where  b.entity_id = p_entity_id and b.year_month = p_yearmonth;

            INSERT INTO accuser.acc_ext_voucher (
                ext_voucher_id,
                voucher_no,
                entity_id,
                book_code,
                posting_type_code,
                year_month,
                effective_date,
                VOUCHER_STATUS_CODE,
                remark,
                BU_VOUCHER_NO,
                task_code,
                create_time,
                creator_id  )
            SELECT
                acc_seq_ext_voucher.nextval,
                voucher_no,
                entity_id,
                book_code,
                posting_type_code,
                year_month,
                effective_date,
                VOUCHER_STATUS_CODE,
                remark,
                BU_VOUCHER_NO,
                task_code,
                LOCALTIMESTAMP, -- 创建时间
                p_userid --creator_id
            from dmuser.DM_fin_voucher b
            WHERE b.entity_id = p_entity_id
              and (case when substr(b.year_month,5,2) = 'JS' then substr(b.year_month,1,4)||'12' else b.year_month end) = p_yearmonth
              and not exists(select 1 from acc_ext_voucher b1 WHERE
                    b.entity_id = b1.entity_id
                                                                and b.year_month = b1.year_month
                                                                and b.voucher_no = b1.voucher_no
                );



            --写入科目专项余额数据表
            INSERT INTO accuser.acc_ext_voucher_detail (
                ext_voucher_dtl_id,
                voucher_no,
                voucher_seq_no,
                account_id,
                currency_code,
                currency_cu_code,
                account_entry_code,
                amount,
                amount_cu,
                exchange_rate,
                remark,
                article1,
                article2,
                article3,
                article4,
                article5,
                article6,
                article7,
                article8,
                article9,
                article10,
                article11,
                article12,
                article13,
                article14,
                article15,
                article16,
                article17,
                task_code,
                create_time,
                creator_id
            ) SELECT
                  acc_seq_ext_voucherdetail.nextval,
                  b.voucher_no,
                  b.voucher_seq_no,
                  b.account_id,
                  b.currency_code,
                  b.currency_cu_code,
                  b.account_entry_code,
                  b.amount,
                  b.amount_cu,
                  b.exchange_rate,
                  b.remark,
                  b.article1,
                  b.article2,
                  b.article3,
                  b.article4,
                  b.article5,
                  b.article6,
                  b.article7,
                  b.article8,
                  b.article9,
                  b.article10,
                  b.article11,
                  b.article12,
                  b.article13,
                  b.article14,
                  b.article15,
                  b.article16,
                  b.article17,
                  b.task_code,
                  LOCALTIMESTAMP, -- 创建时间
                  p_userid --creator_id
            from dmuser.DM_fin_voucher a,dmuser.DM_fin_voucher_detail b
            WHERE a.voucher_no = b.voucher_no
              and a.entity_id = p_entity_id
              --and a.year_month = p_yearmonth
              and (case when substr(a.year_month,5,2) = 'JS' then substr(a.year_month,1,4)||'12' else a.year_month end) = p_yearmonth
              and exists(select 1 from acc_ext_voucher b1 WHERE
                    a.entity_id = b1.entity_id
                                                            and a.year_month = b1.year_month
                                                            and a.voucher_no = b1.voucher_no
                )
              and not exists(select 1 from acc_ext_voucher_detail b1 WHERE
                    b.voucher_no = b1.voucher_no
                );

            --手工类凭证转化到17账套凭证
            acc_pack_annual.proc_annual_data_conversion(p_entity_id, p_bookcode, p_yearmonth, p_userid);

            --修改acc_conf_accountperiod_detail为已准备状态
            update acc_conf_accountperiod_detail set ready_state='1',exec_result='success',task_time=LOCALTIMESTAMP where period_detail_id = v_period_detail_id;

            --提交事务
            COMMIT;
            --同步业务期间执行状态
            accuser.acc_pack_buss_period.proc_period_execution(p_entity_id, p_bookcode, '1');
            --RAISE NOTICE '同步业务期间%的财务凭证信息执行状态完成',p_yearmonth;
            dbms_output.put_line('同步业务期间%的财务凭证信息执行状态完成:'||p_yearmonth);
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
            dbms_output.put_line('**插入数据异常，请检查 SQLERRM:' || to_char(SQLCODE) || '::'|| SUBSTR(SQLERRM, 1, 200));
            --抛出自定义异常信息 【会中断事务】
            v_error_msg := substr('会计同步财务凭证数据发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);

            --往外层抛出异常信息
            raise_application_error(-20003, v_error_msg);
    END proc_add_acc_voucher;

    /***********************************************************************
    NAME :func_get_offshore
    DESCRIPTION :获取海外/海外标识
    DATE :2022-10-31
    AUTHOR :CHENJUNFENG
    BUSINESS RULE : p_type-1 直接/临分分入业务/直接转分出/临分转分出，2-合约分入/合约分入转合约分出
                    p_business_no：p_type-1 参数为保单号，p_type-2 合约号
    ***********************************************************************/
    FUNCTION func_get_offshore(p_type IN VARCHAR2, p_business_no IN VARCHAR2, p_business_direction IN VARCHAR2) RETURN VARCHAR2 IS

        V_offshore varchar2(1);
    BEGIN

        V_offshore := '0';

        if p_type = '1' then
            SELECT MAX(t.offshore_is)
            into V_offshore
            FROM dmuser.DM_policy_main t
            where t.policy_no = p_business_no
              and rownum =1;
        elsif p_type = '2' then
            if p_business_direction = 'I' then
                SELECT MAX(t.offshore_is)
                into V_offshore
                FROM dmuser.DM_reins_treaty t
                where t.treaty_no = p_business_no
                  and rownum =1;
            else
                SELECT MAX(t.offshore_is)
                into V_offshore
                FROM dmuser.DM_reins_treaty      t,
                     dmuser.DM_reins_base_treaty  a
                where a.base_treaty_out_no = substr(p_business_no,1,5)
                  and substr(t.treaty_no,1,5) = a.base_treaty_no
                  and rownum = 1;
            end if;

        else
            V_offshore := '';
        end if;


        RETURN V_offshore;
    EXCEPTION
        WHEN OTHERS THEN
            --dbms_output.put_line('[EXCEPTION]func_get_offshore：' || to_char(SQLCODE) || ','|| SUBSTR(SQLERRM, 1, 200));
            RETURN '0';

    END func_get_offshore;

    /***********************************************************************
    NAME :func_get_offshore
    DESCRIPTION :获取海外/海外标识
    DATE :2022-10-31
    AUTHOR :CHENJUNFENG
    BUSINESS RULE : p_type-1 直接/临分分入业务/直接转分出/临分转分出，2-合约分入/合约分入转合约分出
                    p_business_no：p_type-1 参数为保单号，p_type-2 合约号
    ***********************************************************************/
    FUNCTION func_get_offshore2(p_type IN VARCHAR2, p_buins_type IN VARCHAR2, p_business_no IN VARCHAR2, p_business_direction IN VARCHAR2) RETURN VARCHAR2 IS

        V_offshore varchar2(1);
    BEGIN

        V_offshore := '0';

        --业务信息
        IF p_type = '1' THEN
            if p_buins_type = '1' then
                SELECT MAX(t.offshore_is)
                into V_offshore
                FROM dmuser.DM_policy_main t
                where t.policy_no = p_business_no
                  and rownum =1;
            elsif p_buins_type = '2' then
                if p_business_direction = 'I' then
                    SELECT MAX(t.offshore_is)
                    into V_offshore
                    FROM dmuser.DM_reins_treaty t
                    where t.treaty_no = p_business_no
                      and rownum =1;
                else
                    SELECT MAX(t.offshore_is)
                    into V_offshore
                    FROM dmuser.DM_reins_treaty      t,
                         dmuser.DM_reins_base_treaty  a
                    where a.base_treaty_out_no = substr(p_business_no,1,5)
                      and substr(t.treaty_no,1,5) = a.base_treaty_no
                      and rownum = 1;
                end if;
            else
                V_offshore := '';
            end if;
            --计量单元信息
        ELSIF p_type = '2' THEN
            if p_buins_type = '1' then
                if p_business_direction in ('I','D') THEN
                    SELECT MAX(T.OFFSHORE_IS)
                    INTO V_OFFSHORE
                    FROM DMUSER.dm_buss_cmunit_direct T
                    WHERE ICG_NO = P_BUSINESS_NO
                      AND ROWNUM = 1;
                else
                    SELECT MAX(T.OFFSHORE_IS)
                    INTO V_OFFSHORE
                    FROM DMUSER.dm_buss_cmunit_fac_outwards T
                    WHERE ICG_NO = P_BUSINESS_NO
                      AND ROWNUM = 1;
                end if;
            elsif p_buins_type = '2' then
                SELECT MAX(T.OFFSHORE_IS)
                INTO V_OFFSHORE
                FROM DMUSER.dm_buss_cmunit_treaty T
                WHERE ICG_NO = P_BUSINESS_NO
                  AND ROWNUM = 1;
            else
                V_offshore := '';
            end if;
        ELSE
            V_offshore := '';
        END IF;

        RETURN V_offshore;

    EXCEPTION
        WHEN OTHERS THEN
            dbms_output.put_line('[EXCEPTION]func_get_offshore：' || to_char(SQLCODE) || ','|| SUBSTR(SQLERRM, 1, 200));
            RETURN '0';
    END func_get_offshore2;


    /***********************************************************************
    NAME :func_get_evaluate_approach
    DESCRIPTION :获取评估方法
    DATE :2022-10-31
    AUTHOR :CHENJUNFENG
    BUSINESS RULE : p_BUSINESS_SOURCE_CODE-D 直接/临分分入业务，F-临分，T-合约
                    p_business_direction ：D-不区分（跟p_BUSINESS_SOURCE_CODE-D）、I-分入（跟p_BUSINESS_SOURCE_CODE-T）、O-分出（跟p_BUSINESS_SOURCE_CODE-F/T）
                    p_business_no：p_BUSINESS_SOURCE_CODE-D 参数为保单号，p_BUSINESS_SOURCE_CODE-F/T 合约号
    ***********************************************************************/
    FUNCTION func_get_evaluate_approach(p_ri_arrangement_code IN VARCHAR2,p_business_no IN VARCHAR2, p_business_direction IN VARCHAR2, p_risk_code in varchar2) RETURN VARCHAR2 IS

        v_evaluate_approach varchar2(10);
    BEGIN

        v_evaluate_approach := '';

        if p_ri_arrangement_code = 'D' then
            --直接业务/临分分入
            SELECT MAX(l.evaluate_approach)
            into v_evaluate_approach
            FROM dmuser.DM_policy_premium t,
                 bpluser.bbs_conf_product     p,
                 bpluser.bbs_conf_loa_detail  ld,
                 bpluser.bbs_conf_loa         l
            where /*t.risk_code = p_risk_code
        and*/ t.product_code = p.product_code
              and l.business_model = 'D'
              and l.business_direction = 'D'
              and p.product_id = ld.business_id
              and ld.loa_id = l.loa_id
              and p.product_code = t.product_code
              and t.policy_no = p_business_no
              and p.product_code not like '03%' --非03产品
              and rownum = 1;

            if v_evaluate_approach is null or v_evaluate_approach = '' then
                SELECT MAX(l.evaluate_approach)
                into v_evaluate_approach
                FROM dmuser.DM_policy_premium t,
                     bpluser.bbs_conf_product     p,
                     bpluser.bbs_conf_loa_detail  ld,
                     bpluser.bbs_conf_loa         l,
                     bpluser.bbs_conf_loa_tic tic
                where /*t.risk_code = p_risk_code
            and*/ t.product_code = p.product_code
                  and l.business_model = 'D'
                  and l.business_direction = 'D'
                  and p.product_id = ld.business_id
                  and ld.loa_id = l.loa_id
                  and p.product_code = t.product_code
                  and t.policy_no = p_business_no
                  and p.product_code like '03%' --03 产品
                  and l.loa_id = tic.loa_id
                  and tic.tic_id = (select code_id from bpluser.bpl_v_conf_code where code_code_idx = 'TicCode/'||t.tic_code)
                  and rownum = 1;
            end if;
        elsif p_ri_arrangement_code = 'F' then
            --临分分出
            --因为临分分出业务只有直接业务和临分分入业务做临分分出；
            --没有合约分入转临分分出
            SELECT MAX(l.evaluate_approach)
            into v_evaluate_approach
            FROM dmuser.DM_policy_premium t,
                 bpluser.bbs_conf_product     p,
                 bpluser.bbs_conf_loa_detail  ld,
                 bpluser.bbs_conf_loa         l
            where /*t.risk_code = p_risk_code
        and*/ t.product_code = p.product_code
              and l.business_model = 'F'
              and l.business_direction = 'O'
              and p.product_id = ld.business_id
              and ld.loa_id = l.loa_id
              and p.product_code = t.product_code
              and t.policy_no = p_business_no
              and p.product_code not like '03%' --非03产品
              and rownum = 1;

            if v_evaluate_approach is null or v_evaluate_approach = '' then
                SELECT MAX(l.evaluate_approach)
                into v_evaluate_approach
                FROM dmuser.DM_policy_premium t,
                     bpluser.bbs_conf_product     p,
                     bpluser.bbs_conf_loa_detail  ld,
                     bpluser.bbs_conf_loa         l,
                     bpluser.bbs_conf_loa_tic tic
                where /*t.risk_code = p_risk_code
            and*/ t.product_code = p.product_code
                  and l.business_model = 'F'
                  and l.business_direction = 'O'
                  and p.product_id = ld.business_id
                  and ld.loa_id = l.loa_id
                  and p.product_code = t.product_code
                  and t.policy_no = p_business_no
                  and p.product_code like '03%' --03 产品
                  and l.loa_id = tic.loa_id
                  and tic.tic_id = (select code_id from bpluser.bpl_v_conf_code where code_code_idx = 'TicCode/'||t.tic_code)
                  and rownum = 1;
            end if;
        else
            --合约业务
            if p_business_direction = 'I' then
                SELECT MAX(l.evaluate_approach)
                into v_evaluate_approach
                FROM bpluser.BBS_CONF_BASE_TREATY bct,
                     bpluser.bbs_conf_loa_detail  ld,
                     bpluser.bbs_conf_loa         l
                where l.loa_id = ld.loa_id
                  and l.business_model in ('T')
                  and l.business_direction in ('I')
                  and l.entity_id = 1
                  and ld.business_id = bct.conf_treaty_id
                  and l.entity_id = bct.entity_id
                  and ((bct.base_treaty_no =
                        substr(p_business_no, 0, length(p_business_no) - 4) and
                        bct.TREATY_TYPE_CODE not in ('71', '72')) or
                       (bct.base_treaty_no =
                        substr(p_business_no, 0, length(p_business_no) - 6) and
                        bct.TREATY_TYPE_CODE in ('71', '72')))
                  and rownum = 1;
            else
                SELECT MAX(l.evaluate_approach)
                into v_evaluate_approach
                FROM bpluser.BBS_CONF_BASE_TREATY bct,
                     bpluser.bbs_conf_loa_detail  ld,
                     bpluser.bbs_conf_loa         l
                where l.loa_id = ld.loa_id
                  and l.business_model in ('T')
                  and l.business_direction in ('O')
                  and l.entity_id = 1
                  and ld.business_id = bct.conf_treaty_id
                  and l.entity_id = bct.entity_id
                  and ((bct.base_treaty_no =
                        substr(p_business_no, 0, length(p_business_no) - 4) and
                        bct.TREATY_TYPE_CODE not in ('71', '72')) or
                       (bct.base_treaty_no =
                        substr(p_business_no, 0, length(p_business_no) - 6) and
                        bct.TREATY_TYPE_CODE in ('71', '72')))
                  and rownum = 1;
            end if;
        end if;


        RETURN v_evaluate_approach;
    EXCEPTION
        WHEN OTHERS THEN
            --dbms_output.put_line('[EXCEPTION]func_get_evaluate_approach：' || to_char(SQLCODE) || ','|| SUBSTR(SQLERRM, 1, 200));
            RETURN '';

    END func_get_evaluate_approach;


    /***********************************************************************
    NAME :func_get_business_msg
    DESCRIPTION :获取业务信息
    DATE :2022-10-31
    AUTHOR :CHENJUNFENG
    BUSINESS RULE : p_payment_id-唯一ID
                    p_type ：获取类型，1-p_ri_arrangement_code、2-p_business_no、3-p_business_direction
    ***********************************************************************/
    FUNCTION func_get_business_msg(p_payment_id IN number,p_type IN VARCHAR2) RETURN VARCHAR2 IS

        business_msg varchar2(100);
    BEGIN

        business_msg := '';

        if p_type = '1' then
            --p_ri_arrangement_code
            SELECT MAX(case
                           when t.BUSINESS_SOURCE_CODE = 'DB' and t.RI_DIRECTION_CODE = 'D' then
                               'D' --直接业务
                           when t.BUSINESS_SOURCE_CODE = 'FB' and t.RI_DIRECTION_CODE = 'I' then
                               'D' --临分分入
                           when t.RI_ARRANGEMENT_CODE in ('F', 'NF') and
                                t.RI_DIRECTION_CODE = 'O' then
                               'F' --临分分出
                           when t.BUSINESS_SOURCE_CODE in ('TB') and t.RI_DIRECTION_CODE = 'I' then
                               'T' --合约分入
                           when t.RI_ARRANGEMENT_CODE in ('T','X') and t.RI_DIRECTION_CODE = 'O' then
                               'T' --合约分出
                           else
                               'D' --？
                end)
            into business_msg
            FROM dmuser.DM_acc_payment t
            where t.id = p_payment_id
              and t.EXPENSES_TYPE_CODE != 'cashflow'
              and t.BUSINESS_SOURCE_CODE != 'PB'
              and rownum = 1;
        elsif p_type = '2' then
            --p_business_no
            SELECT MAX(case
                           when t.BUSINESS_SOURCE_CODE = 'DB' and t.RI_DIRECTION_CODE = 'D' then
                               t.policy_no --直接业务
                           when t.BUSINESS_SOURCE_CODE = 'FB' and t.RI_DIRECTION_CODE = 'I' then
                               t.policy_no --临分分入
                           when t.RI_ARRANGEMENT_CODE in ('F', 'NF') and
                                t.RI_DIRECTION_CODE = 'O' then
                               t.policy_no --临分分出
                           when t.BUSINESS_SOURCE_CODE in ('TB') and t.RI_DIRECTION_CODE = 'I' then
                               t.treaty_no --合约分入
                           when t.RI_ARRANGEMENT_CODE in ('T','X') and t.RI_DIRECTION_CODE = 'O' then
                               t.treaty_no --合约分出
                           else
                               'D' --？
                end)
            into business_msg
            FROM dmuser.DM_acc_payment t
            where t.id = p_payment_id
              and t.EXPENSES_TYPE_CODE != 'cashflow'
              and t.BUSINESS_SOURCE_CODE != 'PB'
              and rownum = 1;
        else
            business_msg := '';
        end if;


        RETURN business_msg;
    EXCEPTION
        WHEN OTHERS THEN
            --dbms_output.put_line('[EXCEPTION]func_get_business_msg：' || to_char(SQLCODE) || ','|| SUBSTR(SQLERRM, 1, 200));
            RETURN '';

    END func_get_business_msg;

    /***********************************************************************
    NAME :func_get_business_msg
    DESCRIPTION :获取业务时间
    DATE :2023-09-12
    AUTHOR :CHENJUNFENG
    BUSINESS RULE : p_type ：获取类型，1-出险时间
    ***********************************************************************/
    FUNCTION func_get_date_time(p_business_no IN VARCHAR2, p_buins_type IN VARCHAR2, p_ri_arrangement_code IN VARCHAR2, p_business_direction IN VARCHAR2, p_type IN VARCHAR2) RETURN TIMESTAMP IS

        v_date_time  timestamp;
    BEGIN

        if p_type = '1' then
            if p_buins_type in ('DB','FB')  then
                SELECT t.accident_date_time into v_date_time FROM dmuser.dm_claim_main t left join dmuser.dm_claim_loss a on a.claim_no = t.claim_no
                where a.claim_loss_no = p_business_no and rownum =1;

            elsif p_buins_type in ('TB')  then
                SELECT to_date(t.account_period||'01','YYYYMMDD') into v_date_time FROM dmuser.dm_reins_bill t where t.ri_statement_no = p_business_no and rownum =1;
            else
                v_date_time := sysdate;
            end if;

        else
            v_date_time := null;
        end if;


        RETURN v_date_time;
    EXCEPTION
        WHEN OTHERS THEN
            --dbms_output.put_line('[EXCEPTION]func_get_business_msg：' || to_char(SQLCODE) || ','|| SUBSTR(SQLERRM, 1, 200));
            RETURN sysdate;

    END func_get_date_time;

end acc_pack_data_sync;
/

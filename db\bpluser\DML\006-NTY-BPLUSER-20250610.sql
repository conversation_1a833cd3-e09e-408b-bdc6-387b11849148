--1.删除菜单关联的task
delete from bpl_saa_menu_task where menu_id = (select menu_id from bpl_saa_menu where menu_e_name = 'Buss Reoprt Item View' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Data View'));
delete from bpl_saa_menu_task where menu_id = (select menu_id from bpl_saa_menu where menu_e_name = 'Data Inspection Result' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Data Inspection'));
delete from bpl_saa_menu_task where menu_id = (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management'));

--2.删除菜单
delete from bpl_saa_menu where menu_e_name = 'Buss Reoprt Item View' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Data View');
delete from bpl_saa_menu where menu_e_name = 'Data View' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and upper_id = 0);

delete from bpl_saa_menu where menu_e_name = 'Data Inspection Result' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Data Inspection');
delete from bpl_saa_menu where menu_e_name = 'Data Inspection' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and upper_id = 0);

delete from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management');

--3.删除task
delete from bpl_saa_task where task_code = 'RptRunDataCheck';
delete from bpl_saa_task where task_code = 'rptBussItemList';
delete from bpl_saa_task where task_code = 'rptRuleConfigDelete';

--4.添加task
INSERT INTO bpluser.bpl_saa_task(task_id, task_code, group_name, task_c_name, task_l_name, task_e_name, task_url, valid_is, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_task'), 'RptRunDataCheck', '', '执行数据检查', '執行數據檢查', 'Rpt Run Data Check', '/check_data', '1', 1, '2025-06-10 16:10:10.284', NULL, NULL, '1');

INSERT INTO bpluser.bpl_saa_task(task_id, task_code, group_name, task_c_name, task_l_name, task_e_name, task_url, valid_is, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_task'), 'rptBussItemList', '', '查询列项列表', '查詢列項列表', 'Buss Item List', '/getBussItemList', '1', 1, '2025-06-10 16:11:26.801', NULL, NULL, '1');

INSERT INTO bpluser.bpl_saa_task(task_id, task_code, group_name, task_c_name, task_l_name, task_e_name, task_url, valid_is, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_task'), 'rptRuleConfigDelete', '', '删除单条数据', '刪除單條數據', 'Rule Config Delete', '/deleteById/{checkRuleId}', '1', 1, '2025-06-10 16:11:26.785', NULL, NULL, '1');

--5.添加菜单
INSERT INTO bpluser.bpl_saa_menu(menu_id, upper_id, menu_level, system_code, menu_c_name, menu_l_name, menu_e_name, target_router, action_url, task_code, type, code, icon, display_no, valid_ind, remark, creator_id, create_time, updator_id, update_time, mapping_url) VALUES (nextval('bpl_seq_menu'), (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and upper_id = 0), 2, 'RPT', '数据查看', '數據查看', 'Data View', '', '', NULL, 'M', NULL, 'icon-basicData', 1, '1', NULL, 1, '2025-06-10 16:08:06.913', NULL, NULL, NULL);

INSERT INTO bpluser.bpl_saa_menu(menu_id, upper_id, menu_level, system_code, menu_c_name, menu_l_name, menu_e_name, target_router, action_url, task_code, type, code, icon, display_no, valid_ind, remark, creator_id, create_time, updator_id, update_time, mapping_url) VALUES (nextval('bpl_seq_menu'), (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Data View'), 3, 'RPT', '列报项数据查看', '列報項數據查看', 'Buss Reoprt Item View', '/rpt/data_view/buss_report_item_view_app', '/reporting/bussItem', NULL, 'M', NULL, '', 1, '1', NULL, 1, '2025-06-10 16:08:50.945', NULL, NULL, NULL);


INSERT INTO bpluser.bpl_saa_menu(menu_id, upper_id, menu_level, system_code, menu_c_name, menu_l_name, menu_e_name, target_router, action_url, task_code, type, code, icon, display_no, valid_ind, remark, creator_id, create_time, updator_id, update_time, mapping_url) VALUES (nextval('bpl_seq_menu'), (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and upper_id = 0), 2, 'RPT', '数据检查', '數據檢查', 'Data Inspection', '', '', NULL, 'M', NULL, 'icon-dataCheck', 3, '1', NULL, 1, '2025-06-10 16:09:23.759', NULL, NULL, NULL);

INSERT INTO bpluser.bpl_saa_menu(menu_id, upper_id, menu_level, system_code, menu_c_name, menu_l_name, menu_e_name, target_router, action_url, task_code, type, code, icon, display_no, valid_ind, remark, creator_id, create_time, updator_id, update_time, mapping_url) VALUES (nextval('bpl_seq_menu'), (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Data Inspection'), 3, 'RPT', '数据检查结果', '數據檢查結果', 'Data Inspection Result', '/rpt/dataCheck/data_check_error_app', '/reporting/check_result', NULL, 'M', NULL, '', 1, '1', NULL, 1, '2025-06-10 16:10:10.216', NULL, NULL, NULL);


INSERT INTO "bpluser"."bpl_saa_menu"("menu_id", "upper_id", "menu_level", "system_code", "menu_c_name", "menu_l_name", "menu_e_name", "target_router", "action_url", "task_code", "type", "code", "icon", "display_no", "valid_ind", "remark", "creator_id", "create_time", "updator_id", "update_time", "mapping_url") VALUES (nextval('bpl_seq_menu'), (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management'), 3, 'RPT', '校验规则配置', '校驗規則配置', 'Inspection Rule Configuration', '/rpt/config_manage/checkRules_app', '/reporting/checkRules', NULL, 'M', NULL, '', 6, '1', NULL, 1, '2025-06-10 16:11:26.767', NULL, NULL, NULL);

--6.菜单关联task
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Buss Reoprt Item View' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Data View')), (select task_id from bpl_saa_task where task_e_name = 'Enquiry'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Buss Reoprt Item View' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Data View')), (select task_id from bpl_saa_task where task_code = 'view'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);


INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Data Inspection Result' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Data Inspection')), (select task_id from bpl_saa_task where task_e_name = 'Enquiry'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Data Inspection Result' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Data Inspection')), (select task_id from bpl_saa_task where task_e_name = 'Rpt Run Data Check'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);

INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management')), (select task_id from bpl_saa_task where task_e_name = 'Enquiry'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management')), (select task_id from bpl_saa_task where task_code = 'add'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management')), (select task_id from bpl_saa_task where task_code = 'edit'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management')), (select task_id from bpl_saa_task where task_code = 'view'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management')), (select task_id from bpl_saa_task where task_code = 'delete'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management')), (select task_id from bpl_saa_task where task_e_name = 'Status'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management')), (select task_id from bpl_saa_task where task_e_name = 'Approval'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management')), (select task_id from bpl_saa_task where task_e_name = 'Approval In Batch'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management')), (select task_id from bpl_saa_task where task_code = 'rptRuleConfigDelete'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
INSERT INTO bpluser.bpl_saa_menu_task(menu_task_id, menu_id, task_id, creator_id, create_time, updator_id, update_time, task_type) VALUES (nextval('bpl_seq_saa_menu_task'), (select menu_id from bpl_saa_menu where menu_e_name = 'Inspection Rule Configuration' and upper_id = (select menu_id from BPL_SAA_MENU where system_code = 'RPT' and menu_e_name = 'Configuration Management')), (select task_id from bpl_saa_task where task_e_name = 'Buss Item List'), 1, '2025-06-10 16:11:26.831', NULL, NULL, NULL);
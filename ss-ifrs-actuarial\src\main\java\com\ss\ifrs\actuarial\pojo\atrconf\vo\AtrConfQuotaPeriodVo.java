package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @ClassName BbConfQuotaPeriodVo
 * <AUTHOR>
 * @Date 2021/11/16
 **/
public class AtrConfQuotaPeriodVo implements Serializable {
    /**
     * Database column: atr_buss_quota_detail.quota_detail_id
     * Database remarks: quotaDetailId|主键
     */
    @ApiModelProperty(value = "quotaDetailId|主键", required = true)
    private Long quotaDetailId;

    /**
     * Database column: atr_buss_quota_detail.buss_quota_id
     * Database remarks: bussQuotaId|指标
     */
    @ApiModelProperty(value = "bussQuotaId|指标", required = true)
    private Long quotaId;

    /**
     * Database column: atr_buss_quota_detail.quota_period
     * Database remarks: quotaPeriod|发展期
     */
    @ApiModelProperty(value = "quotaPeriod|发展期", required = false)
    private Long quotaPeriod;

    /**
     * Database column: atr_buss_quota_detail.quota_value
     * Database remarks: quotaValue|指标值
     */
    @ApiModelProperty(value = "quotaValue|指标值", required = false)
    private String quotaValue;



    private Long quotaDefId;

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    private static final long serialVersionUID = 1L;

    public Long getQuotaDetailId() {
        return quotaDetailId;
    }

    public void setQuotaDetailId(Long quotaDetailId) {
        this.quotaDetailId = quotaDetailId;
    }

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getQuotaPeriod() {
        return quotaPeriod;
    }

    public void setQuotaPeriod(Long quotaPeriod) {
        this.quotaPeriod = quotaPeriod;
    }

    public String getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(String quotaValue) {
        this.quotaValue = quotaValue;
    }
}

alter table atr_dap_autoquota_unit_premium alter endorse_seq_no type varchar(60);


alter table atr_dap_dd_premium alter endorse_seq_no type varchar(60);
alter table atr_dap_fo_premium alter ri_endorse_seq_no type varchar(60);
alter table atr_dap_fo_premium alter endorse_seq_no type varchar(60);
alter table atr_dap_to_premium_recovered alter ri_endorse_seq_no type varchar(60);
alter table atr_dap_to_premium_recovered alter endorse_seq_no type varchar(60);
alter table atr_dap_dd_payment_plan alter endorse_seq_no type varchar(60);
alter table atr_dap_dd_premium_paid alter endorse_seq_no type varchar(60);
alter table atr_dap_fo_premium_paid alter ri_endorse_seq_no type varchar(60);
alter table atr_dap_fo_premium_paid alter endorse_seq_no type varchar(60);
alter table atr_dap_dd_claim_recv alter endorse_seq_no type varchar(60);
alter table atr_dap_fo_claim_recv alter ri_endorse_seq_no type varchar(60);
alter table atr_dap_dd_claim_paid alter endorse_seq_no type varchar(60);
alter table atr_dap_fo_claim_paid alter ri_endorse_seq_no type varchar(60);
alter table atr_buss_dd_lrc_icu_calc alter endorse_seq_no type varchar(60);
alter table atr_buss_fo_lrc_icu_calc alter ri_endorse_seq_no type varchar(60);
alter table atr_buss_fo_lrc_icu_calc alter endorse_seq_no type varchar(60);
alter table atr_buss_to_lrc_icu_rep_calc alter ri_endorse_seq_no type varchar(60);
alter table atr_temp_dd_premium_paid_cur alter endorse_seq_no type varchar(60);
alter table atr_temp_dd_premium_paid_lt alter endorse_seq_no type varchar(60);
alter table atr_temp_dd_premium_paid_lte alter endorse_seq_no type varchar(60);
alter table atr_temp_dd_payment_plan alter endorse_seq_no type varchar(60);




call atr_pack_commonutils_add_table_column('atr_dap_dd_premium', 'main_policy_no', 'varchar(60)', '大保单号');
call atr_pack_commonutils_add_table_column('atr_dap_fo_premium', 'main_policy_no', 'varchar(60)', '大保单号');
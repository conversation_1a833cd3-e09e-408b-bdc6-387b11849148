delete from bpluser.bpl_qrtz_conf_task where task_c_name = '计算过渡期已赚保费';
delete from bpluser.bpl_qrtz_conf_task_detail where func_code = 'BUSS_TRANSITION_ED_PREMIUM';
delete from bpluser.bpl_qrtz_job_details where job_name = 'BUSS_TRANSITION_ED_PREMIUM';
delete from bpluser.bpl_qrtz_triggers where trigger_name = 'T_BUSS_TRANSITION_ED_PREMIUM';
delete from bpluser.bpl_qrtz_cron_triggers where trigger_name = 'T_BUSS_TRANSITION_ED_PREMIUM';

INSERT INTO bpluser.bpl_qrtz_conf_task (conf_task_id, task_c_name, task_l_name, task_e_name, url, param, frequency,
                                        valid_is, create_time, update_time, creator_id, updator_id, repeat_is,
                                        task_group, relation_task_id, retry_count, retry_strategy, async_is)
VALUES (nextval('bpl_seq_qrtz_conf_task'), '计算过渡期已赚保费', '计算過渡期已賺保費', 'Calc Transition Ed_Premium',
        'http://ss-ifrs-actuarial/job/calculateTransitionEarnedPremium', null, 'M', '1', '2025-06-04 15:30:58.000000',
        '2025-06-05 17:46:37.777000', 1, 1, '1', 'ATR', null, 1, 'Fixed', '0');


INSERT INTO bpluser.bpl_qrtz_conf_task_detail (task_detail_id, conf_task_id, func_code, func_c_name, func_l_name,
                                               func_e_name, task_type, method, serialized_param, proc_name, param,
                                               frequency, valid_is, create_time, update_time, creator_id, updator_id,
                                               async_is, relation_task_dtl_id, priority_no, retry_count, retry_strategy)
VALUES (nextval('bpl_seq_qrtz_conf_task_detail'),
        (select conf_task_id from bpl_qrtz_conf_task where task_c_name = '计算过渡期已赚保费'),
        'BUSS_TRANSITION_ED_PREMIUM', '计算过渡期已赚保费', '計算過渡期已賺保費',
        'Calc Transition Ed_Premium', 'M', 'atrBussLrcCashFlowApi.calculateTransitionEarnedPremium',
        'javax.servlet.http.HttpServletRequest,com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConvertJsonVo', '', null, 'M',
        '1', '2025-06-04 15:39:32.573000', '2025-06-04 17:28:50.791000', 1, 1, '0', null, null, 1, 'Fixed');


INSERT INTO bpluser.bpl_qrtz_job_details (sched_name, job_name, job_group, description, job_class_name, is_durable,
                                          is_nonconcurrent, is_update_data, requests_recovery, job_data, url,
                                          create_time, job_c_name, job_l_name, entity_id, proc_id, job_e_name,
                                          creator_id, period_buss_code, display_no, original_is, job_frequency,
                                          priority, conf_task_id)
VALUES ('DefaultQuartzScheduler', 'BUSS_TRANSITION_ED_PREMIUM', 'ATR', null, 'com.ss.platform.schedule.work.TaskJob',
        true, true, true, false, e'#
#Wed Jun 04 15:49:07 CST 2025
url=http\\://ss-ifrs-actuarial/job/calculateTransitionEarnedPremium
entityId=1
jobFrequency=M
cookie=
cron=* * * * 12 ?
param=
funcType=1
userCode=admin
', 'http://ss-ifrs-actuarial/job/calculateTransitionEarnedPremium', '2025-06-04 15:49:07.518000', '计算过渡期已赚保费',
        '計算過渡期已賺保費', 1, null, 'Calc Transition Ed_Premium', null, '', null, '', 'M', 5, 3281);


INSERT INTO bpluser.bpl_qrtz_triggers (sched_name, trigger_name, trigger_group, job_name, job_group, description,
                                       next_fire_time, prev_fire_time, priority, trigger_state, trigger_type,
                                       start_time, end_time, calendar_name, misfire_instr, job_data)
VALUES ('DefaultQuartzScheduler', 'T_BUSS_TRANSITION_ED_PREMIUM', 'ATR', 'BUSS_TRANSITION_ED_PREMIUM', 'ATR', null,
        1764518400000, -1, 5, 'WAITING', 'CRON', 1749023347000, 0, null, 2, '');



INSERT INTO bpluser.bpl_qrtz_cron_triggers (sched_name, trigger_name, trigger_group, cron_expression, time_zone_id)
VALUES ('DefaultQuartzScheduler', 'T_BUSS_TRANSITION_ED_PREMIUM', 'ATR', '* * * * 12 ? ', 'Asia/Shanghai');

call acc_pack_commonutils_proc_add_table_column('acc_generalledger_push_log', 'push_type', 'varchar(10)', '推送类型');

call acc_pack_commonutils_proc_add_table_column('ACC_EXT_VOUCHER_DETAIL', 'entity_id', 'int8', '');
call acc_pack_commonutils_proc_add_table_column('ACC_EXT_VOUCHER_DETAIL', 'year_month', 'VARCHAR(6)', '会计期间');

call acc_pack_commonutils_proc_add_table_column('ACC_EXT_VOUCHER_DETAIL', 'article_msg', 'VARCHAR(1000)', '辅助核算信息');

call acc_pack_commonutils_proc_modify_table_columntype('acc_duct_entry_data_success', 'treaty_code' , 'varchar(60)');


DROP TABLE acc_generalledger_push_log;
CREATE TABLE accuser.acc_generalledger_push_log (
  push_log_id int8 NOT NULL,
  entity_id int8,
  book_code varchar(50) COLLATE pg_catalog.default,
  year_month varchar(6) COLLATE pg_catalog.default,
  action_no varchar(100) COLLATE pg_catalog.default,
  execution_status varchar(1) COLLATE pg_catalog.default,
  total_num int4,
  total_success int4,
  total_fail int4,
  pusher_id int8,
  push_start_data timestamp(6),
  push_end_data timestamp(6),
  fail_message text COLLATE pg_catalog.default,
  push_type varchar(10) COLLATE pg_catalog.default,
  PRIMARY KEY (push_log_id)
)
;

ALTER TABLE accuser.acc_generalledger_push_log 
  OWNER TO accuser;

COMMENT ON COLUMN accuser.acc_generalledger_push_log.push_log_id IS '日志主键，自增或唯一标识一次推送记录';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.entity_id IS '账套ID或组织ID，用于标识所属实体';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.book_code IS '账簿代码，标识推送目标的总账账簿';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.year_month IS '期间（年月），格式如202405，标识数据所属期间';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.action_no IS '推送动作编号，用于标识一次具体的推送任务';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.execution_status IS '执行状态 0：待执行| 1：执行中 | 2：执行成功 | 3： 执行失败';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.total_num IS '本次推送的总数据量';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.total_success IS '推送成功的数据条数';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.total_fail IS '推送失败的数据条数';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.pusher_id IS '推送人ID，记录是谁执行了推送动作';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.push_start_data IS '推送时间，即推送操作发生的时间(推送開始時間)';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.push_end_data IS '推送时间，即推送操作发生的时间(推送結束時間)';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.fail_message IS '失败异常信息';

COMMENT ON COLUMN accuser.acc_generalledger_push_log.push_type IS '推送类型';

COMMENT ON TABLE accuser.acc_generalledger_push_log IS '总账推送日志表，用于记录每次将会计数据推送至总账系统的操作记录';



DROP TABLE acc_push_voucher;
CREATE TABLE accuser.acc_push_voucher (
  interface_id int8 NOT NULL DEFAULT,
  status varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT 'NEW'::character varying,
  ledger_id varchar(25) COLLATE pg_catalog.default NOT NULL,
  accounting_date date NOT NULL,
  currency_code varchar(15) COLLATE pg_catalog.default NOT NULL DEFAULT 'CNY'::character varying,
  date_created date NOT NULL,
  actual_flag varchar(1) COLLATE pg_catalog.default NOT NULL DEFAULT 'A'::character varying,
  source_batch_id varchar(50) COLLATE pg_catalog.default NOT NULL,
  user_je_category_name varchar(25) COLLATE pg_catalog.default NOT NULL,
  user_je_source_name varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '会计引擎'::character varying,
  currency_conversion_date date NOT NULL,
  user_currency_conversion_type varchar(30) COLLATE pg_catalog.default NOT NULL DEFAULT 'User'::character varying,
  currency_conversion_rate numeric NOT NULL DEFAULT 1,
  import_flag varchar(1) COLLATE pg_catalog.default NOT NULL DEFAULT 'N'::character varying,
  import_date date,
  error_message varchar(2000) COLLATE pg_catalog.default,
  last_update_date date,
  last_update_by varchar(25) COLLATE pg_catalog.default,
  segment1 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment2 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment3 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment4 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment5 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment6 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment7 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment8 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment9 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment10 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment11 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment12 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  segment13 varchar(25) COLLATE pg_catalog.default NOT NULL DEFAULT '0'::character varying,
  entered_dr numeric(32,2),
  entered_cr numeric(32,2),
  accounted_dr numeric(32,2),
  accounted_cr numeric(32,2),
  doc_sequence_value varchar(100) COLLATE pg_catalog.default,
  reference1 varchar(100) COLLATE pg_catalog.default,
  reference2 varchar(240) COLLATE pg_catalog.default,
  reference3 varchar(100) COLLATE pg_catalog.default,
  reference4 varchar(100) COLLATE pg_catalog.default,
  reference5 varchar(240) COLLATE pg_catalog.default,
  reference10 varchar(240) COLLATE pg_catalog.default,
  je_batch_id int8,
  period_name varchar(15) COLLATE pg_catalog.default,
  je_header_id int8,
  je_line_num int8,
  chart_of_accounts_id int8,
  functional_currency_code varchar(15) COLLATE pg_catalog.default,
  warning_code varchar(4) COLLATE pg_catalog.default,
  stat_amount numeric(32,2),
  group_id int8,
  request_id int8,
  set_of_books_id varchar(10) COLLATE pg_catalog.default DEFAULT 'BookI17'::character varying,
  attribute_category varchar(30) COLLATE pg_catalog.default,
  attribute1 varchar(150) COLLATE pg_catalog.default,
  attribute2 varchar(150) COLLATE pg_catalog.default,
  attribute3 varchar(150) COLLATE pg_catalog.default,
  attribute4 varchar(150) COLLATE pg_catalog.default,
  attribute5 varchar(150) COLLATE pg_catalog.default,
  attribute6 varchar(150) COLLATE pg_catalog.default,
  attribute7 varchar(150) COLLATE pg_catalog.default,
  attribute8 varchar(150) COLLATE pg_catalog.default,
  attribute9 varchar(150) COLLATE pg_catalog.default,
  attribute10 varchar(150) COLLATE pg_catalog.default,
  attribute11 varchar(150) COLLATE pg_catalog.default,
  attribute12 varchar(150) COLLATE pg_catalog.default,
  attribute13 varchar(150) COLLATE pg_catalog.default,
  attribute14 varchar(150) COLLATE pg_catalog.default,
  attribute15 varchar(150) COLLATE pg_catalog.default,
  voucher_no varchar(32) COLLATE pg_catalog.default,
  voucher_dtl_id int8,
  push_type varchar(10) COLLATE pg_catalog.default,
  entity_id int8,
  year_month varchar(6) COLLATE pg_catalog.default,
  book_code varchar(10) COLLATE pg_catalog.default,
  proc_id int8,
  PRIMARY KEY (interface_id),
  CHECK (accounted_dr IS NULL AND accounted_cr IS NOT NULL OR accounted_dr IS NOT NULL AND accounted_cr IS NULL),
  CHECK (actual_flag::text = ANY (ARRAY['A'::character varying, 'B'::character varying, 'E'::character varying]::text[])),
  CHECK (entered_dr IS NULL AND entered_cr IS NOT NULL OR entered_dr IS NOT NULL AND entered_cr IS NULL),
  CHECK (import_flag::text = ANY (ARRAY['N'::character varying, 'P'::character varying, 'E'::character varying, 'Y'::character varying]::text[])),
  CHECK (status::text = ANY (ARRAY['NEW'::character varying, 'PROCESSED'::character varying, 'POSTED'::character varying]::text[]))
)
;

ALTER TABLE accuser.acc_push_voucher 
  OWNER TO accuser;

COMMENT ON COLUMN accuser.acc_push_voucher.status IS '凭证导入状态，NEW新建；PROCESSED已导入；POSTED已过账';

COMMENT ON COLUMN accuser.acc_push_voucher.ledger_id IS '账套名称,必须与EBS系统账套ID一致。测试环境账套ID为2081，生产环境需要总账投产后生成。';

COMMENT ON COLUMN accuser.acc_push_voucher.actual_flag IS '余额类型 A-实际;B-预算;E-保留款';

COMMENT ON COLUMN accuser.acc_push_voucher.source_batch_id IS '会计引擎凭证批次号,建议规则：系统代码(例如IFRS17)+系统时间(YYYYMMDDHHMMSS)';

COMMENT ON COLUMN accuser.acc_push_voucher.user_je_source_name IS '凭证来源名称（固定值会计引擎）';

COMMENT ON COLUMN accuser.acc_push_voucher.import_flag IS '导入标识，N未导入；P处理中；E数据有误；Y导入成功';

COMMENT ON COLUMN accuser.acc_push_voucher.import_date IS 'Oracle系统处理日期，由Oracle系统回写';

COMMENT ON COLUMN accuser.acc_push_voucher.error_message IS 'Oracle回写的详细错误信息';

COMMENT ON COLUMN accuser.acc_push_voucher.segment1 IS 'HGIC_COA_COM_IFRS 公司段';

COMMENT ON COLUMN accuser.acc_push_voucher.segment2 IS 'HGIC_COA_DEPT_IFRS 部门段';

COMMENT ON COLUMN accuser.acc_push_voucher.segment3 IS 'HGIC_COA_ACC_IFRS 科目段IFRS';

COMMENT ON COLUMN accuser.acc_push_voucher.segment4 IS 'HGIC_COA_DETAIL 明细段';

COMMENT ON COLUMN accuser.acc_push_voucher.segment5 IS 'HGIC_COA_PROD 产品段';

COMMENT ON COLUMN accuser.acc_push_voucher.segment6 IS 'HGIC_COA_SUBPROD 补充产品段';

COMMENT ON COLUMN accuser.acc_push_voucher.segment7 IS 'HGIC_COA_CHAN 渠道段';

COMMENT ON COLUMN accuser.acc_push_voucher.segment8 IS 'HGIC_COA_CF 现金流量段';

COMMENT ON COLUMN accuser.acc_push_voucher.segment9 IS 'HGIC_COA_GOC 合同组段';

COMMENT ON COLUMN accuser.acc_push_voucher.segment10 IS 'HGIC_COA_POC 合同组合段';

COMMENT ON COLUMN accuser.acc_push_voucher.segment11 IS 'HGIC_COA_NOTE 附注段';

COMMENT ON COLUMN accuser.acc_push_voucher.segment12 IS 'HGIC_COA_FUT1 备用段1';

COMMENT ON COLUMN accuser.acc_push_voucher.segment13 IS 'HGIC_COA_FUT2 备用段2';

COMMENT ON COLUMN accuser.acc_push_voucher.reference3 IS '必须为空';

COMMENT ON COLUMN accuser.acc_push_voucher.reference5 IS '凭证说明说明。需要包含会计引擎的生成的总账凭证编号。';

COMMENT ON TABLE accuser.acc_push_voucher IS 'Oracle总账接口表，用于凭证导入EBS系统';

call ods_pack_commonutils.drop_sequence('ODS_SEQ_ACC_PAYMENT');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_ACC_RECEIVABLE');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_ACCOUNT');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_CURRENCY');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_CURRENCY_RATE');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_ENTITY');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_PRODUCT');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_RISK');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_RISK_CLASS');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_BASE_RISK_MAPPING');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_CLAIM_LOSS');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_CLAIM_LOSS_DETAIL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_CLAIM_MAIN');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_CLAIM_OUTSTANDING');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_DATA_PUSH_SIGNAL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_DATA_PUSH_SIGNALHIS');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_FIN_ARTICLE_BALANCE');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_FIN_LEDGER_BALANCE');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_FIN_VOUCHER');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_FIN_VOUCHER_DETAIL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_POLICY_MAIN');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_POLICY_PAYMENT_PLAN');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_POLICY_PREMIUM');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_BASE_TREATY');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_BILL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_BILL_DETAIL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_FLOAT_CHARGE');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_OUTWARD');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_OUTWARD_DETAIL');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_REINSURER');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_RISK');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_TREATY');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_TREATY_CLASS');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_TREATY_PAYMENT_PLAN');
call ods_pack_commonutils.drop_sequence('ODS_SEQ_REINS_TREATY_SECTION');

call ods_pack_commonutils.add_sequence('ODS_SEQ_ACC_PAYMENT');
call ods_pack_commonutils.add_sequence('ODS_SEQ_ACC_RECEIVABLE');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_ACCOUNT');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_CURRENCY');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_CURRENCY_RATE');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_ENTITY');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_PRODUCT');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_RISK');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_RISK_CLASS');
call ods_pack_commonutils.add_sequence('ODS_SEQ_BASE_RISK_MAPPING');
call ods_pack_commonutils.add_sequence('ODS_SEQ_CLAIM_LOSS');
call ods_pack_commonutils.add_sequence('ODS_SEQ_CLAIM_LOSS_DETAIL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_CLAIM_MAIN');
call ods_pack_commonutils.add_sequence('ODS_SEQ_CLAIM_OUTSTANDING');
call ods_pack_commonutils.add_sequence('ODS_SEQ_DATA_PUSH_SIGNAL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_DATA_PUSH_SIGNALHIS');
call ods_pack_commonutils.add_sequence('ODS_SEQ_FIN_ARTICLE_BALANCE');
call ods_pack_commonutils.add_sequence('ODS_SEQ_FIN_LEDGER_BALANCE');
call ods_pack_commonutils.add_sequence('ODS_SEQ_FIN_VOUCHER');
call ods_pack_commonutils.add_sequence('ODS_SEQ_FIN_VOUCHER_DETAIL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_POLICY_MAIN');
call ods_pack_commonutils.add_sequence('ODS_SEQ_POLICY_PAYMENT_PLAN');
call ods_pack_commonutils.add_sequence('ODS_SEQ_POLICY_PREMIUM');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_BASE_TREATY');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_BILL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_BILL_DETAIL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_FLOAT_CHARGE');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_OUTWARD');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_OUTWARD_DETAIL');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_REINSURER');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_RISK');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_TREATY');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_TREATY_CLASS');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_TREATY_PAYMENT_PLAN');
call ods_pack_commonutils.add_sequence('ODS_SEQ_REINS_TREATY_SECTION');


grant select on odsuser.ODS_SEQ_ACC_PAYMENT to dmuser;
grant select on odsuser.ODS_SEQ_ACC_RECEIVABLE to dmuser;
grant select on odsuser.ODS_SEQ_BASE_ACCOUNT to dmuser;
grant select on odsuser.ODS_SEQ_BASE_CURRENCY to dmuser;
grant select on odsuser.ODS_SEQ_BASE_CURRENCY_RATE to dmuser;
grant select on odsuser.ODS_SEQ_BASE_ENTITY to dmuser;
grant select on odsuser.ODS_SEQ_BASE_PRODUCT to dmuser;
grant select on odsuser.ODS_SEQ_BASE_RISK to dmuser;
grant select on odsuser.ODS_SEQ_BASE_RISK_CLASS to dmuser;
grant select on odsuser.ODS_SEQ_BASE_RISK_MAPPING to dmuser;
grant select on odsuser.ODS_SEQ_CLAIM_LOSS to dmuser;
grant select on odsuser.ODS_SEQ_CLAIM_LOSS_DETAIL to dmuser;
grant select on odsuser.ODS_SEQ_CLAIM_MAIN to dmuser;
grant select on odsuser.ODS_SEQ_CLAIM_OUTSTANDING to dmuser;
grant select on odsuser.ODS_SEQ_DATA_PUSH_SIGNAL to dmuser;
grant select on odsuser.ODS_SEQ_DATA_PUSH_SIGNALHIS to dmuser;
grant select on odsuser.ODS_SEQ_FIN_ARTICLE_BALANCE to dmuser;
grant select on odsuser.ODS_SEQ_FIN_LEDGER_BALANCE to dmuser;
grant select on odsuser.ODS_SEQ_FIN_VOUCHER to dmuser;
grant select on odsuser.ODS_SEQ_FIN_VOUCHER_DETAIL to dmuser;
grant select on odsuser.ODS_SEQ_POLICY_MAIN to dmuser;
grant select on odsuser.ODS_SEQ_POLICY_PAYMENT_PLAN to dmuser;
grant select on odsuser.ODS_SEQ_POLICY_PREMIUM to dmuser;
grant select on odsuser.ODS_SEQ_REINS_BASE_TREATY to dmuser;
grant select on odsuser.ODS_SEQ_REINS_BILL to dmuser;
grant select on odsuser.ODS_SEQ_REINS_BILL_DETAIL to dmuser;
grant select on odsuser.ODS_SEQ_REINS_FLOAT_CHARGE to dmuser;
grant select on odsuser.ODS_SEQ_REINS_OUTWARD to dmuser;
grant select on odsuser.ODS_SEQ_REINS_OUTWARD_DETAIL to dmuser;
grant select on odsuser.ODS_SEQ_REINS_REINSURER to dmuser;
grant select on odsuser.ODS_SEQ_REINS_RISK to dmuser;
grant select on odsuser.ODS_SEQ_REINS_TREATY to dmuser;
grant select on odsuser.ODS_SEQ_REINS_TREATY_CLASS to dmuser;
grant select on odsuser.ODS_SEQ_REINS_TREATY_PAYMENT_PLAN to dmuser;
grant select on odsuser.ODS_SEQ_REINS_TREATY_SECTION to dmuser;



# FO业务endorse_type_code特殊处理测试场景

## 测试场景1：specialProcessType=1，累计应收 ≠ 累计实收（应收大于实收）

### 测试数据
- 保单号：FO_TEST_POLICY_001
- endorse_type_code：01,15,16
- year_month：202501（当前评估期）
- specialProcessType：1

### 金额数据
- 签单保费(premium)：10000
- 净额结算手续费(net_fee)：1000
- **累计应收总额**：11000

- 历史累计实收保费：6000
- 当期实收保费：2000
- 历史累计实收净额结算：600
- 当期实收净额结算：200
- **累计实收总额**：8800

- 历史累计已赚保费：5000

### 预期结果
**maxDevNo = 2**（因为累计应收11000 ≠ 累计实收8800）

**第0期(dev_no=0)**：
- edPremium：5000（10000-5000）
- edRate：0.5（5000/10000）
- edNetFee：500（1000*0.5）
- recvPremium：2000（当期实收保费）
- netFee：200（当期实收净额结算）

**第1期(dev_no=1)**：
- edPremium：0
- edRate：0
- edNetFee：0
- recvPremium：2000（10000-6000-2000）
- netFee：200（1000-600-200）

## 测试场景2：specialProcessType=1，累计应收 = 累计实收

### 测试数据
- 保单号：FO_TEST_POLICY_002
- endorse_type_code：01,15,16
- year_month：202501（当前评估期）
- specialProcessType：1

### 金额数据
- 签单保费(premium)：10000
- 净额结算手续费(net_fee)：1000
- **累计应收总额**：11000

- 历史累计实收保费：8000
- 当期实收保费：2000
- 历史累计实收净额结算：800
- 当期实收净额结算：200
- **累计实收总额**：11000

- 历史累计已赚保费：6000

### 预期结果
**maxDevNo = 1**（因为累计应收11000 = 累计实收11000）

**第0期(dev_no=0)**：
- edPremium：4000（10000-6000）
- edRate：0.4（4000/10000）
- edNetFee：400（1000*0.4）
- recvPremium：2000（当期实收保费）
- netFee：200（当期实收净额结算）

**无第1期**

## 测试场景3：specialProcessType=1，累计应收 ≠ 累计实收（实收大于应收）

### 测试数据
- 保单号：FO_TEST_POLICY_003
- endorse_type_code：01,15,16
- year_month：202501（当前评估期）
- specialProcessType：1

### 金额数据
- 签单保费(premium)：10000
- 净额结算手续费(net_fee)：1000
- **累计应收总额**：11000

- 历史累计实收保费：9000
- 当期实收保费：3000
- 历史累计实收净额结算：900
- 当期实收净额结算：300
- **累计实收总额**：13200

- 历史累计已赚保费：6000

### 预期结果
**maxDevNo = 2**（因为累计应收11000 ≠ 累计实收13200）

**第0期(dev_no=0)**：
- edPremium：4000（10000-6000）
- edRate：0.4（4000/10000）
- edNetFee：400（1000*0.4）
- recvPremium：3000（当期实收保费）
- netFee：300（当期实收净额结算）

**第1期(dev_no=1)**：
- edPremium：0
- edRate：0
- edNetFee：0
- recvPremium：-2000（10000-9000-3000，负数表示超收）
- netFee：-200（1000-900-300，负数表示超收）

## 测试场景4：specialProcessType=2

### 测试数据
- 保单号：FO_TEST_POLICY_004
- endorse_type_code：01,15,16
- year_month：202412（历史评估期）
- specialProcessType：2

### 金额数据
- 签单保费(premium)：10000
- 净额结算手续费(net_fee)：1000
- **累计应收总额**：11000

- 历史累计实收保费：6000
- 当期实收保费：2000
- 历史累计实收净额结算：600
- 当期实收净额结算：200
- **累计实收总额**：8800

### 预期结果
**maxDevNo = 2**（因为累计应收11000 ≠ 累计实收8800）

**第0期(dev_no=0)**：
- edPremium：0（specialProcessType=2时已赚为0）
- edRate：0
- edNetFee：0
- recvPremium：2000（当期实收保费）
- netFee：200（当期实收净额结算）

**第1期(dev_no=1)**：
- edPremium：0（specialProcessType=2时已赚为0）
- edRate：0
- edNetFee：0
- recvPremium：2000（10000-6000-2000）
- netFee：200（1000-600-200）

## 测试场景5：正常业务记录（不受影响）

### 测试数据
- 保单号：FO_NORMAL_POLICY_001
- endorse_type_code：01,02,03（不包含15和16）
- specialProcessType：0

### 预期结果
- 按照原有逻辑正常处理
- 不受特殊处理逻辑影响

## SQL逻辑测试场景

### SQL场景1：当月有15/16批单
**数据**：
- 保单号：FO_SQL_TEST_001
- 原单：year_month=202412, endorse_type_code=01
- 批单：year_month=202501, endorse_type_code=15
- 评估期：202501

**预期SQL结果**：
- 原单记录：special_process_type=1
- 批单记录：special_process_type=1

### SQL场景2：历史有15/16批单，当月没有
**数据**：
- 保单号：FO_SQL_TEST_002
- 原单：year_month=202412, endorse_type_code=01
- 批单：year_month=202412, endorse_type_code=16
- 评估期：202501

**预期SQL结果**：
- 原单记录：special_process_type=2
- 批单记录：special_process_type=2

### SQL场景3：无15/16批单
**数据**：
- 保单号：FO_SQL_TEST_003
- 原单：year_month=202412, endorse_type_code=01
- 批单：year_month=202501, endorse_type_code=02
- 评估期：202501

**预期SQL结果**：
- 原单记录：special_process_type=0
- 批单记录：special_process_type=0

## 循环逻辑修复验证

### 修复内容
通过调整特殊处理的maxDevNo计算逻辑，确保：
- 保持循环条件`i < maxDevNo`不变，不影响正常业务
- 当累计应收=累计实收时，maxDevNo=1，循环执行1次（i=0），计算第0期
- 当累计应收≠累计实收时，maxDevNo=2，循环执行2次（i=0,1），计算第0期和第1期

### 关键验证场景

#### 循环验证场景1：累计应收=累计实收的情况
**测试数据**：specialProcessType=1，累计应收=累计实收
**预期结果**：
- maxDevNo=1
- 循环执行1次（i=0）
- 第0期数据正常计算
- 不会出现数据丢失

#### 循环验证场景2：累计应收≠累计实收的情况
**测试数据**：specialProcessType=1，累计应收≠累计实收
**预期结果**：
- maxDevNo=2
- 循环执行2次（i=0,1）
- 第0期和第1期数据都正常计算
- 不会出现第1期数据丢失

## 关键验证点

1. **循环逻辑验证**：确保循环条件`i <= maxDevNo`正确执行
2. **SQL逻辑验证**：special_process_type在同一保单号下的一致性
3. **累计应收计算**：premium + net_fee
4. **累计实收计算**：preAccumPaidPremium + curPaidPremium + preAccumNetFee + curPaidNetFee
5. **maxDevNo判断**：累计应收 ≠ 累计实收时为1，相等时为0
6. **已赚计算**：specialProcessType=1时第0期正常计算，第1期为0；specialProcessType=2时全部为0
7. **现金流分配**：第0期放当期实收，第1期放差额（可正可负）
8. **字段范围限制**：只处理premium和net_fee两个字段
9. **正常业务验证**：确保修复不影响正常业务记录的处理结果

## 与DD业务对比验证

### 相同的验证点
1. SQL层面的special_process_check CTE逻辑
2. specialProcessType的判断逻辑
3. 动态maxDevNo计算逻辑
4. 现金流分配原则

### FO业务特有的验证点
1. **字段范围**：确保只处理premium和net_fee
2. **数据源**：确保从atr_dap_fo_paid表获取数据
3. **累计计算**：确保不包含iacf等其他费用字段

## 测试执行建议

1. **单元测试**：针对每个测试场景编写单元测试
2. **集成测试**：在测试环境验证完整的业务流程
3. **数据对比**：对比实现前后的计算结果
4. **边界测试**：测试各种边界条件和异常情况
5. **性能测试**：验证大数据量情况下的性能表现

## 预期测试结果

所有测试场景应该与DD业务的相应场景产生一致的业务逻辑结果，但在字段处理范围上有所区别。特别是要确保：

1. 特殊处理逻辑只对包含15/16批改类型的保单生效
2. 正常业务记录不受任何影响
3. 现金流分配逻辑正确处理正数和负数情况
4. SQL层面的特殊处理类型判断准确无误


call acc_pack_commonutils_proc_add_table_column('ACC_EXT_VOUCHER_DETAIL', 'entity_id', 'int8', '');
call acc_pack_commonutils_proc_add_table_column('ACC_EXT_VOUCHER_DETAIL', 'year_month', 'VARCHAR(6)', '会计期间');

call acc_pack_commonutils_proc_add_table_column('ACC_EXT_VOUCHER_DETAIL', 'article_msg', 'VARCHAR(1000)', '辅助核算信息');

call acc_pack_commonutils_proc_modify_table_columntype('acc_duct_entry_data_success', 'treaty_code' , 'varchar(60)');
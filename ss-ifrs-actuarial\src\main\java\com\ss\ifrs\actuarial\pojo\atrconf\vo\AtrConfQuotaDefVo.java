/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-11-11 09:59:46
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFact;
import com.ss.platform.core.annotation.SsTranslateCode;
import com.ss.platform.core.constant.SystemConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-11 09:59:46<br/>
 * Description: null<br/>
 * Table Name: bbs_conf_quota_def<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 * <AUTHOR>
 */
@ApiModel(value = "指标定义表")
public class AtrConfQuotaDefVo implements Serializable {
    /**
     * Database column: bbs_conf_quota_def.quota_def_id
     * Database remarks: quotaDefId|主键
     */
    @ApiModelProperty(value = "quotaDefId|主键", required = true)
    private Long quotaDefId;

    /**
     * Database column: bbs_conf_quota_def.quota_c_name
     * Database remarks: quotaCName|指标简体名称
     */
    @ApiModelProperty(value = "quotaCName|指标简体名称", required = false)
    @NotBlank(message = "The Chinese Name can't be null|中文名称不能为空|中文名稱不能為空")
    @Size(max = 200, message = "The Chinese Name's length is too long|中文名称过长|中文名稱過長")
    private String quotaCName;

    /**
     * Database column: bbs_conf_quota_def.quota_t_name
     * Database remarks: quotaLName|指标本地语言名称
     */
    @ApiModelProperty(value = "quotaLName|指标本地语言名称", required = false)
    @NotBlank(message = "The Traditional Chinese Name can't be null|繁体名称为空|繁體名稱不能為空")
    @Size(max = 200, message = "The Traditional Chinese Name's length is too long|繁体名称过长|繁體名稱過長")
    private String quotaLName;

    /**
     * Database column: bbs_conf_quota_def.quota_e_name
     * Database remarks: quotaEName|指标英文名称
     */
    @ApiModelProperty(value = "quotaEName|指标英文名称", required = false)
    @NotBlank(message = "The English Name can't be null|英文名称不能为空|英文名稱不能為空")
    @Size(max = 200, message = "The English Name's length is too long|英文名称过长|英文名稱過長")
    private String quotaEName;

    /**
     * Database column: bbs_conf_quota_defhis.code_type
     * Database remarks: codeType|指标类型
     */
    @ApiModelProperty(value = "codeType|基础代码类型", required = false)
    private String codeType;

    /**
     * Database column: bbs_conf_quota_def.quota_code
     * Database remarks: quotaCode|指标编码
     */
    @ApiModelProperty(value = "quotaCode|指标编码", required = false)
    @NotBlank(message = "The Code can't be null|编码不能为空|編碼不能為空")
    @Size(max = 32, message = "The Code's length is too long|编码过长|編碼過長")
    private String quotaCode;

    /**
     * Database column: bbs_conf_quota_def.quota_group
     * Database remarks: quotaGroup|指标分组
     */
    @ApiModelProperty(value = "quotaClass|指标归类", required = false)
    @SsTranslateCode(context = SystemConstant.AtrIdentity.APP_CONTEXT,codeCodeIdx = "QuotaClass")
    private String quotaClass;

    /**
     * Database column: bbs_conf_quota_def.quota_group
     * Database remarks: quotaGroup|指标分组
     */
    @ApiModelProperty(value = "quotaGroup|指标分组", required = false)
    @SsTranslateCode(context = SystemConstant.BbsIdentity.APP_CONTEXT,codeCodeIdx = "QuotaGroup")
    private String quotaGroup;

    /**
     * Database column: bbs_conf_quota_def.quota_type
     * Database remarks: quotaType|指标类型
     */
    @ApiModelProperty(value = "quotaType|指标类型", required = false)
    @NotBlank(message = "The Quota Type can't be null|指标类型不能为空|指標類型不能為空")
    @Size(max = 64, message = "The Quota Type's length is too long|指标类型过长|指標類型過長")
    @SsTranslateCode(context = SystemConstant.BbsIdentity.APP_CONTEXT,codeCodeIdx = "QuotaType")
    private String quotaType;

    /**
     * Database column: bbs_conf_quota_def.quota_value_type
     * Database remarks: quotaValueType|指标数据类型
     */
    @ApiModelProperty(value = "quotaValueType|指标数据类型", required = false)
    @NotBlank(message = "The Quota Data Type can't be null|指标数据类型不能为空|指標數據類型不能為空")
    @Size(max = 64, message = "The Quota Data Type's length is too long|指标数据类型过长|指標數據類型過長")
    @SsTranslateCode(context = SystemConstant.BbsIdentity.APP_CONTEXT,codeCodeIdx = "DataClass")
    private String quotaValueType;

    /**
     * Database column: bbs_conf_quota_def.rule_id
     * Database remarks: ruleId|规则ID
     */
    @ApiModelProperty(value = "ruleId|规则ID", required = true)
    private Long ruleId;

    /**
     * Database column: bbs_conf_quota_def.business_model
     * Database remarks: businessModel|业务模型：D-直保/再保临分,T-再保合约
     */
    @ApiModelProperty(value = "businessModel|业务模型：D-直保/再保临分,T-再保合约", required = false)
    private String businessModel;

    /**
     * Database column: bbs_conf_quota_def.treaty_no
     * Database remarks: treatyNo|合约号
     */
    @ApiModelProperty(value = "treatyNo|合约号", required = false)
    private String treatyNo;

    /**
     * Database column: bbs_conf_quota_def.percent_hundred_is
     * Database remarks: percentHundredIs|是否统计百分百
     */
    @ApiModelProperty(value = "percentHundredIs|是否统计百分百", required = false)
    @SsTranslateCode(context = SystemConstant.BbsIdentity.APP_CONTEXT,codeCodeIdx = "YesOrNo")
    private String percentHundredIs;

    /**
     * Database column: bbs_conf_quota_def.display_no
     * Database remarks: displayNo|排序序号
     */
    @ApiModelProperty(value = "displayNo|排序序号", required = true)
    @NotNull(message = "The Sort Sequence No. can't be null|排序序号不能为空|排序序號不能為空")
    //@DecimalMax(value = "2048", message = "Sort Sequence No. must be less than 2048|排序序号必须小于2048|排序序號必須小於2048")
    private Long displayNo;

    /**
     * Database column: bbs_conf_quota_def.version_no
     * Database remarks: serialNo|版本号
     */
    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Integer serialNo;

    /**
     * Database column: bbs_conf_quota_def.valid_is
     * Database remarks: validIs|是否有效
     */
    @ApiModelProperty(value = "validIs|是否有效", required = true)
    @NotBlank(message = "The Status cannot be empty|状态不能为空|狀態不能為空")
    @Size(max = 1,message = "The Status must be a character|状态只能是1个字符|狀態只能是1個字符")
    private String validIs;

    @ApiModelProperty(value = "quotaEffect|假设值影响", required = false)
    private String quotaEffect;

    /**
     * Database column: bbs_conf_quota_def.audit_state
     * Database remarks: audit_State|审核状态
     */
    @ApiModelProperty(value = "audit_State|审核状态", required = false)
    @SsTranslateCode(context = SystemConstant.BbsIdentity.APP_CONTEXT,codeCodeIdx = "AuditStatus/View")
    private String auditState;

    /**
     * Database column: bbs_conf_quota_def.checked_msg
     * Database remarks: checked_msg|审核意见
     */
    @ApiModelProperty(value = "checked_msg|审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: bbs_conf_quota_def.checked_id
     * Database remarks: checked_id|审核人
     */
    @ApiModelProperty(value = "checked_id|审核人", required = false)
    private Long checkedId;

    /**
     * Database column: bbs_conf_quota_def.checked_time
     * Database remarks: checked_time|审核时间
     */
    @ApiModelProperty(value = "checked_time|审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: bbs_conf_quota_def.creator_id
     * Database remarks: creator_id|创建人
     */
    @ApiModelProperty(value = "creator_id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: bbs_conf_quota_def.create_time
     * Database remarks: create_time|创建时间
     */
    @ApiModelProperty(value = "create_time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: bbs_conf_quota_def.updator_id
     * Database remarks: updator_id|最后修改人
     */
    @ApiModelProperty(value = "updator_id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: bbs_conf_quota_def.update_time
     * Database remarks: update_time|最后修改时间
     */
    @ApiModelProperty(value = "update_time|最后修改时间", required = false)
    private Date updateTime;

    /**
     * Database column: bbs_conf_quota_def.dimension
     * Database remarks: dimension|颗粒度
     */
    @ApiModelProperty(value = "dimension|颗粒度", required = false)
    @SsTranslateCode(context = SystemConstant.AtrIdentity.APP_CONTEXT,codeCodeIdx = "QuotaDimension/Base")
    private String dimension;

    /**
     * Database column: bbs_conf_quota_def.remark
     * Database remarks: remark|备注
     */
    @ApiModelProperty(value = "remark|备注", required = false)
    private String remark;

    private String creatorName;

    private String updatorName;

    private String businessSourceCode;

    private String quotaName;
    private Long entityId;
    private String riskClassCode;
    private String value;
    private Long quotaId;
    private String ruleExpression;
    private String ruleCode;
    private String ruleCName;
    private String ruleLName;
    private String ruleEName;
    private String msgCName;
    private String msgLName;
    private String msgEName;
    private String codeCName;
    private String codeLName;
    private String codeEName;
    private String riskCName;
    private String riskLName;
    private String riskEName;
    private String loaCName;
    private String loaLName;
    private String loaEName;

    private String quotaValueTypeName;
    private String quotaTypeName;

    private List<String> valueList;

    LinkedHashMap<String, Object> childQuota;

    private List<AtrConfQuotaDefFact> quotaDefList;

    private String businessDirection;

    private Long quotaDefFactId;

    private List<String> userModelList;

    private String targetRouter;

    private String language;

    private static final long serialVersionUID = 1L;

    private String templateFileName;

    private String autoCalculatedIs;

    public LinkedHashMap<String, Object> getChildQuota() {
        return childQuota;
    }

    public void setChildQuota(LinkedHashMap<String, Object> childQuota) {
        this.childQuota = childQuota;
    }

    public List<AtrConfQuotaDefFact> getQuotaDefList() {
        return quotaDefList;
    }

    public void setQuotaDefList(List<AtrConfQuotaDefFact> quotaDefList) {
        this.quotaDefList = quotaDefList;
    }

    public String getBusinessDirection() {
        return businessDirection;
    }

    public void setBusinessDirection(String businessDirection) {
        this.businessDirection = businessDirection;
    }

    public Long getQuotaDefFactId() {
        return quotaDefFactId;
    }

    public void setQuotaDefFactId(Long quotaDefFactId) {
        this.quotaDefFactId = quotaDefFactId;
    }

    public List<String> getUserModelList() {
        return userModelList;
    }

    public void setUserModelList(List<String> userModelList) {
        this.userModelList = userModelList;
    }

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public String getQuotaCName() {
        return quotaCName;
    }

    public void setQuotaCName(String quotaCName) {
        this.quotaCName = quotaCName;
    }

    public String getQuotaLName() {
        return quotaLName;
    }

    public void setQuotaLName(String quotaLName) {
        this.quotaLName = quotaLName;
    }

    public String getQuotaEName() {
        return quotaEName;
    }

    public void setQuotaEName(String quotaEName) {
        this.quotaEName = quotaEName;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public String getQuotaGroup() {
        return quotaGroup;
    }

    public void setQuotaGroup(String quotaGroup) {
        this.quotaGroup = quotaGroup;
    }

    public String getQuotaType() {
        return quotaType;
    }

    public void setQuotaType(String quotaType) {
        this.quotaType = quotaType;
    }

    public String getQuotaValueType() {
        return quotaValueType;
    }

    public void setQuotaValueType(String quotaValueType) {
        this.quotaValueType = quotaValueType;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getPercentHundredIs() {
        return percentHundredIs;
    }

    public void setPercentHundredIs(String percentHundredIs) {
        this.percentHundredIs = percentHundredIs;
    }

    public Long getDisplayNo() {
        return displayNo;
    }

    public void setDisplayNo(Long displayNo) {
        this.displayNo = displayNo;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getQuotaName() {
        return quotaName;
    }

    public void setQuotaName(String quotaName) {
        this.quotaName = quotaName;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public List<String> getValueList() {
        return valueList;
    }

    public void setValueList(List<String> valueList) {
        this.valueList = valueList;
    }

    public String getRuleExpression() {
        return ruleExpression;
    }

    public void setRuleExpression(String ruleExpression) {
        this.ruleExpression = ruleExpression;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleCName() {
        return ruleCName;
    }

    public void setRuleCName(String ruleCName) {
        this.ruleCName = ruleCName;
    }

    public String getRuleLName() {
        return ruleLName;
    }

    public void setRuleLName(String ruleLName) {
        this.ruleLName = ruleLName;
    }

    public String getRuleEName() {
        return ruleEName;
    }

    public void setRuleEName(String ruleEName) {
        this.ruleEName = ruleEName;
    }

    public String getMsgCName() {
        return msgCName;
    }

    public void setMsgCName(String msgCName) {
        this.msgCName = msgCName;
    }

    public String getMsgLName() {
        return msgLName;
    }

    public void setMsgLName(String msgLName) {
        this.msgLName = msgLName;
    }

    public String getMsgEName() {
        return msgEName;
    }

    public void setMsgEName(String msgEName) {
        this.msgEName = msgEName;
    }

    public String getCodeCName() {
        return codeCName;
    }

    public void setCodeCName(String codeCName) {
        this.codeCName = codeCName;
    }

    public String getCodeLName() {
        return codeLName;
    }

    public void setCodeLName(String codeLName) {
        this.codeLName = codeLName;
    }

    public String getCodeEName() {
        return codeEName;
    }


    public void setCodeEName(String codeEName) {
        this.codeEName = codeEName;
    }

    public String getBusinessModel() {
        return businessModel;
    }

    public void setBusinessModel(String businessModel) {
        this.businessModel = businessModel;
    }

    public String getTreatyNo() {
        return treatyNo;
    }

    public void setTreatyNo(String treatyNo) {
        this.treatyNo = treatyNo;
    }

    public String getRiskCName() {
        return riskCName;
    }

    public void setRiskCName(String riskCName) {
        this.riskCName = riskCName;
    }

    public String getRiskLName() {
        return riskLName;
    }

    public void setRiskLName(String riskLName) {
        this.riskLName = riskLName;
    }

    public String getRiskEName() {
        return riskEName;
    }

    public void setRiskEName(String riskEName) {
        this.riskEName = riskEName;
    }

    public String getLoaCName() {
        return loaCName;
    }

    public void setLoaCName(String loaCName) {
        this.loaCName = loaCName;
    }

    public String getLoaLName() {
        return loaLName;
    }

    public void setLoaLName(String loaLName) {
        this.loaLName = loaLName;
    }

    public String getLoaEName() {
        return loaEName;
    }

    public void setLoaEName(String loaEName) {
        this.loaEName = loaEName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getQuotaValueTypeName() {
        return quotaValueTypeName;
    }

    public void setQuotaValueTypeName(String quotaValueTypeName) {
        this.quotaValueTypeName = quotaValueTypeName;
    }

    public String getQuotaTypeName() {
        return quotaTypeName;
    }

    public void setQuotaTypeName(String quotaTypeName) {
        this.quotaTypeName = quotaTypeName;
    }

    public String getQuotaClass() {
        return quotaClass;
    }

    public void setQuotaClass(String quotaClass) {
        this.quotaClass = quotaClass;
    }

    public String getQuotaEffect() {
        return quotaEffect;
    }

    public void setQuotaEffect(String quotaEffect) {
        this.quotaEffect = quotaEffect;
    }

    public String getAutoCalculatedIs() {
        return autoCalculatedIs;
    }

    public void setAutoCalculatedIs(String autoCalculatedIs) {
        this.autoCalculatedIs = autoCalculatedIs;
    }
}
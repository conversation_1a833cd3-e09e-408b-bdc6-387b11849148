--2、根据指定的机构业务线查询详细的产品以及合约
select be.entity_code ||' -- '|| be.entity_e_name,
       '直保/临分分入' as business_model,
       loa.loa_code,
       loa.loa_e_name,
       loa.evaluate_approach,
       (case when loa.coverage_unit is not null then loa.coverage_unit||' -- '||bvcc.code_e_name else null end)as coverage_unit,
       cp.product_code as buss_code,
       cp.product_e_name as buss_e_name
  from bpluser.bbs_conf_loa loa
  join bpluser.bbs_conf_loa_detail load on loa.loa_id = load.loa_id  
  join bpluser.bbs_conf_product cp on cp.entity_id = loa.entity_id and cp.product_id = load.business_id
  left join bpluser.bpl_v_conf_code bvcc on bvcc.code_code_idx = 'CoverageUnit/'||loa.coverage_unit
  join bpluser.bbs_entity be on be.entity_id = loa.entity_id and be.entity_code = '01'
 where upper(loa.business_model) || upper(loa.business_direction) = upper('DD')
union all
select be.entity_code ||' -- '|| be.entity_e_name,
       '临分分出' as business_model,
       loa.loa_code, 
       loa.loa_e_name,
       loa.evaluate_approach,
       (case when loa.coverage_unit is not null then loa.coverage_unit||' -- '||bvcc.code_e_name else null end)as coverage_unit,
       cp.product_code as buss_code,
       cp.product_e_name as buss_e_name
  from bpluser.bbs_conf_loa loa
  join bpluser.bbs_conf_loa_detail load on loa.loa_id = load.loa_id 
  join bpluser.bbs_conf_product cp on cp.entity_id = loa.entity_id and cp.product_id = load.business_id
  left join bpluser.bpl_v_conf_code bvcc on bvcc.code_code_idx = 'CoverageUnit/'||loa.coverage_unit
  join bpluser.bbs_entity be on be.entity_id = loa.entity_id and be.entity_code = '01'
  where upper(loa.business_model) || upper(loa.business_direction) = upper('DD')
union all
select be.entity_code ||' -- '|| be.entity_e_name, 
       '合约分入' as business_model, 
       loa.loa_code, 
       loa.loa_e_name, 
       loa.evaluate_approach,
       (case when loa.coverage_unit is not null then loa.coverage_unit||' -- '||bvcc.code_e_name else null end)as coverage_unit,
       cbt.base_treaty_no as buss_code,
       cbt.treaty_e_name as buss_e_name
  from bpluser.bbs_conf_loa loa
  join bpluser.bbs_conf_loa_detail load on loa.loa_id = load.loa_id 
  join bpluser.bbs_conf_base_treaty cbt on cbt.entity_id = loa.entity_id and cbt.conf_treaty_id = load.business_id
  left join bpluser.bpl_v_conf_code bvcc on bvcc.code_code_idx = 'CoverageUnit/'||loa.coverage_unit
  join bpluser.bbs_entity be on be.entity_id = loa.entity_id and be.entity_code = '01'
 where upper(loa.business_model) || upper(loa.business_direction) = upper('TI')
union all
select be.entity_code ||' -- '|| be.entity_e_name, 
       '合约分出' as business_model,        
       loa.loa_code, 
       loa.loa_e_name, 
       loa.evaluate_approach,
       (case when loa.coverage_unit is not null then loa.coverage_unit||' -- '||bvcc.code_e_name else null end)as coverage_unit,
       cbt.base_treaty_no as buss_code,
       cbt.treaty_e_name as buss_e_name
  from bpluser.bbs_conf_loa loa
  join bpluser.bbs_conf_loa_detail load on loa.loa_id = load.loa_id 
  join bpluser.bbs_conf_base_treaty cbt on cbt.entity_id = loa.entity_id and cbt.conf_treaty_id = load.business_id
  left join bpluser.bpl_v_conf_code bvcc on bvcc.code_code_idx = 'CoverageUnit/'||loa.coverage_unit
  join bpluser.bbs_entity be on be.entity_id = loa.entity_id and be.entity_code = '01'
 where upper(loa.business_model) || upper(loa.business_direction) = upper('TO')
 order by business_model, loa_code,buss_code;

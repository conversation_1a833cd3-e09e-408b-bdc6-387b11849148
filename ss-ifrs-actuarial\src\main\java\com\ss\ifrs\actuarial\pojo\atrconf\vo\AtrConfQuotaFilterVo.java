/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-09-16 14:50:26
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.ObjectUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-09-16 14:50:26<br/>
 * Description: 指标配置表<br/>
 * Table Name: ATR_CONF_QUOTA<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "指标配置表")
public class AtrConfQuotaFilterVo implements Serializable {
    /**
     * Database column: ATR_CONF_QUOTA.QUOTA_ID
     * Database remarks: quotaId|主键
     */
    @ApiModelProperty(value = "quotaId|主键", required = true)
    private Long quotaId;

    /**
     * Database column: ATR_CONF_QUOTA.CENTER_ID
     * Database remarks: entityId|业务单位
     */
    @ApiModelProperty(value = "entityId|业务单位", required = true)
    @NotNull(message = "TheBusiness Unit can't be null|业务单位不能为空|業務單位不能為空")
    private Long entityId;

    /**
     * Database column: ATR_CONF_QUOTA.MODEL_DEF_ID
     * Database remarks: model_def_id|计量模型
     */
    @ApiModelProperty(value = "model_def_id|计量模型", required = true)
    private String businessSourceCode;

    private String quotaClass;

    private String yearMonth;

    /**
     * Database column: ATR_CONF_QUOTA.QUOTA_DEF_ID
     * Database remarks: quotaDefId|指标定义
     */
    @ApiModelProperty(value = "quotaDefId|指标定义", required = true)
    @NotNull(message = "The quotaDefId can't be null|指标定义不能为空|指標定義不能為空")
    private Long quotaDefId;


    /**
     * Database column: ATR_CONF_QUOTA_NEW.dimension
     * Database remarks: dimension|假设颗粒度类型( 合同组合、合同组、保单)
     */
    @ApiModelProperty(value = "dimension|假设颗粒度类型( 合同组合、合同组、保单)", required = false)
    private String dimension;

    /**
     * Database column: ATR_CONF_QUOTA_NEW.dimension_value
     * Database remarks: dimension_value|假设颗粒度值
     */
    @ApiModelProperty(value = "dimension_value|假设颗粒度值", required = true)
    @ExcelProperty(value = "Dimension_Value")
    private String dimensionValue;

    private String riskClassCode;

    public AtrConfQuotaFilterVo(Long entityId, String yearMonth, String dimension, String dimensionValue, String riskClassCode) {
        this.entityId = entityId;
        this.yearMonth = yearMonth;
        this.dimension = dimension;
        this.dimensionValue = dimensionValue;
        this.riskClassCode = riskClassCode;
    }

    public AtrConfQuotaFilterVo(Long entityId, String yearMonth, String dimension, String dimensionValue) {
        this.entityId = entityId;
        this.yearMonth = yearMonth;
        this.dimension = dimension;
        this.dimensionValue = dimensionValue;
    }

    // 必须重写 equals 和 hashCode 方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AtrConfQuotaFilterVo nameAge = (AtrConfQuotaFilterVo) o;
        if (ObjectUtils.isNotEmpty(nameAge))
        {
            return nameAge.entityId == entityId && nameAge.yearMonth.equals(yearMonth) && nameAge.dimension.equals(dimension)
                    && nameAge.dimensionValue.equals(dimensionValue)
                    && ((ObjectUtils.isEmpty(nameAge.riskClassCode) && ObjectUtils.isEmpty(riskClassCode)) ||nameAge.riskClassCode.equals(riskClassCode));
        } else {
            return false;
        }
    }

    @Override
    public int hashCode() {
        return Objects.hash(entityId, yearMonth, dimension, dimensionValue, riskClassCode);
    }

    private static final long serialVersionUID = 1L;

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getQuotaClass() {
        return quotaClass;
    }

    public void setQuotaClass(String quotaClass) {
        this.quotaClass = quotaClass;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getDimensionValue() {
        return dimensionValue;
    }

    public void setDimensionValue(String dimensionValue) {
        this.dimensionValue = dimensionValue;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }
}
---校验异常插入0的任推送记录，插入的状态为成功状态
INSERT INTO ODS_DATA_PUSH_SIGNAL
    (DATA_PUSH_SIGNAL_ID,
     PUSH_MODEL,
     YEAR_MONTH,
     ROW_COUNT,
     PUSH_TIME,
     TASK_CODE,
     START_DEAL_TIME,
     TASK_STATUS,
     DEAL_MSG,
     END_DEAL_TIME)
SELECT
     ods_seq_data_push_signal.nextval as DATA_PUSH_SIGNAL_ID,--根据序列动态滚动
     DPS.PUSH_MODEL AS PUSH_MODEL,
     DPS.YEAR_MONTH AS YEAR_MONTH,
     0 AS ROW_COUNT,
     SYSTIMESTAMP AS PUSH_TIME,
     SUBSTR(DPS.TASK_CODE, 0, 13) || lpad((TO_NUMBER(SUBSTR(DPS.TASK_CODE, 14, 5)) + 1),5,'0') AS TASK_CODE,--往当前task_code加1个序号
     NULL AS START_DEAL_TIME,
     '3' AS TASK_STATUS,--0-待处理，1-校验中，2-失败，3-成功，4-无效
     NULL AS DEAL_MSG,
     NULL AS END_DEAL_TIME
FROM (SELECT T.YEAR_MONTH, T.PUSH_MODEL, MAX(TASK_CODE) AS TASK_CODE
          FROM ODS_DATA_PUSH_SIGNAL T
         WHERE T.TASK_STATUS = '2' --0-待处理，1-校验中，2-失败，3-成功，4-无效
		   --AND T.YEAR_MONTH = '202201' --需要变动的业务期间
           AND NOT EXISTS (SELECT 1
                  FROM ODS_DATA_PUSH_SIGNAL A
                 WHERE A.YEAR_MONTH = T.YEAR_MONTH
                   AND A.PUSH_MODEL = T.PUSH_MODEL
                   AND A.TASK_STATUS = '3' --前面已有成功的不会重复插入
                )
         GROUP BY T.YEAR_MONTH, T.PUSH_MODEL) DPS;

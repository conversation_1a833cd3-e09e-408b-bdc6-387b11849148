----------------------------------------------
----- 实收保费临时表 (直保&临分分入， 当期)
----------------------------------------------

-- table
drop table if exists atr_temp_dd_premium_paid_cur cascade;
create table atr_temp_dd_premium_paid_cur (
                                              policy_no varchar(60) collate "C" not null,
                                              endorse_seq_no varchar(3) collate "C" not null,
                                              premium numeric(32,8),
                                              commission numeric(32,8)
) partition by hash (policy_no);

create table atr_temp_dd_premium_paid_cur_p_0 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 0);
create table atr_temp_dd_premium_paid_cur_p_1 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 1);
create table atr_temp_dd_premium_paid_cur_p_2 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 2);
create table atr_temp_dd_premium_paid_cur_p_3 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 3);
create table atr_temp_dd_premium_paid_cur_p_4 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 4);
create table atr_temp_dd_premium_paid_cur_p_5 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 5);
create table atr_temp_dd_premium_paid_cur_p_6 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 6);
create table atr_temp_dd_premium_paid_cur_p_7 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 7);
create table atr_temp_dd_premium_paid_cur_p_8 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 8);
create table atr_temp_dd_premium_paid_cur_p_9 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 9);
create table atr_temp_dd_premium_paid_cur_p_10 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 10);
create table atr_temp_dd_premium_paid_cur_p_11 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 11);
create table atr_temp_dd_premium_paid_cur_p_12 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 12);
create table atr_temp_dd_premium_paid_cur_p_13 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 13);
create table atr_temp_dd_premium_paid_cur_p_14 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 14);
create table atr_temp_dd_premium_paid_cur_p_15 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 15);
create table atr_temp_dd_premium_paid_cur_p_16 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 16);
create table atr_temp_dd_premium_paid_cur_p_17 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 17);
create table atr_temp_dd_premium_paid_cur_p_18 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 18);
create table atr_temp_dd_premium_paid_cur_p_19 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 19);
create table atr_temp_dd_premium_paid_cur_p_20 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 20);
create table atr_temp_dd_premium_paid_cur_p_21 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 21);
create table atr_temp_dd_premium_paid_cur_p_22 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 22);
create table atr_temp_dd_premium_paid_cur_p_23 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 23);
create table atr_temp_dd_premium_paid_cur_p_24 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 24);
create table atr_temp_dd_premium_paid_cur_p_25 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 25);
create table atr_temp_dd_premium_paid_cur_p_26 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 26);
create table atr_temp_dd_premium_paid_cur_p_27 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 27);
create table atr_temp_dd_premium_paid_cur_p_28 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 28);
create table atr_temp_dd_premium_paid_cur_p_29 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 29);
create table atr_temp_dd_premium_paid_cur_p_30 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 30);
create table atr_temp_dd_premium_paid_cur_p_31 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 31);
create table atr_temp_dd_premium_paid_cur_p_32 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 32);
create table atr_temp_dd_premium_paid_cur_p_33 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 33);
create table atr_temp_dd_premium_paid_cur_p_34 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 34);
create table atr_temp_dd_premium_paid_cur_p_35 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 35);
create table atr_temp_dd_premium_paid_cur_p_36 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 36);
create table atr_temp_dd_premium_paid_cur_p_37 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 37);
create table atr_temp_dd_premium_paid_cur_p_38 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 38);
create table atr_temp_dd_premium_paid_cur_p_39 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 39);
create table atr_temp_dd_premium_paid_cur_p_40 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 40);
create table atr_temp_dd_premium_paid_cur_p_41 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 41);
create table atr_temp_dd_premium_paid_cur_p_42 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 42);
create table atr_temp_dd_premium_paid_cur_p_43 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 43);
create table atr_temp_dd_premium_paid_cur_p_44 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 44);
create table atr_temp_dd_premium_paid_cur_p_45 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 45);
create table atr_temp_dd_premium_paid_cur_p_46 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 46);
create table atr_temp_dd_premium_paid_cur_p_47 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 47);
create table atr_temp_dd_premium_paid_cur_p_48 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 48);
create table atr_temp_dd_premium_paid_cur_p_49 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 49);
create table atr_temp_dd_premium_paid_cur_p_50 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 50);
create table atr_temp_dd_premium_paid_cur_p_51 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 51);
create table atr_temp_dd_premium_paid_cur_p_52 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 52);
create table atr_temp_dd_premium_paid_cur_p_53 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 53);
create table atr_temp_dd_premium_paid_cur_p_54 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 54);
create table atr_temp_dd_premium_paid_cur_p_55 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 55);
create table atr_temp_dd_premium_paid_cur_p_56 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 56);
create table atr_temp_dd_premium_paid_cur_p_57 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 57);
create table atr_temp_dd_premium_paid_cur_p_58 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 58);
create table atr_temp_dd_premium_paid_cur_p_59 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 59);
create table atr_temp_dd_premium_paid_cur_p_60 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 60);
create table atr_temp_dd_premium_paid_cur_p_61 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 61);
create table atr_temp_dd_premium_paid_cur_p_62 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 62);
create table atr_temp_dd_premium_paid_cur_p_63 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 63);
create table atr_temp_dd_premium_paid_cur_p_64 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 64);
create table atr_temp_dd_premium_paid_cur_p_65 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 65);
create table atr_temp_dd_premium_paid_cur_p_66 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 66);
create table atr_temp_dd_premium_paid_cur_p_67 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 67);
create table atr_temp_dd_premium_paid_cur_p_68 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 68);
create table atr_temp_dd_premium_paid_cur_p_69 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 69);
create table atr_temp_dd_premium_paid_cur_p_70 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 70);
create table atr_temp_dd_premium_paid_cur_p_71 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 71);
create table atr_temp_dd_premium_paid_cur_p_72 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 72);
create table atr_temp_dd_premium_paid_cur_p_73 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 73);
create table atr_temp_dd_premium_paid_cur_p_74 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 74);
create table atr_temp_dd_premium_paid_cur_p_75 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 75);
create table atr_temp_dd_premium_paid_cur_p_76 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 76);
create table atr_temp_dd_premium_paid_cur_p_77 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 77);
create table atr_temp_dd_premium_paid_cur_p_78 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 78);
create table atr_temp_dd_premium_paid_cur_p_79 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 79);
create table atr_temp_dd_premium_paid_cur_p_80 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 80);
create table atr_temp_dd_premium_paid_cur_p_81 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 81);
create table atr_temp_dd_premium_paid_cur_p_82 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 82);
create table atr_temp_dd_premium_paid_cur_p_83 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 83);
create table atr_temp_dd_premium_paid_cur_p_84 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 84);
create table atr_temp_dd_premium_paid_cur_p_85 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 85);
create table atr_temp_dd_premium_paid_cur_p_86 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 86);
create table atr_temp_dd_premium_paid_cur_p_87 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 87);
create table atr_temp_dd_premium_paid_cur_p_88 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 88);
create table atr_temp_dd_premium_paid_cur_p_89 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 89);
create table atr_temp_dd_premium_paid_cur_p_90 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 90);
create table atr_temp_dd_premium_paid_cur_p_91 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 91);
create table atr_temp_dd_premium_paid_cur_p_92 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 92);
create table atr_temp_dd_premium_paid_cur_p_93 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 93);
create table atr_temp_dd_premium_paid_cur_p_94 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 94);
create table atr_temp_dd_premium_paid_cur_p_95 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 95);
create table atr_temp_dd_premium_paid_cur_p_96 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 96);
create table atr_temp_dd_premium_paid_cur_p_97 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 97);
create table atr_temp_dd_premium_paid_cur_p_98 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 98);
create table atr_temp_dd_premium_paid_cur_p_99 partition of atr_temp_dd_premium_paid_cur
    for values with (modulus 100, remainder 99);



create index idx_atr_temp_dd_premium_paid_cur_business on atr_temp_dd_premium_paid_cur(policy_no, endorse_seq_no);


comment on table atr_temp_dd_premium_paid_cur
  is '实收保费临时表 (直保&临分分入， 当期)';

comment on column atr_temp_dd_premium_paid_cur.policy_no
  is '保单号';
comment on column atr_temp_dd_premium_paid_cur.endorse_seq_no
  is '批单序号';
comment on column atr_temp_dd_premium_paid_cur.premium
  is '保费';
comment on column atr_temp_dd_premium_paid_cur.commission
  is '佣金';




----------------------------------------------
----- 实收保费临时表 (直保&临分分入， 小于当期)
----------------------------------------------

-- table
drop table if exists atr_temp_dd_premium_paid_lt cascade;
create table atr_temp_dd_premium_paid_lt (
                                             policy_no varchar(60) collate "C" not null,
                                             endorse_seq_no varchar(3) collate "C" not null,
                                             premium numeric(32,8),
                                             commission numeric(32,8)
) partition by hash (policy_no);

create table atr_temp_dd_premium_paid_lt_p_0 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 0);
create table atr_temp_dd_premium_paid_lt_p_1 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 1);
create table atr_temp_dd_premium_paid_lt_p_2 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 2);
create table atr_temp_dd_premium_paid_lt_p_3 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 3);
create table atr_temp_dd_premium_paid_lt_p_4 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 4);
create table atr_temp_dd_premium_paid_lt_p_5 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 5);
create table atr_temp_dd_premium_paid_lt_p_6 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 6);
create table atr_temp_dd_premium_paid_lt_p_7 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 7);
create table atr_temp_dd_premium_paid_lt_p_8 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 8);
create table atr_temp_dd_premium_paid_lt_p_9 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 9);
create table atr_temp_dd_premium_paid_lt_p_10 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 10);
create table atr_temp_dd_premium_paid_lt_p_11 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 11);
create table atr_temp_dd_premium_paid_lt_p_12 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 12);
create table atr_temp_dd_premium_paid_lt_p_13 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 13);
create table atr_temp_dd_premium_paid_lt_p_14 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 14);
create table atr_temp_dd_premium_paid_lt_p_15 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 15);
create table atr_temp_dd_premium_paid_lt_p_16 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 16);
create table atr_temp_dd_premium_paid_lt_p_17 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 17);
create table atr_temp_dd_premium_paid_lt_p_18 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 18);
create table atr_temp_dd_premium_paid_lt_p_19 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 19);
create table atr_temp_dd_premium_paid_lt_p_20 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 20);
create table atr_temp_dd_premium_paid_lt_p_21 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 21);
create table atr_temp_dd_premium_paid_lt_p_22 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 22);
create table atr_temp_dd_premium_paid_lt_p_23 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 23);
create table atr_temp_dd_premium_paid_lt_p_24 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 24);
create table atr_temp_dd_premium_paid_lt_p_25 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 25);
create table atr_temp_dd_premium_paid_lt_p_26 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 26);
create table atr_temp_dd_premium_paid_lt_p_27 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 27);
create table atr_temp_dd_premium_paid_lt_p_28 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 28);
create table atr_temp_dd_premium_paid_lt_p_29 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 29);
create table atr_temp_dd_premium_paid_lt_p_30 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 30);
create table atr_temp_dd_premium_paid_lt_p_31 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 31);
create table atr_temp_dd_premium_paid_lt_p_32 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 32);
create table atr_temp_dd_premium_paid_lt_p_33 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 33);
create table atr_temp_dd_premium_paid_lt_p_34 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 34);
create table atr_temp_dd_premium_paid_lt_p_35 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 35);
create table atr_temp_dd_premium_paid_lt_p_36 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 36);
create table atr_temp_dd_premium_paid_lt_p_37 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 37);
create table atr_temp_dd_premium_paid_lt_p_38 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 38);
create table atr_temp_dd_premium_paid_lt_p_39 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 39);
create table atr_temp_dd_premium_paid_lt_p_40 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 40);
create table atr_temp_dd_premium_paid_lt_p_41 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 41);
create table atr_temp_dd_premium_paid_lt_p_42 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 42);
create table atr_temp_dd_premium_paid_lt_p_43 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 43);
create table atr_temp_dd_premium_paid_lt_p_44 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 44);
create table atr_temp_dd_premium_paid_lt_p_45 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 45);
create table atr_temp_dd_premium_paid_lt_p_46 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 46);
create table atr_temp_dd_premium_paid_lt_p_47 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 47);
create table atr_temp_dd_premium_paid_lt_p_48 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 48);
create table atr_temp_dd_premium_paid_lt_p_49 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 49);
create table atr_temp_dd_premium_paid_lt_p_50 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 50);
create table atr_temp_dd_premium_paid_lt_p_51 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 51);
create table atr_temp_dd_premium_paid_lt_p_52 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 52);
create table atr_temp_dd_premium_paid_lt_p_53 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 53);
create table atr_temp_dd_premium_paid_lt_p_54 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 54);
create table atr_temp_dd_premium_paid_lt_p_55 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 55);
create table atr_temp_dd_premium_paid_lt_p_56 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 56);
create table atr_temp_dd_premium_paid_lt_p_57 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 57);
create table atr_temp_dd_premium_paid_lt_p_58 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 58);
create table atr_temp_dd_premium_paid_lt_p_59 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 59);
create table atr_temp_dd_premium_paid_lt_p_60 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 60);
create table atr_temp_dd_premium_paid_lt_p_61 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 61);
create table atr_temp_dd_premium_paid_lt_p_62 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 62);
create table atr_temp_dd_premium_paid_lt_p_63 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 63);
create table atr_temp_dd_premium_paid_lt_p_64 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 64);
create table atr_temp_dd_premium_paid_lt_p_65 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 65);
create table atr_temp_dd_premium_paid_lt_p_66 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 66);
create table atr_temp_dd_premium_paid_lt_p_67 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 67);
create table atr_temp_dd_premium_paid_lt_p_68 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 68);
create table atr_temp_dd_premium_paid_lt_p_69 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 69);
create table atr_temp_dd_premium_paid_lt_p_70 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 70);
create table atr_temp_dd_premium_paid_lt_p_71 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 71);
create table atr_temp_dd_premium_paid_lt_p_72 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 72);
create table atr_temp_dd_premium_paid_lt_p_73 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 73);
create table atr_temp_dd_premium_paid_lt_p_74 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 74);
create table atr_temp_dd_premium_paid_lt_p_75 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 75);
create table atr_temp_dd_premium_paid_lt_p_76 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 76);
create table atr_temp_dd_premium_paid_lt_p_77 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 77);
create table atr_temp_dd_premium_paid_lt_p_78 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 78);
create table atr_temp_dd_premium_paid_lt_p_79 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 79);
create table atr_temp_dd_premium_paid_lt_p_80 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 80);
create table atr_temp_dd_premium_paid_lt_p_81 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 81);
create table atr_temp_dd_premium_paid_lt_p_82 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 82);
create table atr_temp_dd_premium_paid_lt_p_83 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 83);
create table atr_temp_dd_premium_paid_lt_p_84 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 84);
create table atr_temp_dd_premium_paid_lt_p_85 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 85);
create table atr_temp_dd_premium_paid_lt_p_86 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 86);
create table atr_temp_dd_premium_paid_lt_p_87 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 87);
create table atr_temp_dd_premium_paid_lt_p_88 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 88);
create table atr_temp_dd_premium_paid_lt_p_89 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 89);
create table atr_temp_dd_premium_paid_lt_p_90 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 90);
create table atr_temp_dd_premium_paid_lt_p_91 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 91);
create table atr_temp_dd_premium_paid_lt_p_92 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 92);
create table atr_temp_dd_premium_paid_lt_p_93 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 93);
create table atr_temp_dd_premium_paid_lt_p_94 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 94);
create table atr_temp_dd_premium_paid_lt_p_95 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 95);
create table atr_temp_dd_premium_paid_lt_p_96 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 96);
create table atr_temp_dd_premium_paid_lt_p_97 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 97);
create table atr_temp_dd_premium_paid_lt_p_98 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 98);
create table atr_temp_dd_premium_paid_lt_p_99 partition of atr_temp_dd_premium_paid_lt
    for values with (modulus 100, remainder 99);



create index idx_atr_temp_dd_premium_paid_lt_business on atr_temp_dd_premium_paid_lt(policy_no, endorse_seq_no);


comment on table atr_temp_dd_premium_paid_lt
  is '实收保费临时表 (直保&临分分入， 小于当期)';

comment on column atr_temp_dd_premium_paid_lt.policy_no
  is '保单号';
comment on column atr_temp_dd_premium_paid_lt.endorse_seq_no
  is '批单序号';
comment on column atr_temp_dd_premium_paid_lt.premium
  is '保费';
comment on column atr_temp_dd_premium_paid_lt.commission
  is '佣金';




----------------------------------------------
----- 实收保费临时表 (直保&临分分入， 小于或等于当期)
----------------------------------------------

-- table
drop table if exists atr_temp_dd_premium_paid_lte cascade;
create table atr_temp_dd_premium_paid_lte (
                                              policy_no varchar(60) collate "C" not null,
                                              endorse_seq_no varchar(3) collate "C" not null,
                                              premium numeric(32,8),
                                              commission numeric(32,8)
) partition by hash (policy_no);

create table atr_temp_dd_premium_paid_lte_p_0 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 0);
create table atr_temp_dd_premium_paid_lte_p_1 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 1);
create table atr_temp_dd_premium_paid_lte_p_2 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 2);
create table atr_temp_dd_premium_paid_lte_p_3 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 3);
create table atr_temp_dd_premium_paid_lte_p_4 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 4);
create table atr_temp_dd_premium_paid_lte_p_5 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 5);
create table atr_temp_dd_premium_paid_lte_p_6 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 6);
create table atr_temp_dd_premium_paid_lte_p_7 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 7);
create table atr_temp_dd_premium_paid_lte_p_8 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 8);
create table atr_temp_dd_premium_paid_lte_p_9 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 9);
create table atr_temp_dd_premium_paid_lte_p_10 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 10);
create table atr_temp_dd_premium_paid_lte_p_11 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 11);
create table atr_temp_dd_premium_paid_lte_p_12 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 12);
create table atr_temp_dd_premium_paid_lte_p_13 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 13);
create table atr_temp_dd_premium_paid_lte_p_14 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 14);
create table atr_temp_dd_premium_paid_lte_p_15 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 15);
create table atr_temp_dd_premium_paid_lte_p_16 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 16);
create table atr_temp_dd_premium_paid_lte_p_17 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 17);
create table atr_temp_dd_premium_paid_lte_p_18 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 18);
create table atr_temp_dd_premium_paid_lte_p_19 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 19);
create table atr_temp_dd_premium_paid_lte_p_20 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 20);
create table atr_temp_dd_premium_paid_lte_p_21 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 21);
create table atr_temp_dd_premium_paid_lte_p_22 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 22);
create table atr_temp_dd_premium_paid_lte_p_23 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 23);
create table atr_temp_dd_premium_paid_lte_p_24 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 24);
create table atr_temp_dd_premium_paid_lte_p_25 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 25);
create table atr_temp_dd_premium_paid_lte_p_26 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 26);
create table atr_temp_dd_premium_paid_lte_p_27 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 27);
create table atr_temp_dd_premium_paid_lte_p_28 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 28);
create table atr_temp_dd_premium_paid_lte_p_29 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 29);
create table atr_temp_dd_premium_paid_lte_p_30 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 30);
create table atr_temp_dd_premium_paid_lte_p_31 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 31);
create table atr_temp_dd_premium_paid_lte_p_32 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 32);
create table atr_temp_dd_premium_paid_lte_p_33 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 33);
create table atr_temp_dd_premium_paid_lte_p_34 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 34);
create table atr_temp_dd_premium_paid_lte_p_35 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 35);
create table atr_temp_dd_premium_paid_lte_p_36 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 36);
create table atr_temp_dd_premium_paid_lte_p_37 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 37);
create table atr_temp_dd_premium_paid_lte_p_38 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 38);
create table atr_temp_dd_premium_paid_lte_p_39 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 39);
create table atr_temp_dd_premium_paid_lte_p_40 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 40);
create table atr_temp_dd_premium_paid_lte_p_41 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 41);
create table atr_temp_dd_premium_paid_lte_p_42 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 42);
create table atr_temp_dd_premium_paid_lte_p_43 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 43);
create table atr_temp_dd_premium_paid_lte_p_44 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 44);
create table atr_temp_dd_premium_paid_lte_p_45 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 45);
create table atr_temp_dd_premium_paid_lte_p_46 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 46);
create table atr_temp_dd_premium_paid_lte_p_47 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 47);
create table atr_temp_dd_premium_paid_lte_p_48 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 48);
create table atr_temp_dd_premium_paid_lte_p_49 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 49);
create table atr_temp_dd_premium_paid_lte_p_50 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 50);
create table atr_temp_dd_premium_paid_lte_p_51 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 51);
create table atr_temp_dd_premium_paid_lte_p_52 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 52);
create table atr_temp_dd_premium_paid_lte_p_53 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 53);
create table atr_temp_dd_premium_paid_lte_p_54 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 54);
create table atr_temp_dd_premium_paid_lte_p_55 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 55);
create table atr_temp_dd_premium_paid_lte_p_56 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 56);
create table atr_temp_dd_premium_paid_lte_p_57 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 57);
create table atr_temp_dd_premium_paid_lte_p_58 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 58);
create table atr_temp_dd_premium_paid_lte_p_59 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 59);
create table atr_temp_dd_premium_paid_lte_p_60 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 60);
create table atr_temp_dd_premium_paid_lte_p_61 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 61);
create table atr_temp_dd_premium_paid_lte_p_62 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 62);
create table atr_temp_dd_premium_paid_lte_p_63 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 63);
create table atr_temp_dd_premium_paid_lte_p_64 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 64);
create table atr_temp_dd_premium_paid_lte_p_65 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 65);
create table atr_temp_dd_premium_paid_lte_p_66 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 66);
create table atr_temp_dd_premium_paid_lte_p_67 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 67);
create table atr_temp_dd_premium_paid_lte_p_68 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 68);
create table atr_temp_dd_premium_paid_lte_p_69 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 69);
create table atr_temp_dd_premium_paid_lte_p_70 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 70);
create table atr_temp_dd_premium_paid_lte_p_71 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 71);
create table atr_temp_dd_premium_paid_lte_p_72 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 72);
create table atr_temp_dd_premium_paid_lte_p_73 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 73);
create table atr_temp_dd_premium_paid_lte_p_74 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 74);
create table atr_temp_dd_premium_paid_lte_p_75 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 75);
create table atr_temp_dd_premium_paid_lte_p_76 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 76);
create table atr_temp_dd_premium_paid_lte_p_77 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 77);
create table atr_temp_dd_premium_paid_lte_p_78 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 78);
create table atr_temp_dd_premium_paid_lte_p_79 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 79);
create table atr_temp_dd_premium_paid_lte_p_80 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 80);
create table atr_temp_dd_premium_paid_lte_p_81 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 81);
create table atr_temp_dd_premium_paid_lte_p_82 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 82);
create table atr_temp_dd_premium_paid_lte_p_83 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 83);
create table atr_temp_dd_premium_paid_lte_p_84 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 84);
create table atr_temp_dd_premium_paid_lte_p_85 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 85);
create table atr_temp_dd_premium_paid_lte_p_86 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 86);
create table atr_temp_dd_premium_paid_lte_p_87 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 87);
create table atr_temp_dd_premium_paid_lte_p_88 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 88);
create table atr_temp_dd_premium_paid_lte_p_89 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 89);
create table atr_temp_dd_premium_paid_lte_p_90 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 90);
create table atr_temp_dd_premium_paid_lte_p_91 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 91);
create table atr_temp_dd_premium_paid_lte_p_92 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 92);
create table atr_temp_dd_premium_paid_lte_p_93 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 93);
create table atr_temp_dd_premium_paid_lte_p_94 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 94);
create table atr_temp_dd_premium_paid_lte_p_95 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 95);
create table atr_temp_dd_premium_paid_lte_p_96 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 96);
create table atr_temp_dd_premium_paid_lte_p_97 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 97);
create table atr_temp_dd_premium_paid_lte_p_98 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 98);
create table atr_temp_dd_premium_paid_lte_p_99 partition of atr_temp_dd_premium_paid_lte
    for values with (modulus 100, remainder 99);



create index idx_atr_temp_dd_premium_paid_lte_business on atr_temp_dd_premium_paid_lte(policy_no, endorse_seq_no);


comment on table atr_temp_dd_premium_paid_lte
  is '实收保费临时表 (直保&临分分入， 小于或等于当期)';

comment on column atr_temp_dd_premium_paid_lte.policy_no
  is '保单号';
comment on column atr_temp_dd_premium_paid_lte.endorse_seq_no
  is '批单序号';
comment on column atr_temp_dd_premium_paid_lte.premium
  is '保费';
comment on column atr_temp_dd_premium_paid_lte.commission
  is '佣金';




----------------------------------------------
----- 缴费计划临时表 (直保&临分分入)
----------------------------------------------

-- table
drop table if exists atr_temp_dd_payment_plan cascade;
create table atr_temp_dd_payment_plan (
                                          policy_no varchar(60) collate "C" not null,
                                          endorse_seq_no varchar(3) collate "C" not null,
                                          est_payment_date date not null
) partition by hash (policy_no);

create table atr_temp_dd_payment_plan_p_0 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 0);
create table atr_temp_dd_payment_plan_p_1 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 1);
create table atr_temp_dd_payment_plan_p_2 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 2);
create table atr_temp_dd_payment_plan_p_3 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 3);
create table atr_temp_dd_payment_plan_p_4 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 4);
create table atr_temp_dd_payment_plan_p_5 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 5);
create table atr_temp_dd_payment_plan_p_6 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 6);
create table atr_temp_dd_payment_plan_p_7 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 7);
create table atr_temp_dd_payment_plan_p_8 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 8);
create table atr_temp_dd_payment_plan_p_9 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 9);
create table atr_temp_dd_payment_plan_p_10 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 10);
create table atr_temp_dd_payment_plan_p_11 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 11);
create table atr_temp_dd_payment_plan_p_12 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 12);
create table atr_temp_dd_payment_plan_p_13 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 13);
create table atr_temp_dd_payment_plan_p_14 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 14);
create table atr_temp_dd_payment_plan_p_15 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 15);
create table atr_temp_dd_payment_plan_p_16 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 16);
create table atr_temp_dd_payment_plan_p_17 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 17);
create table atr_temp_dd_payment_plan_p_18 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 18);
create table atr_temp_dd_payment_plan_p_19 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 19);
create table atr_temp_dd_payment_plan_p_20 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 20);
create table atr_temp_dd_payment_plan_p_21 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 21);
create table atr_temp_dd_payment_plan_p_22 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 22);
create table atr_temp_dd_payment_plan_p_23 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 23);
create table atr_temp_dd_payment_plan_p_24 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 24);
create table atr_temp_dd_payment_plan_p_25 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 25);
create table atr_temp_dd_payment_plan_p_26 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 26);
create table atr_temp_dd_payment_plan_p_27 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 27);
create table atr_temp_dd_payment_plan_p_28 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 28);
create table atr_temp_dd_payment_plan_p_29 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 29);
create table atr_temp_dd_payment_plan_p_30 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 30);
create table atr_temp_dd_payment_plan_p_31 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 31);
create table atr_temp_dd_payment_plan_p_32 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 32);
create table atr_temp_dd_payment_plan_p_33 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 33);
create table atr_temp_dd_payment_plan_p_34 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 34);
create table atr_temp_dd_payment_plan_p_35 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 35);
create table atr_temp_dd_payment_plan_p_36 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 36);
create table atr_temp_dd_payment_plan_p_37 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 37);
create table atr_temp_dd_payment_plan_p_38 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 38);
create table atr_temp_dd_payment_plan_p_39 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 39);
create table atr_temp_dd_payment_plan_p_40 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 40);
create table atr_temp_dd_payment_plan_p_41 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 41);
create table atr_temp_dd_payment_plan_p_42 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 42);
create table atr_temp_dd_payment_plan_p_43 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 43);
create table atr_temp_dd_payment_plan_p_44 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 44);
create table atr_temp_dd_payment_plan_p_45 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 45);
create table atr_temp_dd_payment_plan_p_46 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 46);
create table atr_temp_dd_payment_plan_p_47 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 47);
create table atr_temp_dd_payment_plan_p_48 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 48);
create table atr_temp_dd_payment_plan_p_49 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 49);
create table atr_temp_dd_payment_plan_p_50 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 50);
create table atr_temp_dd_payment_plan_p_51 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 51);
create table atr_temp_dd_payment_plan_p_52 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 52);
create table atr_temp_dd_payment_plan_p_53 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 53);
create table atr_temp_dd_payment_plan_p_54 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 54);
create table atr_temp_dd_payment_plan_p_55 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 55);
create table atr_temp_dd_payment_plan_p_56 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 56);
create table atr_temp_dd_payment_plan_p_57 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 57);
create table atr_temp_dd_payment_plan_p_58 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 58);
create table atr_temp_dd_payment_plan_p_59 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 59);
create table atr_temp_dd_payment_plan_p_60 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 60);
create table atr_temp_dd_payment_plan_p_61 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 61);
create table atr_temp_dd_payment_plan_p_62 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 62);
create table atr_temp_dd_payment_plan_p_63 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 63);
create table atr_temp_dd_payment_plan_p_64 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 64);
create table atr_temp_dd_payment_plan_p_65 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 65);
create table atr_temp_dd_payment_plan_p_66 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 66);
create table atr_temp_dd_payment_plan_p_67 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 67);
create table atr_temp_dd_payment_plan_p_68 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 68);
create table atr_temp_dd_payment_plan_p_69 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 69);
create table atr_temp_dd_payment_plan_p_70 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 70);
create table atr_temp_dd_payment_plan_p_71 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 71);
create table atr_temp_dd_payment_plan_p_72 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 72);
create table atr_temp_dd_payment_plan_p_73 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 73);
create table atr_temp_dd_payment_plan_p_74 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 74);
create table atr_temp_dd_payment_plan_p_75 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 75);
create table atr_temp_dd_payment_plan_p_76 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 76);
create table atr_temp_dd_payment_plan_p_77 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 77);
create table atr_temp_dd_payment_plan_p_78 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 78);
create table atr_temp_dd_payment_plan_p_79 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 79);
create table atr_temp_dd_payment_plan_p_80 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 80);
create table atr_temp_dd_payment_plan_p_81 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 81);
create table atr_temp_dd_payment_plan_p_82 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 82);
create table atr_temp_dd_payment_plan_p_83 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 83);
create table atr_temp_dd_payment_plan_p_84 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 84);
create table atr_temp_dd_payment_plan_p_85 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 85);
create table atr_temp_dd_payment_plan_p_86 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 86);
create table atr_temp_dd_payment_plan_p_87 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 87);
create table atr_temp_dd_payment_plan_p_88 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 88);
create table atr_temp_dd_payment_plan_p_89 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 89);
create table atr_temp_dd_payment_plan_p_90 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 90);
create table atr_temp_dd_payment_plan_p_91 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 91);
create table atr_temp_dd_payment_plan_p_92 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 92);
create table atr_temp_dd_payment_plan_p_93 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 93);
create table atr_temp_dd_payment_plan_p_94 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 94);
create table atr_temp_dd_payment_plan_p_95 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 95);
create table atr_temp_dd_payment_plan_p_96 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 96);
create table atr_temp_dd_payment_plan_p_97 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 97);
create table atr_temp_dd_payment_plan_p_98 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 98);
create table atr_temp_dd_payment_plan_p_99 partition of atr_temp_dd_payment_plan
    for values with (modulus 100, remainder 99);



create index idx_atr_temp_dd_payment_plan_business on atr_temp_dd_payment_plan(policy_no, endorse_seq_no);


comment on table atr_temp_dd_payment_plan
  is '缴费计划临时表 (直保&临分分入)';

comment on column atr_temp_dd_payment_plan.policy_no
  is '保单号';
comment on column atr_temp_dd_payment_plan.endorse_seq_no
  is '批单序号';
comment on column atr_temp_dd_payment_plan.est_payment_date
  is '应缴日期';



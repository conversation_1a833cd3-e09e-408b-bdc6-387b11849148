/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-20 10:16:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-20 10:16:00<br/>
 * Description: 假设值事故年月明细表轨迹 （基于事故年月）<br/>
 * Table Name: ATR_CONF_QUOTA_DYM_DETAILHIS<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "假设值事故年月明细表轨迹 （基于事故年月）")
public class AtrConfQuotaDymDetailHis implements Serializable {
    /**
     * Database column: ATR_CONF_QUOTA_DYM_DETAILHIS.QUOTA_DETAIL_HIS_ID
     * Database remarks: null
     */
    private Long quotaDetailHisId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DETAILHIS.QUOTA_DETAIL_ID
     * Database remarks: 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private Long quotaDetailId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DETAILHIS.QUOTA_ID
     * Database remarks: 主表ID
     */
    @ApiModelProperty(value = "主表ID", required = false)
    private Long quotaId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DETAILHIS.DAMAGE_YEAR_MONTH
     * Database remarks: 事故年月
     */
    @ApiModelProperty(value = "事故年月", required = false)
    private String accidentYearMonth;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DETAILHIS.QUOTA_VALUE
     * Database remarks: 指标值
     */
    @ApiModelProperty(value = "指标值", required = false)
    private String quotaValue;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DETAILHIS.CREATOR_ID
     * Database remarks: 创建人
     */
    @ApiModelProperty(value = "创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DETAILHIS.CREATE_TIME
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DETAILHIS.UPDATOR_ID
     * Database remarks: 最后修改人
     */
    @ApiModelProperty(value = "最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DETAILHIS.UPDATE_TIME
     * Database remarks: 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间", required = false)
    private Date updateTime;

    /**
     * Database column: ATR_CONF_QUOTA_DYM_DETAILHIS.SERIAL_NO
     * Database remarks: 版本号与主表一致
     */
    @ApiModelProperty(value = "版本号与主表一致", required = false)
    private Integer serialNo;

    private static final long serialVersionUID = 1L;

    public Long getQuotaDetailHisId() {
        return quotaDetailHisId;
    }

    public void setQuotaDetailHisId(Long quotaDetailHisId) {
        this.quotaDetailHisId = quotaDetailHisId;
    }

    public Long getQuotaDetailId() {
        return quotaDetailId;
    }

    public void setQuotaDetailId(Long quotaDetailId) {
        this.quotaDetailId = quotaDetailId;
    }

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public String getAccidentYearMonth() {
        return accidentYearMonth;
    }

    public void setAccidentYearMonth(String accidentYearMonth) {
        this.accidentYearMonth = accidentYearMonth;
    }

    public String getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(String quotaValue) {
        this.quotaValue = quotaValue;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }
}
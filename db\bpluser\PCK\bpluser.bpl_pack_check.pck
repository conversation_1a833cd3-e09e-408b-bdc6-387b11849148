create or replace package bpl_pack_check is
 
  FUNCTION FUNC_ANALYTICAL_RELYON(p_entity_id  IN NUMBER,
                                  P_<PERSON><PERSON><PERSON>CODE  IN VARCHAR2,
                                  P_YEARMONTH IN VARCHAR2,
                                  P_<PERSON><PERSON><PERSON>ON    IN VARCHAR2,
                                  <PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>  IN VARCHAR2) RETURN VARCHAR2;
                                  
  FUNCTION FUNC_CHECKRULESQLISEXECUTE(P_RELYON   IN VARCHAR2,
                                      P_<PERSON><PERSON><PERSON><PERSON><PERSON> IN VARCHAR2) RETURN NUMBER;
                                      
  PROCEDURE PROC_CHECKRULES(P_TASKCODE   VARCHAR,
                            P_ENTITY_ID   NUMERIC,
                            P_B<PERSON><PERSON>CODE   VARCHAR,
                            P_YEARMONTH  VARCHAR,
                            P_PROCID     NUMERIC,
                            P_CREATORID  NUMERIC);

 
end bpl_pack_check;
/
create or replace package body bpl_pack_check is


  /***********************************************************************
   NAME : bpl_PROC_ANALYTICAL_RELYON
   DESCRIPTION : 脚本附加条件解析函数
   DATE :2021-01-12
   AUTHOR :YINXH
   -------
   MODIFY LOG
   UPDATE DATE :
   UPDATE BY   :
   UPDATE DESC :
  ***********************************************************************/
  FUNCTION func_analytical_relyon(p_entity_id IN NUMBER, p_bookcode IN VARCHAR2,p_yearmonth IN VARCHAR2,
                                  p_relyon IN VARCHAR2, p_ruleexpr IN VARCHAR2) RETURN VARCHAR2 IS

    v_execute_sql   VARCHAR2(4000); --脚本解析结果
    v_centeridflag  VARCHAR2(1);    --附件条件勾选标记1：业务单位
    v_bookcodeflag  VARCHAR2(1);    --附件条件勾选标记2：账套
    v_yearmonthflag VARCHAR2(1);    --附件条件勾选标记3：会计期间
  BEGIN
    BEGIN
      -- 判断脚本是否包含WHERE条件语句，若无则添加 WHERE 1 = 1
      IF INSTR(upper(p_ruleexpr), upper('where')) > 0 THEN
        v_execute_sql := p_ruleexpr || ' ';
      ELSE
        v_execute_sql := p_ruleexpr || ' where 1 = 1 ';
      END IF;

      -- RELY_ON:脚本附件条件，示例：'101'，分别代表已勾选核算单位，未勾选账套，已勾选会计期间
      IF p_relyon IS NOT NULL
         AND length(COALESCE(p_relyon, '0')) = 3 THEN

        -- 解析附加条件
        v_centeridflag  := substr(p_relyon, 1, 1);
        v_bookcodeflag  := substr(p_relyon, 2, 1);
        v_yearmonthflag := substr(p_relyon, 3, 1);

        -- 拼接附加条件1，如果包含{entity_id}，就做替换，否则就做追加
        IF v_centeridflag = '1' THEN
          IF INSTR(upper(p_ruleexpr), upper('{entity_id}')) > 0 THEN
            v_execute_sql := REPLACE(upper(v_execute_sql), upper('{entity_id}'), p_entity_id||'');
          ELSE
            v_execute_sql := v_execute_sql || ' and entity_id = ' || p_entity_id;
          END IF;
        END IF;

        -- 拼接附加条件3
        IF v_yearmonthflag = '1' THEN
          IF INSTR(upper(p_ruleexpr), upper('{year_month}')) > 0 THEN
            v_execute_sql := REPLACE(upper(v_execute_sql), upper('{year_month}'), '''' || p_yearmonth || '''');
          ELSE
            v_execute_sql := v_execute_sql || ' and year_month = ''' || p_yearmonth || '''';
          END IF;
        END IF;

        -- 拼接附加条件2
        IF v_bookcodeflag = '1' THEN
          IF INSTR(upper(p_ruleexpr), upper('{book_code}')) > 0 THEN
            v_execute_sql := REPLACE(upper(v_execute_sql), upper('{book_code}'), '''' || p_bookcode || '''');
          ELSE
            v_execute_sql := v_execute_sql || ' and book_code = ''' || p_bookcode || '''';
          END IF;
        END IF;

      ELSE
        v_execute_sql := p_ruleexpr;

      END IF;

    EXCEPTION
      WHEN OTHERS THEN
        --抛出异常提示信息
        dbms_output.put_line('**SQLSTATE: '||to_char(SQLCODE)||'; **SQLERRM: '||SUBSTR(SQLERRM, 1, 200)||',【脚本解析失败，请检查脚本Sql：'||v_execute_sql||'，附件条件：'||p_relyon||'】');

    END;
    --返回脚本结果
    RETURN v_execute_sql;
    -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息
  END func_analytical_relyon;

  /***********************************************************************
    NAME : bpl_PROC_CHECKRULESQLISEXECUTE
    DESCRIPTION : 检查规则脚本是否可执行
    DATE :2021-01-12
    AUTHOR :YINXH

    -------
    MODIFY LOG
    UPDATE DATE :
    UPDATE BY   :
    UPDATE DESC :
  ***********************************************************************/
  FUNCTION func_checkrulesqlisexecute(p_relyon IN VARCHAR2, p_ruleexpr IN VARCHAR2) RETURN NUMBER IS

    v_result                    NUMBER(10); --脚本执行结果值
    v_execute_sql               VARCHAR2(4000); --脚本解析结果
    -- 校验SQL默认数据
    v_centerid_check            NUMBER(10) := 1;
    v_bookcode_check            VARCHAR2(4000) := 'BookI17';
    v_yearmonth_check           VARCHAR2(4000) := '202101';
    v_ruleexprsql               VARCHAR2(4000); --规则校验执行脚本(拼接附加条件后)
    v_prefix_exprsql            VARCHAR2(4000); --规则校验执行脚本(按from拆分后的前缀部分)
    v_suffix_exprsql            VARCHAR2(4000);
    v_expression_sql            VARCHAR2(4000);
  BEGIN
    -- 调用函数：解析附加条件后拼接成可执行SQL脚本返回
    v_execute_sql := bpl_pack_check.func_analytical_relyon(v_centerid_check,
                                                               v_bookcode_check,
                                                               v_yearmonth_check,
                                                               p_relyon,
                                                               p_ruleexpr);
    --脚本试执行
    BEGIN
      -- 不分场景试执行脚本
        EXECUTE IMMEDIATE v_execute_sql;
    EXCEPTION
      WHEN OTHERS THEN
        v_result := 0;
        dbms_output.put_line('**SQLSTATE: '||to_char(SQLCODE)||'; **SQLERRM: '||SUBSTR(SQLERRM, 1, 200)||',【脚本1执行失败，请检查脚本Sql：'|| v_execute_sql);
        RETURN COALESCE(v_result, 1);
    END;
    --返回脚本结果 0-执行不通过；1-执行通过
    RETURN COALESCE(v_result, 1);
    -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息
  END func_checkrulesqlisexecute;

  /***********************************************************************
    NAME : bpl_PROC_CHECKRULES
    DESCRIPTION : 规则校验
    DATE :2020-12-19
    AUTHOR :YINXH

    -------
    MODIFY LOG
    UPDATE DATE : 2021/01/27 17:49:38
    UPDATE BY   : YINXH
    UPDATE DESC : 规则匹配时增加生效日期、失效日期条件过滤
  ***********************************************************************/
  PROCEDURE proc_checkrules(p_taskcode varchar, p_entity_id numeric, p_bookcode varchar,
                            p_yearmonth varchar, p_procid numeric, p_creatorid numeric) IS
    -- 声明变量
    v_rule_id                 NUMBER(12); --规则id
    v_actlogid                 NUMBER(12); --业务日志表ID
    v_exec_rule_result         NUMBER(12); --校验规则执行结果
    v_current_rule_check_state CHAR(1); --当前节点匹配规则的检查状态
    v_uwProcId                 NUMBER(12); --业财对账[承保]ID
    v_errormessage             VARCHAR2(500); --规则校验不通过信息
    v_busmessage               VARCHAR2(500):=''; --业务异常主键
    v_checkexpr                VARCHAR2(4000); --规则校验执行脚本信息记录
    v_ruleexprsql              VARCHAR2(4000); --规则校验执行脚本(拼接附加条件后)
    v_prefix_exprsql           VARCHAR2(4000); --规则校验执行脚本(按from拆分后的前缀部分)
    v_suffix_exprsql           VARCHAR2(4000);
    i                          NUMBER(12);
    bussness_no_list type_array_table;  --数组用于临时存放业务主键值

  BEGIN

    -- 52-业财对账[承保]流程节点特殊处理：
    SELECT PROC_ID
      INTO V_UWPROCID
      FROM BPLUSER.bms_conf_action_procdef
     WHERE PROC_CODE = 'ACC_ACCOUNTCHECK_BIZFI_UW_ACR';

    -- 【节点循环】：根据 核算单位ID 和 账套编码，获取当前节点所有父节点及其所有子节点
    FOR rec_proc IN (SELECT bp.system_code,
                            bp.proc_id,
                            bp.parent_proc_id
                       FROM bpluser.bms_conf_action_procdef bp
                      WHERE bp.valid_is = '1'
                        AND EXISTS (SELECT 1
                               FROM bpl_CONF_CHECKRULE ac
                              WHERE ac.proc_id = bp.proc_id
                                AND ac.valid_is = '1'
                                AND ac.audit_state = '1'
                                AND ac.effective_date <= SYSDATE
                                AND ac.expriration_date >= SYSDATE
                                AND ac.entity_id = p_entity_id
                                AND (ac.book_code = p_bookcode or  p_bookcode is null)
                                ) --过滤不存在有效(有效标记、有效期内)且审核通过的校验规则配置的节点
                     CONNECT BY nocycle PRIOR bp.proc_id = bp.parent_proc_id
                      START WITH bp.proc_id = p_procid
                      ORDER BY bp.proc_id ASC) LOOP

      v_actlogid := bpl_seq_log_action.nextval; --按节点获取新的业务日志ID
      dbms_output.put_line('sss' ||rec_proc.proc_id );

      -- 将当前校验规则写入业务日志表(精确到节点)
      bpl_pack_action_log.proc_add_actionlog(v_actlogid,
                                             p_taskcode, 
                                             rec_proc.system_code,
                                             p_entity_id,
                                             p_bookcode,
                                             p_yearmonth,
                                             rec_proc.proc_id,
                                             rec_proc.parent_proc_id,
                                             '0', --默认不通过，后续按节点规则取并集状态更新
                                             p_creatorid --创建人员
                                             );

      -- 【规则循环】：循环获取节点的所有匹配规则，并校验
      FOR rec_checkrule IN (SELECT cr.check_rule_id,
                                   cr.entity_id,     --核算单位ID
                                   cr.book_code,     --账套编码
                                   cr.proc_id,       --流程节点ID
                                   cr.rule_expr,     --SQL脚本规则
                                   cr.rely_on,       --规则附件条件
                                   cr.rule_direction, --规则方向：1-正向规则，0-反向规则
                                   cr.warning_type
                              FROM bpl_CONF_CHECKRULE cr
                             WHERE cr.entity_id = p_entity_id
                               AND (cr.book_code = p_bookcode or p_bookcode is null)
                               AND cr.valid_is = '1'
                               AND cr.audit_state = '1'
                               AND cr.effective_date <= LOCALTIMESTAMP
                               AND cr.expriration_date >= LOCALTIMESTAMP
                               AND cr.proc_id = rec_proc.proc_id) LOOP

        BEGIN
          v_rule_id := rec_checkrule.check_rule_id;
          dbms_output.put_line('v_rule_id: '||v_rule_id);
          -- 执行SQL脚本规则，并记录结果
          IF rec_checkrule.rule_expr IS NOT NULL THEN
            BEGIN
              -- 调用函数：解析附加条件后拼接成可执行SQL脚本返回
              v_ruleexprsql := func_analytical_relyon(p_entity_id,
                                                      p_bookcode,
                                                      p_yearmonth,
                                                      rec_checkrule.rely_on,
                                                      rec_checkrule.rule_expr);
              v_busmessage := '';
              -- 分场景执行脚本
              -- 1、脚本自带统计函数 count
              IF instr(upper(v_ruleexprsql), upper(' count(')) > 0 THEN
                dbms_output.put_line('v_ruleexprsql1: '||v_ruleexprsql);
                EXECUTE IMMEDIATE v_ruleexprsql INTO v_exec_rule_result; --结果计数值

              ELSE
                -- 脚本不包含统计函数"count"的脚本需要另外统一进行结果统计
                -- 先将业务主键批量插入数组列表
                
                dbms_output.put_line('v_ruleexprsql2: '||v_ruleexprsql);
                EXECUTE IMMEDIATE to_char(v_ruleexprsql)  BULK COLLECT
                    INTO bussness_no_list;

                -- 再分特殊流程节点处理
                FOR i IN 1 .. bussness_no_list.count LOOP
                  -- 截取前5个业务主键存入日志子表的业务异常主键字段
                  IF i <= 5 THEN
                    v_busmessage := v_busmessage || ',' || bussness_no_list(i);
                  END IF;
                END LOOP;
                COMMIT; --提交事务

                -- 去除第一个逗号分割符
                SELECT TRIM(leading ',' FROM v_busmessage) INTO v_busmessage from dual;

                IF v_busmessage IS NOT NULL   THEN
                  v_exec_rule_result := 1; --有值则给结果计数值为 1
                ELSE
                  v_exec_rule_result := 0; --无值则给结果计数值为 0
                END IF;

              END IF;

              -- 记录脚本执行信息
              v_checkexpr := v_ruleexprsql;

            END;

          ELSE
            v_exec_rule_result := 0;
            v_checkexpr        := 'Sql脚本为空';
          END IF;

          -- 规则方向为正向，且执行SQL脚本结果值大于指定值，则视为校验通过
          IF rec_checkrule.rule_direction = '1' THEN
            -- 正向规则时，仅大于0校验通过
            IF v_exec_rule_result > 0 THEN
              v_current_rule_check_state := '1';
            ELSE
              v_current_rule_check_state := '0';
            END IF;
          ELSE
            -- 反向规则时，大于0为不通过
            IF v_exec_rule_result > 0 THEN
              v_current_rule_check_state := '0';
            ELSE
              v_current_rule_check_state := '1';
            END IF;
          END IF;
          -- 将当前校验规则写入业务日志详情表(精确到规则)
          bpl_pack_action_log.proc_add_actionlogdetails(v_actlogid,
                                                        rec_checkrule.check_rule_id,  
                                                        rec_checkrule.warning_type,
                                                        v_checkexpr,
                                                        v_current_rule_check_state, --规则校验状态
                                                        v_errormessage,
                                                        v_busmessage,
                                                        p_creatorid --创建人员
                                                        );

           /*BEGIN
            -- 调用数据平台pck，回写计量单元数据节点及节点状态（仅限于业财对账[承保]-52）
           FOR vouchervo IN (SELECT a.policy_no,
                                     a.endorse_seq_no,
                                     a.risk_code,
                                     bc.proc_id,
                                     bc.proc_state
                               	  FROM bpl_temp_checkrules bc 
																	left join bpl_BUSS_VOUCHER c 
																	on bc.voucher_no = c.voucher_no 
																	left join bpl_BUSS_ENTRY_DATA b   
																	on c.VOUCHER_ID = b.VOUCHER_ID
																	left join bpl_DAP_ENTRY_DATA a
																	on a.entry_data_id = b.entry_data_id AND a.fee_type = 'R10' 
                               WHERE bc.proc_id = v_uwProcId --节点限制：业财对账[承保]-52
                                 AND bc.entity_id = p_entity_id
                                 AND bc.book_code = p_bookcode
                                 AND bc.year_month = p_yearmonth
                                 AND c.state = '1' --凭证状态为正常-1
																 AND b.VOUCHER_ID is not null
                              ) LOOP
              -- 调用数据平台pck，回写计量单元数据节点及节点状态

              dmuser.dm_pack_buss_cmunit.proc_procdeal(p_entity_id,
                                                      'P', --P-保批单；G-合同组；C-计量单元
                                                      vouchervo.policy_no,
                                                      vouchervo.endorse_seq_no,
                                                      vouchervo.risk_code,
                                                      vouchervo.proc_id,
                                                      vouchervo.proc_state,
                                                      p_creatorid);

            END LOOP;
          END;*/
        END;
      END LOOP;

      --当前循环节点校验完毕后更新节点校验状态
      bpl_pack_action_log.proc_update_logst(v_actlogid,p_creatorid);
       COMMIT;
    END LOOP;
 
   EXCEPTION
     WHEN OTHERS THEN
       ROLLBACK;
       dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());  
       dbms_output.put_line('**错误代码: '||to_char(SQLCODE)||'; **SQLERRM: '||SUBSTR(SQLERRM, 1, 200)||' 【数据检查发生异常，请检查'||v_rule_id||'】');
  END proc_checkrules;
end bpl_pack_check;
/

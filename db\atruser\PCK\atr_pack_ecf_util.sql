---------- drop ---------------
do language plpgsql
$$
    declare
        rec record;
    begin
        for rec in select t.proname, t.prokind from pg_proc t where t.proname ~ '^atr_pack_ecf_util_.*'
            loop
                if rec.prokind = 'f' then
                    call atr_pack_commonutils_proc_drop_func(rec.proname::varchar);
                elsif rec.prokind = 'p' then
                    call atr_pack_commonutils_proc_drop_proc(rec.proname::varchar);
                end if;
            end loop;
    end;
$$;



---------- create ---------------
CREATE FUNCTION atruser.atr_pack_ecf_util_func_add_year_months(p_year_month character varying, n integer) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
declare

    -------------------------
    -- 返回 上/下 n 个业务年月
    -------------------------
begin
    return to_char(to_date(p_year_month, 'yyyymm') + n * '1 month'::interval,
                   'yyyymm');
end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_diff_only_months(p_start timestamp with time zone, p_end timestamp with time zone) RETURNS integer
    LANGUAGE sql IMMUTABLE
    AS $$
    --------------------------------------------------------------------
    -- 返回月份差， 细粒度到月，  例如 2021-01-04/2021-02-03 将返回 1 。
    --------------------------------------------------------------------
select extract(year from age(date_trunc('month', p_end), date_trunc('month', p_start))) * 12
           + extract(month from age(date_trunc('month', p_end), date_trunc('month', p_start))) ;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_calc_diff_months2(p_start_date timestamp with time zone, p_end_date timestamp with time zone) RETURNS integer
    LANGUAGE sql IMMUTABLE
    AS $$
    -------------------------
    -- 计算起期到止期的月份差， 如果出现小数， 则向上取整
    -------------------------
select ceil(atr_pack_ecf_util_func_diff_only_months(date_trunc('day', p_end_date),
                                                    date_trunc('day', p_start_date)));

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_calc_diff_months(p_year_month text, p_end_date timestamp with time zone) RETURNS integer
    LANGUAGE sql IMMUTABLE
    AS $$
    -------------------------
    -- 计算某年月到指定终止日期的月份差， 如果出现小数， 则向上取整
    -------------------------
    select atr_pack_ecf_util_func_calc_diff_months2(to_date(p_year_month, 'yyyymm')::timestamp,
                                                    p_end_date);

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_create_action_no() RETURNS character varying
    LANGUAGE plpgsql
    AS $$
declare
    -------------------------
    -- 生成全局唯一的执行编号
    -------------------------
    v_temp  smallint;
    v_denom smallint := 1296;
    v_seq   int;
    v_str   varchar(36);
begin
    v_str := to_char(clock_timestamp(), 'yyyyMMdd_HH24miss') || '_' || random_str(2);
    v_seq := mod(nextval('atr_seq_action_no'), 46656);
    for i in 1 .. 3
        loop
            v_temp := mod(floor(v_seq / v_denom)::int, 36);
            if v_temp < 10 then
                v_str := v_str || v_temp;
            else
                v_str := v_str || chr(v_temp + 55);
            end if;
            v_denom := v_denom / 36;
        end loop;

    return v_str;
end ;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_create_task_code(p_module character varying, p_center_id bigint, p_year_month character varying, p_currecy_code character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
declare
    v_key_code  varchar(60);
    v_task_code varchar(60);
begin
    v_key_code := p_module || p_center_id || p_year_month || p_currecy_code;

    merge into atr_conf_max_no t
    using (select v_key_code key_code) x
    on t.key_code = x.key_code
    when matched then
        update set cur_value = t.cur_value + 1
    when not matched then
        insert (key_code, cur_value) values (x.key_code, 1);

    select v_key_code || lpad(t.cur_value::varchar, 5, '0')
    into v_task_code
    from atr_conf_max_no t
    where t.key_code = v_key_code;

    return v_task_code;
end;
$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_get_end_of_quarter(p_date timestamp without time zone) RETURNS date
    LANGUAGE plpgsql
    AS $$
declare

    ----------------
    -- 获取季末那天
    ----------------
    v_month smallint;
    v_diff  smallint;

begin
    v_month := to_char(p_date, 'mm');

    if v_month <= 3 then
        v_diff := 3 - v_month;
    elsif v_month <= 6 then
        v_diff := 6 - v_month;
    elsif v_month <= 9 then
        v_diff := 9 - v_month;
    else
        v_diff := 12 - v_month;
    end if;

    return date_trunc('day', last_day(p_date + v_diff * '1 month'::interval));

end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_get_previos_year_month(p_year_month character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
declare

    -------------------------
    -- 返回上一个业务年月
    -------------------------
begin
    return atr_pack_ecf_util_func_add_year_months(p_year_month, -1);
end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_get_quota_max_dev_no(p_quota_code character varying, p_action_no character varying, p_main_id bigint, p_dimension character varying) RETURNS integer
    LANGUAGE plpgsql
    AS $$
declare

    v_max_dev_no bigint := null;

begin

    select case
               when d.dev_is is null or d.dev_is <> '1' then
                   null
               else
                   coalesce(case
                                when d.dimension = 'U' then
                                    (select max(q.dev_no)
                                     from atr_buss_quota_value q
                                     where q.action_no = t.action_no
                                       and q.quota_code = p_quota_code
                                       and q.dimension = 'U'
                                       and q.dimension_value = t.business_no)
                                end,
                            case
                                when d.dimension = 'C' then
                                    (select max(q.dev_no)
                                     from atr_buss_quota_value q
                                     where q.action_no = t.action_no
                                       and q.quota_code = p_quota_code
                                       and q.dimension = 'C'
                                       and q.dimension_value = t.icg_no)
                                end,
                            (select max(q.dev_no)
                             from atr_buss_quota_value q
                             where q.action_no = t.action_no
                               and q.quota_code = p_quota_code
                               and q.dimension = 'G'
                               and q.dimension_value = t.loa_code))
               end
    into v_max_dev_no
    from atr_temp_ecf_quota_param t,
         atr_buss_quota_def d
    where t.main_id = p_main_id
      and t.dimension = p_dimension
      and t.action_no = p_action_no
      and t.action_no = d.action_no
      and d.quota_code = p_quota_code;

    return v_max_dev_no;
end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_get_quota_value(p_quota_code character varying, p_action_no character varying, p_main_id bigint, p_dimension character varying, p_dev_no bigint, p_allow_zero integer, p_default integer) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
declare

    v_quota_value decimal(19, 6);

begin

    select (case
                when d.dimension = 'U' and exists
                    (select q.id
                     from atr_buss_quota_value q
                     where q.action_no = t.action_no
                       and q.quota_code = p_quota_code
                       and q.dimension = 'U'
                       and q.dimension_value = t.business_no) then
                    (select q.quota_value
                     from atr_buss_quota_value q
                     where q.action_no = t.action_no
                       and q.quota_code = p_quota_code
                       and q.dimension = 'U'
                       and q.dimension_value = t.business_no
                       and q.dev_no = p_dev_no)
                when d.dimension = 'C' and exists
                    (select q.id
                     from atr_buss_quota_value q
                     where q.action_no = t.action_no
                       and q.quota_code = p_quota_code
                       and q.dimension = 'C'
                       and q.dimension_value = t.icg_no) then
                    (select q.quota_value
                     from atr_buss_quota_value q
                     where q.action_no = t.action_no
                       and q.quota_code = p_quota_code
                       and q.dimension = 'C'
                       and q.dimension_value = t.icg_no
                       and q.dev_no = p_dev_no)
                else
                    (select q.quota_value
                     from atr_buss_quota_value q
                     where q.action_no = t.action_no
                       and q.quota_code = p_quota_code
                       and q.dimension = 'G'
                       and q.dimension_value = t.loa_code
                       and q.dev_no = p_dev_no)
        end)
    into v_quota_value
    from atr_temp_ecf_quota_param t,
         atr_buss_quota_def d
    where t.main_id = p_main_id
      and t.dimension = p_dimension
      and t.action_no = p_action_no
      and t.action_no = d.action_no
      and d.quota_code = p_quota_code;

    if p_allow_zero is null or p_allow_zero = 0 then
        if v_quota_value = 0 then
            v_quota_value := null;
        end if;
    end if;

    return coalesce(v_quota_value, p_default);

end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_next_year_month(p_year_month character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
declare

    -------------------------
    -- 返回下一个业务年月
    -------------------------
begin
    return atr_pack_ecf_util_func_add_year_months(p_year_month, 1);
end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_sum_quota_value(p_quota_code character varying, p_action_no character varying, p_main_id bigint, p_dimension character varying, p_dev_no_start bigint, p_dev_no_end bigint) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
declare
    v_sum decimal(19, 4);
begin
    select (case
                when d.dimension = 'U' and exists
                    (select *
                     from atr_buss_quota_value q
                     where q.action_no = t.action_no
                       and q.quota_code = p_quota_code
                       and q.model_def_id = t.model_def_id
                       and q.dimension = 'U'
                       and q.dimension_value = t.business_no) then
                    (select sum(q.quota_value)
                     from atr_buss_quota_value q
                     where q.action_no = t.action_no
                       and q.quota_code = p_quota_code
                       and q.model_def_id = t.model_def_id
                       and q.dimension = 'U'
                       and q.dimension_value = t.business_no
                       and q.dev_no >= p_dev_no_start
                       and q.dev_no <= p_dev_no_end)
                when d.dimension = 'C' and exists
                    (select *
                     from atr_buss_quota_value q
                     where q.action_no = t.action_no
                       and q.quota_code = p_quota_code
                       and q.model_def_id = t.model_def_id
                       and q.dimension = 'C'
                       and q.dimension_value = t.icg_no) then
                    (select sum(q.quota_value)
                     from atr_buss_quota_value q
                     where q.action_no = t.action_no
                       and q.quota_code = p_quota_code
                       and q.model_def_id = t.model_def_id
                       and q.dimension = 'C'
                       and q.dimension_value = t.icg_no
                       and q.dev_no >= p_dev_no_start
                       and q.dev_no <= p_dev_no_end)
                else
                    (select sum(q.quota_value)
                     from atr_buss_quota_value q
                     where q.action_no = t.action_no
                       and q.quota_code = p_quota_code
                       and q.model_def_id = t.model_def_id
                       and q.dimension = 'G'
                       and q.dimension_value = t.loa_code
                       and q.dev_no >= p_dev_no_start
                       and q.dev_no <= p_dev_no_end)
        end)
    into v_sum
    from atr_temp_ecf_quota_param t,
         atr_buss_quota_def d
    where t.main_id = p_main_id
      and t.dimension = p_dimension
      and t.action_no = p_action_no
      and t.action_no = d.action_no
      and d.quota_code = p_quota_code;

    return v_sum;
end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_func_zero_to_null(p_number numeric) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
declare

    -------------------------
    -- 如果数字为 0， 返回 null
    -------------------------
begin
    if p_number = 0 then
        return null;
    end if;
    return p_number;
end;

$$;

CREATE FUNCTION atruser.atr_pack_ecf_util_nvl0(p_val numeric, p_default numeric) RETURNS numeric
    LANGUAGE sql IMMUTABLE
    AS $$
    --------------------------------------------
    --- 如果入参 p_val 是 null 或 0， 将返回默认值 p_default； 否则返回 p_val
    --------------------------------------------
    select case when p_val = 0 then p_default else coalesce(p_val, p_default) end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_util_proc_check_buss_period(IN p_type character varying, IN p_entity_id numeric, IN p_buss_source character varying, IN p_year_month character varying)
    LANGUAGE plpgsql
    AS $$
declare
    v_min_year_month varchar(6);
    v_empty          varchar(2000);
    v_dup            varchar(2000);
    v_error          varchar(4000) := '';
begin

    if p_type = 'LIC' then
        select min(t.year_month)
        into v_min_year_month
        from atr_buss_lic_action t
        where t.confirm_is = '1'
          and t.business_source_code = p_buss_source
          and t.entity_id = p_entity_id;

        if v_min_year_month is null then
            return;
        end if;

        if v_min_year_month = p_year_month then
            raise '%', p_year_month || ' has a confirmed version';
        end if;

        with temp as
                 (select c.year_month, count(a.year_month) count_
                  from atr_conf_year_month c
                           left join atr_buss_lic_action a
                                     on a.year_month = c.year_month
                                         and a.business_source_code = p_buss_source
                                         and a.confirm_is = '1'
                  where c.year_month >= v_min_year_month
                    and c.year_month < p_year_month
                  group by c.year_month)
        select (select string_agg(t.year_month, ',' order by t.year_month)
                from temp t
                where t.count_ = 0),
               (select string_agg(t.year_month, ',' order by t.year_month)
                from temp t
                where t.count_ > 1)
        into v_empty, v_dup;

    elsif p_type = 'LRC' then
        select min(t.year_month)
        into v_min_year_month
        from atr_buss_lrc_action t
        where t.confirm_is = '1'
          and t.business_source_code = p_buss_source
          and t.entity_id = p_entity_id;

        if v_min_year_month is null then
            return;
        end if;

        if v_min_year_month = p_year_month then
            raise '%', p_year_month || ' has a confirmed version';
        end if;

        with temp as
                 (select c.year_month, count(a.year_month) count_
                  from atr_conf_year_month c
                           left join atr_buss_lrc_action a
                                     on a.year_month = c.year_month
                                         and a.business_source_code = p_buss_source
                                         and a.confirm_is = '1'
                  where c.year_month >= v_min_year_month
                    and c.year_month < p_year_month
                  group by c.year_month)
        select (select string_agg(t.year_month, ',' order by t.year_month)
                from temp t
                where t.count_ = 0),
               (select string_agg(t.year_month, ',' order by t.year_month)
                from temp t
                where t.count_ > 1)
        into v_empty, v_dup;

    end if;

    if v_empty is not null then
        v_error := 'There is no confirmed business year_month: ' || v_empty || '. ';
    end if;

    if v_dup is not null then
        v_error := v_error ||
                   'There are duplicate confirmed business year_months: ' ||
                   v_dup || '. ';
    end if;

    if length(v_error) > 0 then
        raise '%', v_error;
    end if;

end ;
$$;

CREATE PROCEDURE atruser.atr_pack_ecf_util_proc_collect_quota_param(IN p_module character varying, IN p_dimension character varying, IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare

    ------------------------------------------
    -- 收集这个批次用于查询假设值的查询参数
    ------------------------------------------
begin

    delete
    from atr_temp_ecf_quota_param t
    where t.action_no = p_action_no
      and t.dimension = p_dimension;
    commit;

    if p_dimension = 'U' then
        insert into atr_temp_ecf_quota_param(dimension,
                                             main_id,
                                             action_no,
                                             loa_code,
                                             icg_no,
                                             business_no)
        select *
        from (select p_dimension,
                     t.id,
                     t.action_no,
                     t.loa_code,
                     t.icg_no,
                     t.policy_no business_no
              from atr_buss_dd_lrc_icu_calc t
              where t.action_no = p_action_no
              union all
              select p_dimension,
                     t.id,
                     t.action_no,
                     t.loa_code,
                     t.icg_no,
                     t.ri_policy_no business_no
              from atr_buss_fo_lrc_icu_calc t
              where t.action_no = p_action_no
              union all
              select p_dimension,
                     t.id,
                     t.action_no,
                     t.loa_code,
                     t.icg_no,
                     t.treaty_no business_no
              from atr_buss_ti_lrc_icu_calc t
              where t.action_no = p_action_no
              union all
              select p_dimension,
                     t.id,
                     t.action_no,
                     t.loa_code,
                     t.icg_no,
                     t.treaty_no business_no
              from atr_buss_to_lrc_icu_calc t
              where t.action_no = p_action_no) x;

        commit;

    elsif p_dimension = 'C' then
        if p_module = 'LIC' then
            insert into atr_temp_ecf_quota_param
                (dimension, main_id, action_no, loa_code, icg_no)
            select *
            from (select p_dimension,
                         t.id,
                         t.action_no,
                         t.loa_code,
                         t.icg_no
                  from atr_buss_dd_lic_icg_calc t
                  where t.action_no = p_action_no
                  union all
                  select p_dimension,
                         t.id,
                         t.action_no,
                         t.loa_code,
                         t.icg_no
                  from atr_buss_fo_lic_icg_calc t
                  where t.action_no = p_action_no
                  union all
                  select p_dimension,
                         t.id,
                         t.action_no,
                         t.loa_code,
                         t.icg_no
                  from atr_buss_ti_lic_icg_calc t
                  where t.action_no = p_action_no
                  union all
                  select p_dimension,
                         t.id,
                         t.action_no,
                         t.loa_code,
                         t.icg_no
                  from atr_buss_to_lic_icg_calc t
                  where t.action_no = p_action_no) x;

        else

            insert into atr_temp_ecf_quota_param
                (dimension, main_id, action_no, loa_code, icg_no)
            select *
            from (select p_dimension,
                         t.id,
                         t.action_no,
                         t.loa_code,
                         t.icg_no
                  from atr_buss_dd_lrc_icg_calc t
                  where t.action_no = p_action_no
                  union all
                  select p_dimension,
                         t.id,
                         t.action_no,
                         t.loa_code,
                         t.icg_no
                  from atr_buss_fo_lrc_icg_calc t
                  where t.action_no = p_action_no
                  union all
                  select p_dimension,
                         t.id,
                         t.action_no,
                         t.loa_code,
                         t.icg_no
                  from atr_buss_ti_lrc_icg_calc t
                  where t.action_no = p_action_no
                  union all
                  select p_dimension,
                         t.id,
                         t.action_no,
                         t.loa_code,
                         t.icg_no
                  from atr_buss_to_lrc_icg_calc t
                  where t.action_no = p_action_no) x;
        end if;

        commit;
    end if;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_util_proc_log0(IN p_module character varying, IN p_log_type character varying, IN p_action_no character varying, IN p_business_info character varying, IN p_mark character varying, IN p_msg character varying, IN p_thread_no integer)
    LANGUAGE plpgsql
    AS $$
declare
begin

    begin
        if p_module = 'LIC' then
            insert into atr_log_lic_trace(id,
                                          log_type,
                                          action_no,
                                          business_info,
                                          mark,
                                          msg,
                                          create_time,
                                          thread_no)
            values (nextval('atr_seq_log_lic_trace'),
                    p_log_type,
                    p_action_no,
                    p_business_info,
                    p_mark,
                    substr(p_msg, 1, 4000),
                    clock_timestamp(),
                    p_thread_no);
        elsif p_module = 'LRC' then
            insert into atr_log_lrc_trace(id,
                                          log_type,
                                          action_no,
                                          business_info,
                                          mark,
                                          msg,
                                          create_time,
                                          thread_no)
            values (nextval('atr_seq_log_lrc_trace'),
                    p_log_type,
                    p_action_no,
                    p_business_info,
                    p_mark,
                    substr(p_msg, 1, 4000),
                    clock_timestamp(),
                    p_thread_no);
        end if;
    exception
        when others then
            raise warning '%', sqlerrm;
    end;

    commit;
end;

$$;

CREATE PROCEDURE atruser.atr_pack_ecf_util_proc_log(IN p_module character varying, IN p_log_type character varying, IN p_action_no character varying, IN p_business_info character varying, IN p_mark character varying, IN p_msg character varying, IN p_thread_no integer)
    LANGUAGE plpgsql
    AS $$
declare
    v_type          smallint := 2; -- 0-什么都不做， 1-控制台输出， 2-日志表
    v_log_level     smallint;
    v_log_level_min smallint := 1; -- 1-DEBUG, 2-INFO, 3-WARN, 4-ERROR
begin

    select case p_log_type
               when 'DEBUG' then
                   1
               when 'INFO' then
                   2
               when 'WARN' then
                   3
               when 'ERROR' then
                   4
               when 'ALWAYS' then
                   9
               else
                   -1
               end
    into strict v_log_level;

    if v_log_level < v_log_level_min then
        return;
    end if;

    if v_type = 0 then
        null;
    elsif v_type = 1 then
        raise notice 'LOG_TYPE: %; ACTION_NO: %; BUSINESS_INFO: %; MARK: %; MSG: %; P_THREAD_NO: %',
            p_log_type, p_action_no, p_business_info, p_mark, p_msg, p_thread_no;
    elsif v_type = 2 then
        call atr_pack_ecf_util_proc_log0(p_module,
                                         p_log_type,
                                         p_action_no,
                                         p_business_info,
                                         p_mark,
                                         p_msg,
                                         p_thread_no);
    end if;
end;

$$;


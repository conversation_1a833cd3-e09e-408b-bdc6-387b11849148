alter sequence atr_seq_buss_ibnrcalc_acc_dev_data restart with 1;
alter sequence atr_seq_buss_ibnrcalc_acc_future_data restart with 1;
alter sequence atr_seq_buss_ibnrcalc_acc_result restart with 1;
alter sequence atr_seq_buss_ibnrcalc_accident_node restart with 1;
alter sequence atr_seq_buss_ibnrcalc_action restart with 1;
alter sequence atr_seq_buss_ibnrcalc_dev_data restart with 1;
alter sequence atr_seq_buss_ibnrcalc_final_acc_trace restart with 1;
alter sequence atr_seq_buss_ibnrcalc_final_trace restart with 1;
alter sequence atr_seq_buss_ibnrcalc_icp restart with 1;
alter sequence atr_seq_buss_ibnrcalc_inflation_ratio restart with 1;
alter sequence atr_seq_buss_ibnrcalc_settled_conf restart with 1;
alter sequence atr_seq_log_ibnrcalc restart with 1;
alter sequence atr_seq_temp_ibnrcalc_acc_dev_buss_amount restart with 1;

truncate table atr_buss_ibnrcalc_acc_dev_data;
truncate table atr_buss_ibnrcalc_acc_future_data;
truncate table atr_buss_ibnrcalc_acc_result;
truncate table atr_buss_ibnrcalc_accident_node;
truncate table atr_buss_ibnrcalc_action;
truncate table atr_buss_ibnrcalc_dev_data;
truncate table atr_buss_ibnrcalc_final_acc_trace;
truncate table atr_buss_ibnrcalc_final_trace;
truncate table atr_buss_ibnrcalc_icp;
truncate table atr_buss_ibnrcalc_inflation_ratio;
truncate table atr_buss_ibnrcalc_settled_conf;
truncate table atr_log_ibnrcalc;
truncate table atr_temp_ibnrcalc_acc_dev_buss_amount;


--准备金
truncate table atr_buss_reserve_upr;
truncate table atr_buss_reserve_upr_detail;
truncate table atr_buss_reserve_dac;
truncate table atr_buss_reserve_dac_detail;
truncate table atr_buss_reserve_os;
truncate table atr_buss_reserve_os_detail;

-- ibnr 导入
alter sequence atr_seq_buss_ibnr_import_main restart with 1;
alter sequence atr_seq_buss_ibnr_import_dtl restart with 1;
truncate table atr_buss_ibnr_import_main;
truncate table atr_buss_ibnr_import_detail;
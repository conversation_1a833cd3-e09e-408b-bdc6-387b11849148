
update BPL_QRTZ_JOB_DETAILS t set job_c_name = '同步数据到精算平台', job_l_name = '同步數據到精算平臺' where t.job_name = 'ATR_SYNC_DATA';


-- 费用类型
update bbs_conf_fee_type_mapping t
set business_source_code = case t.business_source_code
                               when 'DB' then
                                   'DD'
                               when 'FB' then 'FO'
                               when 'TB' then 'TO'
                               else t.business_source_code end
where t.expenses_type_code = '-5';

-- 修改自动任务名称
update bpl_qrtz_job_details t set job_c_name = '同步数据到精算平台', job_l_name = '同步數據到精算平臺'
                                where t.job_name = 'ATR_SYNC_DATA';



-- ibnr calc 自动任务
delete from bpl_qrtz_conf_task where task_c_name = 'IBNR 计算';
delete from bpl_qrtz_conf_task_detail where func_code = 'ATR_IBNR_CALC';
delete from bpl_qrtz_job_details where job_name = 'ATR_IBNR_CALC';
delete from bpl_qrtz_triggers where job_name = 'ATR_IBNR_CALC';
delete from bpl_qrtz_cron_triggers where trigger_name = 'T_ATR_IBNR_CALC';

INSERT INTO bpluser.bpl_qrtz_conf_task (conf_task_id, task_c_name, task_l_name, task_e_name, url, param,
                                        frequency, valid_is, create_time, update_time, creator_id, updator_id,
                                        repeat_is, task_group, relation_task_id, retry_count, retry_strategy, async_is)
VALUES (nextval('bpl_seq_qrtz_conf_task'), 'IBNR 计算', 'IBNR 計算', 'IBNR Calculation',
        'http://ss-ifrs-actuarial/job/ibnr_calc', null, 'M', '1', null, null, null, 1,
        '1', 'ATR', null, 1, 'NotFixed', '0');



INSERT INTO bpluser.bpl_qrtz_conf_task_detail (task_detail_id, conf_task_id, func_code, func_c_name, func_l_name,
                                               func_e_name, task_type, method, serialized_param, proc_name, param,
                                               frequency, valid_is, create_time, update_time, creator_id, updator_id,
                                               async_is, relation_task_dtl_id, priority_no, retry_count, retry_strategy)
VALUES (nextval('bpl_seq_qrtz_conf_task_detail'),
        (select conf_task_id from bpl_qrtz_conf_task where task_c_name = 'IBNR 计算'),
        'ATR_IBNR_CALC', 'IBNR 计算', 'IBNR 計算', 'IBNR Calculation', 'M',
        'atrIbnrCalcApi.doJob',
        'javax.servlet.http.HttpServletRequest,com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrCalcJobVo',
        null,
        null, 'M', '1', null, null, null, null, '0', null, 1, 1, 'Fixed');

INSERT INTO bpluser.bpl_qrtz_job_details (sched_name, job_name, job_group, description, job_class_name, is_durable,
                                          is_nonconcurrent, is_update_data, requests_recovery, job_data, url,
                                          create_time, job_c_name, job_l_name, entity_id, proc_id, job_e_name,
                                          creator_id, period_buss_code, display_no, original_is, job_frequency,
                                          priority, conf_task_id)
VALUES ('DefaultQuartzScheduler', 'ATR_IBNR_CALC', 'ATR', null, 'com.ss.platform.schedule.work.TaskJob', true, true,
        true, false, '#
#Thu Aug 01 15:05:40 CST 2024
url=http\\://ss-ifrs-actuarial/job/ibnr_calc
entityId=1
jobFrequency=M
cookie=
cron=0 */1 16 * 7 ?
param=
funcType=1
userCode=admin
'::bytea, 'http://ss-ifrs-actuarial/job/ibnr_calc', null, 'IBNR 计算',
        'IBNR 計算', 1, null, 'IBNR Calculation', null, 'ATR_IBNR_CALC', null, '1', 'M', 2,
        (select conf_task_id from bpl_qrtz_conf_task where task_c_name = 'IBNR 计算'));


INSERT INTO bpluser.bpl_qrtz_triggers (sched_name, trigger_name, trigger_group, job_name, job_group, description,
                                       next_fire_time, prev_fire_time, priority, trigger_state, trigger_type,
                                       start_time, end_time, calendar_name, misfire_instr, job_data)
VALUES ('DefaultQuartzScheduler', 'T_ATR_IBNR_CALC', 'ATR', 'ATR_IBNR_CALC', 'ATR', null, 1751356800000, -1, 5, 'WAITING',
        'CRON', 1725937826000, 0, null, 2, '');

INSERT INTO bpluser.bpl_qrtz_cron_triggers (sched_name, trigger_name, trigger_group, cron_expression, time_zone_id)
VALUES ('DefaultQuartzScheduler', 'T_ATR_IBNR_CALC', 'ATR', '0 */1 16 * 7 ?', 'Asia/Shanghai');

commit;



---------- drop ---------------
do language plpgsql
$$
    declare
        rec record;
    begin
        for rec in select t.proname, t.prokind from pg_proc t where t.proname ~ '^atr_pack_ibnrcalc_.*'
            loop
                if rec.prokind = 'f' then
                    call atr_pack_commonutils_proc_drop_func(rec.proname::varchar);
                elsif rec.prokind = 'p' then
                    call atr_pack_commonutils_proc_drop_proc(rec.proname::varchar);
                end if;
            end loop;
    end;
$$;
DROP TYPE IF EXISTS atruser.atr_pack_ibnrcalc_record_buss;



---------- create ---------------
CREATE TYPE atruser.atr_pack_ibnrcalc_record_buss AS (
	start_bom date,
	start_year_month character varying(6),
	start_year character varying(4),
	end_bom date,
	end_eom date,
	end_year_month character varying(6),
	end_year character varying(4),
	end_next_day date,
	deadline_year character varying(4)
);

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_check_dm_buss_period(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    v_count        smallint;
    v_period_state varchar(10);
    rec            record;
begin

    select t.* into rec from atr_buss_ibnrcalc_action t where t.action_no = p_action_no;

    select count(*), max(t.period_state)
    into v_count, v_period_state
    from dmuser.dm_conf_bussperiod t
    where t.entity_id = rec.entity_id
      and t.year_month = rec.extraction_deadline;

    if v_count = 0 then
        raise exception '%', '数据平台中业务年月' || rec.extraction_deadline ||
                             '未开始， 请核实数据平台的业务期间控制表' using errcode = '45071';
    end if;

    if v_count > 1 then
        raise exception '%', '数据平台业务期间控制表有多条业务年月' || rec.extraction_deadline ||
                             '的数据，无法匹配' using errcode = '45072';
    end if;

    if v_period_state <> '3' then
        raise exception '%', '数据平台中业务年月' || rec.extraction_deadline ||
                             '未完成， 请核实数据平台的业务期间控制表' using errcode = '45073';
    end if;
end;


$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_init_buss_record(IN p_action_no character varying, INOUT p_buss atruser.atr_pack_ibnrcalc_record_buss)
    LANGUAGE plpgsql
    AS $$
declare
    rec record;
begin
    select t.* into rec from atr_buss_ibnrcalc_action t where t.action_no = p_action_no;

    p_buss.deadline_year := substr(rec.extraction_deadline, 1, 4);

    if rec.extraction_interval = 'Y' then
        p_buss.end_bom := to_date(p_buss.deadline_year, 'yyyy') - '1 month'::interval;
        p_buss.start_bom := date_trunc('year', p_buss.end_bom - (rec.extraction_zones - 1) * '1 year'::interval);
    elsif rec.extraction_interval = 'M' then
        p_buss.end_bom := to_date(rec.extraction_deadline, 'yyyymm') - '1 month'::interval;
        p_buss.start_bom := date_trunc('month', p_buss.end_bom - (rec.extraction_zones - 1) * '1 month'::interval);
    else
        raise 'Unsupported extraction interval %', rec.extraction_interval;
    end if;

    p_buss.start_year_month := to_char(p_buss.start_bom, 'yyyymm');
    p_buss.start_year := to_char(p_buss.start_bom, 'yyyy');
    p_buss.end_eom := last_day(p_buss.end_bom);
    p_buss.end_year := to_char(p_buss.end_eom, 'yyyy');
    p_buss.end_year_month := to_char(p_buss.end_eom, 'yyyymm');
    p_buss.end_next_day := p_buss.end_eom + '1 day'::interval;
end;
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_log(IN p_action_no character varying, IN p_mark character varying)
    LANGUAGE plpgsql
    AS $$
begin
    insert into atr_log_ibnrcalc (id, action_no, create_time, mark)
    values (nextval('atr_seq_log_ibnrcalc'), p_action_no, clock_timestamp(), p_mark);

    commit;
end;
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_calc_step1(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    rec         record;
    p_buss      atr_pack_ibnrcalc_record_buss;
    v_ibnr_type varchar(1);
    v_loa_code  varchar(20);
begin
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_start');

    delete from atr_buss_ibnrcalc_icp where action_no = p_action_no;
    delete from atr_buss_ibnrcalc_inflation_ratio where action_no = p_action_no;
    delete from atr_buss_ibnrcalc_accident_node where action_no = p_action_no;
    delete from atr_buss_ibnrcalc_acc_dev_data where action_no = p_action_no;
    delete from atr_buss_ibnrcalc_dev_data where action_no = p_action_no;
    delete from atr_buss_ibnrcalc_acc_result where action_no = p_action_no;
    delete from atr_temp_ibnrcalc_acc_dev_buss_amount where action_no = p_action_no;

    select t.* into rec from atr_buss_ibnrcalc_action t where t.action_no = p_action_no;


    select case when rec.ibnr_type = '' then null else rec.ibnr_type end,
           case when rec.loa_code = '' then null else rec.loa_code end
    into v_ibnr_type, v_loa_code;

    call atr_pack_ibnrcalc_proc_init_buss_record(p_action_no, p_buss);

    call atr_pack_ibnrcalc_proc_check_dm_buss_period(p_action_no);

    -- 收集业务的出险信息
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_init_tmp_buss_claim');

    drop table if exists tmp_buss_claim;

    create temp table tmp_buss_claim as
    select distinct entity_id, claim_no, accident_date_time::date as accident_date
    from dmuser.dm_claim_loss c
    where accident_date_time >= p_buss.start_bom
      and accident_date_time < p_buss.end_next_day
      and c.entity_id = rec.entity_id;

    create index idx_tmp_buss_claim on tmp_buss_claim (claim_no);

    -- 出险时间节点层信息
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_init_acc_node_start');

    if rec.extraction_interval = 'Y' then
        insert into atr_buss_ibnrcalc_accident_node (id, action_no, accident_node,
                                                     accident_date_start, accident_date_end_next_day,
                                                     max_dev_no, action_max_dev_no)
        select nextval('atr_seq_buss_ibnrcalc_accident_node')          as id,
               p_action_no,
               x.y                                                     as accident_node,
               to_date(x.y, 'yyyy')                                    as accident_date_start,
               to_date(x.y, 'yyyy') + '1 year'::interval               as accident_date_end_next_day,
               p_buss.end_year::smallint - x.y::smallint               as max_dev_no,
               p_buss.end_year::smallint - p_buss.start_year::smallint as action_max_dev_no
        from (select distinct substr(ym.year_month, 1, 4) as y
              from atr_conf_year_month ym
              where ym.year_month >= p_buss.start_year_month
                and ym.year_month <= p_buss.end_year_month) x
        order by x.y;
    else
        insert into atr_buss_ibnrcalc_accident_node (id, action_no, accident_node,
                                                     accident_date_start, accident_date_end_next_day,
                                                     max_dev_no, action_max_dev_no)
        select nextval('atr_seq_buss_ibnrcalc_accident_node')             as id,
               p_action_no,
               x.ym                                                       as accident_node,
               to_date(x.ym, 'yyyymm')                                    as accident_date_start,
               last_day(to_date(x.ym, 'yyyymm')) + '1 day'::interval      as accident_date_end_next_day,
               months_between(to_date(p_buss.end_year_month, 'yyyymm'),
                              to_date(x.ym, 'yyyymm'))                    as max_dev_no,
               months_between(to_date(p_buss.end_year_month, 'yyyymm'),
                              to_date(p_buss.start_year_month, 'yyyymm')) as action_max_dev_no
        from (select distinct ym.year_month as ym
              from atr_conf_year_month ym
              where ym.year_month >= p_buss.start_year_month
                and ym.year_month <= p_buss.end_year_month) x
        order by x.ym;
    end if;


    -- 已决
    ----  已决 DD
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_temp_settled_dd_start');

    insert into atr_temp_ibnrcalc_acc_dev_buss_amount (id, action_no, portfolio_no, accident_node, dev_no,
                                                       business_source_code, amount_type, amount, expense)
    select nextval('atr_seq_temp_ibnrcalc_acc_dev_buss_amount') as id,
           p_action_no,
           x1.portfolio_no,
           x1.accident_node,
           x1.dev_no,
           'DD'                                                 as business_source_code,
           'settled'                                            as amount_type,
           -sum(x1.amount)                                      as amount,
           -sum(x1.expense)                                     as expense
    from (select x0.portfolio_no,
                 x0.amount,
                 x0.expense,
                 case
                     when rec.extraction_interval = 'Y' then
                         to_char(x0.accident_date, 'yyyy')
                     else
                         to_char(x0.accident_date, 'yyyymm')
                     end  as accident_node,
                 (case
                      when
                          rec.extraction_interval = 'Y' then
                          coalesce((select s.dev_no
                                    from atr_buss_ibnrcalc_settled_conf s
                                    where s.entity_id = x0.entity_id
                                      and s.claim_no = x0.claim_no
                                      and s.currency_code = x0.currency_code
                                      and s.approval_date = x0.approval_date),
                                   extract(year from x0.approval_date::date) -
                                   extract(year from x0.accident_date))
                      else
                          months_between(date_trunc('month', x0.approval_date),
                                         date_trunc('month', x0.accident_date))
                     end) as dev_no
          from (select cm.portfolio_no,
                       t.entity_id,
                       t.currency_code,
                       t.claim_no,
                       t.approval_date::date                                                              as approval_date,
                       c.accident_date,
                       sum(t.amount)                                                                      as amount,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code u,
                                                 atr_conf_code cc
                                            where u.code_code = 'SettledExpenseBF'
                                              and u.code_id = cc.upper_code_id
                                              and cc.code_code = t.expenses_type_code) then t.amount end) as expense
                from tmp_buss_claim c,
                     dmuser.dm_claim_loss_detail t,
                     dmuser.dm_buss_cmunit_direct cm
                where t.entity_id = cm.entity_id
                  and t.policy_no = cm.policy_no
                  and cm.year_month is not null
                  and c.entity_id = t.entity_id
                  and c.claim_no = t.claim_no
                  and t.entity_id = rec.entity_id
                  and t.currency_code = rec.currency_code
                  and (v_ibnr_type is null or v_ibnr_type = '1')
                  and (v_loa_code is null or v_loa_code = cm.loa_code)
                group by cm.portfolio_no, t.entity_id, t.currency_code, t.claim_no, t.approval_date::date,
                         c.accident_date) x0) x1,
         atr_buss_ibnrcalc_accident_node n
    where n.action_no = p_action_no
      and n.accident_node = x1.accident_node
      and x1.dev_no >= 0
      and x1.dev_no <= n.max_dev_no
    group by x1.portfolio_no, x1.accident_node, x1.dev_no
    order by x1.portfolio_no, x1.accident_node, x1.dev_no;


    ----  已决 FO
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_temp_settled_fo_start');

    insert into atr_temp_ibnrcalc_acc_dev_buss_amount (id, action_no, portfolio_no, accident_node, dev_no,
                                                       business_source_code, amount_type, amount, expense)
    select nextval('atr_seq_temp_ibnrcalc_acc_dev_buss_amount') as id,
           p_action_no,
           x1.portfolio_no,
           x1.accident_node,
           x1.dev_no,
           'FO'                                                 as business_source_code,
           'settled'                                            as amount_type,
           sum(x1.amount)                                       as amount,
           sum(x1.expense)                                      as expense
    from (select x0.portfolio_no,
                 x0.amount,
                 x0.expense,
                 case
                     when rec.extraction_interval = 'Y' then
                         to_char(x0.accident_date, 'yyyy')
                     else
                         to_char(x0.accident_date, 'yyyymm')
                     end  as accident_node,
                 (case
                      when
                          rec.extraction_interval = 'Y' then
                          coalesce((select s.dev_no
                                    from atr_buss_ibnrcalc_settled_conf s
                                    where s.entity_id = x0.entity_id
                                      and s.claim_no = x0.claim_no
                                      and s.currency_code = x0.currency_code
                                      and s.approval_date = x0.gl_posting_date),
                                   extract(year from x0.gl_posting_date) -
                                   extract(year from x0.accident_date))
                      else
                          months_between(date_trunc('month', x0.gl_posting_date),
                                         date_trunc('month', x0.accident_date))
                     end) as dev_no
          from (select cm.portfolio_no,
                       t.entity_id,
                       d.currency_code,
                       d.claim_no,
                       d.gl_posting_date::date        as gl_posting_date,
                       c.accident_date,
                       sum(d.amount)                  as amount,
                       sum(case
                               when exists (select 1
                                            from atr_conf_code u,
                                                 atr_conf_code cc
                                            where u.code_code = 'SettledExpenseAF'
                                              and u.code_id = cc.upper_code_id
                                              and cc.code_code = d.expenses_type_code)
                                   then d.amount end) as expense
                from tmp_buss_claim c,
                     dmuser.dm_buss_cmunit_fac_outwards cm,
                     dmuser.dm_reins_outward t,
                     dmuser.dm_reins_outward_detail d,
                     bpluser.bbs_conf_fee_type_mapping mp
                where cm.entity_id = t.entity_id
                  and cm.fac_no = t.ri_policy_no
                  and cm.year_month is not null
                  and c.entity_id = d.entity_id
                  and c.claim_no = d.claim_no
                  and t.entity_id = d.entity_id
                  and t.ri_policy_no = d.ri_policy_no
                  and t.ri_endorse_seq_no = d.ri_endorse_seq_no
                  and mp.business_source_code = 'FO'
                  and d.expenses_type_code = mp.expenses_type_code
                  and mp.fee_class in ('Claim')
                  and t.entity_id = rec.entity_id
                  and d.currency_code = rec.currency_code
                  and (v_ibnr_type is null or v_ibnr_type = '2')
                  and (v_loa_code is null or v_loa_code = cm.loa_code)
                group by cm.portfolio_no, t.entity_id, d.currency_code, d.claim_no, d.gl_posting_date::date,
                         c.accident_date) x0) x1,
         atr_buss_ibnrcalc_accident_node n
    where n.action_no = p_action_no
      and n.accident_node = x1.accident_node
      and x1.dev_no >= 0
      and x1.dev_no <= n.max_dev_no
    group by x1.portfolio_no, x1.accident_node, x1.dev_no
    order by x1.portfolio_no, x1.accident_node, x1.dev_no;


    ----  已决 TO
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_temp_settled_to-01');

    drop table if exists tmp_reins_ow_claim;

    create temp table tmp_reins_ow_claim as
    select t.entity_id,
           d.treaty_no,
           d.claim_no,
           d.expenses_type_code,
           d.gl_posting_date::date as gl_posting_date,
           sum(d.amount)           as amount
    from dmuser.dm_reins_outward t,
         dmuser.dm_reins_outward_detail d,
         bpluser.bbs_conf_fee_type_mapping mp
    where t.entity_id = d.entity_id
      and t.ri_policy_no = d.ri_policy_no
      and t.ri_endorse_seq_no = d.ri_endorse_seq_no
      and mp.business_source_code = 'TO'
      and d.expenses_type_code = mp.expenses_type_code
      and mp.fee_class = 'Claim'
      and t.entity_id = rec.entity_id
      and d.currency_code = rec.currency_code
    group by t.entity_id, d.treaty_no, d.claim_no, d.expenses_type_code, d.gl_posting_date::date;

    create index idx_tmp_reins_ow_claim_c_t on tmp_reins_ow_claim (claim_no, treaty_no);

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_temp_settled_to-02');

    insert into atr_temp_ibnrcalc_acc_dev_buss_amount (id, action_no, portfolio_no, accident_node, dev_no,
                                                       business_source_code, amount_type, amount, expense)
    select nextval('atr_seq_temp_ibnrcalc_acc_dev_buss_amount') as id,
           p_action_no,
           x1.portfolio_no,
           x1.accident_node,
           x1.dev_no,
           'TO'                                                 as business_source_code,
           'settled'                                            as amount_type,
           sum(x1.amount)                                       as amount,
           sum(x1.expense)                                      as expense
    from (select cm.portfolio_no,
                 t.entity_id,
                 t.claim_no,
                 t.gl_posting_date,
                 c.accident_date,
                 sum(t.amount)                                                                      as amount,
                 sum(case
                         when exists (select 1
                                      from atr_conf_code u,
                                           atr_conf_code cc
                                      where u.code_code = 'SettledExpenseAF'
                                        and u.code_id = cc.upper_code_id
                                        and cc.code_code = t.expenses_type_code) then t.amount end) as expense,
                 case
                     when rec.extraction_interval = 'Y' then
                         to_char(c.accident_date, 'yyyy')
                     else
                         to_char(c.accident_date, 'yyyymm')
                     end                                                                            as accident_node,
                 (case
                      when
                          rec.extraction_interval = 'Y' then
                          coalesce((select s.dev_no
                                    from atr_buss_ibnrcalc_settled_conf s
                                    where s.entity_id = t.entity_id
                                      and s.claim_no = t.claim_no
                                      and s.currency_code = rec.currency_code
                                      and s.approval_date = t.gl_posting_date),
                                   extract(year from t.gl_posting_date) -
                                   extract(year from c.accident_date))
                      else
                          months_between(date_trunc('month', t.gl_posting_date),
                                         date_trunc('month', c.accident_date))
                     end)                                                                           as dev_no
          from tmp_buss_claim c,
               dmuser.dm_buss_cmunit_treaty cm,
               tmp_reins_ow_claim t
          where cm.entity_id = t.entity_id
            and cm.treaty_no = t.treaty_no
            and cm.ri_direction_code = 'O'
            and cm.year_month is not null
            and t.entity_id = rec.entity_id
            and c.claim_no = t.claim_no
            and (v_ibnr_type is null or v_ibnr_type = '2')
            and (v_loa_code is null or v_loa_code = cm.loa_code)
          group by cm.portfolio_no, t.entity_id, t.claim_no, t.gl_posting_date, c.accident_date) x1,
         atr_buss_ibnrcalc_accident_node n
    where n.action_no = p_action_no
      and n.accident_node = x1.accident_node
      and x1.dev_no >= 0
      and x1.dev_no <= n.max_dev_no
    group by x1.portfolio_no, x1.accident_node, x1.dev_no
    order by x1.portfolio_no, x1.accident_node, x1.dev_no;


    -- 未决
    ----  未决 DD
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_temp_os_dd_start');

    insert into atr_temp_ibnrcalc_acc_dev_buss_amount (id, action_no, portfolio_no, accident_node, dev_no,
                                                       business_source_code, amount_type, amount)
    select nextval('atr_seq_temp_ibnrcalc_acc_dev_buss_amount') as id,
           p_action_no,
           x.portfolio_no,
           x.accident_node,
           x.dev_no,
           'DD'                                                 as business_source_code,
           'os'                                                 as amount_type,
           -sum(x.amount)                                       as amount
    from (select cm.portfolio_no,
                 sum(t.outstanding_amount) as amount,
                 case
                     when rec.extraction_interval = 'Y' then
                         to_char(c.accident_date, 'yyyy')
                     else
                         to_char(c.accident_date, 'yyyymm')
                     end                   as accident_node,
                 case
                     when rec.extraction_interval = 'Y' then
                         t.year::int - to_char(c.accident_date, 'yyyy')::int
                     else
                         months_between(to_date(t.year || t.month, 'yyyymm'),
                                        date_trunc('month', c.accident_date))
                     end                   as dev_no
          from tmp_buss_claim c,
               dmuser.dm_claim_outstanding t,
               dmuser.dm_buss_cmunit_direct cm
          where t.year || lpad(t.month, 2, '0') = p_buss.end_year_month
            and t.business_type_code = 'BF'
            and t.entity_id = rec.entity_id
            and t.currency_code = rec.currency_code
            and t.entity_id = cm.entity_id
            and t.policy_no = cm.policy_no
            and cm.year_month is not null
            and c.entity_id = t.entity_id
            and c.claim_no = t.claim_no
            and (v_ibnr_type is null or v_ibnr_type = '1')
            and (v_loa_code is null or v_loa_code = cm.loa_code)
          group by cm.portfolio_no, c.accident_date, t.year, t.month) x
    group by x.portfolio_no, x.accident_node, x.dev_no
    order by x.portfolio_no, x.accident_node, x.dev_no;


    ----  未决 FO
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_temp_os_fo_start');

    insert into atr_temp_ibnrcalc_acc_dev_buss_amount (id, action_no, portfolio_no, accident_node, dev_no,
                                                       business_source_code, amount_type, amount)
    select nextval('atr_seq_temp_ibnrcalc_acc_dev_buss_amount') as id,
           p_action_no,
           x.portfolio_no,
           x.accident_node,
           x.dev_no,
           'FO'                                                 as business_source_code,
           'os'                                                 as amount_type,
           sum(x.amount)                                        as amount
    from (select cm.portfolio_no,
                 sum(t.outstanding_amount) as amount,
                 case
                     when rec.extraction_interval = 'Y' then
                         to_char(c.accident_date, 'yyyy')
                     else
                         to_char(c.accident_date, 'yyyymm')
                     end                   as accident_node,
                 case
                     when rec.extraction_interval = 'Y' then
                         t.year::int - to_char(c.accident_date, 'yyyy')::int
                     else
                         months_between(to_date(t.year || t.month, 'yyyymm'),
                                        date_trunc('month', c.accident_date))
                     end                   as dev_no
          from tmp_buss_claim c,
               dmuser.dm_claim_outstanding t,
               dmuser.dm_buss_cmunit_fac_outwards cm
          where t.year || lpad(t.month, 2, '0') = p_buss.end_year_month
            and t.business_type_code = 'AF'
            and t.entity_id = rec.entity_id
            and t.entity_id = cm.entity_id
            and t.ri_policy_no = cm.fac_no
            and cm.year_month is not null
            and c.entity_id = t.entity_id
            and c.claim_no = t.claim_no
            and (v_ibnr_type is null or v_ibnr_type = '2')
            and (v_loa_code is null or v_loa_code = cm.loa_code)
          group by cm.portfolio_no, c.accident_date, t.year, t.month) x
    group by x.portfolio_no, x.accident_node, x.dev_no
    order by x.portfolio_no, x.accident_node, x.dev_no;


    ----  未决 TO
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_temp_os_to_start');

    insert into atr_temp_ibnrcalc_acc_dev_buss_amount (id, action_no, portfolio_no, accident_node, dev_no,
                                                       business_source_code, amount_type, amount)
    select nextval('atr_seq_temp_ibnrcalc_acc_dev_buss_amount') as id,
           p_action_no,
           x.portfolio_no,
           x.accident_node,
           x.dev_no,
           'TO'                                                 as business_source_code,
           'os'                                                 as amount_type,
           sum(x.amount)                                        as amount
    from (select cm.portfolio_no,
                 sum(t.outstanding_amount) as amount,
                 case
                     when rec.extraction_interval = 'Y' then
                         to_char(c.accident_date, 'yyyy')
                     else
                         to_char(c.accident_date, 'yyyymm')
                     end                   as accident_node,
                 case
                     when rec.extraction_interval = 'Y' then
                         t.year::int - to_char(c.accident_date, 'yyyy')::int
                     else
                         months_between(to_date(t.year || t.month, 'yyyymm'),
                                        date_trunc('month', c.accident_date))
                     end                   as dev_no
          from tmp_buss_claim c,
               dmuser.dm_claim_outstanding t,
               dmuser.dm_buss_cmunit_treaty cm
          where t.year || lpad(t.month, 2, '0') = p_buss.end_year_month
            and t.business_type_code = 'AF'
            and t.entity_id = rec.entity_id
            and t.entity_id = cm.entity_id
            and t.reference_no = cm.treaty_no
            and cm.ri_direction_code = 'O'
            and cm.year_month is not null
            and c.entity_id = t.entity_id
            and c.claim_no = t.claim_no
            and (v_ibnr_type is null or v_ibnr_type = '2')
            and (v_loa_code is null or v_loa_code = cm.loa_code)
          group by cm.portfolio_no, c.accident_date, t.year, t.month) x
    group by x.portfolio_no, x.accident_node, x.dev_no
    order by x.portfolio_no, x.accident_node, x.dev_no;


    -- 合同组合层级的信息
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_init_icp_start');

    insert into atr_buss_ibnrcalc_icp (id, action_no, portfolio_no, loa_code, business_source_code, ibnr_type)
    select nextval('atr_seq_buss_ibnrcalc_icp') as id,
           p_action_no,
           t.portfolio_no,
           (case
                when t.business_source_code = 'DD'
                    then (select cm.loa_code
                          from dmuser.dm_buss_cmunit_direct cm
                          where cm.portfolio_no = t.portfolio_no
                          limit 1)
                when t.business_source_code = 'FO'
                    then (select cm.loa_code
                          from dmuser.dm_buss_cmunit_fac_outwards cm
                          where cm.portfolio_no = t.portfolio_no
                          limit 1)
                when t.business_source_code = 'TO'
                    then (select cm.loa_code
                          from dmuser.dm_buss_cmunit_treaty cm
                          where cm.portfolio_no = t.portfolio_no
                          limit 1)
               end)                             as loa_code,
           t.business_source_code,
           (case
                when t.business_source_code = 'DD'
                    then '1'
                when t.business_source_code in ('FO', 'TO')
                    then '2' end)               as ibnr_type
    from atr_temp_ibnrcalc_acc_dev_buss_amount t
    where t.action_no = p_action_no
    group by t.business_source_code, t.portfolio_no
    order by t.business_source_code, t.portfolio_no;


    -- init acc dev data
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_init_acc_dev_start');

    if rec.extraction_interval = 'Y' then
        insert into atr_buss_ibnrcalc_acc_dev_data (id, action_no, icp_id, accident_node, dev_no)
        select nextval('atr_seq_buss_ibnrcalc_acc_dev_data'),
               p_action_no,
               t.id                            as icp_id,
               n.accident_node,
               y.y::int - n.accident_node::int as dev_no
        from atr_buss_ibnrcalc_icp t,
             atr_buss_ibnrcalc_accident_node n,
             (select distinct substr(year_month, 1, 4) as y from atr_conf_year_month) y
        where t.action_no = p_action_no
          and t.action_no = n.action_no
          and y.y >= n.accident_node
          and y.y <= p_buss.end_year
        order by t.id, n.accident_node, dev_no;
    else
        insert into atr_buss_ibnrcalc_acc_dev_data (id, action_no, icp_id, accident_node, dev_no)
        select nextval('atr_seq_buss_ibnrcalc_acc_dev_data'),
               p_action_no,
               t.id                                                                                 as icp_id,
               n.accident_node,
               months_between(to_date(ym.year_month, 'yyyymm'), to_date(n.accident_node, 'yyyymm')) as dev_no
        from atr_buss_ibnrcalc_icp t,
             atr_buss_ibnrcalc_accident_node n,
             (select * from atr_conf_year_month) ym
        where t.action_no = p_action_no
          and t.action_no = n.action_no
          and ym.year_month >= n.accident_node
          and ym.year_month <= p_buss.end_year_month
        order by t.id, n.accident_node, dev_no;
    end if;

    -- 合并已决、未决 ori
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_merge_settled_os_start');

    merge into atr_buss_ibnrcalc_acc_dev_data ac
    using (select p.id                                                       as icp_id,
                  t.accident_node,
                  t.dev_no,
                  sum(case when t.amount_type = 'settled' then t.amount end) as settled_amount,
                  sum(case when t.amount_type = 'os' then t.amount end)      as os_amount
           from atr_temp_ibnrcalc_acc_dev_buss_amount t,
                atr_buss_ibnrcalc_icp p
           where p.action_no = p_action_no
             and t.action_no = p.action_no
             and t.portfolio_no = p.portfolio_no
             and t.business_source_code = p.business_source_code
           group by p.id, t.accident_node, t.dev_no) x
    on (ac.action_no = p_action_no and ac.icp_id = x.icp_id
        and ac.accident_node = x.accident_node
        and ac.dev_no = x.dev_no)
    when matched then
        update
        set settled_amount_ori = x.settled_amount,
            os_amount          = x.os_amount;


    -- 通货膨胀比例
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_inf_ratio_start');

    insert into atr_buss_ibnrcalc_inflation_ratio (id, action_no, buss_year, dev_year, inf_ratio)
    with t_year as (select distinct substr(year_month, 1, 4) as y
                    from atr_conf_year_month
                    where year_month >= p_buss.start_year
                      and year_month <= p_buss.deadline_year || '99')
    select nextval('atr_seq_buss_ibnrcalc_inflation_ratio'),
           rec.action_no,
           t1.y                     as buss_year,
           t2.y                     as dev_year,
           coalesce(t.inf_ratio, 1) as inf_ratio
    from t_year t1
             inner join t_year t2
                        on t2.y >= t1.y
                            and t1.y <= p_buss.end_year
             left join atr_conf_inflation_ratio t
                       on t1.y = t.buss_year
                           and t2.y = t.dev_year
    order by t1.y, t2.y;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step1_end');

end ;
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_calc_step2(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    rec          record;
    v_max_dev_no smallint;
    p_buss       atr_pack_ibnrcalc_record_buss;
begin

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_start');

    delete from atr_buss_ibnrcalc_dev_data where action_no = p_action_no;
    delete from atr_buss_ibnrcalc_acc_result where action_no = p_action_no;

    select t.* into rec from atr_buss_ibnrcalc_action t where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_init_buss_record(p_action_no, p_buss);

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_settled_af_inf_start');

    -- 已决 (计算通胀)
    update atr_buss_ibnrcalc_acc_dev_data t
    set settled_amount = t.settled_amount_ori * coalesce((select r.inf_ratio
                                                          from atr_buss_ibnrcalc_inflation_ratio r
                                                          where r.buss_year = substr(t.accident_node, 1, 4)
                                                            and r.dev_year = p_buss.deadline_year
                                                            and r.action_no = p_action_no),
                                                         1)
    where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_settled_cumulative_start');

    -- 已决 (累计)
    update atr_buss_ibnrcalc_acc_dev_data t
    set settled_amount_cumulative = (select sum(t2.settled_amount)
                                     from atr_buss_ibnrcalc_acc_dev_data t2
                                     where t2.action_no = t.action_no
                                       and t2.icp_id = t.icp_id
                                       and t2.accident_node = t.accident_node
                                       and t2.dev_no <= t.dev_no)
    where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_dev_factor_start');

    -- 逐年进展因子
    merge into atr_buss_ibnrcalc_acc_dev_data ac
    using (select t.id,
                  t.settled_amount_cumulative,
                  (select t2.settled_amount_cumulative
                   from atr_buss_ibnrcalc_acc_dev_data t2
                   where t2.action_no = t.action_no
                     and t2.icp_id = t.icp_id
                     and t2.accident_node = t.accident_node
                     and t2.dev_no = t.dev_no + 1) as settled_amount_next
           from atr_buss_ibnrcalc_acc_dev_data t,
                atr_buss_ibnrcalc_accident_node n
           where t.action_no = n.action_no
             and t.accident_node = n.accident_node
             and t.action_no = p_action_no
             and t.dev_no < n.max_dev_no) x
    on (ac.id = x.id)
    when matched then
        update
        set dev_factor = case
                             when x.settled_amount_cumulative = 0 or x.settled_amount_cumulative is null then
                                 1
                             else
                                     x.settled_amount_next / x.settled_amount_cumulative
            end;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_init_dev_start');

    -- init dev data
    insert into atr_buss_ibnrcalc_dev_data (id, action_no, icp_id, dev_no)
    select nextval('atr_seq_buss_ibnrcalc_dev_data'),
           p_action_no,
           a.icp_id,
           a.dev_no
    from atr_buss_ibnrcalc_acc_dev_data a
    where a.action_no = p_action_no
    group by a.icp_id, a.dev_no
    order by a.icp_id, a.dev_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_dev_simple_start');

    -- 均值-Simple All
    update atr_buss_ibnrcalc_dev_data t
    set av_simple = (select avg(a.dev_factor)
                     from atr_buss_ibnrcalc_acc_dev_data a
                     where a.action_no = t.action_no
                       and a.icp_id = t.icp_id
                       and a.dev_no = t.dev_no)
    where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_dev_weighted_start');

    -- Weighted All
    merge into atr_buss_ibnrcalc_dev_data d
    using (select x.icp_id,
                  x.dev_no,
                  case
                      when sum(x.settled_amount_cumulative) = 0 or sum(x.settled_amount_cumulative) is null then
                          1
                      else
                              sum(x.settled_amount_next) / sum(x.settled_amount_cumulative)
                      end as av_weighted
           from (select t.icp_id,
                        t.dev_no,
                        t.settled_amount_cumulative,
                        (select t2.settled_amount_cumulative
                         from atr_buss_ibnrcalc_acc_dev_data t2
                         where t2.action_no = t.action_no
                           and t2.icp_id = t.icp_id
                           and t2.accident_node = t.accident_node
                           and t2.dev_no = t.dev_no + 1) as settled_amount_next
                 from atr_buss_ibnrcalc_acc_dev_data t,
                      atr_buss_ibnrcalc_icp p
                 where t.action_no = rec.action_no
                   and t.icp_id = p.id
                   and t.accident_node < (case
                                              when rec.extraction_interval = 'Y' then
                                                  (p_buss.end_year::int - t.dev_no)::varchar
                                              else
                                                  to_char(p_buss.end_bom - t.dev_no * '1 month'::interval,
                                                          'yyyymm')
                     end)) x
           group by x.icp_id, x.dev_no) x1
    on (d.action_no = p_action_no and d.icp_id = x1.icp_id and d.dev_no = x1.dev_no)
    when matched then
        update
        set av_weighted = x1.av_weighted;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_dev_last_start');

    -- last 2、 last 3、 选择值-Current
    for i in 2..3
        loop
            merge into atr_buss_ibnrcalc_dev_data d
            using (select x.icp_id,
                          x.dev_no,
                          avg(x.dev_factor) as dev_value
                   from (select t.action_no,
                                t.icp_id,
                                t.dev_no,
                                t.dev_factor
                         from atr_buss_ibnrcalc_acc_dev_data t,
                              atr_buss_ibnrcalc_icp p
                         where t.action_no = rec.action_no
                           and t.icp_id = p.id
                           and t.accident_node >= (case
                                                       when rec.extraction_interval = 'Y' then
                                                           (p_buss.end_year::int - i - t.dev_no)::varchar
                                                       else
                                                           to_char(p_buss.end_bom -
                                                                   (i + t.dev_no) * '1 month'::interval, 'yyyymm')
                             end)
                           and t.accident_node <= (case
                                                       when rec.extraction_interval = 'Y' then
                                                           (p_buss.end_year::int - 1 - t.dev_no)::varchar
                                                       else
                                                           to_char(p_buss.end_bom -
                                                                   (1 + t.dev_no) * '1 month'::interval, 'yyyymm')
                             end)) x
                   group by x.action_no, x.icp_id, x.dev_no) x1
            on (d.action_no = p_action_no and d.icp_id = x1.icp_id and d.dev_no = x1.dev_no)
            when matched then
                update
                set av_last2   = case when i = 2 then x1.dev_value else d.av_last2 end,
                    av_last3   = case when i = 3 then x1.dev_value else d.av_last3 end,
                    sl_current = case when i = 2 then x1.dev_value else d.av_last2 end;
        end loop;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_dev_cumulative_start');

    -- 选择值-Cumulative
    select coalesce(max(t.action_max_dev_no), -1)
    into v_max_dev_no
    from atr_buss_ibnrcalc_accident_node t
    where t.action_no = p_action_no;

    for i in 0..v_max_dev_no
        loop
            if i = 0 then
                update atr_buss_ibnrcalc_dev_data t
                set sl_cumulative = 1
                where t.action_no = p_action_no
                  and t.dev_no = v_max_dev_no;
            else
                update atr_buss_ibnrcalc_dev_data t
                set sl_cumulative = t.sl_current * (select t1.sl_cumulative
                                                    from atr_buss_ibnrcalc_dev_data t1
                                                    where t1.action_no = rec.action_no
                                                      and t1.icp_id = t.icp_id
                                                      and t1.dev_no = t.dev_no + 1)
                where t.action_no = p_action_no
                  and t.dev_no = (v_max_dev_no - i);
            end if;
        end loop;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_dev_expect_reported_start');

    -- 选择值-Expect % Reported
    update atr_buss_ibnrcalc_dev_data t
    set sl_expected_reported_ratio_ori = case
                                             when t.sl_cumulative = 0 or t.sl_cumulative is null then
                                                 1
                                             else
                                                     1 / t.sl_cumulative
        end
    where t.action_no = p_action_no;

    -- 选择值-Expect % Reported (用户手动调整)
    update atr_buss_ibnrcalc_dev_data t
    set sl_expected_reported_ratio = t.sl_expected_reported_ratio_ori
    where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step2_end');

end
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_calc_step3(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    rec    record;
    p_buss atr_pack_ibnrcalc_record_buss;
begin

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_start');

    delete from atr_buss_ibnrcalc_acc_result where action_no = p_action_no;

    select t.* into rec from atr_buss_ibnrcalc_action t where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_init_buss_record(p_action_no, p_buss);

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_init_result_start');

    -- init result
    insert into atr_buss_ibnrcalc_acc_result (id, action_no, icp_id, accident_node)
    select nextval('atr_seq_buss_ibnrcalc_acc_result'),
           p_action_no,
           p.id as icp_id,
           n.accident_node
    from atr_buss_ibnrcalc_accident_node n,
         atr_buss_ibnrcalc_icp p
    where n.action_no = p.action_no
      and p.action_no = p_action_no
    order by p.id, n.id;

    -- 已赚保费(自动计算）
    ---- DD
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_dd_ed_premium_start-01');

    drop table if exists tmp_ibnrcalc_prem_unit;

    create temp table tmp_ibnrcalc_prem_unit as
    select p.id                   as icp_id,
           p.loa_code,
           t.effective_date::date as effective_date,
           t.expiry_date::date    as expiry_date,
           sum(t.premium)         as premium
    from atr_buss_ibnrcalc_icp p,
         dmuser.dm_policy_main t,
         dmuser.dm_buss_cmunit_direct cm
    where p.action_no = p_action_no
      and p.business_source_code = 'DD'
      and t.effective_date < p_buss.end_next_day
      and t.expiry_date >= p_buss.start_bom
      and t.entity_id = rec.entity_id
      and t.entity_id = cm.entity_id
      and t.policy_no = cm.policy_no
      and cm.year_month <= p_buss.end_year_month
      and cm.loa_code = p.loa_code
      and cm.portfolio_no = p.portfolio_no
    group by p.id, p.loa_code, t.effective_date::date, t.expiry_date::date;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_dd_ed_premium_start-02');

    create index idx_tmp_ibnrcalc_prem_unit_icp_id on tmp_ibnrcalc_prem_unit (icp_id);

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_dd_ed_premium_start-03');

    merge into atr_buss_ibnrcalc_acc_result t
    using (select x.id,
                  sum((case
                           when coalesce(x.num_start, 0) = 0 then
                               case
                                   when x.num_end <> 0 then
                                       1 - x.num_end
                                   else
                                       0
                                   end
                           else x.num_start - x.num_end end) * x.premium) as ed_premium
           from (select r.id,
                        atr_pack_evaluate_func_evaluate_accrual(rec.entity_id, t.loa_code,
                                                                n.accident_date_start,
                                                                t.effective_date,
                                                                t.expiry_date) as num_start,
                        atr_pack_evaluate_func_evaluate_accrual(rec.entity_id, t.loa_code,
                                                                n.accident_date_end_next_day - '1 day'::interval,
                                                                t.effective_date,
                                                                t.expiry_date) as num_end,
                        sum(t.premium)                                         as premium
                 from atr_buss_ibnrcalc_acc_result r,
                      atr_buss_ibnrcalc_accident_node n,
                      tmp_ibnrcalc_prem_unit t
                 where r.action_no = p_action_no
                   and r.action_no = n.action_no
                   and r.accident_node = n.accident_node
                   and r.icp_id = t.icp_id
                 group by r.id, t.loa_code, n.accident_date_start, n.accident_date_end_next_day, t.effective_date,
                          t.expiry_date) x
           group by x.id) x1
    on t.id = x1.id
    when matched then
        update set ed_premium_ori = x1.ed_premium;

    ---- FO
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_fo_ed_premium_start-01');

    drop table if exists tmp_ibnrcalc_prem_unit;

    create temp table tmp_ibnrcalc_prem_unit as
    select p.id                   as icp_id,
           p.loa_code,
           t.effective_date::date as effective_date,
           t.expiry_date::date    as expiry_date,
           sum(d.amount)          as premium
    from atr_buss_ibnrcalc_icp p,
         dmuser.dm_reins_outward t,
         dmuser.dm_reins_outward_detail d,
         bpluser.bbs_conf_fee_type_mapping mp,
         dmuser.dm_buss_cmunit_fac_outwards cm
    where p.action_no = p_action_no
      and p.business_source_code = 'FO'
      and t.effective_date < p_buss.end_next_day
      and t.expiry_date >= p_buss.start_bom
      and t.entity_id = d.entity_id
      and t.ri_policy_no = d.ri_policy_no
      and t.ri_endorse_seq_no = d.ri_endorse_seq_no
      and mp.business_source_code = 'FO'
      and d.expenses_type_code = mp.expenses_type_code
      and mp.fee_class in ('Premium')
      and t.entity_id = rec.entity_id
      and t.entity_id = cm.entity_id
      and t.ri_policy_no = cm.fac_no
      and cm.year_month <= p_buss.end_year_month
      and cm.loa_code = p.loa_code
      and cm.portfolio_no = p.portfolio_no
    group by p.id, p.loa_code, t.effective_date::date, t.expiry_date::date;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_fo_ed_premium_start-02');

    create index idx_tmp_ibnrcalc_prem_unit_icp_id on tmp_ibnrcalc_prem_unit (icp_id);

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_fo_ed_premium_start-03');

    merge into atr_buss_ibnrcalc_acc_result t
    using (select x.id,
                  sum((case
                           when coalesce(x.num_start, 0) = 0 then
                               case
                                   when x.num_end <> 0 then
                                       1 - x.num_end
                                   else
                                       0
                                   end
                           else x.num_start - x.num_end end) * x.premium) as ed_premium
           from (select r.id,
                        atr_pack_evaluate_func_evaluate_accrual(rec.entity_id, t.loa_code,
                                                                n.accident_date_start,
                                                                t.effective_date,
                                                                t.expiry_date) as num_start,
                        atr_pack_evaluate_func_evaluate_accrual(rec.entity_id, t.loa_code,
                                                                n.accident_date_end_next_day - '1 day'::interval,
                                                                t.effective_date,
                                                                t.expiry_date) as num_end,
                        sum(t.premium)                                         as premium
                 from atr_buss_ibnrcalc_acc_result r,
                      atr_buss_ibnrcalc_accident_node n,
                      tmp_ibnrcalc_prem_unit t
                 where r.action_no = p_action_no
                   and r.action_no = n.action_no
                   and r.accident_node = n.accident_node
                   and r.icp_id = t.icp_id
                 group by r.id, t.loa_code, n.accident_date_start, n.accident_date_end_next_day, t.effective_date,
                          t.expiry_date) x
           group by x.id) x1
    on t.id = x1.id
    when matched then
        update set ed_premium_ori = x1.ed_premium;

    ---- TO
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_to_ed_premium_start-01');

    drop table if exists tmp_reins_ow;

    create temp table tmp_reins_ow as
    select d.treaty_no,
           t.effective_date::date as effective_date,
           t.expiry_date::date    as expiry_date,
           sum(d.amount)          as amount
    from dmuser.dm_reins_outward t,
         dmuser.dm_reins_outward_detail d,
         bpluser.bbs_conf_fee_type_mapping mp
    where t.effective_date < p_buss.end_next_day
      and t.expiry_date >= p_buss.start_bom
      and t.entity_id = d.entity_id
      and t.ri_policy_no = d.ri_policy_no
      and t.ri_endorse_seq_no = d.ri_endorse_seq_no
      and mp.business_source_code = 'TO'
      and d.expenses_type_code = mp.expenses_type_code
      and mp.fee_class = 'Premium'
      and t.entity_id = rec.entity_id
    group by d.treaty_no, t.effective_date::date, t.expiry_date::date;

    create index idx_tmp_reins_ow_treaty_no on tmp_reins_ow (treaty_no);

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_to_ed_premium_start-02');

    drop table if exists tmp_reins_ow_treaty;

    create temp table tmp_reins_ow_treaty as
    select distinct treaty_no from dmuser.dm_reins_outward_detail where entity_id = rec.entity_id;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_to_ed_premium_start-03');

    drop table if exists tmp_ibnrcalc_prem_unit;

    create temp table tmp_ibnrcalc_prem_unit as
    select x.icp_id,
           x.loa_code,
           x.effective_date,
           x.expiry_date,
           sum(x.premium) as premium
    from (select p.id             as icp_id,
                 p.loa_code,
                 t.effective_date as effective_date,
                 t.expiry_date    as expiry_date,
                 sum(t.amount)    as premium
          from atr_buss_ibnrcalc_icp p,
               tmp_reins_ow t,
               dmuser.dm_buss_cmunit_treaty cm
          where p.action_no = p_action_no
            and p.business_source_code = 'TO'
            and cm.entity_id = rec.entity_id
            and t.treaty_no = cm.treaty_no
            and cm.ri_direction_code = 'O'
            and cm.year_month <= p_buss.end_year_month
            and cm.loa_code = p.loa_code
            and cm.portfolio_no = p.portfolio_no
          group by p.id, p.loa_code, t.effective_date::date, t.expiry_date::date
          union all
          select p.id                    as icp_id,
                 p.loa_code,
                 cm.effective_date::date as effective_date,
                 cm.expiry_date::date    as expiry_date,
                 sum(d.amount)           as premium
          from atr_buss_ibnrcalc_icp p,
               dmuser.dm_buss_cmunit_treaty cm,
               dmuser.dm_reins_bill t,
               dmuser.dm_reins_bill_detail d,
               bpluser.bbs_conf_fee_type_mapping e
          where p.action_no = p_action_no
            and p.business_source_code = 'TO'
            and t.entity_id = rec.entity_id
            and cm.entity_id = t.entity_id
            and cm.entity_id = d.entity_id
            and cm.treaty_no = t.treaty_no
            and t.ri_statement_no = d.ri_statement_no
            and e.fee_class = 'Premium'
            and e.business_source_code = 'TO'
            and d.expenses_type_code = e.expenses_type_code
            and cm.ri_direction_code = 'O'
            and cm.effective_date < p_buss.end_next_day
            and cm.expiry_date >= p_buss.start_bom
            and not exists
              (select 1
               from tmp_reins_ow_treaty o
               where o.treaty_no = cm.treaty_no)
            and cm.year_month <= p_buss.end_year_month
            and cm.loa_code = p.loa_code
            and cm.portfolio_no = p.portfolio_no
          group by p.id, p.loa_code, cm.effective_date::date, cm.expiry_date::date) x
    group by x.icp_id,
             x.loa_code,
             x.effective_date,
             x.expiry_date;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_to_ed_premium_start-04');

    create index idx_tmp_ibnrcalc_prem_unit_icp_id on tmp_ibnrcalc_prem_unit (icp_id);

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_to_ed_premium_start-05');

    merge into atr_buss_ibnrcalc_acc_result t
    using (select x.id,
                  sum((case
                           when coalesce(x.num_start, 0) = 0 then
                               case
                                   when x.num_end <> 0 then
                                       1 - x.num_end
                                   else
                                       0
                                   end
                           else x.num_start - x.num_end end) * x.premium) as ed_premium
           from (select r.id,
                        atr_pack_evaluate_func_evaluate_accrual(rec.entity_id, t.loa_code,
                                                                n.accident_date_start,
                                                                t.effective_date,
                                                                t.expiry_date) as num_start,
                        atr_pack_evaluate_func_evaluate_accrual(rec.entity_id, t.loa_code,
                                                                n.accident_date_end_next_day - '1 day'::interval,
                                                                t.effective_date,
                                                                t.expiry_date) as num_end,
                        sum(t.premium)                                         as premium
                 from atr_buss_ibnrcalc_acc_result r,
                      atr_buss_ibnrcalc_accident_node n,
                      tmp_ibnrcalc_prem_unit t
                 where r.action_no = p_action_no
                   and r.action_no = n.action_no
                   and r.accident_node = n.accident_node
                   and r.icp_id = t.icp_id
                 group by r.id, t.loa_code, n.accident_date_start, n.accident_date_end_next_day, t.effective_date,
                          t.expiry_date) x
           group by x.id) x1
    on t.id = x1.id
    when matched then
        update set ed_premium_ori = x1.ed_premium;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_manual_ed_premium_start');

    -- ed_premium
    update atr_buss_ibnrcalc_acc_result t
    set ed_premium = t.ed_premium_ori
    where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_result_settled_os_reported_start');

    -- settled 、 os、 reported
    merge into atr_buss_ibnrcalc_acc_result t
    using (select t.icp_id,
                  t.accident_node,
                  sum(t.settled_amount)                                         as settled,
                  sum(t.os_amount)                                              as os,
                  sum(coalesce(t.settled_amount, 0) + coalesce(t.os_amount, 0)) as reported_amount
           from atr_buss_ibnrcalc_acc_dev_data t
           where t.action_no = p_action_no
           group by t.icp_id, t.accident_node) x
    on t.action_no = p_action_no and t.icp_id = x.icp_id and t.accident_node = x.accident_node
    when matched then
        update
        set setteld_amount  = x.settled,
            os_amount       = x.os,
            reported_amount = x.reported_amount;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_result_reported_loss_ratio_start');

    -- Reported Loss Ratio
    update atr_buss_ibnrcalc_acc_result t
    set reported_loss_ratio =
            case
                when t.ed_premium = 0 or t.ed_premium is null then
                    1
                else
                    abs(t.setteld_amount / t.ed_premium)
                end
    where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_result_expected_reported_start');

    -- Expected % Reported
    update atr_buss_ibnrcalc_acc_result t
    set expected_reported_ratio = (select d.sl_expected_reported_ratio
                                   from atr_buss_ibnrcalc_dev_data d,
                                        atr_buss_ibnrcalc_accident_node n
                                   where d.action_no = p_action_no
                                     and n.action_no = p_action_no
                                     and d.icp_id = t.icp_id
                                     and n.accident_node = t.accident_node
                                     and d.dev_no = n.max_dev_no)
    where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_result_lr_method_start');

    -- LR 算法
    ----- Expected Loss Ratio
    update atr_buss_ibnrcalc_acc_result t
    set lr_expected_loss_ratio = t.reported_loss_ratio
    where t.action_no = p_action_no;

    ----- Ultimate Loss
    update atr_buss_ibnrcalc_acc_result t
    set lr_ultimate_loss = t.lr_expected_loss_ratio * t.ed_premium
    where t.action_no = p_action_no;

    ----- IBNR
    update atr_buss_ibnrcalc_acc_result t
    set lr_ibnr = t.lr_ultimate_loss - t.setteld_amount
    where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_result_cl_method_start');

    -- CL 算法
    ----- Proj. Reported Ultimate
    update atr_buss_ibnrcalc_acc_result t
    set cl_proj_reported_ultimate = t.setteld_amount / t.expected_reported_ratio
    where t.action_no = p_action_no;

    ----- IBNR
    update atr_buss_ibnrcalc_acc_result t
    set cl_ibnr = t.cl_proj_reported_ultimate - t.setteld_amount
    where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_result_bf_method_start');

    -- BF 算法
    ----- Expected Loss Ratio
    update atr_buss_ibnrcalc_acc_result t
    set bf_expected_loss_ratio = t.reported_loss_ratio
    where t.action_no = p_action_no;

    ----- Proj. Reported Ultimate
    update atr_buss_ibnrcalc_acc_result t
    set bf_proj_reported_ultimate = t.setteld_amount +
                                    t.ed_premium * t.bf_expected_loss_ratio * (1 - t.expected_reported_ratio)
    where t.action_no = p_action_no;

    ----- IBNR
    update atr_buss_ibnrcalc_acc_result t
    set bf_ibnr = t.bf_proj_reported_ultimate - t.setteld_amount
    where t.action_no = p_action_no;

    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step3_end');

end;
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_calc_step3_manual(IN p_action_no character varying, IN p_icp_id bigint, IN p_edpremium_change_is character varying)
    LANGUAGE plpgsql
    AS $$
begin

    if p_edpremium_change_is = '1' then
        -- Reported Loss Ratio
        update atr_buss_ibnrcalc_acc_result t
        set reported_loss_ratio =
                case
                    when t.ed_premium = 0 or t.ed_premium is null then
                        1
                    else
                        abs(t.setteld_amount / t.ed_premium)
                    end
        where t.action_no = p_action_no
          and t.icp_id = p_icp_id;

        update atr_buss_ibnrcalc_acc_result t
        set lr_expected_loss_ratio = t.reported_loss_ratio,
            bf_expected_loss_ratio = t.reported_loss_ratio
        where t.action_no = p_action_no
          and t.icp_id = p_icp_id;

    end if;

    -- LR 算法
    ----- Ultimate Loss
    update atr_buss_ibnrcalc_acc_result t
    set lr_ultimate_loss = t.lr_expected_loss_ratio * t.ed_premium
    where t.action_no = p_action_no
      and t.icp_id = p_icp_id;

    ----- IBNR
    update atr_buss_ibnrcalc_acc_result t
    set lr_ibnr = t.lr_ultimate_loss - t.setteld_amount
    where t.action_no = p_action_no
      and t.icp_id = p_icp_id;

    -- BF 算法
    ----- Proj. Reported Ultimate
    update atr_buss_ibnrcalc_acc_result t
    set bf_proj_reported_ultimate = t.setteld_amount +
                                    t.ed_premium * t.bf_expected_loss_ratio * (1 - t.expected_reported_ratio)
    where t.action_no = p_action_no
      and t.icp_id = p_icp_id;

    ----- IBNR
    update atr_buss_ibnrcalc_acc_result t
    set bf_ibnr = t.bf_proj_reported_ultimate - t.setteld_amount
    where t.action_no = p_action_no;

end;
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_calc_step4_acc_trace(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    rec  record;
    v_id bigint;
begin

    for rec in (select t               as metod_type,
                       case
                           when t = 'LR' then r.lr_ibnr
                           when t = 'CL' then r.cl_ibnr
                           when t = 'BF' then r.bf_ibnr
                           end         as ibnr,
                       r.icp_id        as icp_id,
                       r.accident_node as accident_node,
                       r.os_amount as os_amount

                from atr_buss_ibnrcalc_acc_result r,
                     string_to_table('LR,CL,BF', ',') t
                where action_no = p_action_no
                order by id)
        loop


            --  OS - Case Reserve
            insert into atr_buss_ibnrcalc_final_acc_trace (id, action_no, icp_id, method_type, accident_node,
                                                           trace_type, factor, amount, ibnr, os)
            select nextval('atr_seq_buss_ibnrcalc_final_acc_trace'),
                   p_action_no,
                   rec.icp_id,
                   rec.metod_type,
                   rec.accident_node,
                   'OS',
                   null,
                   rec.os_amount,
                   null,
                   rec.os_amount;


            --  IBNR - IBNR
            insert into atr_buss_ibnrcalc_final_acc_trace (id, action_no, icp_id, method_type, accident_node,
                                                           trace_type, factor, amount, ibnr, os)
            select nextval('atr_seq_buss_ibnrcalc_final_acc_trace'),
                   p_action_no,
                   rec.icp_id,
                   rec.metod_type,
                   rec.accident_node,
                   'IBNR',
                   null,
                   rec.ibnr,
                   rec.ibnr,
                   null;


            --  ICHE - Inderect Claim Handling Expense
            ---- factor
            v_id := nextval('atr_seq_buss_ibnrcalc_final_acc_trace');

            insert into atr_buss_ibnrcalc_final_acc_trace (id, action_no, icp_id, method_type, accident_node,
                                                           trace_type, factor, amount, ibnr, os)
            select v_id,
                   p_action_no,
                   rec.icp_id,
                   rec.metod_type,
                   rec.accident_node,
                   'ICHE',
                   (select t.factor
                    from atr_buss_ibnrcalc_final_trace t
                    where t.action_no = p_action_no
                      and t.icp_id = rec.icp_id
                      and t.method_type = rec.metod_type
                      and t.trace_type = 'ICHE') as factor,
                   null,
                   null,
                   null;

            ---- ibnr、 os
            merge into atr_buss_ibnrcalc_final_acc_trace t
            using (select sum(ibnr) as ibnr, sum(os) as os
                   from atr_buss_ibnrcalc_final_acc_trace t0
                   where t0.action_no = p_action_no
                     and t0.icp_id = rec.icp_id
                     and t0.method_type = rec.metod_type
                     and t0.accident_node = rec.accident_node
                     and t0.trace_type in ('IBNR', 'OS')) x
            on (t.id = v_id)
            when matched then
                update
                set ibnr = t.factor * x.ibnr,
                    os   = t.factor * x.os * 0.5;

            ---- amount
            update atr_buss_ibnrcalc_final_acc_trace t
            set amount = t.ibnr + t.os
            where t.id = v_id;


            --  BSCL - Best estimate claim liabilities
            v_id := nextval('atr_seq_buss_ibnrcalc_final_acc_trace');

            insert into atr_buss_ibnrcalc_final_acc_trace (id, action_no, icp_id, method_type, accident_node,
                                                           trace_type, factor, amount, ibnr, os)
            select v_id,
                   p_action_no,
                   rec.icp_id,
                   rec.metod_type,
                   rec.accident_node,
                   'BSCL',
                   null,
                   sum(t0.amount) as amount,
                   sum(t0.ibnr)   as ibnr,
                   sum(t0.os)     as os
            from atr_buss_ibnrcalc_final_acc_trace t0
            where t0.action_no = p_action_no
              and t0.icp_id = rec.icp_id
              and t0.method_type = rec.metod_type
              and t0.accident_node = rec.accident_node
              and t0.trace_type in ('IBNR', 'OS', 'ICHE');


            --  TVD - Time value discount
            ---- factor
            v_id := nextval('atr_seq_buss_ibnrcalc_final_acc_trace');

            insert into atr_buss_ibnrcalc_final_acc_trace (id, action_no, icp_id, method_type, accident_node,
                                                           trace_type, factor, amount, ibnr, os)
            select v_id,
                   p_action_no,
                   rec.icp_id,
                   rec.metod_type,
                   rec.accident_node,
                   'TVD',
                   (select t.factor
                    from atr_buss_ibnrcalc_final_trace t
                    where t.action_no = p_action_no
                      and t.icp_id = rec.icp_id
                      and t.method_type = rec.metod_type
                      and t.trace_type = 'TVD') as factor,
                   null,
                   null,
                   null;

            ---- IBNR、OS
            merge into atr_buss_ibnrcalc_final_acc_trace t
            using (select sum(ibnr) as ibnr, sum(os) as os
                   from atr_buss_ibnrcalc_final_acc_trace t0
                   where t0.action_no = p_action_no
                     and t0.icp_id = rec.icp_id
                     and t0.method_type = rec.metod_type
                     and t0.accident_node = rec.accident_node
                     and t0.trace_type in ('BSCL')) x
            on (t.id = v_id)
            when matched then
                update
                set ibnr = t.factor * x.ibnr,
                    os   = t.factor * x.os;

            ---- amount
            update atr_buss_ibnrcalc_final_acc_trace t
            set amount = t.ibnr + t.os
            where t.id = v_id;

            --  DBCL - Discounted BE claim liabilities
            v_id := nextval('atr_seq_buss_ibnrcalc_final_acc_trace');

            insert into atr_buss_ibnrcalc_final_acc_trace (id, action_no, icp_id, method_type, accident_node,
                                                           trace_type, factor, amount, ibnr, os)
            select v_id,
                   p_action_no,
                   rec.icp_id,
                   rec.metod_type,
                   rec.accident_node,
                   'DBCL',
                   null,
                   sum(case when t0.trace_type = 'BSCL' then t0.amount end)
                       - sum(case when t0.trace_type = 'TVD' then t0.amount end) as amount,
                   sum(case when t0.trace_type = 'BSCL' then t0.ibnr end)
                       - sum(case when t0.trace_type = 'TVD' then t0.ibnr end)   as ibnr,
                   sum(case when t0.trace_type = 'BSCL' then t0.os end)
                       - sum(case when t0.trace_type = 'TVD' then t0.os end)     as os
            from atr_buss_ibnrcalc_final_acc_trace t0
            where t0.action_no = p_action_no
              and t0.icp_id = rec.icp_id
              and t0.method_type = rec.metod_type
              and t0.accident_node = rec.accident_node
              and t0.trace_type in ('BSCL', 'TVD');

            --  PAD - PAD
            v_id := nextval('atr_seq_buss_ibnrcalc_final_acc_trace');

            insert into atr_buss_ibnrcalc_final_acc_trace (id, action_no, icp_id, method_type, accident_node,
                                                           trace_type, factor, amount, ibnr, os)
            select v_id,
                   p_action_no,
                   rec.icp_id,
                   rec.metod_type,
                   rec.accident_node,
                   'PAD',
                   (select t.factor
                    from atr_buss_ibnrcalc_final_trace t
                    where t.action_no = p_action_no
                      and t.icp_id = rec.icp_id
                      and t.method_type = rec.metod_type
                      and t.trace_type = 'PAD') as factor,
                   null,
                   null,
                   null;

            merge into atr_buss_ibnrcalc_final_acc_trace t
            using (select sum(ibnr) as ibnr, sum(os) as os
                   from atr_buss_ibnrcalc_final_acc_trace t0
                   where t0.action_no = p_action_no
                     and t0.icp_id = rec.icp_id
                     and t0.method_type = rec.metod_type
                     and t0.accident_node = rec.accident_node
                     and t0.trace_type in ('DBCL')) x
            on (t.id = v_id)
            when matched then
                update
                set ibnr = t.factor * x.ibnr,
                    os   = t.factor * x.os;

            update atr_buss_ibnrcalc_final_acc_trace t
            set amount = t.ibnr + t.os
            where t.id = v_id;

            v_id := nextval('atr_seq_buss_ibnrcalc_final_acc_trace');
            --  CL - Claim Liabilities
            insert into atr_buss_ibnrcalc_final_acc_trace (id, action_no, icp_id, method_type, accident_node,
                                                           trace_type, factor, amount, ibnr, os)
            select v_id,
                   p_action_no,
                   rec.icp_id,
                   rec.metod_type,
                   rec.accident_node,
                   'CL',
                   null,
                   sum(t0.amount) as amount,
                   sum(t0.ibnr)   as ibnr,
                   sum(t0.os)     as os
            from atr_buss_ibnrcalc_final_acc_trace t0
            where t0.action_no = p_action_no
              and t0.icp_id = rec.icp_id
              and t0.method_type = rec.metod_type
              and t0.accident_node = rec.accident_node
              and t0.trace_type in ('DBCL', 'PAD');


            v_id := nextval('atr_seq_buss_ibnrcalc_final_acc_trace');
            --  IBNR_T - IBNR TOTAL
            insert into atr_buss_ibnrcalc_final_acc_trace (id, action_no, icp_id, method_type, accident_node,
                                                           trace_type, factor, amount)
            select v_id,
                   p_action_no,
                   rec.icp_id,
                   rec.metod_type,
                   rec.accident_node,
                   'IBNR_T',
                   null,
                   sum(case when t0.trace_type = 'CL' then t0.amount end)
                       - sum(case when t0.trace_type = 'OS' then t0.amount end) as amount
            from atr_buss_ibnrcalc_final_acc_trace t0
            where t0.action_no = p_action_no
              and t0.icp_id = rec.icp_id
              and t0.method_type = rec.metod_type
              and t0.accident_node = rec.accident_node
              and t0.trace_type in ('OS', 'CL');

        end loop;

    commit;

end ;
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_calc_step4(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    param_record record;
    action_info  record;
    dev_count    bigint;
    item_count   bigint := 0;
begin
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step4_start');

    -- 获取发展期数

    delete from atr_buss_ibnrcalc_acc_future_data where action_no = p_action_no;
    -- 清空轨迹表数据
    delete from atr_buss_ibnrcalc_final_trace where action_no = p_action_no;
    -- 获取发展期期数
    select *
    into action_info
    from atr_buss_ibnrcalc_action
    where action_no = p_action_no;
    -- 获取发展期数量
    select count(*)
    into dev_count
    from atr_buss_ibnrcalc_acc_result
    where action_no = p_action_no
    group by icp_id
    limit 1;
    -- 获取理赔费用所占比例
    merge into atr_buss_ibnrcalc_icp abii
    using (select sum(case when t.amount_type = 'settled' then t.expense end) settled_expense,
                  sum(case when t.amount_type = 'settled' then t.amount end)  settled_claim,
                  sum(case when t.amount_type = 'os' then t.amount end)       os_amount,
                  action_no,
                  portfolio_no,
                  business_source_code
           from atr_temp_ibnrcalc_acc_dev_buss_amount t
           where t.action_no = p_action_no
           group by action_no, portfolio_no, business_source_code) t
    on abii.action_no = t.action_no
        and abii.portfolio_no = t.portfolio_no
        and abii.business_source_code = t.business_source_code
    when matched then
        update
        set settled_expense = coalesce(t.settled_expense, 0),
            settled_claim   = coalesce(t.settled_claim, 0),
            os_amount       = coalesce(t.os_amount, 0),
            incurred_amount = coalesce(t.settled_claim, 0) + coalesce(t.os_amount, 0),
            expenses_ratio  = case
                                  when coalesce(t.settled_claim, 0) + coalesce(t.os_amount, 0) <> 0 then
                                      coalesce(t.settled_expense, 0) /
                                      (coalesce(t.settled_claim, 0) + coalesce(t.os_amount, 0))
                                  else 0
                end;

    -- 更新Unpaid Claim信息
    merge into atr_buss_ibnrcalc_acc_result c
    using (select a.action_no,
                  a.icp_id,
                  a.accident_node,
                  (coalesce(a.cl_ibnr, 0) + coalesce(a.os_amount, 0)) as cl_amount,
                  (coalesce(a.bf_ibnr, 0) + coalesce(a.os_amount, 0)) as bf_amount,
                  (coalesce(a.lr_ibnr, 0) + coalesce(a.os_amount, 0)) as lr_amount
           from atr_buss_ibnrcalc_acc_result a
           where a.action_no = p_action_no) q
    on c.action_no = q.action_no and c.icp_id = q.icp_id and c.accident_node = q.accident_node
    when matched then
        update
        set lr_unpaid_amount = q.lr_amount,
            bf_unpaid_amount = q.bf_amount,
            cl_unpaid_amount = q.cl_amount;
    -- reported in 计算 查询发展期参数，以分组维度从发展期0开始计算
    -- 获取分组信息
    drop table if exists ibnrcalc_param_future_data_temp;
    create temporary table ibnrcalc_param_future_data_temp as
    SELECT a.action_no,
           a.future_node,
           a.icp_id,
           (1 - a.sl_expected_reported_ratio) as                          param2,
           LEAD(a.sl_expected_reported_ratio)
           OVER (PARTITION BY a.icp_id ORDER BY a.dev_no) -
           a.sl_expected_reported_ratio       AS                          param1,
           ((row_number() over (order by icp_id,dev_no) - 1) % dev_count) dev_no
    from atr_buss_ibnrcalc_dev_data a
    where a.action_no = p_action_no
    order by icp_id, a.dev_no;
    for param_record in select a.*, b.param2 param2, b.param1 param1
                        from (WITH numbers AS (SELECT generate_series(0, dev_count - 1) AS num)
                              SELECT a.action_no,
                                     a.accident_node,
                                     a.icp_id,
                                     cl_unpaid_amount                                                      cluc,
                                     bf_unpaid_amount                                                      bfuc,
                                     lr_unpaid_amount                                                      lruc,
                                     ((row_number() over (order by icp_id,accident_node) - 1) % dev_count) dev_no,
                                     (select max(accident_node)
                                      from atr_buss_ibnrcalc_acc_result
                                      where action_no = p_action_no)                                       max_accident_node
                              from atr_buss_ibnrcalc_acc_result a
                                       left join numbers on 1 = 1
                              where a.action_no = p_action_no
                              order by icp_id) a
                                 inner join ibnrcalc_param_future_data_temp b
                                            on a.action_no = b.action_no and a.icp_id = b.icp_id and a.dev_no = b.dev_no
                        order by icp_id, accident_node, dev_no
        loop
            insert into atr_buss_ibnrcalc_acc_future_data(id, action_no, icp_id, accident_node, dev_no,
                                                          future_node,
                                                          lr_reported_amount, cl_reported_amount,
                                                          bf_reported_amount)
            values (nextval('atr_seq_buss_ibnrcalc_acc_future_data'), param_record.action_no,
                    param_record.icp_id,
                    param_record.accident_node,
                    param_record.dev_no, case
                                             when action_info.extraction_interval = 'Y'
                                                 then CAST((param_record.max_accident_node::int + param_record.dev_no + 1) AS VARCHAR)
                                             when action_info.extraction_interval = 'M'
                                                 then to_char(
                                                     to_date(param_record.max_accident_node, 'yyyymm') +
                                                     INTERVAL '1 months' * (param_record.dev_no + 1),
                                                     'yyyymm') end, case
                                                                        when param_record.param2 = 0
                                                                            then 0
                                                                        when param_record.dev_no = dev_count - 1
                                                                            then (select (param_record.lruc - coalesce(sum(lr_reported_amount), 0))
                                                                                  from atr_buss_ibnrcalc_acc_future_data
                                                                                  where action_no = param_record.action_no
                                                                                    and icp_id = param_record.icp_id
                                                                                    and accident_node = param_record.accident_node)
                                                                        else (select param_record.param1 /
                                                                                     param_record.param2 *
                                                                                     (param_record.lruc - coalesce(sum(lr_reported_amount), 0))
                                                                              from atr_buss_ibnrcalc_acc_future_data
                                                                              where action_no = param_record.action_no
                                                                                and icp_id = param_record.icp_id
                                                                                and accident_node = param_record.accident_node) end,
                    case
                        when param_record.param2 = 0
                            then 0
                        when param_record.dev_no = dev_count - 1
                            then (select (param_record.cluc - coalesce(sum(cl_reported_amount), 0))
                                  from atr_buss_ibnrcalc_acc_future_data
                                  where action_no = param_record.action_no
                                    and icp_id = param_record.icp_id
                                    and accident_node = param_record.accident_node)
                        else (select param_record.param1 / param_record.param2 *
                                     (param_record.cluc - coalesce(sum(cl_reported_amount), 0))
                              from atr_buss_ibnrcalc_acc_future_data
                              where action_no = param_record.action_no
                                and icp_id = param_record.icp_id
                                and accident_node = param_record.accident_node) end, case
                                                                                         when param_record.param2 = 0
                                                                                             then 0
                                                                                         when param_record.dev_no = dev_count - 1
                                                                                             then (select (param_record.bfuc - coalesce(sum(bf_reported_amount), 0))
                                                                                                   from atr_buss_ibnrcalc_acc_future_data
                                                                                                   where action_no = param_record.action_no
                                                                                                     and icp_id = param_record.icp_id
                                                                                                     and accident_node = param_record.accident_node)
                                                                                         else (select
                                                                                                   param_record.param1 /
                                                                                                   param_record.param2 *
                                                                                                   (param_record.bfuc - coalesce(sum(bf_reported_amount), 0))
                                                                                               from atr_buss_ibnrcalc_acc_future_data
                                                                                               where action_no = param_record.action_no
                                                                                                 and icp_id = param_record.icp_id
                                                                                                 and accident_node = param_record.accident_node) end);
            item_count = item_count + 1;
        end loop;
    -- 更新atr_buss_ibnrcalc_d  ev_dat中的unpaid_amount的值
    merge into atr_buss_ibnrcalc_dev_data abidd
    using (select a.*, interest_ratio, time_period
           from (select a.action_no,
                        a.icp_id,
                        a.dev_no,
                        future_node,
                        sum(a.lr_reported_amount) lr_reported_amount,
                        sum(a.cl_reported_amount) cl_reported_amount,
                        sum(a.bf_reported_amount) bf_reported_amount
                 from atr_buss_ibnrcalc_acc_future_data a

                 group by a.action_no,
                          a.icp_id,
                          a.dev_no, future_node) a
                    inner join (select b.id, c.dev_no, interest_ratio, time_period
                                from atr_buss_ibnrcalc_icp b
                                         inner join atr_conf_ibnrcalc_quota_dev c on b.loa_code = c.loa_code) q
                               on a.icp_id = q.id and q.dev_no = a.dev_no) t
    on abidd.action_no = t.action_no and abidd.icp_id = t.icp_id and abidd.dev_no = t.dev_no and
       abidd.action_no = p_action_no
    when matched then
        update
        set future_node                 = t.future_node,
            lr_interest_ratio           = t.interest_ratio,
            cl_interest_ratio           = t.interest_ratio,
            bf_interest_ratio           = t.interest_ratio,
            time_period                 = t.time_period
                ,
            lr_discounted_unpaid_amount = t.lr_reported_amount / power(1 + t.interest_ratio, t.time_period)
                ,
            cl_discounted_unpaid_amount = t.cl_reported_amount / power(1 + t.interest_ratio, t.time_period)
                ,
            bf_discounted_unpaid_amount = t.bf_reported_amount / power(1 + t.interest_ratio, t.time_period);
    -- 计算time_value_discount 的值
    merge into atr_buss_ibnrcalc_icp a
    using (select a.action_no,
                  a.icp_id,
                  case
                      when a.lr_reported_amount != 0
                          then
                          1 - (b.lr_discounted_unpaid_amount / a.lr_reported_amount)
                      else 1 end lr_time_value_discount,
                  case
                      when a.cl_reported_amount != 0 then 1 - (b.cl_discounted_unpaid_amount / a.cl_reported_amount)
                      else 1 end cl_time_value_discount,
                  case
                      when a.bf_reported_amount != 0 then 1 - (b.bf_discounted_unpaid_amount / a.bf_reported_amount)
                      else 1 end bf_time_value_discount,
                  lr_discounted_unpaid_amount,
                  cl_discounted_unpaid_amount,
                  bf_discounted_unpaid_amount
           from (select action_no,
                        icp_id,
                        sum(lr_reported_amount) lr_reported_amount,
                        sum(cl_reported_amount) cl_reported_amount,
                        sum(bf_reported_amount) bf_reported_amount
                 from atr_buss_ibnrcalc_acc_future_data
                 where action_no = p_action_no
                 group by action_no, icp_id) a
                    inner join (select action_no,
                                       icp_id,
                                       sum(lr_discounted_unpaid_amount) lr_discounted_unpaid_amount,
                                       sum(cl_discounted_unpaid_amount) cl_discounted_unpaid_amount,
                                       sum(bf_discounted_unpaid_amount) bf_discounted_unpaid_amount
                                from atr_buss_ibnrcalc_dev_data
                                where action_no = p_action_no
                                group by action_no, icp_id) b on a.action_no = b.action_no and a.icp_id = b.icp_id) q
    on a.action_no = q.action_no and a.id = q.icp_id
    when matched then
        update
        set lr_time_value_discount      = q.lr_time_value_discount,
            bf_time_value_discount      = q.bf_time_value_discount,
            cl_time_value_discount      = q.cl_time_value_discount,
            lr_discounted_unpaid_amount = q.lr_discounted_unpaid_amount,
            cl_discounted_unpaid_amount = q.cl_discounted_unpaid_amount,
            bf_discounted_unpaid_amount = q.bf_discounted_unpaid_amount;
    -- 插入ibnr和os的轨迹值
    for i in 0..1
        loop
            insert into atr_buss_ibnrcalc_final_trace (id, action_no, icp_id, ibnr, os, display_no, method_type,
                                                       trace_type,
                                                       amount)
            select nextval('atr_seq_buss_ibnrcalc_final_trace'),
                   action_no,
                   icp_id,
                   case when i = 1 then coalesce(sum(lr_ibnr), 0) else 0 end,
                   case when i = 0 then coalesce(sum(os_amount), 0) else 0 end,
                   i,
                   'LR',
                   case when i = 0 then 'OS' else 'IBNR' end,
                   case when i = 0 then coalesce(sum(os_amount), 0) else coalesce(sum(lr_ibnr), 0) end
            from atr_buss_ibnrcalc_acc_result
            where action_no = p_action_no
            group by icp_id, action_no
            union all
            select nextval('atr_seq_buss_ibnrcalc_final_trace'),
                   action_no,
                   icp_id,
                   case when i = 1 then sum(bf_ibnr) else null end,
                   case when i = 0 then sum(os_amount) else null end,
                   i,
                   'BF',
                   case when i = 0 then 'OS' else 'IBNR' end,
                   case when i = 0 then sum(os_amount) else sum(bf_ibnr) end
            from atr_buss_ibnrcalc_acc_result
            where action_no = p_action_no
            group by icp_id, action_no
            union all
            select nextval('atr_seq_buss_ibnrcalc_final_trace'),
                   action_no,
                   icp_id,
                   case when i = 1 then sum(cl_ibnr) else null end,
                   case when i = 0 then sum(os_amount) else null end,
                   i,
                   'CL',
                   case when i = 0 then 'OS' else 'IBNR' end,
                   case when i = 0 then sum(os_amount) else sum(cl_ibnr) end
            from atr_buss_ibnrcalc_acc_result
            where action_no = p_action_no
            group by icp_id, action_no;
        end loop;
    --  display 从2开始
    -- 计算 Inderect Claim Handling Expense 的值
    insert into atr_buss_ibnrcalc_final_trace(id, action_no, icp_id, method_type, display_no, trace_type, factor, os,
                                              ibnr, amount)

    select a.*, b.ibnr, a.os + b.ibnr
    from (select nextval('atr_seq_buss_ibnrcalc_final_trace'),
                 a.action_no,
                 a.icp_id,
                 a.method_type,
                 10,
                 'ICHE',
                 b.expenses_ratio,
                 coalesce(a.os, 0) * coalesce(b.expenses_ratio, 0) * 0.5 os
          from atr_buss_ibnrcalc_final_trace a
                   inner join atr_buss_ibnrcalc_icp b on a.action_no = b.action_no and a.icp_id = b.id
          where upper(a.trace_type) = 'OS') a
             inner join (select a.action_no,
                                a.icp_id,
                                a.method_type,
                                a.ibnr * b.expenses_ratio ibnr
                         from atr_buss_ibnrcalc_final_trace a
                                  inner join atr_buss_ibnrcalc_icp b on a.action_no = b.action_no and a.icp_id = b.id
                         where upper(a.trace_type) = 'IBNR') b
                        on a.action_no = b.action_no and a.icp_id = b.icp_id and a.method_type = b.method_type
    where a.action_no = p_action_no;
    -- 计算 Best estimate claim liabilities 的值
    insert into atr_buss_ibnrcalc_final_trace(id, action_no, icp_id, method_type, display_no, trace_type, factor,
                                              amount,
                                              ibnr, os)
    select nextval('atr_seq_buss_ibnrcalc_final_trace'),
           action_no,
           icp_id,
           method_type,
           20,
           'BSCL',
           null,
           coalesce(sum(ibnr), 0) + coalesce(sum(os), 0),
           coalesce(sum(ibnr), 0),
           coalesce(sum(os), 0)
    from atr_buss_ibnrcalc_final_trace
    where upper(trace_type) in ('ICHE', 'OS', 'IBNR')
      and action_no = p_action_no
    group by action_no, icp_id, method_type;
    -- 计算 Time value discount的值
    insert into atr_buss_ibnrcalc_final_trace(id, action_no, icp_id, method_type, display_no, trace_type, factor,
                                              ibnr, os, amount)

    select nextval('atr_seq_buss_ibnrcalc_final_trace'),
           a.action_no,
           a.id,
           a.method_type,
           30,
           'TVD',
           a.time_value_discount,
           coalesce(b.ibnr, 0) * coalesce(time_value_discount, 0),
           coalesce(b.os, 0) * coalesce(time_value_discount, 0),
           coalesce(b.ibnr, 0) * coalesce(time_value_discount, 0) + coalesce(b.os, 0) * coalesce(time_value_discount, 0)
    from (select action_no, id, lr_time_value_discount time_value_discount, 'LR' method_type
          from atr_buss_ibnrcalc_icp
          union all
          select action_no, id, cl_time_value_discount time_value_discount, 'CL' method_type
          from atr_buss_ibnrcalc_icp
          union all
          select action_no, id, bf_time_value_discount time_value_discount, 'BF' method_type
          from atr_buss_ibnrcalc_icp) a
             inner join atr_buss_ibnrcalc_final_trace b
                        on a.action_no = b.action_no and a.id = b.icp_id and a.method_type = b.method_type
    where b.trace_type = 'BSCL'
      and b.action_no = p_action_no;
    -- 计算 Discounted BE claim liabilities
    insert into atr_buss_ibnrcalc_final_trace(id, action_no, icp_id, method_type, display_no, trace_type, factor,
                                              amount,
                                              ibnr, os)
    select nextval('atr_seq_buss_ibnrcalc_final_trace'),
           a.action_no,
           a.icp_id,
           a.method_type,
           40,
           'DBCL',
           null,
           coalesce(a.ibnr, 0) + coalesce(a.os, 0) - coalesce(b.ibnr, 0) - coalesce(b.os, 0),
           coalesce(a.ibnr, 0) - coalesce(b.ibnr, 0),
           coalesce(a.os, 0) - coalesce(b.os, 0)
    from atr_buss_ibnrcalc_final_trace a
             inner join atr_buss_ibnrcalc_final_trace b
                        on a.action_no = b.action_no and a.method_type = b.method_type and a.icp_id = b.icp_id and
                           a.action_no = p_action_no
    where a.trace_type = 'BSCL'
      and b.trace_type = 'TVD';
    -- 计算 PAD
    insert into atr_buss_ibnrcalc_final_trace(id, action_no, icp_id, method_type, display_no, trace_type, factor,
                                              amount,
                                              ibnr, os)
    select nextval('atr_seq_buss_ibnrcalc_final_trace'),
           action_no,
           icp_id,
           method_type,
           50,
           'PAD',
           0.2,
           (coalesce(a.ibnr, 0) + coalesce(a.os, 0)) * 0.2,
           coalesce(a.ibnr, 0) * 0.2,
           coalesce(a.os, 0) * 0.2
    from atr_buss_ibnrcalc_final_trace a
    where a.trace_type = 'DBCL'
      and action_no = p_action_no;
    -- 计算 Claim Liabilities
    insert into atr_buss_ibnrcalc_final_trace(id, action_no, icp_id, method_type, display_no, trace_type, factor,
                                              amount,
                                              ibnr, os)
    select nextval('atr_seq_buss_ibnrcalc_final_trace'),
           action_no,
           icp_id,
           method_type,
           60,
           'CL',
           null,
           coalesce(sum(ibnr), 0) + coalesce(sum(os), 0),
           coalesce(sum(ibnr), 0),
           coalesce(sum(os), 0)
    from atr_buss_ibnrcalc_final_trace
    where upper(trace_type) in ('DBCL', 'PAD')
      and action_no = p_action_no
    group by action_no, icp_id, method_type;

    -- 计算 IBNR_T
    insert into atr_buss_ibnrcalc_final_trace(id, action_no, icp_id, method_type, display_no, trace_type, factor,
                                              amount)
    select nextval('atr_seq_buss_ibnrcalc_final_trace'),
           action_no,
           icp_id,
           method_type,
           70,
           'IBNR_T',
           null,
           sum(case when trace_type = 'CL' then amount end) - sum(case when trace_type = 'OS' then amount end)
    from atr_buss_ibnrcalc_final_trace
    where trace_type in ('OS', 'CL')
      and action_no = p_action_no
    group by action_no, icp_id, method_type;

    -- 出险节点独立计算
    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step4_acc_node');

    call atr_pack_ibnrcalc_proc_calc_step4_acc_trace(p_action_no);


    call atr_pack_ibnrcalc_proc_log(p_action_no, 'step4_end');

    -- 兜底操作， 避免前端空指针异常
    update atr_buss_ibnrcalc_final_trace t
    set factor = coalesce(t.factor, 0),
        amount = coalesce(t.amount, 0),
        ibnr   = coalesce(t.ibnr, 0),
        os     = coalesce(t.os, 0)
    where t.action_no = p_action_no;

    update atr_buss_ibnrcalc_icp t
    set settled_expense             = coalesce(t.settled_expense, 0),
        settled_claim               = coalesce(t.settled_claim, 0),
        os_amount                   = coalesce(t.os_amount, 0),
        incurred_amount             = coalesce(t.incurred_amount, 0),
        expenses_ratio              = coalesce(t.expenses_ratio, 0),
        lr_time_value_discount      = coalesce(lr_time_value_discount, 0),
        cl_time_value_discount      = coalesce(cl_time_value_discount, 0),
        bf_time_value_discount      = coalesce(bf_time_value_discount, 0),
        lr_discounted_unpaid_amount = coalesce(lr_discounted_unpaid_amount, 0),
        cl_discounted_unpaid_amount = coalesce(cl_discounted_unpaid_amount, 0),
        bf_discounted_unpaid_amount = coalesce(bf_discounted_unpaid_amount, 0)
    where t.action_no = p_action_no;

    update atr_buss_ibnrcalc_dev_data t
    set time_period       = coalesce(time_period, 0),
        lr_interest_ratio = coalesce(lr_interest_ratio, 0),
        cl_interest_ratio = coalesce(cl_interest_ratio, 0),
        bf_interest_ratio = coalesce(bf_interest_ratio, 0)
    where t.action_no = p_action_no;

    commit;


end
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_calc_step4_acc_trace_manual(IN p_action_no character varying, IN p_modify_type integer, IN p_method_type character varying, IN p_icp_id bigint)
    LANGUAGE plpgsql
    AS $$
declare
    rec record;
begin
    for rec in select r.accident_node
               from atr_buss_ibnrcalc_acc_result r
               where action_no = p_action_no
               order by id
        loop
            if p_modify_type in (1, 2) then
                -- 删除Best estimate claim liabilities 的值
                delete
                from atr_buss_ibnrcalc_final_acc_trace
                where icp_id = p_icp_id
                  and action_no = p_action_no
                  and method_type = p_method_type
                  and accident_node = rec.accident_node
                  and trace_type = 'BSCL';
                -- 计算 Inderect Claim Handling Expense 的值
                update atr_buss_ibnrcalc_final_acc_trace q
                set os     = (select os * 0.5 * q.factor
                              from atr_buss_ibnrcalc_final_acc_trace
                              where icp_id = p_icp_id
                                and action_no = p_action_no
                                and method_type = p_method_type
                                and accident_node = rec.accident_node
                                and trace_type = 'OS'),
                    ibnr   = (select ibnr * q.factor
                              from atr_buss_ibnrcalc_final_acc_trace
                              where icp_id = p_icp_id
                                and action_no = p_action_no
                                and method_type = p_method_type
                                and accident_node = rec.accident_node
                                and trace_type = 'IBNR'),
                    amount = (select ibnr * q.factor
                              from atr_buss_ibnrcalc_final_acc_trace
                              where icp_id = p_icp_id
                                and action_no = p_action_no
                                and method_type = p_method_type
                                and accident_node = rec.accident_node
                                and trace_type = 'IBNR') + (select os * 0.5 * q.factor
                                                            from atr_buss_ibnrcalc_final_acc_trace
                                                            where icp_id = p_icp_id
                                                              and action_no = p_action_no
                                                              and method_type = p_method_type
                                                              and accident_node = rec.accident_node
                                                              and trace_type = 'OS')
                where q.icp_id = p_icp_id
                  and accident_node = rec.accident_node
                  and q.action_no = p_action_no
                  and q.method_type = p_method_type
                  and q.trace_type = 'ICHE';
                -- 计算 Best estimate claim liabilities 的值
                insert into atr_buss_ibnrcalc_final_acc_trace(id, action_no, icp_id,
                                                              method_type,
                                                              accident_node,
                                                              trace_type, factor,
                                                              amount,
                                                              ibnr, os)
                select nextval('atr_seq_buss_ibnrcalc_final_acc_trace'),
                       action_no,
                       icp_id,
                       method_type,
                       rec.accident_node,
                       'BSCL',
                       null,
                       sum(ibnr) + sum(os),
                       sum(ibnr),
                       sum(os)
                from atr_buss_ibnrcalc_final_acc_trace
                where upper(trace_type) in ('ICHE', 'OS', 'IBNR')
                  and action_no = p_action_no
                  and method_type = p_method_type
                  and icp_id = p_icp_id
                  and accident_node = rec.accident_node
                group by action_no, icp_id, method_type,rec.accident_node;
            end if;
            if p_modify_type in (1, 2, 3) then
                delete
                from atr_buss_ibnrcalc_final_acc_trace
                where icp_id = p_icp_id
                  and action_no = p_action_no
                  and method_type = p_method_type
                  and accident_node = rec.accident_node
                  and trace_type = 'DBCL';
                update atr_buss_ibnrcalc_final_acc_trace q
                set os     = os_b,
                    ibnr   = ibnr_b,
                    amount = os_b + ibnr_b
                from (select a.os * b.factor os_b, a.ibnr * b.factor ibnr_b
                      from atr_buss_ibnrcalc_final_acc_trace a
                               inner join atr_buss_ibnrcalc_final_acc_trace b on
                          a.icp_id = b.icp_id and a.action_no = b.action_no and a.method_type = b.method_type and a.accident_node = b.accident_node
                      where a.icp_id = p_icp_id
                        and a.action_no = p_action_no
                        and a.method_type = p_method_type
                        and a.trace_type = 'BSCL'
                        and b.trace_type = 'TVD') b
                where icp_id = p_icp_id
                  and accident_node = rec.accident_node
                  and action_no = p_action_no
                  and method_type = p_method_type
                  and trace_type = 'TVD';
                insert into atr_buss_ibnrcalc_final_acc_trace(id, action_no, icp_id, method_type, trace_type, factor,
                                                              accident_node,
                                                              amount,
                                                              ibnr, os)
                select nextval('atr_seq_buss_ibnrcalc_final_acc_trace'),
                       a.action_no,
                       a.icp_id,
                       a.method_type,
                       'DBCL',
                       null,
                       rec.accident_node,
                       a.ibnr + a.os - b.ibnr - b.os,
                       a.ibnr - b.ibnr,
                       a.os - b.os
                from atr_buss_ibnrcalc_final_acc_trace a
                         inner join atr_buss_ibnrcalc_final_acc_trace b
                                    on a.action_no = b.action_no and a.method_type = b.method_type and
                                       a.icp_id = b.icp_id and a.accident_node = b.accident_node
                where a.trace_type = 'BSCL'
                  and b.trace_type = 'TVD'
                  and a.accident_node = rec.accident_node
                  and a.method_type = p_method_type
                  and a.action_no = p_action_no
                  and a.icp_id = p_icp_id;
            end if;
            if p_modify_type in (1, 2, 3, 4) then
                delete
                from atr_buss_ibnrcalc_final_acc_trace
                where icp_id = p_icp_id
                  and action_no = p_action_no
                  and method_type = p_method_type
                  and accident_node = rec.accident_node
                  and trace_type = 'CL';
                update atr_buss_ibnrcalc_final_acc_trace q
                set os     = os_b,
                    ibnr   = ibnr_b,
                    amount = coalesce(os_b,0) + coalesce(ibnr_b,0)
                from (select a.os * b.factor os_b, a.ibnr * b.factor ibnr_b
                      from atr_buss_ibnrcalc_final_acc_trace a
                               inner join atr_buss_ibnrcalc_final_acc_trace b
                                          on a.action_no = b.action_no and a.method_type = b.method_type and
                                             a.icp_id = b.icp_id and a.accident_node = b.accident_node
                      where a.icp_id = p_icp_id
                        and a.action_no = p_action_no
                        and a.method_type = p_method_type
                        and a.accident_node = rec.accident_node
                        and a.trace_type = 'DBCL'
                        and b.trace_type = 'PAD') b
                where icp_id = p_icp_id
                  and action_no = p_action_no
                  and method_type = p_method_type
                  and accident_node = rec.accident_node
                  and trace_type = 'PAD';
                insert into atr_buss_ibnrcalc_final_acc_trace(id, action_no, icp_id, method_type,
                                                              accident_node,
                                                              trace_type, factor,
                                                              amount,
                                                              ibnr, os)
                select nextval('atr_seq_buss_ibnrcalc_final_acc_trace'),
                       action_no,
                       icp_id,
                       method_type,
                       rec.accident_node,
                       'CL',
                       null,
                       sum(ibnr) + sum(os),
                       sum(ibnr),
                       sum(os)
                from atr_buss_ibnrcalc_final_acc_trace
                where upper(trace_type) in ('DBCL', 'PAD')
                  and action_no = p_action_no
                  and method_type = p_method_type
                  and accident_node = rec.accident_node
                  and icp_id = p_icp_id
                group by action_no, icp_id, method_type,rec.accident_node;
            end if;
        end loop;

end
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_calc_step4_manual(IN p_action_no character varying, IN p_modify_type integer, IN p_method_type character varying, IN p_icp_id bigint)
    LANGUAGE plpgsql
    AS $$
    /*
    以合同组合为维度来进行修改
    - 修改Interest Ratio -> modify_type = 1，区分算法
        discount_unpaid 需要重算
        Time Value Discount  需要重算
        轨迹表 Time value discount（包含）以下需要重算
    - 修改Inderect Claim Handling Expense -> modify_type = 2,区分算法
        轨迹表 Inderect Claim Handling Expense（包含）以下需用重算
    - 修改Time value discount -> modify_type = 3, 区分算法
        轨迹表 Inderect Claim Handling Expense（包含）以下需用重算
    - 修改PAD -> modify_type = 4,  区分算法
        轨迹表 PAD（包含）以下需要重算
*/
DECLARE
BEGIN
    if p_modify_type = 1 then
        -- 计算 discount_unpaid
        merge into atr_buss_ibnrcalc_dev_data abidd
        using (select a.*, time_period
               from (select a.action_no,
                            a.icp_id,
                            a.dev_no,
                            sum(a.lr_reported_amount) lr_reported_amount,
                            sum(a.bf_reported_amount) bf_reported_amount,
                            sum(a.cl_reported_amount) cl_reported_amount
                     from atr_buss_ibnrcalc_acc_future_data a
                     group by a.action_no,
                              a.icp_id,
                              a.dev_no, future_node) a
                        inner join (select b.id, c.dev_no, time_period
                                    from atr_buss_ibnrcalc_icp b
                                             inner join atr_conf_ibnrcalc_quota_dev c on b.loa_code = c.loa_code) q
                                   on a.icp_id = q.id and q.dev_no = a.dev_no) t
        on abidd.action_no = t.action_no and abidd.icp_id = t.icp_id and
           abidd.dev_no = t.dev_no and
           abidd.action_no = p_action_no and
           abidd.action_no = p_action_no and
           abidd.icp_id = p_icp_id
        when matched then
            update
            set lr_discounted_unpaid_amount = t.lr_reported_amount / power(1 + lr_interest_ratio, t.time_period),
                cl_discounted_unpaid_amount = t.cl_reported_amount / power(1 + cl_interest_ratio, t.time_period),
                bf_discounted_unpaid_amount = t.bf_reported_amount / power(1 + bf_interest_ratio, t.time_period);
        -- 计算Time Value Discount
        merge into atr_buss_ibnrcalc_icp a
        using (select a.action_no,
                      a.icp_id,
                      case when a.lr_reported_amount != 0
                  then
                  1 - (b.lr_discounted_unpaid_amount / a.lr_reported_amount)
                  else 1 end lr_time_value_discount,
                  case when a.cl_reported_amount != 0 then 1 - (b.cl_discounted_unpaid_amount / a.cl_reported_amount) else 1 end cl_time_value_discount,
                  case when a.bf_reported_amount != 0 then 1 - (b.bf_discounted_unpaid_amount / a.bf_reported_amount) else 1 end bf_time_value_discount,
                      lr_discounted_unpaid_amount,
                      cl_discounted_unpaid_amount,
                      bf_discounted_unpaid_amount
               from (select action_no,
                            icp_id,
                            sum(lr_reported_amount) lr_reported_amount,
                            sum(cl_reported_amount) cl_reported_amount,
                            sum(bf_reported_amount) bf_reported_amount
                     from atr_buss_ibnrcalc_acc_future_data
                     where action_no = p_action_no
                     group by action_no, icp_id) a
                        inner join (select action_no,
                                           icp_id,
                                           sum(lr_discounted_unpaid_amount) lr_discounted_unpaid_amount,
                                           sum(cl_discounted_unpaid_amount) cl_discounted_unpaid_amount,
                                           sum(bf_discounted_unpaid_amount) bf_discounted_unpaid_amount
                                    from atr_buss_ibnrcalc_dev_data
                                    where action_no = p_action_no
                                    group by action_no, icp_id) b
                                   on a.action_no = b.action_no and a.icp_id = b.icp_id) q
        on a.action_no = q.action_no and a.id = q.icp_id
        when matched then
            update
            set lr_time_value_discount      = q.lr_time_value_discount,
                bf_time_value_discount      = q.bf_time_value_discount,
                cl_time_value_discount      = q.cl_time_value_discount,
                lr_discounted_unpaid_amount = q.lr_discounted_unpaid_amount,
                cl_discounted_unpaid_amount = q.cl_discounted_unpaid_amount,
                bf_discounted_unpaid_amount = q.bf_discounted_unpaid_amount;
    end if;
    if p_modify_type in (1, 2) then
        -- 删除Best estimate claim liabilities 的值
        delete
        from atr_buss_ibnrcalc_final_trace
        where icp_id = p_icp_id
          and action_no = p_action_no
          and method_type = p_method_type
          and trace_type = 'BSCL';
        -- 计算 Inderect Claim Handling Expense 的值
        update atr_buss_ibnrcalc_final_trace q
        set os     = (select os * 0.5 * q.factor
                      from atr_buss_ibnrcalc_final_trace
                      where icp_id = p_icp_id
                        and action_no = p_action_no
                        and method_type = p_method_type
                        and trace_type = 'OS'),
            ibnr   = (select ibnr * q.factor
                      from atr_buss_ibnrcalc_final_trace
                      where icp_id = p_icp_id
                        and action_no = p_action_no
                        and method_type = p_method_type
                        and trace_type = 'IBNR'),
            amount = (select ibnr * q.factor
                      from atr_buss_ibnrcalc_final_trace
                      where icp_id = p_icp_id
                        and action_no = p_action_no
                        and method_type = p_method_type
                        and trace_type = 'IBNR') + (select os * 0.5 * q.factor
                                                    from atr_buss_ibnrcalc_final_trace
                                                    where icp_id = p_icp_id
                                                      and action_no = p_action_no
                                                      and method_type = p_method_type
                                                      and trace_type = 'OS')
        where q.icp_id = p_icp_id
          and q.action_no = p_action_no
          and q.method_type = p_method_type
          and q.trace_type = 'ICHE';
        -- 计算 Best estimate claim liabilities 的值
        insert into atr_buss_ibnrcalc_final_trace(id, action_no, icp_id,
                                                  method_type, display_no,
                                                  trace_type, factor,
                                                  amount,
                                                  ibnr, os)
        select nextval('atr_seq_buss_ibnrcalc_final_trace'),
               action_no,
               icp_id,
               method_type,
               20,
               'BSCL',
               null,
               sum(ibnr) + sum(os),
               sum(ibnr),
               sum(os)
        from atr_buss_ibnrcalc_final_trace
        where upper(trace_type) in ('ICHE', 'OS', 'IBNR')
          and action_no = p_action_no
          and method_type = p_method_type
        group by action_no, icp_id, method_type;
    end if;
    if p_modify_type in (1, 2, 3) then
        delete
        from atr_buss_ibnrcalc_final_trace
        where icp_id = p_icp_id
          and action_no = p_action_no
          and method_type = p_method_type
          and trace_type = 'DBCL';
        update atr_buss_ibnrcalc_final_trace q
        set os     = os_b,
            ibnr   = ibnr_b,
            amount = os_b + ibnr_b
        from (select a.os * b.factor os_b, a.ibnr * b.factor ibnr_b
              from atr_buss_ibnrcalc_final_trace a
                       inner join atr_buss_ibnrcalc_final_trace b on
                  a.icp_id = b.icp_id and a.action_no = b.action_no and a.method_type = b.method_type
              where a.icp_id = p_icp_id
                and a.action_no = p_action_no
                and a.method_type = p_method_type
                and a.trace_type = 'BSCL'
                and b.trace_type = 'TVD') b
        where icp_id = p_icp_id
          and action_no = p_action_no
          and method_type = p_method_type
          and trace_type = 'TVD';
        insert into atr_buss_ibnrcalc_final_trace(id, action_no, icp_id, method_type, display_no, trace_type, factor,
                                                  amount,
                                                  ibnr, os)
        select nextval('atr_seq_buss_ibnrcalc_final_trace'),
               a.action_no,
               a.icp_id,
               a.method_type,
               40,
               'DBCL',
               null,
               a.ibnr + a.os - b.ibnr - b.os,
               a.ibnr - b.ibnr,
               a.os - b.os
        from atr_buss_ibnrcalc_final_trace a
                 inner join atr_buss_ibnrcalc_final_trace b
                            on a.action_no = b.action_no and a.method_type = b.method_type and a.icp_id = b.icp_id
        where a.trace_type = 'BSCL'
          and b.trace_type = 'TVD'
          and a.method_type = p_method_type
          and a.method_type = p_method_type
          and a.icp_id = p_icp_id;
    end if;
    if p_modify_type in (1, 2, 3, 4) then
        delete
        from atr_buss_ibnrcalc_final_trace
        where icp_id = p_icp_id
          and action_no = p_action_no
          and method_type = p_method_type
          and trace_type = 'CL';
        update atr_buss_ibnrcalc_final_trace q
        set os     = os_b,
            ibnr   = ibnr_b,
            amount = os_b + ibnr_b
        from (select a.os * b.factor os_b, a.ibnr * b.factor ibnr_b
              from atr_buss_ibnrcalc_final_trace a
                       inner join atr_buss_ibnrcalc_final_trace b
                                  on a.action_no = b.action_no and a.method_type = b.method_type and a.icp_id = b.icp_id
              where a.icp_id = p_icp_id
                and a.action_no = p_action_no
                and a.method_type = p_method_type
                and a.trace_type = 'DBCL'
                and b.trace_type = 'PAD') b
        where icp_id = p_icp_id
          and action_no = p_action_no
          and method_type = p_method_type
          and trace_type = 'PAD';
        insert into atr_buss_ibnrcalc_final_trace(id, action_no, icp_id, method_type, display_no, trace_type, factor,
                                                  amount,
                                                  ibnr, os)
        select nextval('atr_seq_buss_ibnrcalc_final_trace'),
               action_no,
               icp_id,
               method_type,
               60,
               'CL',
               null,
               sum(ibnr) + sum(os),
               sum(ibnr),
               sum(os)
        from atr_buss_ibnrcalc_final_trace
        where upper(trace_type) in ('DBCL', 'PAD')
          and action_no = p_action_no
          and method_type = p_method_type
        group by action_no, icp_id, method_type;
    end if;

    call atr_pack_ibnrcalc_proc_calc_step4_acc_trace_manual(p_action_no,p_modify_type,p_method_type,p_icp_id);
    -- 兜底操作， 避免前端空指针异常
    update atr_buss_ibnrcalc_final_trace t
    set factor = coalesce(t.factor, 0),
        amount = coalesce(t.amount, 0),
        ibnr   = coalesce(t.ibnr, 0),
        os     = coalesce(t.os, 0)
    where t.action_no = p_action_no
      and t.icp_id = p_icp_id;

    update atr_buss_ibnrcalc_icp t
    set settled_expense             = coalesce(t.settled_expense, 0),
        settled_claim               = coalesce(t.settled_claim, 0),
        os_amount                   = coalesce(t.os_amount, 0),
        incurred_amount             = coalesce(t.incurred_amount, 0),
        expenses_ratio              = coalesce(t.expenses_ratio, 0),
        lr_time_value_discount      = coalesce(lr_time_value_discount, 0),
        cl_time_value_discount      = coalesce(cl_time_value_discount, 0),
        bf_time_value_discount      = coalesce(bf_time_value_discount, 0),
        lr_discounted_unpaid_amount = coalesce(lr_discounted_unpaid_amount, 0),
        cl_discounted_unpaid_amount = coalesce(cl_discounted_unpaid_amount, 0),
        bf_discounted_unpaid_amount = coalesce(bf_discounted_unpaid_amount, 0)
    where t.action_no = p_action_no
      and t.id = p_icp_id;

    update atr_buss_ibnrcalc_dev_data t
    set time_period       = coalesce(time_period, 0),
        lr_interest_ratio = coalesce(lr_interest_ratio, 0),
        cl_interest_ratio = coalesce(cl_interest_ratio, 0),
        bf_interest_ratio = coalesce(bf_interest_ratio, 0)
    where t.action_no = p_action_no
      and t.icp_id = p_icp_id;

end
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_delete(IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
begin
    delete from atr_buss_ibnrcalc_action t where t.action_no = p_action_no;
    delete from atr_buss_ibnrcalc_accident_node t where t.action_no = p_action_no;
    delete from atr_buss_ibnrcalc_icp t where t.action_no = p_action_no;
    delete from atr_buss_ibnrcalc_inflation_ratio t where t.action_no = p_action_no;
    delete from atr_buss_ibnrcalc_acc_dev_data t where t.action_no = p_action_no;
    delete from atr_buss_ibnrcalc_dev_data t where t.action_no = p_action_no;
    delete from atr_buss_ibnrcalc_acc_result t where t.action_no = p_action_no;
    delete from atr_temp_ibnrcalc_acc_dev_buss_amount t where t.action_no = p_action_no;
end;
$$;

CREATE PROCEDURE atruser.atr_pack_ibnrcalc_proc_job_entry(IN p_entity_id bigint, IN p_year_month character varying, IN p_action_no character varying)
    LANGUAGE plpgsql
    AS $$
declare
    v_extraction_zones smallint := 32;
    v_confirm_method   varchar(2);
    v_count            int;
    v_cur_year_month   varchar(6);
    v_bussperiod_id    bigint;
begin

    select max(t.year_month), max(t.buss_period_id), count(*)
    into v_cur_year_month, v_bussperiod_id, v_count
    from atr_conf_bussperiod t
    where t.period_state = '0';

    if v_count = 0 then
        raise '%', 'There is no initial state for the business period.';
    end if;

    if v_count > 1 then
        raise '%', 'There are multiple initial business periods, and it is impossible to choose.';
    end if;

    if p_year_month <> v_cur_year_month then
        raise 'Business period conflict, should it be % or %?', p_year_month, v_cur_year_month;
    end if;

    select t.code_e_name::decimal
    into v_extraction_zones
    from atr_v_conf_code t
    where t.code_code_idx = 'IBNR_CALC_JOB_PARAM/EXTRACTION_ZONES';
    select t.code_e_name
    into v_confirm_method
    from atr_v_conf_code t
    where t.code_code_idx = 'IBNR_CALC_JOB_PARAM/CONFIRM_METHOD';

    v_extraction_zones := coalesce(v_extraction_zones, 36);
    v_confirm_method := coalesce(v_confirm_method, 'BF');

    insert into atr_buss_ibnrcalc_action (id, action_no, entity_id, currency_code, extraction_interval,
                                          extraction_zones, extraction_deadline, status, confirm_is, create_time)
    select nextval('atr_seq_buss_ibnrcalc_action') as id,
           p_action_no,
           p_entity_id,
           'IDR',
           'M'                                     as extraction_interval,
           v_extraction_zones,
           p_year_month                            as extraction_deadline,
           'R'                                     as status,
           '0'                                     as confirm_is,
           clock_timestamp()                       as create_time;

    commit;

    call atr_pack_ibnrcalc_proc_calc_step1(p_action_no);
    call atr_pack_ibnrcalc_proc_calc_step2(p_action_no);
    call atr_pack_ibnrcalc_proc_calc_step3(p_action_no);
    call atr_pack_ibnrcalc_proc_calc_step4(p_action_no);

    update atr_buss_ibnrcalc_action t
    set confirm_is     = '1',
        confirm_method = v_confirm_method,
        complete_time  = clock_timestamp(),
        status         = 'S'
    where t.action_no = p_action_no;

    update atr_conf_bussperiod_detail t
    set ready_state = '1'
    where t.buss_period_id = v_bussperiod_id
      and t.biz_code = 'ATR_IBNR_CALC';

    commit;

end
$$;


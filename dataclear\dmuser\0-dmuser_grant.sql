grant SELECT on DM_C<PERSON>IM_LOSS_DETAIL to ATRUSER;
grant SELECT on DM_REINS_OUTWARD to ATRUSER;
grant SELECT on DM_CLAIM_OUTSTANDING to ATR<PERSON><PERSON>;
grant SELECT on DM_REINS_TREATY to ATRUSER;
grant SELECT on DM_BUSS_CMUNIT_TREATY to ATRUSER;
grant SELECT on DM_CLAIM_MAIN to ATRUSER;
grant SELECT on DM_BUSS_CMUNIT_FAC_OUTWARDS to ATRUSER;
grant SELECT on DM_POLICY_PREMIUM to ATRUSER;
grant SELECT on DM_BUSS_CMUNIT_DIRECT to ATRUSER;
grant SELECT on DM_ACC_PAYMENT to ATRUSER;
grant SELECT on DM_ACC_RECEIVABLE to ATRUSER;
grant SELECT on DM_REINS_BILL_DETAIL to ATRUSER;
grant SELECT on DM_POLICY_MAIN to ATRUSER;
grant SELECT on DM_POLICY_PAYMENT_PLAN to ATRUSER;
grant SELECT on DM_<PERSON><PERSON>IM_LOSS to ATRUSER;
grant SELECT on DM_REINS_BILL to ATRUSER;
grant SELECT on DM_REINS_OUTWARD_DETAIL to ATRUSER;



grant SELECT on DM_FIN_VOUCHER to ACCUSER;
grant SELECT on DM_BUSS_CMUNIT_DIRECT to ACCUSER;
grant SELECT on DM_FIN_VOUCHER_DETAIL to ACCUSER;
grant SELECT on DM_FIN_LEDGER_BALANCE to ACCUSER;
grant SELECT on DM_REINS_TREATY to ACCUSER;
grant SELECT on DM_BUSS_CMUNIT_TREATY to ACCUSER;
grant SELECT on DM_POLICY_PREMIUM to ACCUSER;
grant SELECT on DM_CLAIM_LOSS to ACCUSER;
grant SELECT on DM_FIN_ARTICLE_BALANCE to ACCUSER;
grant SELECT on DM_REINS_BASE_TREATY to ACCUSER;
grant SELECT on DM_POLICY_MAIN to ACCUSER;
grant SELECT on DM_BUSS_CMUNIT_FAC_OUTWARDS to ACCUSER;
grant SELECT on DM_REINS_OUTWARD_DETAIL to ACCUSER;
grant SELECT on DM_ACC_PAYMENT to ACCUSER;
grant SELECT on DM_CLAIM_MAIN to ACCUSER;
grant EXECUTE on DM_PACK_BUSS_CMUNIT to ACCUSER;


grant SELECT on DM_BUSS_CMUNIT_TREATY to EXPUSER;
grant SELECT on DM_CLAIM_MAIN to EXPUSER;
grant SELECT on DM_FIN_LEDGER_BALANCE to EXPUSER;
grant SELECT on DM_POLICY_PREMIUM to EXPUSER;
grant SELECT on DM_BUSS_CMUNIT_DIRECT to EXPUSER;
grant SELECT on DM_FIN_ARTICLE_BALANCE to EXPUSER;
grant SELECT on DM_REINS_TREATY to EXPUSER;
grant SELECT on DM_REINS_BILL_DETAIL to EXPUSER;
grant SELECT on DM_POLICY_MAIN to EXPUSER;
grant SELECT on DM_POLICY_PAYMENT_PLAN to EXPUSER;
grant SELECT on DM_CLAIM_LOSS to EXPUSER;
grant SELECT on DM_REINS_BILL to EXPUSER;
grant SELECT on DM_CLAIM_LOSS_DETAIL to EXPUSER;




grant SELECT on DM_FIN_VOUCHER to ACCUSER;
grant SELECT on DM_BUSS_CMUNIT_DIRECT to ACCUSER;
grant SELECT on DM_BUSS_CMUNIT_DIRECT to EXPUSER;
grant SELECT on DM_BUSS_CMUNIT_DIRECT to ATRUSER;
grant SELECT on DM_CLAIM_LOSS_DETAIL to ATRUSER;
grant SELECT on DM_REINS_BILL to ATRUSER;
grant SELECT on DM_REINS_BILL to EXPUSER;
grant SELECT on DM_FIN_VOUCHER_DETAIL to ACCUSER;
grant SELECT on DM_FIN_LEDGER_BALANCE to ACCUSER;
grant SELECT on DM_FIN_LEDGER_BALANCE to EXPUSER;
grant SELECT on DM_REINS_TREATY to ATRUSER;
grant SELECT on DM_REINS_TREATY to EXPUSER;
grant SELECT on DM_REINS_TREATY to ACCUSER;
grant SELECT on DM_REINS_BILL_DETAIL to ATRUSER;
grant SELECT on DM_REINS_BILL_DETAIL to EXPUSER;
grant SELECT on DM_BUSS_CMUNIT_TREATY to ACCUSER;
grant SELECT on DM_BUSS_CMUNIT_TREATY to ATRUSER;
grant SELECT on DM_BUSS_CMUNIT_TREATY to EXPUSER;
grant SELECT on DM_POLICY_PREMIUM to ATRUSER;
grant SELECT on DM_POLICY_PREMIUM to EXPUSER;
grant SELECT on DM_POLICY_PREMIUM to ACCUSER;
grant SELECT on DM_CLAIM_LOSS to ATRUSER;
grant SELECT on DM_CLAIM_LOSS to ACCUSER;
grant SELECT on DM_CLAIM_LOSS to EXPUSER;
grant SELECT on DM_REINS_OUTWARD to ATRUSER;
grant SELECT on DM_REINS_BASE_TREATY to ACCUSER;
grant SELECT on DM_CLAIM_OUTSTANDING to ATRUSER;
grant SELECT on DM_POLICY_MAIN to ACCUSER;
grant SELECT on DM_POLICY_MAIN to ATRUSER;
grant SELECT on DM_POLICY_MAIN to EXPUSER;
grant SELECT on DM_ACC_RECEIVABLE to ATRUSER;
grant SELECT on DM_BUSS_CMUNIT_FAC_OUTWARDS to ACCUSER;
grant SELECT on DM_BUSS_CMUNIT_FAC_OUTWARDS to ATRUSER;
grant SELECT on DM_REINS_OUTWARD_DETAIL to ATRUSER;
grant SELECT on DM_POLICY_PAYMENT_PLAN to ATRUSER;
grant SELECT on DM_ACC_PAYMENT to ACCUSER;
grant SELECT on DM_ACC_PAYMENT to ATRUSER;
grant SELECT on DM_CLAIM_MAIN to ATRUSER;
grant SELECT on DM_CLAIM_MAIN to EXPUSER;
grant SELECT on DM_CLAIM_MAIN to ACCUSER;
grant EXECUTE on DM_PACK_BUSS_CMUNIT to ACCUSER;
grant SELECT on DM_BASE_PRODUCT to BPLUSER;
grant SELECT on DM_BASE_RISK_CLASS to BPLUSER;
grant SELECT on DM_BASE_RISK to BPLUSER;
grant SELECT on DM_BASE_RISK_MAPPING to BPLUSER;
grant SELECT on DM_REINS_BASE_TREATY to BPLUSER;
grant SELECT on DM_REINS_TREATY to BPLUSER;
grant SELECT on DM_REINS_TREATY_SECTION to BPLUSER;
grant SELECT on DM_REINS_REINSURER to BPLUSER;
grant SELECT on DM_REINS_FLOAT_CHARGE to BPLUSER;
grant SELECT on DM_POLICY_PAYMENT_PLAN to EXPUSER;
grant SELECT on DM_CLAIM_LOSS_DETAIL to EXPUSER;
grant SELECT on DM_FIN_ARTICLE_BALANCE to EXPUSER;
grant SELECT on DM_BASE_CURRENCY to BPLUSER;
grant SELECT on DM_BASE_CURRENCY_RATE to BPLUSER;
grant SELECT on DM_REINS_TREATY_CLASS to BPLUSER;
grant SELECT on DM_REINS_TREATY_PAYMENT_PLAN to BPLUSER;
grant SELECT on DM_REINS_RISK to BPLUSER;
grant SELECT on DM_BASE_ENTITY to BPLUSER;
grant SELECT on DM_BASE_ACCOUNT to BPLUSER;
grant SELECT on DM_FIN_ARTICLE_BALANCE to ACCUSER;
grant SELECT on DM_REINS_OUTWARD_DETAIL to ACCUSER;



--授權給bpluser
grant select on dmuser.dm_acc_payment to bplsuer;

